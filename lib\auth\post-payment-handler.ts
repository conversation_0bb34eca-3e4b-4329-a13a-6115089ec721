import { NextRequest, NextResponse } from 'next/server';

/**
 * Handle post-payment scenarios where subscription might not be immediately updated
 */
export async function handlePostPaymentValidation(request: NextRequest): Promise<{
  shouldSkipValidation: boolean;
  reason?: string;
}> {
  try {
    const url = new URL(request.url);

    // Check for payment success indicators
    const paymentSuccess = url.searchParams.get('payment_success') === 'true';
    const fromStripe = url.searchParams.get('fromStripe') === 'true';
    const sessionId = url.searchParams.get('session_id');

    if (paymentSuccess || fromStripe || sessionId) {
      return {
        shouldSkipValidation: true,
        reason: 'Payment completion detected'
      };
    }

    return { shouldSkipValidation: false };

  } catch (error) {
    console.error('Error in post-payment validation:', error);
    // On error, don't skip validation
    return { shouldSkipValidation: false };
  }
}

/**
 * Check if a subscription status indicates a recent payment that might still be processing
 */
export function isProcessingPayment(subscriptionStatus: string | null): boolean {
  if (!subscriptionStatus) return false;

  const processingStatuses = [
    'incomplete',
    'incomplete_expired',
    'past_due' // Sometimes shows briefly during payment processing
  ];

  return processingStatuses.includes(subscriptionStatus);
}

/**
 * Get a user-friendly message for subscription validation errors
 */
export function getValidationErrorMessage(
  errorType: string,
  subscriptionStatus?: string | null,
  planName?: string | null
): string {
  switch (errorType) {
    case 'validation-failed':
      return 'There was an error validating your subscription. This might be temporary - please try refreshing the page.';

    case 'subscription-required':
      return 'You need an active subscription to access this feature. Please subscribe to a plan to continue.';

    case 'feature-restricted':
      return `This feature is not available in your current plan${planName ? ` (${planName})` : ''}. Please upgrade to access this feature.`;

    case 'past-due':
      return 'Your subscription payment is past due. Please update your payment method to continue using premium features.';

    case 'inactive':
      if (isProcessingPayment(subscriptionStatus)) {
        return 'Your payment is being processed. Please wait a moment and try again.';
      }
      return 'Your subscription is inactive. Please subscribe to a plan to access premium features.';

    case 'no-team':
      return 'No team found for your account. Please contact support for assistance.';

    default:
      return 'There was an issue with your subscription. Please try again or contact support.';
  }
}

/**
 * Create a retry mechanism for subscription validation
 */
export async function retrySubscriptionValidation(
  validationFunction: () => Promise<boolean>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<boolean> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await validationFunction();
      if (result) {
        return true;
      }
    } catch (error) {
      console.error(`Subscription validation attempt ${attempt} failed:`, error);
    }

    if (attempt < maxRetries) {
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delayMs * attempt));
    }
  }

  return false;
}
