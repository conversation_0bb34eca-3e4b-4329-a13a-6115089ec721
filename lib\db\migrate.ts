import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import fs from 'fs';
import path from 'path';

// Next.js automatically loads environment variables from .env files

const runMigrations = async () => {
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not set');
  }

  // Create migrations meta directory if it doesn't exist
  const metaDir = path.join(process.cwd(), 'lib', 'db', 'migrations', 'meta');
  if (!fs.existsSync(metaDir)) {
    fs.mkdirSync(metaDir, { recursive: true });
  }

  // Create _journal.json if it doesn't exist
  const journalPath = path.join(metaDir, '_journal.json');
  if (!fs.existsSync(journalPath)) {
    fs.writeFileSync(journalPath, JSON.stringify({ entries: [] }));
  }

  // Connect to the database with increased timeout
  const connection = postgres(process.env.POSTGRES_URL, {
    max: 1,
    idle_timeout: 60,
    connect_timeout: 60,
  });

  const db = drizzle(connection);

  try {
    console.log('Starting database migration...');

    // Run the migration
    await migrate(db, { migrationsFolder: './lib/db/migrations' });

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    // Always close the connection
    await connection.end();
  }
};

// Run migrations
console.log('Preparing to run migrations...');
runMigrations()
  .then(() => {
    console.log('All migrations completed');
    process.exit(0);
  })
  .catch((err) => {
    console.error('Migration failed:', err);
    process.exit(1);
  });