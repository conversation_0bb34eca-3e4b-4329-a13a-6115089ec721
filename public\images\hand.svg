<svg width="223" height="222" viewBox="0 0 223 222" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_6323_6393)">
<rect x="60.5" y="30" width="102" height="102" rx="26" fill="#3384FF"/>
</g>
<g clip-path="url(#clip0_6323_6393)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M111 56L113.919 58.3612L117.632 57.7672L118.967 61.2718L122.482 62.6033L121.886 66.3059L124.254 69.2165L121.886 72.1271L122.482 75.8297L118.967 77.1613L117.632 80.6659L113.919 80.0719L111 82.433L108.081 80.0719L104.367 80.6659L103.032 77.1613L99.5174 75.8297L100.113 72.1271L97.7452 69.2165L100.113 66.3059L99.5174 62.6033L103.032 61.2718L104.367 57.7672L108.081 58.3612L111 56ZM113.467 97.9661H124.343C125.217 97.0999 126.175 96.199 127.143 95.2832C129.183 93.3577 131.313 91.348 133.502 88.8334C136.004 85.9574 136.272 84.9625 137.032 82.1459C137.176 81.6163 137.335 81.0223 137.543 80.2847L138.844 75.7258L138.859 75.6763C139.554 73.6468 139.608 72.3053 139.32 71.5331C139.231 71.2955 139.117 71.1371 138.983 71.053C138.878 70.9837 138.739 70.9589 138.596 70.9738C138.258 71.0084 137.876 71.2213 137.528 71.6024L133.666 80.7748C133.631 80.8589 133.582 80.9381 133.527 81.0074C133.299 81.4183 132.991 81.8192 132.594 82.2004L125.743 89.8184C125.371 90.2342 124.731 90.2689 124.319 89.8976C123.902 89.5264 123.867 88.8878 124.239 88.477C125.167 87.4474 132.365 80.4035 131.998 78.8245C131.541 76.8791 126.637 81.8737 126.051 82.3885L126.036 82.4033C123.162 85.1159 121.926 85.7594 119.672 86.9326C119.2 87.1801 118.684 87.4474 118.044 87.7939C117.791 87.9325 117.542 88.1008 117.299 88.2839C117.041 88.4819 116.793 88.6849 116.559 88.8829C115.368 89.8828 114.743 90.6946 114.385 91.5905C114.013 92.5211 113.879 93.6547 113.695 95.209C113.621 95.8327 113.566 96.4564 113.521 97.0751C113.497 97.3771 113.482 97.6741 113.467 97.9661ZM108.532 97.9661H97.6509C96.7772 97.0999 95.8191 96.199 94.8461 95.2832C92.8058 93.3577 90.6762 91.348 88.487 88.8334C85.985 85.9574 85.717 84.9625 84.9574 82.1459C84.8135 81.6163 84.6546 81.0223 84.4461 80.2847L83.1455 75.7258L83.1306 75.6763C82.4356 73.6468 82.381 72.3053 82.6689 71.5331C82.7583 71.2955 82.8725 71.1371 83.0065 71.053C83.1108 70.9837 83.2497 70.9589 83.3937 70.9738C83.7313 71.0084 84.1135 71.2213 84.461 71.6024L88.3232 80.7748C88.3579 80.8589 88.4075 80.9381 88.4622 81.0074C88.6905 81.4183 88.9983 81.8192 89.3954 82.2004L96.246 89.8184C96.6183 90.2342 97.2587 90.2689 97.6707 89.8976C98.0877 89.5264 98.1225 88.8878 97.7502 88.477C96.8318 87.4424 89.6337 80.3986 90.001 78.8195C90.4578 76.8742 95.3624 81.8687 95.9482 82.3835L95.963 82.3984C98.8373 85.111 100.073 85.7545 102.327 86.9276C102.799 87.1751 103.315 87.4424 103.955 87.7889C104.209 87.9275 104.457 88.0958 104.7 88.279C104.958 88.477 105.206 88.6799 105.44 88.8779C106.631 89.8778 107.257 90.6896 107.614 91.5856C107.986 92.5162 108.12 93.6497 108.304 95.204C108.378 95.8277 108.433 96.4514 108.478 97.0702C108.503 97.3771 108.517 97.6741 108.532 97.9661ZM95.1191 98.3126C94.9652 98.4908 94.8709 98.7235 94.8709 98.9759V104.985C94.8709 105.545 95.3276 105.995 95.8836 105.995H109.575C110.136 105.995 110.588 105.54 110.588 104.985V98.9215C110.588 98.278 110.548 97.585 110.503 96.9415C110.458 96.2831 110.399 95.6297 110.324 94.9813C110.126 93.2834 109.977 92.041 109.5 90.848C109.009 89.6254 108.22 88.571 106.755 87.3385C106.487 87.1108 106.214 86.8831 105.926 86.6702C105.623 86.4425 105.296 86.2247 104.928 86.0218C104.333 85.7 103.777 85.408 103.27 85.1407C101.185 84.0517 100.044 83.4577 97.358 80.9233C97.3381 80.9035 97.3133 80.8837 97.2935 80.8688L94.7021 78.5869C93.3916 77.0573 90.7953 75.2357 88.904 76.889C87.8962 74.4982 86.9034 69.2858 83.6072 68.9641C83.0065 68.9047 82.4257 69.0334 81.9193 69.3601C81.4378 69.6719 81.0407 70.157 80.7925 70.8253C80.3556 71.9885 80.3656 73.7953 81.2194 76.295L82.5101 80.8243C82.6987 81.4777 82.8625 82.1063 83.0164 82.6607C83.8603 85.804 84.1582 86.9177 86.9729 90.1451C89.2018 92.7043 91.3861 94.7635 93.476 96.7385C94.0171 97.2632 94.5631 97.783 95.1191 98.3126ZM97.0552 99.9857C97.1892 100.015 97.3282 100.015 97.4672 99.9857H108.557V103.975H96.8963V99.9857H97.0552ZM126.88 98.3126C127.034 98.4908 127.128 98.7235 127.128 98.9759V104.985C127.128 105.545 126.677 105.995 126.116 105.995H112.429C111.868 105.995 111.417 105.54 111.417 104.985V98.9215C111.417 98.8868 111.417 98.8472 111.422 98.8126C111.436 98.1641 111.461 97.5355 111.501 96.9415C111.546 96.2831 111.605 95.6297 111.68 94.9813C111.878 93.2834 112.027 92.041 112.504 90.848C112.995 89.6254 113.784 88.571 115.249 87.3385C115.517 87.1108 115.79 86.8831 116.078 86.6702C116.381 86.4425 116.708 86.2247 117.076 86.0218C117.671 85.7 118.227 85.408 118.734 85.1407C120.819 84.0517 121.961 83.4577 124.646 80.9233C124.666 80.9035 124.691 80.8837 124.711 80.8688L127.302 78.5869C128.613 77.0573 131.209 75.2357 133.1 76.889C133.592 75.7159 135.289 71.1223 135.875 70.4194C136.6 69.5531 137.518 69.0482 138.397 68.9591C138.998 68.8997 139.578 69.0284 140.085 69.3551C140.566 69.667 140.963 70.1521 141.212 70.8203C141.649 71.9836 141.639 73.7903 140.785 76.2901L139.494 80.8193C139.305 81.4727 139.142 82.1014 138.988 82.6558C138.144 85.799 137.846 86.9128 135.031 90.1402C132.802 92.6993 130.623 94.7585 128.528 96.7336C127.982 97.2632 127.436 97.783 126.88 98.3126ZM124.944 99.9857C124.81 100.015 124.671 100.015 124.532 99.9857H113.442V103.975H125.103V99.9857H124.944ZM111 64.5041L112.365 67.8305L115.964 68.0978L113.214 70.4194L114.072 73.9141L111.005 72.0182L107.937 73.9141L108.795 70.4243L106.035 68.1028L109.634 67.8355L111 64.5091V64.5041ZM111 61.747C115.135 61.747 118.486 65.0932 118.486 69.2116C118.486 73.3349 115.13 76.6762 111 76.6762C106.864 76.6762 103.514 73.33 103.514 69.2116C103.514 65.0932 106.864 61.747 111 61.747Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_6323_6393" x="0.5" y="0" width="222" height="222" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="30"/>
<feGaussianBlur stdDeviation="30"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.278431 0 0 0 0 0.290196 0 0 0 0 0.341176 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6323_6393"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6323_6393" result="shape"/>
</filter>
<clipPath id="clip0_6323_6393">
<rect width="61" height="50" fill="white" transform="translate(80.5 56)"/>
</clipPath>
</defs>
</svg>
