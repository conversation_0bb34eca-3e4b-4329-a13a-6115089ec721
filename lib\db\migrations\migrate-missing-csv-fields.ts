import { sql } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';

// Load environment variables from .env file
dotenv.config();

async function migrateMissingCsvFields() {
  console.log('Running migration to add missing CSV fields...');

  // Check if POSTGRES_URL is set
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not set');
  }

  console.log('Connecting to database...');

  // Create a new connection to the database
  const migrationClient = postgres(process.env.POSTGRES_URL, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'add_missing_csv_fields.sql');
    console.log(`Reading SQL file from: ${sqlFilePath}`);
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log('SQL file content:', sqlContent);

    // Execute the SQL statements
    console.log('Executing SQL statements...');
    await db.execute(sql.raw(sqlContent));

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    console.log('Closing database connection...');
    await migrationClient.end();
  }
}

// Run the migration
migrateMissingCsvFields().catch(console.error);
