import fs from 'fs';
import path from 'path';

/**
 * Cleans up temporary files in the mock-uploads directory
 * that are older than the specified age (in days)
 */
export async function cleanupTempFiles(maxAgeInDays: number = 7) {
  // Only run in development mode
  if (process.env.NODE_ENV !== 'development') {
    return {
      success: true,
      message: 'Skipped cleanup in non-development environment',
      filesRemoved: 0
    };
  }

  try {
    const mockUploadsDir = path.join(process.cwd(), 'public', 'mock-uploads', 'case-studies');
    
    // Check if directory exists
    if (!fs.existsSync(mockUploadsDir)) {
      return {
        success: true,
        message: 'Mock uploads directory does not exist',
        filesRemoved: 0
      };
    }

    const now = Date.now();
    const maxAgeMs = maxAgeInDays * 24 * 60 * 60 * 1000;
    let filesRemoved = 0;

    // Read all files in the directory
    const files = fs.readdirSync(mockUploadsDir);
    
    for (const file of files) {
      const filePath = path.join(mockUploadsDir, file);
      const stats = fs.statSync(filePath);
      
      // Check if file is older than maxAge
      if (now - stats.mtimeMs > maxAgeMs) {
        fs.unlinkSync(filePath);
        filesRemoved++;
      }
    }

    return {
      success: true,
      message: `Removed ${filesRemoved} old temporary files`,
      filesRemoved
    };
  } catch (error) {
    console.error('Error cleaning up temporary files:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      filesRemoved: 0
    };
  }
}
