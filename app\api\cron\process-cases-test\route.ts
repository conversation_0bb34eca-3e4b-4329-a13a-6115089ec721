import { NextResponse } from 'next/server';

export async function GET(req: Request) {
  try {
    console.log('Starting test cron job execution...');
    console.log('Environment variables:');
    console.log('- BASE_URL:', process.env.BASE_URL || 'http://localhost:3000');
    console.log('- BATCH_PROCESSING_API_KEY exists:', !!process.env.BATCH_PROCESSING_API_KEY);
    
    // Call the batch processing API
    const apiUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/api/case-studies/process-batch`;
    console.log('Calling batch processing API at:', apiUrl);
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.BATCH_PROCESSING_API_KEY || 'test-key'}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('Batch processing API response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Batch processing API error:', errorText);
      throw new Error(`Batch processing failed: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    console.log('Batch processing result:', result);
    
    return NextResponse.json({
      success: true,
      message: 'Test cron job executed successfully',
      result,
    });
  } catch (error) {
    console.error('Error executing test cron job:', error);
    return new NextResponse(
      JSON.stringify({
        success: false,
        error: 'Internal Error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
