'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Loader2, CheckCircle, XCircle, Bell } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

export default function InvitationBanner() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const inviteId = searchParams.get('inviteId');
  
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [invitation, setInvitation] = useState<any | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [showBanner, setShowBanner] = useState(!!inviteId);

  // Fetch the current user's email
  useEffect(() => {
    if (!inviteId) return;
    
    const fetchUserEmail = async () => {
      try {
        const response = await fetch('/api/auth/user');
        if (response.ok) {
          const data = await response.json();
          setUserEmail(data.email);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchUserEmail();
  }, [inviteId]);

  // Fetch invitation details
  useEffect(() => {
    if (!inviteId) return;

    const fetchInvitation = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/invitations/${inviteId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          setError(errorData.error || 'Failed to fetch invitation details');
          return;
        }
        
        const data = await response.json();
        setInvitation(data);
      } catch (error) {
        console.error('Error fetching invitation:', error);
        setError('An error occurred while fetching the invitation');
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [inviteId]);

  const handleAcceptInvitation = async () => {
    if (!invitation) return;
    
    try {
      setProcessing(true);
      const response = await fetch('/api/invitations/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invitationId: invitation.id }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process invitation');
      }
      
      toast({
        title: 'Success',
        description: `You have joined ${invitation.teamName} as a ${invitation.role}`,
      });
      
      // Hide the banner
      setShowBanner(false);
      
      // Refresh the page to update the UI
      router.refresh();
    } catch (error: any) {
      console.error('Error accepting invitation:', error);
      setError(error.message || 'An error occurred while processing the invitation');
      toast({
        title: 'Error',
        description: error.message || 'Failed to process invitation',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleDismiss = () => {
    setShowBanner(false);
  };

  // If no invitation ID is provided or banner is dismissed, don't render anything
  if (!inviteId || !showBanner) {
    return null;
  }

  // Show loading state
  if (loading) {
    return (
      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
        <div className="flex items-center">
          <Loader2 className="h-5 w-5 animate-spin text-blue-500 mr-3" />
          <p className="text-blue-700">Loading invitation details...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
        <div className="flex items-center">
          <XCircle className="h-5 w-5 text-red-500 mr-3" />
          <div>
            <p className="text-red-700 font-medium">Invitation Error</p>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
          <Button variant="ghost" size="sm" className="ml-auto" onClick={handleDismiss}>
            Dismiss
          </Button>
        </div>
      </div>
    );
  }

  // Show invitation details
  if (invitation) {
    // Check if the invitation is for the current user
    const isForCurrentUser = userEmail && userEmail === invitation.email;

    return (
      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
        <div className="flex items-center">
          <Bell className="h-5 w-5 text-blue-500 mr-3" />
          <div>
            <p className="text-blue-700 font-medium">Team Invitation</p>
            <p className="text-blue-600 text-sm">
              You've been invited to join <strong>{invitation.teamName}</strong> as a <strong>{invitation.role}</strong>.
            </p>
            {!isForCurrentUser && (
              <p className="text-yellow-600 text-sm mt-1">
                This invitation is for <strong>{invitation.email}</strong>, but you are currently logged in as <strong>{userEmail}</strong>.
              </p>
            )}
          </div>
          <div className="ml-auto flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleDismiss}
            >
              Dismiss
            </Button>
            <Button 
              variant="default" 
              size="sm" 
              onClick={handleAcceptInvitation} 
              disabled={processing || !isForCurrentUser}
            >
              {processing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Accept'
              )}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return null;
}
