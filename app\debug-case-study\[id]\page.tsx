import { db } from '@/lib/db/drizzle';
import { caseStudies } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import Link from 'next/link';

export default async function DebugCaseStudyPage({ params }: { params: { id: string } }) {
  const id = parseInt(params.id);
  
  // Fetch the case study data directly from the database
  const caseStudyData = await db.query.caseStudies.findFirst({
    where: eq(caseStudies.id, id),
    columns: {
      id: true,
      useCaseTitle: true,
      marketIntelligenceData: true,
      marketMetricsData: true,
      marketSize: true,
      marketCAGR: true,
      marketROI: true,
      aiProcessed: true,
      aiProcessingStatus: true,
    }
  });

  if (!caseStudyData) {
    return <div className="p-6 text-center text-red-500 font-medium">Case study not found</div>;
  }

  // Parse the market intelligence data
  let parsedMarketIntelligence = null;
  if (caseStudyData.marketIntelligenceData) {
    try {
      parsedMarketIntelligence = typeof caseStudyData.marketIntelligenceData === 'string' 
        ? JSON.parse(caseStudyData.marketIntelligenceData) 
        : caseStudyData.marketIntelligenceData;
    } catch (error) {
      console.error('Error parsing market intelligence data:', error);
    }
  }

  // Parse the market metrics data
  let parsedMarketMetrics = null;
  if (caseStudyData.marketMetricsData) {
    try {
      parsedMarketMetrics = typeof caseStudyData.marketMetricsData === 'string' 
        ? JSON.parse(caseStudyData.marketMetricsData) 
        : caseStudyData.marketMetricsData;
    } catch (error) {
      console.error('Error parsing market metrics data:', error);
    }
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Debug Case Study: {caseStudyData.useCaseTitle}</h1>
      
      <div className="mb-4">
        <Link href={`/dashboard/case-studies/${id}`} className="text-blue-600 hover:underline">
          View Case Study
        </Link>
      </div>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Raw Data</h2>
          <pre className="whitespace-pre-wrap text-sm">
            {JSON.stringify(caseStudyData, null, 2)}
          </pre>
        </div>
        
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Parsed Market Intelligence</h2>
          <pre className="whitespace-pre-wrap text-sm">
            {parsedMarketIntelligence ? JSON.stringify(parsedMarketIntelligence, null, 2) : 'No data'}
          </pre>
        </div>
        
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Parsed Market Metrics</h2>
          <pre className="whitespace-pre-wrap text-sm">
            {parsedMarketMetrics ? JSON.stringify(parsedMarketMetrics, null, 2) : 'No data'}
          </pre>
        </div>
      </div>
    </div>
  );
}
