'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Eye, EyeClosed, EyeOff, Loader2 } from 'lucide-react';
import { signUp, verifyAndCompleteSignup } from './actions';
import { useActionState } from 'react';
import { ActionState } from '@/lib/auth/middleware';
import Image from 'next/image';
import { SignupVerification } from './signup-verification';
import { industries } from '@/constants';

export function SignUp() {
  const searchParams = useSearchParams();
  const redirect = searchParams.get('redirect');
  const priceId = searchParams.get('priceId');
  const inviteId = searchParams.get('inviteId');
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    industry: '',
    companyName: '',
    agreeToTerms: false,
  });
  const [seePassword, setSeePassword] = useState(false);

  // State for OTP verification
  const [showVerification, setShowVerification] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [verificationData, setVerificationData] = useState<any>(null);

  const [state, formAction] = useActionState<ActionState, FormData>(
    async (prevState, formData) => {
      try {
        // Create a proper FormData object with all the form fields
        const data = new FormData();

        // Add all form fields from the state
        data.append('fullName', formData.get('fullName') as string);
        data.append('email', formData.get('email') as string);
        data.append('password', formData.get('password') as string);
        data.append('industry', formData.get('industry') as string);
        data.append('companyName', formData.get('companyName') as string);
        data.append('agreeToTerms', formData.get('agreeToTerms') as string);

        // Add optional fields if they exist
        if (formData.get('inviteId')) {
          data.append('inviteId', formData.get('inviteId') as string);
        }

        if (formData.get('redirect')) {
          data.append('redirect', formData.get('redirect') as string);
        }

        if (formData.get('priceId')) {
          data.append('priceId', formData.get('priceId') as string);
        }

        // Call the signUp action with the FormData
        const result: any = await signUp(prevState, data);

        // COMMENTED OUT: OTP verification logic

        // If verification is needed, show the OTP verification screen
        if (result.verificationNeeded) {
          setVerificationEmail(result.email);
          setVerificationData(result.formData);
          setShowVerification(true);
          return { success: '' }; // Return empty success to prevent redirect
        }

        return result;
      } catch (error) {
        console.error('Error during signup:', error);
        return { error: 'An unexpected error occurred. Please try again.' };
      }
    },
    {
      error: '',
      success: '/sign-in',
    }
  );

  const handleInputChange = (name: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle verification success
  const handleVerificationSuccess = async (formData: any) => {
    // Create a FormData object to match the expected format in the action
    const data = new FormData();

    // Add the required fields to the FormData
    data.append('email', verificationEmail);

    // Add the userData as a JSON string
    data.append('userData', JSON.stringify(formData));

    const result = await verifyAndCompleteSignup(null as any, data);

    if (result.success) {
      window.location.href = '/sign-in?verified=true';
    }
  };

  // If showing verification, render the verification component
  if (showVerification) {
    // console.log('Rendering verification component', {
    //   verificationEmail,
    //   verificationData,
    //   handleVerificationSuccess,
    // });

    return (
      <SignupVerification
        email={verificationEmail}
        formData={verificationData}
        onVerificationSuccess={handleVerificationSuccess}
      />
    );
  }

  return (
    <div className='min-h-[100dvh] flex'>
      <div className='flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8 bg-background dark:bg-black'>
        <div className='sm:mx-auto sm:w-full sm:max-w-md'>
          <div className='flex items-center mb-6'>
            <div className='block dark:hidden h-[110px] overflow-y-hidden'>
              <Image
                src='/images/logos/main-logo.svg'
                alt='Turinos AI Logo'
                width={180}
                height={110}
                className='w-[180px] h-auto object-contain'
              />
            </div>
            <div className='hidden dark:block'>
              <Image
                src='https://res.cloudinary.com/dyiso4ohk/image/upload/v1746113097/image_veff0p.png'
                alt='Turinos AI Logo'
                width={180}
                height={40}
                className='w-[180px] h-auto object-contain invert'
              />
            </div>
          </div>
          <h2 className='text-xl font-medium text-gray-900 dark:text-white'>
            Welcome to Turinos AI👋
          </h2>
          <p className='mt-2 text-sm text-gray-600 dark:text-white'>
            "Kindly fill in your details below to create an account"
          </p>
        </div>

        <div className='mt-8 sm:mx-auto sm:w-full sm:max-w-md'>
          <form className='space-y-6' action={formAction}>
            <div>
              <Label
                htmlFor='fullName'
                className='block text-sm font-medium text-gray-700 dark:text-white'
              >
                Full Name
              </Label>
              <div className='mt-1'>
                <Input
                  id='fullName'
                  name='fullName'
                  type='text'
                  required
                  value={formData.fullName}
                  onChange={(e) =>
                    handleInputChange('fullName', e.target.value)
                  }
                  className='appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 dark:border-white/20 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white dark:bg-black focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
                  placeholder='Enter your full name'
                />
              </div>
            </div>

            <div>
              <Label
                htmlFor='email'
                className='block text-sm font-medium text-gray-700 dark:text-white'
              >
                Business Email
              </Label>
              <div className='mt-1'>
                <Input
                  id='email'
                  name='email'
                  type='email'
                  required
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className='appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 dark:border-white/20 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white dark:bg-black focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
                  placeholder='Enter your business email'
                />
              </div>
            </div>

            <div>
              <Label
                htmlFor='companyName'
                className='block text-sm font-medium text-gray-700 dark:text-white'
              >
                Company Name
              </Label>
              <div className='mt-1'>
                <Input
                  id='companyName'
                  name='companyName'
                  type='text'
                  required
                  value={formData.companyName}
                  onChange={(e) =>
                    handleInputChange('companyName', e.target.value)
                  }
                  className='appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 dark:border-white/20 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white dark:bg-black focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
                  placeholder='Enter your company name'
                />
              </div>
            </div>

            <div>
              <Label
                htmlFor='password'
                className='block text-sm font-medium text-gray-700 dark:text-white'
              >
                Password
              </Label>
              <div className='mt-1 relative'>
                <Input
                  id='password'
                  name='password'
                  type={seePassword ? 'text' : 'password'}
                  required
                  minLength={8}
                  value={formData.password}
                  onChange={(e) =>
                    handleInputChange('password', e.target.value)
                  }
                  className='appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 dark:border-white/20 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white dark:bg-black focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
                  placeholder='Create a password'
                />
                <button
                  type='button'
                  onClick={() => setSeePassword(!seePassword)}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center text-sm text-gray-500 dark:text-white'
                >
                  {seePassword ? <Eye size={16} /> : <EyeOff size={16} />}
                </button>
              </div>
            </div>

            <div>
              <Label
                htmlFor='industry'
                className='block text-sm font-medium text-gray-700 dark:text-white'
              >
                Industry
              </Label>
              <div className='mt-1'>
                <Select
                  name='industry'
                  value={formData.industry}
                  onValueChange={(value) =>
                    handleInputChange('industry', value)
                  }
                >
                  <SelectTrigger className='w-full dark:bg-black dark:text-white dark:border-white/20'>
                    <SelectValue placeholder='Select your industry' />
                  </SelectTrigger>
                  <SelectContent className='dark:bg-black dark:text-white dark:border-white/20'>
                    {industries.map((industry, index) => (
                      <SelectItem key={index} value={industry.value}>
                        {industry.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className='flex items-center space-x-2'>
              <input
                type='hidden'
                name='agreeToTerms'
                value={formData.agreeToTerms ? 'true' : 'false'}
              />
              <Checkbox
                id='agreeToTerms'
                checked={formData.agreeToTerms}
                onCheckedChange={(checked) =>
                  handleInputChange('agreeToTerms', checked as boolean)
                }
                className='dark:border-white/20 dark:data-[state=checked]:bg-blue-600 dark:data-[state=checked]:border-blue-600'
              />
              <label
                htmlFor='agreeToTerms'
                className='text-sm text-gray-600 dark:text-white'
              >
                I agree to the Terms of Service and acknowledge you've read our
                Privacy Policy
              </label>
            </div>

            {/* Hidden field for invitation ID */}
            {inviteId && (
              <input type='hidden' name='inviteId' value={inviteId} />
            )}

            {state?.error && (
              <div className='text-red-500 dark:text-red-400 text-sm'>
                {state.error}
              </div>
            )}

            {inviteId && (
              <div className='bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-md p-4'>
                <p className='text-blue-800 dark:text-blue-300 text-sm'>
                  You're signing up with an invitation. You'll be added to the
                  team automatically.
                </p>
              </div>
            )}

            <div className='flex space-x-4'>
              <Button
                type='submit'
                className='flex-1 flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800'
                disabled={!formData.agreeToTerms}
              >
                Register with us
              </Button>
            </div>

            {/* Divider commented out
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">Or</span>
                </div>
              </div>
              */}

            {/* Google signup button removed */}

            <div className='text-center text-sm'>
              <span className='text-gray-600 dark:text-white'>
                Already have an account?{' '}
              </span>
              <Link
                href='/sign-in'
                className='text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300'
              >
                Login
              </Link>
            </div>
          </form>
        </div>
      </div>

      {/* Right side with blue background and content */}
      <div className='relative hidden lg:flex flex-1 items-center justify-center overflow-hidden rounded-l-4xl bg-[#3384FF] '>
        {/* Background Image */}
        <Image
          src='/sky.png'
          alt='Background'
          fill
          className='object-cover z-0 opacity-60 bg-blend-multiply'
          priority
        />

        {/* Content */}
        <div className='relative z-10 max-w-lg text-center text-white px-8'>
          {/* Semi-transparent backdrop box */}
          <div className='relative p-12 backdrop-blur-[15px] bg-white/10 '>
            <h2 className='text-4xl font-semibold mb-6 text-white'>
              Start your AI journey with Turinos AI
            </h2>
            <p className='text-xl text-blue-100'>
              Upload samples of your work to impress potential clients
            </p>
          </div>

          {/* Description Text - Positioned below box */}
          <div className='mt-24 text-center'>
            <p className='text-md text-white font-light'>
              Upload samples of your work to impress potential clients
            </p>
          </div>
          {/* <div className="mt-12 flex items-center justify-center gap-8">
            <button className="text-white/70 hover:text-white transition-colors">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            <div className="flex gap-2">
              <div className="w-2 h-2 rounded-full bg-gray-400"></div>
              <div className="w-2 h-2 rounded-full bg-white"></div>
              <div className="w-2 h-2 rounded-full bg-gray-400"></div>
              <div className="w-2 h-2 rounded-full bg-gray-400"></div>
            </div>

            <button className="text-white/70 hover:text-white transition-colors">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div> */}
        </div>
      </div>
    </div>
  );
}
