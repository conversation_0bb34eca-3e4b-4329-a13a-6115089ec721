/**
 * Utility functions for standardized error handling
 */

/**
 * Error types for better categorization
 */
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  NETWORK = 'NETWORK',
  SERVER = 'SERVER',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  UNKNOWN = 'UNKNOWN',
  FILE_PROCESSING = 'FILE_PROCESSING',
  DATA_PROCESSING = 'DATA_PROCESSING'
}

/**
 * Structured error response
 */
export interface ErrorResponse {
  type: ErrorType;
  message: string;
  details?: string;
  originalError?: any;
  statusCode?: number;
}

/**
 * Parses an error from RTK Query or other sources into a standardized format
 * @param error The error to parse
 * @returns A standardized error response
 */
export function parseError(error: any): ErrorResponse {
  // Default error response
  const defaultError: ErrorResponse = {
    type: ErrorType.UNKNOWN,
    message: 'An unexpected error occurred',
    details: 'Please try again later or contact support if the issue persists.'
  };

  // If no error provided
  if (!error) return defaultError;

  // Handle RTK Query errors
  if (error.status !== undefined) {
    // Network error
    if (error.status === 'FETCH_ERROR') {
      return {
        type: ErrorType.NETWORK,
        message: 'Network connection error',
        details: 'Please check your internet connection and try again.',
        originalError: error,
        statusCode: 0
      };
    }

    // Timeout error
    if (error.status === 'TIMEOUT_ERROR') {
      return {
        type: ErrorType.NETWORK,
        message: 'Request timed out',
        details: 'The server took too long to respond. Please try again later.',
        originalError: error,
        statusCode: 0
      };
    }

    // Parse error
    if (error.status === 'PARSING_ERROR') {
      return {
        type: ErrorType.DATA_PROCESSING,
        message: 'Error processing response data',
        details: 'The server response could not be processed correctly.',
        originalError: error,
        statusCode: error.originalStatus
      };
    }

    // Custom error
    if (error.status === 'CUSTOM_ERROR') {
      return {
        type: ErrorType.UNKNOWN,
        message: error.error?.message || 'Custom error',
        details: error.error?.details,
        originalError: error,
        statusCode: error.originalStatus
      };
    }

    // HTTP status code errors
    if (typeof error.status === 'number') {
      // Authentication error
      if (error.status === 401) {
        return {
          type: ErrorType.AUTHENTICATION,
          message: 'Authentication required',
          details: 'Please log in to continue.',
          originalError: error,
          statusCode: error.status
        };
      }

      // Authorization error
      if (error.status === 403) {
        return {
          type: ErrorType.AUTHORIZATION,
          message: 'Access denied',
          details: 'You do not have permission to perform this action.',
          originalError: error,
          statusCode: error.status
        };
      }

      // Not found
      if (error.status === 404) {
        return {
          type: ErrorType.NOT_FOUND,
          message: 'Resource not found',
          details: 'The requested resource could not be found.',
          originalError: error,
          statusCode: error.status
        };
      }

      // Validation error
      if (error.status === 400) {
        return {
          type: ErrorType.VALIDATION,
          message: 'Invalid request',
          details: error.data?.message || 'The request contains invalid data.',
          originalError: error,
          statusCode: error.status
        };
      }

      // Server error
      if (error.status >= 500) {
        return {
          type: ErrorType.SERVER,
          message: 'Server error',
          details: 'An error occurred on the server. Please try again later.',
          originalError: error,
          statusCode: error.status
        };
      }
    }
  }

  // Handle standard JS errors
  if (error instanceof Error) {
    // File processing errors
    if (error.message.includes('file') || error.message.includes('csv') || error.message.includes('parse')) {
      return {
        type: ErrorType.FILE_PROCESSING,
        message: 'File processing error',
        details: error.message,
        originalError: error
      };
    }

    return {
      type: ErrorType.UNKNOWN,
      message: error.message || 'An error occurred',
      details: error.stack,
      originalError: error
    };
  }

  // Handle error as string
  if (typeof error === 'string') {
    return {
      type: ErrorType.UNKNOWN,
      message: error,
      originalError: error
    };
  }

  // Handle error as object with message
  if (typeof error === 'object' && error !== null && 'message' in error) {
    return {
      type: ErrorType.UNKNOWN,
      message: error.message as string,
      details: JSON.stringify(error),
      originalError: error
    };
  }

  // Return default for unhandled error types
  return {
    ...defaultError,
    originalError: error
  };
}

/**
 * Logs an error with appropriate level and formatting
 * @param error The error to log
 * @param context Optional context information
 */
export function logError(error: any, context?: string): void {
  const parsedError = parseError(error);
  
  // Create a structured log message
  const logData = {
    timestamp: new Date().toISOString(),
    errorType: parsedError.type,
    message: parsedError.message,
    details: parsedError.details,
    statusCode: parsedError.statusCode,
    context
  };
  
  // Log with appropriate level based on error type
  switch (parsedError.type) {
    case ErrorType.NETWORK:
    case ErrorType.NOT_FOUND:
    case ErrorType.VALIDATION:
      console.warn('WARNING:', logData);
      console.warn('Original error:', parsedError.originalError);
      break;
    
    case ErrorType.AUTHENTICATION:
    case ErrorType.AUTHORIZATION:
      console.info('INFO:', logData);
      console.info('Original error:', parsedError.originalError);
      break;
    
    case ErrorType.SERVER:
    case ErrorType.UNKNOWN:
    case ErrorType.FILE_PROCESSING:
    case ErrorType.DATA_PROCESSING:
    default:
      console.error('ERROR:', logData);
      console.error('Original error:', parsedError.originalError);
      break;
  }
}

/**
 * Gets a user-friendly error message based on the error type
 * @param error The error to get a message for
 * @returns A user-friendly error message
 */
export function getUserFriendlyMessage(error: any): string {
  const parsedError = parseError(error);
  
  switch (parsedError.type) {
    case ErrorType.VALIDATION:
      return 'The information provided is invalid. Please check your input and try again.';
    
    case ErrorType.NETWORK:
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    
    case ErrorType.SERVER:
      return 'The server encountered an error. Our team has been notified and is working on it.';
    
    case ErrorType.AUTHENTICATION:
      return 'Your session has expired. Please log in again to continue.';
    
    case ErrorType.AUTHORIZATION:
      return 'You do not have permission to perform this action.';
    
    case ErrorType.NOT_FOUND:
      return 'The requested resource could not be found.';
    
    case ErrorType.FILE_PROCESSING:
      return 'There was an error processing your file. Please check the file format and try again.';
    
    case ErrorType.DATA_PROCESSING:
      return 'There was an error processing the data. Please try again or contact support.';
    
    case ErrorType.UNKNOWN:
    default:
      return 'An unexpected error occurred. Please try again later or contact support.';
  }
}
