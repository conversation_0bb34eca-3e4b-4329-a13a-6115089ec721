import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export interface CaseStudyIcon {
  id: number;
  caseStudyId: number;
  iconType: string;
  iconUrl: string;
  order: number;
  createdAt?: string;
}

export interface CaseStudyUser {
  id: number;
  name: string | null;
  email: string;
}

export interface CaseStudyResponse {
  id: number;
  useCaseTitle: string;
  industry: string | null;
  role: string | null;
  headerImage?: string | null;
  createdAt?: string;
  updatedAt?: string;
  icons?: CaseStudyIcon[];
  user?: CaseStudyUser;
  // Add other fields as needed
}

export interface CaseStudiesResponse {
  caseStudies: CaseStudyResponse[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

export interface CaseStudyData {
  id?: number;
  useCaseTitle: string;
  headerImage?: string;
  thumbnailImage?: string;
  industry?: string;
  role?: string;
  vector?: string;
  kpis?: {
    claimCycleTime: string;
    straightThroughRate: string;
    customerComplaintVolume: string;
  };
  introduction?: {
    title: string;
    text: string;
    problems: string[];
    questions: string[];
  };
  process?: {
    title: string;
    steps: Array<{
      title: string;
      description: string;
      icon?: string;
    }>;
  };
  solution?: {
    title: string;
    description: string;
    items: Array<{
      title: string;
      description: string;
      icon?: string;
    }>;
  };
  impact?: {
    title: string;
    metrics: Array<{
      metric: string;
      value: string;
      description: string;
      icon?: string;
    }>;
  };
  conclusion?: {
    title: string;
    text: string;
    resultTitle?: string;
    resultDescription?: string;
  };
  potentiallyImpactedKpis?: string;
}

export interface BulkImportResponse {
  success: boolean;
  imported: number;
  errors?: Array<{
    row: any;
    error: string;
  }>;
}

export const caseStudiesApi = createApi({
  reducerPath: 'caseStudiesApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['CaseStudies', 'CaseStudy'],
  endpoints: (builder) => ({
    getCaseStudies: builder.query<CaseStudiesResponse, { page?: number; pageSize?: number }>({
      query: ({ page = 1, pageSize = 10 }) => `case-studies?page=${page}&pageSize=${pageSize}`,
      providesTags: (result) =>
        result
          ? [
              ...result.caseStudies.map(({ id }) => ({ type: 'CaseStudies' as const, id })),
              { type: 'CaseStudies', id: 'LIST' },
            ]
          : [{ type: 'CaseStudies', id: 'LIST' }],
    }),

    getCaseStudyById: builder.query<CaseStudyResponse, number>({
      query: (id) => `case-studies/${id}`,
      providesTags: (result, error, id) => [{ type: 'CaseStudy', id }],
    }),

    createCaseStudy: builder.mutation<CaseStudyResponse, CaseStudyData>({
      query: (caseStudy) => ({
        url: 'case-studies',
        method: 'POST',
        body: caseStudy,
      }),
      invalidatesTags: [{ type: 'CaseStudies', id: 'LIST' }],
    }),

    updateCaseStudy: builder.mutation<CaseStudyResponse, { id: number; data: CaseStudyData }>({
      query: ({ id, data }) => ({
        url: `case-studies/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'CaseStudies', id: 'LIST' },
        { type: 'CaseStudy', id },
      ],
    }),

    deleteCaseStudy: builder.mutation<{ success: boolean }, number>({
      query: (id) => ({
        url: `case-studies/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'CaseStudies', id: 'LIST' },
        { type: 'CaseStudy', id }
      ],
    }),

    // Bulk import endpoint
    importCaseStudies: builder.mutation<BulkImportResponse, FormData>({
      query: (formData) => ({
        url: 'case-studies/upload-csv',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: [{ type: 'CaseStudies', id: 'LIST' }],
    }),

    // Export endpoint
    exportCaseStudies: builder.query<Blob, void>({
      query: () => ({
        url: 'case-studies/export-csv',
        method: 'GET',
        responseHandler: (response) => response.blob(),
      }),
    }),

    // Media upload is now handled directly with Cloudinary Upload Widget
  }),
});

export const {
  useGetCaseStudiesQuery,
  useGetCaseStudyByIdQuery,
  useCreateCaseStudyMutation,
  useUpdateCaseStudyMutation,
  useDeleteCaseStudyMutation,
  useImportCaseStudiesMutation,
  useLazyExportCaseStudiesQuery,
} = caseStudiesApi;
