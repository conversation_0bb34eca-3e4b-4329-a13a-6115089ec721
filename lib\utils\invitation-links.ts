/**
 * Generate an invitation link for a team invitation
 */
export function generateInvitationLink(invitationId: number): string {
  const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
  return `${baseUrl}/sign-up?inviteId=${invitationId}`;
}

/**
 * Extract invitation ID from a URL
 */
export function extractInvitationId(url: string): number | null {
  try {
    const urlObj = new URL(url);
    const inviteId = urlObj.searchParams.get('inviteId');
    return inviteId ? parseInt(inviteId, 10) : null;
  } catch (error) {
    return null;
  }
}
