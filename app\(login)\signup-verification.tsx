'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { OtpVerification } from '@/components/OtpVerification/OtpVerification';
import Image from 'next/image';
import { toast } from '@/components/ui/use-toast';

interface SignupVerificationProps {
  email: string;
  formData: any;
  onVerificationSuccess: (formData: any) => void;
}

export function SignupVerification({
  email,
  formData,
  onVerificationSuccess,
}: SignupVerificationProps) {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle OTP verification
  const handleVerifyOtp = async (otp: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/otp', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          otp,
          purpose: 'signup',
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Email verified successfully',
          description:
            'Your email has been verified. Proceeding with account creation.',
        });
        return true;
      } else {
        toast({
          title: 'Verification failed',
          description:
            data.message || 'Failed to verify OTP. Please try again.',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      toast({
        title: 'Verification error',
        description: 'An error occurred during verification. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Handle OTP resend
  const handleResendOtp = async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          purpose: 'signup',
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'OTP resent',
          description: 'A new verification code has been sent to your email.',
        });
        return true;
      } else {
        toast({
          title: 'Failed to resend OTP',
          description:
            data.message ||
            'Failed to send new verification code. Please try again.',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Error resending OTP:', error);
      toast({
        title: 'Error',
        description:
          'An error occurred while resending the verification code. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Handle verification success
  const handleVerificationSuccess = () => {
    onVerificationSuccess(formData);
  };

  if (!mounted) return null;

  return (
    <div className='min-h-[100dvh] flex'>
      <div className='flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8 bg-background'>
        <div className='sm:mx-auto sm:w-full sm:max-w-md'>
          <div
            className='flex items-center justify-center mb-6 max-h-[40px] overflow-hidden cursor-pointer'
            onClick={() => {
              window.location.href = '/';
            }}
          >
            <Image
              src='/images/logos/main-logo.svg'
              alt='Turinos AI Logo'
              width={190}
              height={40}
              className='w-[180px] h-auto object-contain'
            />
          </div>

          {/* Card-like container similar to dialog */}
          <div className='bg-white dark:bg-gray-800 p-6 rounded-lg dark:border-gray-700'>
            <div className='mb-6'>
              {/* <h2 className='text-xl font-semibold text-foreground'>
                Verify Your Email
              </h2> */}
              <p className='mt-2 text-sm text-muted-foreground'>
                Please verify your email address to complete your registration
              </p>
            </div>

            <OtpVerification
              email={email}
              purpose='signup'
              onVerificationSuccess={handleVerificationSuccess}
              onResendOtp={handleResendOtp}
              onVerifyOtp={handleVerifyOtp}
            />
          </div>
        </div>
      </div>

      {/* Right side with blue background and content */}
      <div className='relative hidden lg:flex flex-1 items-center justify-center overflow-hidden rounded-l-4xl bg-[#3384FF] dark:bg-blue-900'>
        {/* Background Image */}
        <Image
          src='/sky.png'
          alt='Background'
          fill
          className='object-cover z-0 opacity-60 bg-blend-multiply'
          priority
        />

        {/* Content */}
        <div className='relative z-10 max-w-lg text-center text-white px-8'>
          {/* Semi-transparent backdrop box */}
          <div className='relative p-12 backdrop-blur-[15px] bg-white/10 dark:bg-black/20'>
            <h2 className='text-4xl font-semibold mb-6 text-white'>
              Almost there!
            </h2>
            <p className='text-xl text-blue-100'>
              Verify your email to complete your registration and start your AI
              journey
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
