/**
 * Complete Stripe Plan Reset Script
 * This will DELETE ALL existing plans and create the correct structure:
 * - Starter Plan: 14-day free trial
 * - Pro Plan: Paid subscription
 * - Enterprise: Contact Us (no Stripe product)
 */

import Stripe from 'stripe';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// Your actual pricing (update these values)
const PRO_MONTHLY_PRICE = 60.00; // $60/month
const PRO_YEARLY_PRICE = 600.00;  // $600/year (10 months price)

async function completeReset(): Promise<void> {
  console.log('🚨 COMPLETE STRIPE PLAN RESET');
  console.log('==============================\n');
  
  console.log('This script will:');
  console.log('❌ DELETE all existing Stripe products and prices');
  console.log('✅ CREATE new plans:');
  console.log('   📋 Starter Plan: 14-day free trial');
  console.log(`   📋 Pro Plan: $${PRO_MONTHLY_PRICE}/month or $${PRO_YEARLY_PRICE}/year`);
  console.log('   📋 Enterprise: Contact Us (no Stripe product)');
  console.log('');

  try {
    // Step 1: Check for active subscriptions
    console.log('🔍 Step 1: Checking for active subscriptions...');
    const activeSubscriptions = await stripe.subscriptions.list({ 
      status: 'active',
      limit: 100 
    });

    if (activeSubscriptions.data.length > 0) {
      console.log(`⚠️  WARNING: Found ${activeSubscriptions.data.length} active subscriptions:`);
      activeSubscriptions.data.forEach((sub, index) => {
        console.log(`   ${index + 1}. ${sub.id} (Customer: ${sub.customer})`);
      });
      console.log('   These subscriptions will continue to work, but you may need to migrate them later.');
      console.log('');
    } else {
      console.log('✅ No active subscriptions found - safe to proceed!');
    }

    // Step 2: Archive all existing prices
    console.log('🗑️  Step 2: Archiving all existing prices...');
    const allPrices = await stripe.prices.list({ limit: 100 });
    let archivedPrices = 0;
    
    for (const price of allPrices.data) {
      if (price.active) {
        await stripe.prices.update(price.id, { active: false });
        console.log(`   ✅ Archived price: ${price.id}`);
        archivedPrices++;
      }
    }
    console.log(`   📊 Total prices archived: ${archivedPrices}`);

    // Step 3: Archive all existing products
    console.log('\n🗑️  Step 3: Archiving all existing products...');
    const allProducts = await stripe.products.list({ limit: 100 });
    let archivedProducts = 0;
    
    for (const product of allProducts.data) {
      if (product.active) {
        await stripe.products.update(product.id, { active: false });
        console.log(`   ✅ Archived product: ${product.name} (${product.id})`);
        archivedProducts++;
      }
    }
    console.log(`   📊 Total products archived: ${archivedProducts}`);

    // Step 4: Create Starter Plan (14-day trial)
    console.log('\n🆕 Step 4: Creating Starter Plan...');
    
    const starterProduct = await stripe.products.create({
      name: 'Starter Plan',
      description: 'Perfect for individuals getting started - 14 day free trial',
      metadata: {
        plan_type: 'starter',
        trial_days: '14',
        created_by: 'morphx-complete-reset',
        created_at: new Date().toISOString()
      }
    });
    console.log(`   ✅ Created Starter product: ${starterProduct.name} (${starterProduct.id})`);

    // Create trial price for Starter
    const starterTrialPrice = await stripe.prices.create({
      product: starterProduct.id,
      currency: 'usd',
      unit_amount: 0, // Free
      recurring: {
        interval: 'month',
        trial_period_days: 14
      },
      metadata: {
        plan_type: 'starter',
        billing_interval: 'trial',
        trial_days: '14'
      }
    });
    console.log(`   ✅ Created Starter trial price: Free with 14-day trial (${starterTrialPrice.id})`);

    // Step 5: Create Pro Plan
    console.log('\n🆕 Step 5: Creating Pro Plan...');
    
    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'For teams and businesses ready to scale with AI',
      metadata: {
        plan_type: 'pro',
        created_by: 'morphx-complete-reset',
        created_at: new Date().toISOString()
      }
    });
    console.log(`   ✅ Created Pro product: ${proProduct.name} (${proProduct.id})`);

    // Create monthly price for Pro
    const proMonthlyPrice = await stripe.prices.create({
      product: proProduct.id,
      currency: 'usd',
      unit_amount: Math.round(PRO_MONTHLY_PRICE * 100), // Convert to cents
      recurring: {
        interval: 'month'
      },
      metadata: {
        plan_type: 'pro',
        billing_interval: 'monthly'
      }
    });
    console.log(`   ✅ Created Pro monthly price: $${PRO_MONTHLY_PRICE}/month (${proMonthlyPrice.id})`);

    // Create yearly price for Pro
    const proYearlyPrice = await stripe.prices.create({
      product: proProduct.id,
      currency: 'usd',
      unit_amount: Math.round(PRO_YEARLY_PRICE * 100), // Convert to cents
      recurring: {
        interval: 'year'
      },
      metadata: {
        plan_type: 'pro',
        billing_interval: 'yearly'
      }
    });
    console.log(`   ✅ Created Pro yearly price: $${PRO_YEARLY_PRICE}/year (${proYearlyPrice.id})`);

    // Step 6: Generate environment variables
    console.log('\n📝 Step 6: Environment Variables');
    console.log('=================================');
    console.log('Copy these to your .env file and REPLACE any existing Stripe variables:\n');
    
    console.log('# Stripe Product IDs');
    console.log(`STRIPE_STARTER_PRODUCT_ID=${starterProduct.id}`);
    console.log(`STRIPE_PRO_PRODUCT_ID=${proProduct.id}`);
    console.log('');
    console.log('# Stripe Price IDs');
    console.log(`STRIPE_STARTER_TRIAL_PRICE_ID=${starterTrialPrice.id}`);
    console.log(`STRIPE_PRO_MONTHLY_PRICE_ID=${proMonthlyPrice.id}`);
    console.log(`STRIPE_PRO_YEARLY_PRICE_ID=${proYearlyPrice.id}`);
    console.log('');
    console.log('# Remove these old variables if they exist:');
    console.log('# STRIPE_ENTERPRISE_PRODUCT_ID (not needed)');
    console.log('# STRIPE_ENTERPRISE_MONTHLY_PRICE_ID (not needed)');
    console.log('# STRIPE_ENTERPRISE_YEARLY_PRICE_ID (not needed)');

    // Step 7: Summary
    console.log('\n🎉 RESET COMPLETED SUCCESSFULLY!');
    console.log('==================================');
    console.log('✅ All old products and prices archived');
    console.log('✅ Starter Plan created (14-day trial)');
    console.log(`✅ Pro Plan created ($${PRO_MONTHLY_PRICE}/month, $${PRO_YEARLY_PRICE}/year)`);
    console.log('✅ Environment variables generated');
    
    console.log('\n📋 Next Steps:');
    console.log('1. Copy the environment variables to your .env file');
    console.log('2. Remove any old Stripe environment variables');
    console.log('3. Restart your application');
    console.log('4. Update your pricing page to show only Starter and Pro');
    console.log('5. Set Enterprise to "Contact Us" button (no Stripe integration)');
    console.log('6. Test the new plans in your billing page');

    if (activeSubscriptions.data.length > 0) {
      console.log('\n⚠️  IMPORTANT: Active Subscriptions');
      console.log('You have active subscriptions that may need migration.');
      console.log('Consider creating a migration plan for existing customers.');
    }

    console.log('\n🔗 Plan Structure Created:');
    console.log('┌─────────────┬──────────────────┬─────────────────────┐');
    console.log('│ Plan        │ Price            │ Stripe Integration  │');
    console.log('├─────────────┼──────────────────┼─────────────────────┤');
    console.log('│ Starter     │ 14-day trial     │ ✅ Yes              │');
    console.log(`│ Pro         │ $${PRO_MONTHLY_PRICE}/month        │ ✅ Yes              │`);
    console.log(`│ Pro         │ $${PRO_YEARLY_PRICE}/year         │ ✅ Yes              │`);
    console.log('│ Enterprise  │ Contact Us       │ ❌ No (manual)      │');
    console.log('└─────────────┴──────────────────┴─────────────────────┘');

  } catch (error: any) {
    console.error('\n❌ ERROR DURING RESET:', error.message);
    console.error('Stack trace:', error.stack);
    
    if (error.type === 'StripeAuthenticationError') {
      console.error('\n🔑 Authentication Error:');
      console.error('   Check your STRIPE_SECRET_KEY in .env file');
      console.error('   Make sure you\'re using the correct key (test vs live)');
    }
    
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  console.log('⚠️  WARNING: This will DELETE all existing Stripe products and prices!');
  console.log('   Make sure you have backed up any important data.');
  console.log('   Press Ctrl+C to cancel, or wait 5 seconds to continue...\n');
  
  setTimeout(() => {
    completeReset();
  }, 5000);
}

export { completeReset };
