import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import { sql } from 'drizzle-orm';

// Next.js automatically loads environment variables from .env files

async function main() {
  console.log('Running migration to add bookmarks table...');

  const connectionString = process.env.POSTGRES_URL || process.env.DATABASE_URL || '';
  if (!connectionString) {
    throw new Error('POSTGRES_URL or DATABASE_URL is not defined');
  }

  // Create a new connection to the database
  const migrationClient = postgres(connectionString, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Check if the bookmarks table already exists
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'bookmarks'
      );
    `);

    if (tableExists[0].exists) {
      console.log('Bookmarks table already exists, skipping migration');
      return;
    }

    // Create the bookmarks table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS bookmarks (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        case_study_id INTEGER NOT NULL REFERENCES case_studies(id),
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);

    // Create an index for faster lookups
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_bookmarks_user_id ON bookmarks(user_id);
    `);

    // Create a unique constraint to prevent duplicate bookmarks
    await db.execute(sql`
      CREATE UNIQUE INDEX IF NOT EXISTS idx_bookmarks_user_case_unique ON bookmarks(user_id, case_study_id);
    `);

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}

main().catch((err) => {
  console.error('Migration error:', err);
  process.exit(1);
});
