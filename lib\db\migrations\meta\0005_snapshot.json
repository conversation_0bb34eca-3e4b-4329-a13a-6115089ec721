{"id": "cd47c41e-ce52-4ab3-b6c8-508445c57d49", "prevId": "7050d09b-7d60-4868-a299-62a1c0421729", "version": "7", "dialect": "postgresql", "tables": {"public.activity_logs": {"name": "activity_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"activity_logs_team_id_teams_id_fk": {"name": "activity_logs_team_id_teams_id_fk", "tableFrom": "activity_logs", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "activity_logs_user_id_users_id_fk": {"name": "activity_logs_user_id_users_id_fk", "tableFrom": "activity_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bookmarks": {"name": "bookmarks", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "case_study_id": {"name": "case_study_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"bookmarks_user_id_users_id_fk": {"name": "bookmarks_user_id_users_id_fk", "tableFrom": "bookmarks", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bookmarks_case_study_id_case_studies_id_fk": {"name": "bookmarks_case_study_id_case_studies_id_fk", "tableFrom": "bookmarks", "tableTo": "case_studies", "columnsFrom": ["case_study_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.case_studies": {"name": "case_studies", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "use_case_title": {"name": "use_case_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "industry": {"name": "industry", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "vector": {"name": "vector", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "potentially_impacted_kpis": {"name": "potentially_impacted_kpis", "type": "text", "primaryKey": false, "notNull": false}, "introduction_title": {"name": "introduction_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "introduction_text": {"name": "introduction_text", "type": "text", "primaryKey": false, "notNull": false}, "transition_to_challange": {"name": "transition_to_challange", "type": "text", "primaryKey": false, "notNull": false}, "challange_1": {"name": "challange_1", "type": "text", "primaryKey": false, "notNull": false}, "challange_2": {"name": "challange_2", "type": "text", "primaryKey": false, "notNull": false}, "challange_3": {"name": "challange_3", "type": "text", "primaryKey": false, "notNull": false}, "transition_to_questions": {"name": "transition_to_questions", "type": "text", "primaryKey": false, "notNull": false}, "question_1": {"name": "question_1", "type": "text", "primaryKey": false, "notNull": false}, "question_2": {"name": "question_2", "type": "text", "primaryKey": false, "notNull": false}, "question_3": {"name": "question_3", "type": "text", "primaryKey": false, "notNull": false}, "process_title": {"name": "process_title", "type": "text", "primaryKey": false, "notNull": false}, "process_step_1_title": {"name": "process_step_1_title", "type": "text", "primaryKey": false, "notNull": false}, "process_step_1_description": {"name": "process_step_1_description", "type": "text", "primaryKey": false, "notNull": false}, "process_step_2_title": {"name": "process_step_2_title", "type": "text", "primaryKey": false, "notNull": false}, "process_step_2_description": {"name": "process_step_2_description", "type": "text", "primaryKey": false, "notNull": false}, "process_step_3_title": {"name": "process_step_3_title", "type": "text", "primaryKey": false, "notNull": false}, "process_step_3_description": {"name": "process_step_3_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_title": {"name": "solution_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_description": {"name": "solution_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_1_title": {"name": "solution_1_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_1_description": {"name": "solution_1_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_2_title": {"name": "solution_2_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_2_description": {"name": "solution_2_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_3_title": {"name": "solution_3_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_3_description": {"name": "solution_3_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_4_title": {"name": "solution_4_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_4_description": {"name": "solution_4_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_5_title": {"name": "solution_5_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_5_description": {"name": "solution_5_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_1": {"name": "solution_1", "type": "text", "primaryKey": false, "notNull": false}, "solution_2": {"name": "solution_2", "type": "text", "primaryKey": false, "notNull": false}, "solution_3": {"name": "solution_3", "type": "text", "primaryKey": false, "notNull": false}, "solution_4": {"name": "solution_4", "type": "text", "primaryKey": false, "notNull": false}, "solution_5": {"name": "solution_5", "type": "text", "primaryKey": false, "notNull": false}, "impact_title": {"name": "impact_title", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_1": {"name": "potential_impact_1", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_2": {"name": "potential_impact_2", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_3": {"name": "potential_impact_3", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_4": {"name": "potential_impact_4", "type": "text", "primaryKey": false, "notNull": false}, "impact_metric_1": {"name": "impact_metric_1", "type": "text", "primaryKey": false, "notNull": false}, "impact_value_1": {"name": "impact_value_1", "type": "text", "primaryKey": false, "notNull": false}, "impact_metric_2": {"name": "impact_metric_2", "type": "text", "primaryKey": false, "notNull": false}, "impact_value_2": {"name": "impact_value_2", "type": "text", "primaryKey": false, "notNull": false}, "impact_metric_3": {"name": "impact_metric_3", "type": "text", "primaryKey": false, "notNull": false}, "impact_value_3": {"name": "impact_value_3", "type": "text", "primaryKey": false, "notNull": false}, "impact_metric_4": {"name": "impact_metric_4", "type": "text", "primaryKey": false, "notNull": false}, "impact_value_4": {"name": "impact_value_4", "type": "text", "primaryKey": false, "notNull": false}, "conclusion_title": {"name": "conclusion_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "conclusion_text": {"name": "conclusion_text", "type": "text", "primaryKey": false, "notNull": false}, "conclusion_result": {"name": "conclusion_result", "type": "text", "primaryKey": false, "notNull": false}, "conclusion_result_title": {"name": "conclusion_result_title", "type": "text", "primaryKey": false, "notNull": false}, "feature_image_url": {"name": "feature_image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "preview_image_url": {"name": "preview_image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ai_processed": {"name": "ai_processed", "type": "timestamp", "primaryKey": false, "notNull": false}, "ai_processing_status": {"name": "ai_processing_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "ai_processing_error": {"name": "ai_processing_error", "type": "text", "primaryKey": false, "notNull": false}, "ai_request_id": {"name": "ai_request_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "market_intelligence_data": {"name": "market_intelligence_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "market_metrics_data": {"name": "market_metrics_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "market_size": {"name": "market_size", "type": "bigint", "primaryKey": false, "notNull": false}, "market_cagr": {"name": "market_cagr", "type": "numeric", "primaryKey": false, "notNull": false}, "market_roi": {"name": "market_roi", "type": "numeric", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.case_study_icons": {"name": "case_study_icons", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "case_study_id": {"name": "case_study_id", "type": "integer", "primaryKey": false, "notNull": true}, "icon_type": {"name": "icon_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "icon_url": {"name": "icon_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"case_study_icons_case_study_id_case_studies_id_fk": {"name": "case_study_icons_case_study_id_case_studies_id_fk", "tableFrom": "case_study_icons", "tableTo": "case_studies", "columnsFrom": ["case_study_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contact_messages": {"name": "contact_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "inquiry_type": {"name": "inquiry_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'unread'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "replied_at": {"name": "replied_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "replied_by": {"name": "replied_by", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"contact_messages_replied_by_users_id_fk": {"name": "contact_messages_replied_by_users_id_fk", "tableFrom": "contact_messages", "tableTo": "users", "columnsFrom": ["replied_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.coupon_codes": {"name": "coupon_codes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "discount_type": {"name": "discount_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "discount_amount": {"name": "discount_amount", "type": "numeric", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "max_uses": {"name": "max_uses", "type": "integer", "primaryKey": false, "notNull": false}, "current_uses": {"name": "current_uses", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "valid_from": {"name": "valid_from", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "valid_until": {"name": "valid_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"coupon_codes_created_by_users_id_fk": {"name": "coupon_codes_created_by_users_id_fk", "tableFrom": "coupon_codes", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"coupon_codes_code_unique": {"name": "coupon_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "integer", "primaryKey": false, "notNull": true}, "invited_at": {"name": "invited_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}}, "indexes": {}, "foreignKeys": {"invitations_team_id_teams_id_fk": {"name": "invitations_team_id_teams_id_fk", "tableFrom": "invitations", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitations_invited_by_users_id_fk": {"name": "invitations_invited_by_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_members": {"name": "team_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"team_members_user_id_users_id_fk": {"name": "team_members_user_id_users_id_fk", "tableFrom": "team_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "team_members_team_id_teams_id_fk": {"name": "team_members_team_id_teams_id_fk", "tableFrom": "team_members", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_product_id": {"name": "stripe_product_id", "type": "text", "primaryKey": false, "notNull": false}, "plan_name": {"name": "plan_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subscription_status": {"name": "subscription_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"teams_stripe_customer_id_unique": {"name": "teams_stripe_customer_id_unique", "nullsNotDistinct": false, "columns": ["stripe_customer_id"]}, "teams_stripe_subscription_id_unique": {"name": "teams_stripe_subscription_id_unique", "nullsNotDistinct": false, "columns": ["stripe_subscription_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'member'"}, "industry": {"name": "industry", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "agreed_to_terms": {"name": "agreed_to_terms", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}