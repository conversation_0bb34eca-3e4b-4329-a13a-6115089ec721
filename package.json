{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "migrate": "tsx lib/db/migrate.ts", "generate": "drizzle-kit generate:pg", "db:setup": "npx tsx lib/db/setup.ts", "db:seed": "npx tsx lib/db/seed.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:migrate-ai": "npx tsx lib/db/migrations/migrate-ai-fields.ts", "db:migrate-market": "npx tsx lib/db/migrations/migrate-market-intelligence-fields.ts", "db:migrate-bookmarks": "npx tsx lib/db/migrations/migrate-bookmarks.ts", "db:migrate-csv-fields": "npx tsx lib/db/migrations/migrate-missing-csv-fields.ts", "db:migrate-coupon-codes": "npx tsx lib/db/migrations/migrate-coupon-codes.ts", "db:migrate-contact-messages": "npx tsx lib/db/migrations/migrate-contact-messages.ts", "db:fix-relations": "npx tsx lib/db/migrations/fix-contact-messages-relation.ts", "db:seed-coupons": "npx tsx lib/db/migrations/seed-sample-coupons.ts", "db:migrate-otp": "npx tsx lib/db/migrations/run-otp-migration.ts", "db:create-plans-schema": "npx tsx lib/db/migrations/create-plans-schema.ts", "db:update-plans-schema": "npx tsx lib/db/migrations/update-plans-schema.ts", "stripe:create-products": "node scripts/create-stripe-products.js", "stripe:create-new-products": "node scripts/create-new-stripe-products.js", "stripe:cleanup": "npx tsx scripts/stripe-plan-cleanup.ts", "stripe:interactive": "npx tsx scripts/stripe-plan-interactive.ts", "stripe:audit": "npx tsx scripts/stripe-audit.ts", "stripe:setup-correct": "npx tsx scripts/stripe-setup-correct.ts", "stripe:reset": "npx tsx scripts/stripe-complete-reset.ts", "stripe:fix-starter": "npx tsx scripts/fix-starter-pricing.ts", "stripe:setup-new": "npx tsx scripts/setup-new-stripe-account.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/postcss": "4.0.3", "@tanstack/react-query": "^5.72.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.13.1", "@types/nodemailer": "^6.4.17", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "autoprefixer": "^10.4.20", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "csv-parse": "^5.5.5", "csv-stringify": "^6.5.2", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-kit": "^0.30.4", "drizzle-orm": "^0.39.1", "framer-motion": "^12.6.3", "jose": "^5.9.6", "lucide-react": "^0.474.0", "motion": "^12.6.3", "next": "15.2.0-canary.33", "next-auth": "^4.24.11", "next-mdx-remote": "^5.0.0", "nodemailer": "^6.10.1", "postcss": "^8.5.1", "postgres": "^3.4.5", "react": "19.0.0", "react-dom": "19.0.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "recharts": "^2.15.1", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "stripe": "^17.6.0", "tailwind-merge": "^3.1.0", "tailwindcss": "4.0.3", "tailwindcss-animate": "^1.0.7", "tailwindcss-react-aria-components": "1.2.0", "typescript": "^5.7.3", "uuid": "^9.0.1", "zod": "^3.24.1"}, "devDependencies": {"@types/uuid": "^9.0.8", "tailwind-scrollbar": "^4.0.2"}}