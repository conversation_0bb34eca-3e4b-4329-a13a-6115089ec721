export interface ProcessStep {
  title: string;
  description: string;
  icon?: string;
}

export interface Solution {
  title: string;
  description: string;
  icon?: string;
}

export interface Impact {
  metric: string;
  value: string;
  description: string;
  icon?: string;
}

export interface MarketIntelligenceInsight {
  type: string;
  summary: string;
  source_url: string;
}

export interface MarketIntelligenceSection {
  title: string;
  description: string;
  insights: MarketIntelligenceInsight[];
}

export interface MarketMetricsDashboard {
  'Market Size ($ Value)'?: number;
  'CAGR'?: number;
  'ROI Range'?: number;
  [key: string]: number | undefined;
}

export interface CaseStudyIcon {
  id?: number;
  caseStudyId?: number;
  iconType: string;
  iconUrl: string;
  order: number;
}

export interface CaseStudyData {
  id: string;
  title: string;
  headerImage: string;
  thumbnailImage?: string;
  industry: string;
  role: string;
  vector?: string;
  potentiallyImpactedKpis?: string;
  kpis: {
    [key: string]: string;
  };
  introduction: {
    title: string;
    text: string;
    transitionToChallange: string;
    problems: string[];
    problemIcons?: string[];
    transitionToQuestions: string;
    questions: string[];
    questionIcons?: string[];
  };
  process: {
    title: string;
    steps: ProcessStep[];
  };
  solution: {
    title: string;
    description: string;
    items: Solution[];
  };
  impact: {
    title: string;
    metrics: Impact[];
  };
  conclusion: {
    title: string;
    text: string;
    resultTitle?: string;
    result: string;
  };
  marketIntelligenceData?: string;
  aiProcessed?: boolean;
  aiProcessingStatus?: string | null;
  aiProcessingError?: string | null;
  marketIntelligence?: MarketIntelligenceSection[];
  marketMetrics?: MarketMetricsDashboard;
  marketMetricsData?: string;
  icons?: CaseStudyIcon[];
}

// API response type for a single case study
export interface SingleCaseStudyResponse {
  id: number;
  useCaseTitle: string;
  industry?: string;
  role?: string;
  vector?: string;
  headerImage?: string;
  previewImageUrl?: string;
  featureImageUrl?: string;
  potentiallyImpactedKpis?: string;
  user?: {
    id: number;
    name: string;
    email: string;
  };
  userId?: number;
  createdAt?: string;
  updatedAt?: string;
  icons?: CaseStudyIcon[];
  [key: string]: any; // Allow additional properties
}

// API response type for multiple case studies
export interface CaseStudyResponse {
  caseStudies: SingleCaseStudyResponse[];
  total: number;
  page: number;
  pageSize: number;
}