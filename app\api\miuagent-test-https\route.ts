import { NextResponse } from 'next/server';
import https from 'https';

export async function GET(req: Request) {
  try {
    console.log('Testing MIUAgent API connection using https module...');

    // Function to make an HTTPS request
    const makeRequest = (url: string, method: string, data?: any) => {
      return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const options = {
          hostname: urlObj.hostname,
          port: 443,
          path: urlObj.pathname,
          method: method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.MIUAGENT_API_KEY || 'test-key'}`,
          },
        };

        const req = https.request(options, (res) => {
          let responseData = '';

          res.on('data', (chunk) => {
            responseData += chunk;
          });

          res.on('end', () => {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              data: responseData,
            });
          });
        });

        req.on('error', (error) => {
          reject(error);
        });

        if (data) {
          req.write(JSON.stringify(data));
        }

        req.end();
      });
    };

    // Prepare a simple test request
    const testRequest = {
      id: `test-req-${Date.now()}`,
      data: JSON.stringify({
        use_case_id: 'test-case',
        title: 'Test Case',
        short_description: 'This is a test case to verify API connectivity',
        problem_statement: 'Testing API connectivity',
        industry: 'Technology',
        role: 'Developer',
      }),
    };

    // Try different API endpoints
    const endpoints = [
      { url: 'https://miuagent-api.leafcraftstudios.com/v1/insights/trigger', method: 'POST', data: testRequest },
      { url: 'https://api.miuagent.com/v1/insights/trigger', method: 'POST', data: testRequest },
      { url: 'https://miuagent-api.leafcraftstudios.com/v1/metrics', method: 'GET' },
      { url: 'https://api.miuagent.com/v1/metrics', method: 'GET' },
    ];

    const results = [];

    for (const endpoint of endpoints) {
      try {
        console.log(`Testing endpoint: ${endpoint.url}`);
        const response = await makeRequest(endpoint.url, endpoint.method, endpoint.data);
        results.push({
          endpoint: endpoint.url,
          method: endpoint.method,
          statusCode: response.statusCode,
          response: response.data,
        });
      } catch (error) {
        console.error(`Error testing ${endpoint.url}:`, error);
        results.push({
          endpoint: endpoint.url,
          method: endpoint.method,
          error: error.message || String(error),
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'MIUAgent API test completed using https module',
      results,
    });
  } catch (error) {
    console.error('Error testing MIUAgent API:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
