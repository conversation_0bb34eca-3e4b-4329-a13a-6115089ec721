'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send, X, <PERSON>ader2, <PERSON><PERSON>, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface AIChatBoxProps {
  isOpen: boolean;
  onClose: () => void;
  caseStudyTitle?: string;
}

export const AIChatBox: React.FC<AIChatBoxProps> = ({
  isOpen,
  onClose,
  caseStudyTitle
}) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      role: 'assistant',
      content: `Hello! I'm your AI assistant for "${caseStudyTitle || 'this case study'}". You can ask me questions about:

• Key insights and findings
• Implementation strategies
• Similar case studies
• Industry trends related to this topic
• Technical details or explanations

How can I assist you today?`,
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of chat when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!input.trim()) return;

    // Add user message to chat
    const userMessage: Message = {
      role: 'user',
      content: input,
      timestamp: new Date()
    };

    // Store the current input for use in the API call
    const currentInput = input;

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    console.log('Sending message:', currentInput);

    try {
      // Send message to our API route that forwards to MIUAgent API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentInput, // Use the stored input
          caseStudyTitle: caseStudyTitle || 'AI Agents Case Study', // Only send title as per API requirements
          history: messages.map(msg => ({
            role: msg.role,
            content: msg.content
          }))
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Error response from API:', {
          status: response.status,
          statusText: response.statusText,
          data
        });
        throw new Error(data.error || `Failed to get response from AI: ${response.status} ${response.statusText}`);
      }

      if (data.error) {
        console.error('Error in API response:', data.error);
        throw new Error(data.error);
      }

      // Process the response
      console.log('Received response from API:', data);

      // Check if the response was successful
      // We'll be more lenient here since we've already handled success in the API
      if (data.success === false) {
        console.error('Error in API response:', data);
        throw new Error(data.error || 'Failed to get response from AI');
      }

      // Extract the response from the API data
      let responseText = '';

      // Try to extract the response from various possible formats
      if (typeof data === 'string') {
        responseText = data;
      } else if (data.response) {
        responseText = data.response;
      } else if (data.message) {
        responseText = data.message;
      } else if (data.data?.message) {
        responseText = data.data.message;
      } else if (data.data?.conversation) {
        const assistantMsg = data.data.conversation.find(
          (msg: any) => msg.role === 'assistant' || msg.role === 'system'
        );
        if (assistantMsg) {
          responseText = assistantMsg.content;
        }
      } else {
        responseText = "I'm sorry, I couldn't process your request at this time.";
      }

      console.log('Extracted response text:', responseText);

      // Add AI response to chat
      const aiMessage: Message = {
        role: 'assistant',
        content: responseText,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error communicating with AI:', error);

      // Add error message
      const errorMessage: Message = {
        role: 'assistant',
        content: "I'm sorry, I encountered an error while processing your request. This could be due to a connection issue or the AI service being temporarily unavailable. Please try again in a moment.",
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)] h-[600px] max-h-[calc(100vh-6rem)] shadow-xl">
      <Card className="flex flex-col h-full">
        <CardHeader className="py-3 px-4 border-b flex flex-row items-center justify-between space-y-0">
          <CardTitle className="text-md font-medium flex items-center">
            <Bot className="h-5 w-5 mr-2 text-blue-500" />
            AI Assistant
          </CardTitle>
          <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message, index) => (
            <div
              key={index}
              className={cn(
                "flex flex-col max-w-[80%] rounded-lg p-3",
                message.role === 'user'
                  ? "ml-auto bg-blue-500 text-white"
                  : "mr-auto bg-gray-100 text-gray-800"
              )}
            >
              <div className="flex items-center mb-1">
                {message.role === 'user' ? (
                  <User className="h-4 w-4 mr-1" />
                ) : (
                  <Bot className="h-4 w-4 mr-1" />
                )}
                <span className="text-xs opacity-70">
                  {message.role === 'user' ? 'You' : 'AI Assistant'}
                </span>
              </div>
              <div className="whitespace-pre-wrap text-sm">{message.content}</div>
              <div className="text-xs opacity-50 mt-1 self-end">
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex items-center justify-center py-2">
              <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
              <span className="ml-2 text-sm text-gray-500">AI is thinking...</span>
            </div>
          )}
          <div ref={messagesEndRef} />
        </CardContent>

        <CardFooter className="p-3 border-t">
          <div className="flex w-full items-center space-x-2">
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className="flex-1 min-h-10 resize-none"
              rows={1}
              disabled={isLoading}
            />
            <Button
              size="icon"
              onClick={handleSendMessage}
              disabled={isLoading || !input.trim()}
              className="h-10 w-10 rounded-full bg-blue-500 hover:bg-blue-600"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AIChatBox;
