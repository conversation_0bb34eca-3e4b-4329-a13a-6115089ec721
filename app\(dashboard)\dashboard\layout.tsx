'use client';

import { use, useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Users,
  Settings,
  LayoutDashboard,
  LogOut,
  HelpCircle,
  FileText,
  Tags,
  TrendingUp as TrendingUpIcon,
  Bookmark,
  Menu,
  X,
  Ticket,
  MessageSquare,
  CreditCard,
  Users2Icon,
  UserSquare2,
} from 'lucide-react';
import { useUser } from '@/lib/auth';
import { cn } from '@/lib/utils';
import { signOut } from '@/app/(login)/actions';
import Image from 'next/image';
import InvitationBanner from '@/components/InvitationBanner';
import { ThemeToggle } from '@/components/theme-toggle';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  // State for sidebar visibility
  const [isSidebarOpen, setSidebarOpen] = useState(false);
  // State for profile dropdown menu
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);

  // Set sidebar open by default on desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setSidebarOpen(true);
      } else {
        setSidebarOpen(false);
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        profileRef.current &&
        !profileRef.current.contains(event.target as Node)
      ) {
        setIsProfileOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  const { userPromise } = useUser();
  const user: any = use(userPromise);

  const router = useRouter();

  const handleSignOut = async () => {
    try {
      setIsProfileOpen(false); // Close dropdown
      await signOut();
      router.refresh();
      router.push('/');
    } catch (error) {
      console.error('Error during sign out:', error);
      // Still try to redirect to home page even if there was an error
      router.push('/');
    }
  };

  // Close dropdown when clicking a menu item
  const handleMenuItemClick = () => {
    setIsProfileOpen(false);
  };

  let onlyOwnerNavItems = [
    {
      href: '/dashboard/team',
      icon: Users,
      label: 'Team',
      matchPatterns: ['/dashboard/team', '/dashboard/invitations'],
    },
    {
      href: '/dashboard/trending',
      icon: TrendingUpIcon,
      label: 'Trending Cases',
      matchPatterns: ['/dashboard/trending'],
    },
  ];

  let onlyTeamMemberAndAdminNavItems = [
    {
      href: '/dashboard/import-cases',
      icon: FileText,
      label: 'Import Cases',
      matchPatterns: ['/dashboard/import-cases'],
    },
  ];

  let mainNavItems =
    user?.role === 'owner'
      ? [
          {
            href: '/dashboard/owner',
            icon: LayoutDashboard,
            label: 'Dashboard',
            matchPatterns: [
              '/dashboard/owner',
              '/dashboard/case-studies/[id]',
              '/dashboard/case-studies/[id]/edit',
            ],
          },
          {
            href: '/dashboard/case-studies',
            icon: Tags,
            label: 'Case Studies',
            matchPatterns: [
              '/dashboard/case-studies',
              '/dashboard/case-studies/[id]',
            ],
          },
          {
            href: '/dashboard/contact-messages',
            icon: MessageSquare,
            label: 'Contact Messages',
            matchPatterns: [
              '/dashboard/contact-messages',
              '/dashboard/contact-messages/[id]',
            ],
          },
          {
            href: '/dashboard/users',
            icon: UserSquare2,
            label: 'Users',
            matchPatterns: ['/dashboard/users'],
          },
        ]
      : [
          {
            href: '/dashboard/member',
            icon: LayoutDashboard,
            label: 'Dashboard',
            matchPatterns: [
              '/dashboard/member',
              '/dashboard/case-studies/[id]',
            ],
          },
          {
            href: '/dashboard/trends',
            icon: Bookmark,
            label: 'Bookmarks',
            matchPatterns: [
              '/dashboard/trends',
              '/dashboard/trends/case-studies/[id]/view',
            ],
          },
          // {
          //   href: '/dashboard/reports', icon: FileText, label: 'MIU', matchPatterns: [
          //     '/dashboard/reports',
          //     '/dashboard/reports/[id]/view',
          //     '/dashboard/reports/[id]'
          //   ]
          // },
        ];

  if (user?.teamRole === 'owner') {
    mainNavItems = [...mainNavItems, ...onlyOwnerNavItems];
  }
  if (user?.teamRole === 'teamMember' || user?.teamRole === 'owner') {
    mainNavItems = [...mainNavItems, ...onlyTeamMemberAndAdminNavItems];
  }

  // Bottom navigation items - logout button is handled separately for custom styling
  const bottomNavItems = [
    {
      href: '/dashboard/settings',
      icon: Settings,
      label: 'Settings',
      matchPatterns: ['/dashboard/settings'],
    },
    // Only show billing for members, not owners
    ...(user?.role !== 'owner'
      ? [
          {
            href: '/dashboard/billing',
            icon: CreditCard,
            label: 'Billing',
            matchPatterns: ['/dashboard/billing'],
          },
        ]
      : []),
    {
      href: '/dashboard/help',
      icon: HelpCircle,
      label: 'Help',
      matchPatterns: ['/dashboard/help'],
    },
  ];

  return (
    <div className='flex h-[100dvh] bg-background overflow-hidden'>
      {/* Mobile Sidebar Toggle Button */}
      <button
        className='fixed bottom-4 left-4 z-50 lg:hidden bg-blue-600 text-white p-3 rounded-full shadow-lg'
        onClick={() => setSidebarOpen(!isSidebarOpen)}
      >
        {isSidebarOpen ? (
          <X className='h-6 w-6' />
        ) : (
          <Menu className='h-6 w-6' />
        )}
      </button>

      {/* Sidebar Overlay */}
      {isSidebarOpen && (
        <div
          className='fixed inset-0 bg-black/50 z-40 lg:hidden'
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 transform bg-card transition-transform duration-200 ease-in-out lg:relative lg:translate-x-0 flex flex-col shadow-lg',
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        {/* Main Navigation */}
        <nav className='flex flex-col justify-between h-full flex-1 overflow-hidden'>
          <div className='p-2 border-t '>
            <div className='flex items-center justify-center mb-4 mx-2 mt-2'>
              <a
                href='/'
                className='cursor-pointer max-h-[100px] overflow-y-hidden'
              >
                <Image
                  src='/images/logos/main-logo.svg'
                  alt='Turinos AI Logo'
                  width={160}
                  height={40}
                  className='w-[160px] h-auto object-contain dark:hidden'
                />
                <Image
                  src='https://res.cloudinary.com/dyiso4ohk/image/upload/v1746113300/image__1_-removebg-preview_gmuuzq.png'
                  alt='Turinos AI Logo Dark'
                  width={180}
                  height={40}
                  className='w-[180px] h-auto object-contain hidden dark:block'
                />
              </a>
            </div>
            <div className='flex flex-col gap-2 border-t pt-4 '>
              {mainNavItems.map((item) => {
                // Check if current path matches any of the patterns
                let isActive = item.matchPatterns.some((pattern) => {
                  // Handle dynamic routes by replacing [id] with actual path segment
                  if (pattern.includes('[id]')) {
                    // Convert pattern like '/dashboard/case-studies/[id]' to regex
                    // that matches any value in place of [id]
                    const patternRegex = pattern.replace(/\[id\]/g, '[^/]+');
                    const regex = new RegExp(
                      `^${patternRegex}(/.*)?$`.replace(/\//g, '\\/')
                    );
                    return regex.test(pathname);
                  }
                  // For exact matches or paths that start with the pattern
                  return (
                    pathname === pattern || pathname.startsWith(`${pattern}/`)
                  );
                });

                // Special case for case studies
                if (
                  pathname.startsWith('/dashboard/case-studies/') &&
                  pathname.split('/').length >= 4 &&
                  !isNaN(parseInt(pathname.split('/')[3]))
                ) {
                  // This is a case study detail page
                  if (
                    user?.role === 'owner' &&
                    item.href === '/dashboard/owner'
                  ) {
                    isActive = true; // Highlight Dashboard for owners
                  } else if (
                    user?.role === 'member' &&
                    item.href === '/dashboard/member'
                  ) {
                    isActive = true; // Highlight Dashboard for members
                  }
                }

                return (
                  <Link key={item.href} href={item.href}>
                    <Button
                      variant='ghost'
                      className={cn(
                        'w-full justify-start text-sm font-medium p-2 rounded-lg',
                        isActive
                          ? 'bg-blue-600 text-white hover:bg-blue-600 hover:text-white dark:bg-blue-800'
                          : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                      )}
                    >
                      <item.icon
                        className={cn(
                          'mr-3 h-5 w-5',
                          isActive
                            ? 'text-white'
                            : 'text-gray-400 dark:text-gray-400'
                        )}
                      />
                      {item.label}
                    </Button>
                  </Link>
                );
              })}
            </div>
          </div>

          {/* User Profile Section with Dropdown at the bottom */}
          <div className='border-t p-2 mt-auto mb-0'>
            {/* Theme Toggle */}
            <div className='flex items-center justify-between px-2 py-2 mb-2 border-b border-border'>
              <span className='text-sm font-medium text-muted-foreground'>
                Theme
              </span>
              <ThemeToggle />
            </div>
            {/* User Profile Button with Dropdown */}
            <div className='relative' ref={profileRef}>
              {/* Dropdown Menu - Positioned ABOVE the profile button */}
              {isProfileOpen && (
                <div className='absolute top-0 transform -translate-y-full left-0 right-0 bg-popover border border-border rounded-md shadow-xl z-10 overflow-hidden animate-in fade-in-50 slide-in-from-bottom-5 duration-200'>
                  {/* Logout Button - Displayed first with red styling */}
                  <button
                    className='w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-950/30'
                    onClick={handleSignOut}
                  >
                    <LogOut className='mr-3 h-5 w-5 text-red-500' />
                    Logout
                  </button>

                  {/* Divider */}
                  <div className='h-px bg-border my-1'></div>

                  {/* Settings and Help buttons */}
                  {bottomNavItems.map((item) => {
                    // Check if current path matches any of the patterns
                    let isActive = item.matchPatterns.some((pattern) => {
                      // Handle dynamic routes by replacing [id] with actual path segment
                      if (pattern.includes('[id]')) {
                        // Convert pattern like '/dashboard/case-studies/[id]' to regex
                        // that matches any value in place of [id]
                        const patternRegex = pattern.replace(
                          /\[id\]/g,
                          '[^/]+'
                        );
                        const regex = new RegExp(
                          `^${patternRegex}(/.*)?$`.replace(/\//g, '\\/')
                        );
                        return regex.test(pathname);
                      }
                      // For exact matches or paths that start with the pattern
                      return (
                        pathname === pattern ||
                        pathname.startsWith(`${pattern}/`)
                      );
                    });

                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className='block'
                        onClick={handleMenuItemClick}
                      >
                        <button
                          className={`w-full flex items-center px-4 py-2 text-sm ${
                            isActive
                              ? 'text-blue-600 bg-blue-50 dark:bg-blue-950/30 dark:text-blue-400'
                              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800/50'
                          }`}
                        >
                          <item.icon
                            className={`mr-3 h-5 w-5 ${
                              isActive ? 'text-blue-500' : 'text-gray-400'
                            }`}
                          />
                          {item.label}
                        </button>
                      </Link>
                    );
                  })}
                </div>
              )}

              {/* User Profile Button */}
              <button
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className='w-full flex items-center p-2 rounded-md bg-card hover:bg-accent transition-colors mb-0'
              >
                <div className='w-10 h-10 rounded-full bg-pink-100 overflow-hidden mr-3'>
                  {user?.name ? (
                    <div className='w-full h-full flex items-center justify-center bg-pink-200 text-pink-800 font-medium'>
                      {user.name.charAt(0)}
                    </div>
                  ) : (
                    <div className='w-full h-full flex items-center justify-center bg-pink-200'>
                      <Users className='h-5 w-5 text-pink-500' />
                    </div>
                  )}
                </div>
                <div className='flex-1 min-w-0 text-left'>
                  <p className='text-sm font-medium text-foreground truncate'>
                    {user?.name || 'Emily Carter'}
                  </p>
                  <p className='text-xs text-muted-foreground truncate'>
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                    isProfileOpen ? '' : 'rotate-180'
                  }`}
                  viewBox='0 0 20 20'
                  fill='currentColor'
                >
                  <path
                    fillRule='evenodd'
                    d='M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z'
                    clipRule='evenodd'
                  />
                </svg>
              </button>
            </div>
          </div>
        </nav>
      </aside>

      {/* Main content */}
      <div className='flex flex-1 flex-col overflow-hidden w-full'>
        {/* Content area */}
        <main className='flex-1 overflow-auto bg-background p-2 lg:p-4'>
          <InvitationBanner />
          <div className='mt-4 max-w-full pb-8 overflow-hidden'>{children}</div>
        </main>
      </div>
    </div>
  );
}
