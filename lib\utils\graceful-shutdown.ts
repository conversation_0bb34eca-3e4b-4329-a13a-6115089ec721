import { client } from '@/lib/db/drizzle';
import { EventEmitter } from 'events';

let isShuttingDown = false;
let handlersRegistered = false;

// Increase the max listeners limit for the process object
if (typeof process !== 'undefined' && process.env.NEXT_RUNTIME === 'nodejs') {
  // Set max listeners to 20 (default is 10)
  process.setMaxListeners(20);
  console.log(`Increased process max listeners to ${process.getMaxListeners()}`);
}

export function setupGracefulShutdown() {
  // Only run in a Node.js environment and not during Next.js rendering
  // Also check if handlers are already registered to prevent duplicates
  if (typeof process !== 'undefined' &&
      process.env.NEXT_RUNTIME === 'nodejs' &&
      !isShuttingDown &&
      !handlersRegistered &&
      typeof process.on === 'function') {
    try {
      // Check current listener count
      const currentListenerCount = process.listenerCount('SIGTERM');
      console.log(`Current SIGTERM listener count: ${currentListenerCount}`);

      // Only register if we haven't already
      if (currentListenerCount < 10) {
        // Handle graceful shutdown on SIGTERM and SIGINT
        process.on('SIGTERM', handleShutdown);
        process.on('SIGINT', handleShutdown);
        handlersRegistered = true;
        console.log('Graceful shutdown handlers registered');
      } else {
        console.log('Skipping registration of shutdown handlers - already at limit');
      }
    } catch (error) {
      console.error('Error setting up graceful shutdown:', error);
    }
  }
}

async function handleShutdown() {
  if (isShuttingDown) return;
  isShuttingDown = true;

  console.log('Graceful shutdown initiated...');

  try {
    // Close database connections
    console.log('Closing database connections...');
    await client.end();
    console.log('Database connections closed successfully');

    // Add other cleanup tasks here if needed

    console.log('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    console.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
}
