import { sql } from 'drizzle-orm';
import dotenv from 'dotenv';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import { up } from './20240712_add_otp_verification';

// Load environment variables from .env file
dotenv.config();

async function runOtpMigration() {
  console.log('Running migration to add OTP verification table and isVerified field...');

  // Check if POSTGRES_URL is set
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not set');
  }

  console.log('Connecting to database...');

  // Create a new connection to the database
  const migrationClient = postgres(process.env.POSTGRES_URL, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Execute the migration
    console.log('Executing migration...');
    await up(db);

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    console.log('Closing database connection...');
    await migrationClient.end();
  }
}

// Run the migration
runOtpMigration().catch(console.error);
