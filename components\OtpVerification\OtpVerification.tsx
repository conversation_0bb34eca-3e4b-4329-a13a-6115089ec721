'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface OtpVerificationProps {
  email: string;
  purpose: 'signup' | 'password-reset';
  onVerificationSuccess: () => void;
  onResendOtp: () => Promise<boolean>;
  onVerifyOtp: (otp: string) => Promise<boolean>;
}

export function OtpVerification({
  email,
  purpose,
  onVerificationSuccess,
  onResendOtp,
  onVerifyOtp,
}: OtpVerificationProps) {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const router = useRouter();

  // Handle countdown for resend button
  useEffect(() => {
    if (countdown > 0 && !canResend) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
  }, [countdown, canResend]);

  // Handle OTP input change
  const handleOtpChange = (index: number, value: string) => {
    // Only allow numbers
    if (value && !/^\d+$/.test(value)) return;

    const newOtp = [...otp];

    // Handle paste event (if multiple digits are pasted)
    if (value.length > 1) {
      // Extract only digits from pasted content
      const digits = value.replace(/\D/g, '').split('').slice(0, 6);

      // Fill the OTP array with the digits
      for (let i = 0; i < digits.length && i < 6; i++) {
        newOtp[i] = digits[i];
      }

      setOtp(newOtp);

      // Focus the next empty input or the last input if all are filled
      const nextEmptyIndex = newOtp.findIndex((digit) => !digit);
      const focusIndex = nextEmptyIndex === -1 ? 5 : nextEmptyIndex;
      const nextInput = document.getElementById(`otp-${focusIndex}`);
      if (nextInput) {
        nextInput.focus();
      }

      return;
    }

    // Handle single digit input
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) {
        nextInput.focus();
      }
    }
  };

  // Handle key press for backspace
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      if (prevInput) {
        prevInput.focus();
      }
    }
  };

  // Handle paste event directly
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    if (!pastedData) return;

    // Extract only digits and limit to 6
    const digits = pastedData.replace(/\D/g, '').slice(0, 6);
    if (digits.length === 0) return;

    // Create a new OTP array with the pasted digits
    const newOtp = [...otp];
    for (let i = 0; i < digits.length && i < 6; i++) {
      newOtp[i] = digits[i];
    }

    setOtp(newOtp);

    // Focus the next empty input or the last input if all are filled
    const nextEmptyIndex = newOtp.findIndex((digit) => !digit);
    const focusIndex = nextEmptyIndex === -1 ? 5 : nextEmptyIndex;
    const nextInput = document.getElementById(`otp-${focusIndex}`);
    if (nextInput) {
      nextInput.focus();
    }
  };

  // Handle OTP verification
  const handleVerify = async () => {
    const otpValue = otp.join('');
    console.log(otpValue);

    if (otpValue.length !== 6) {
      setError('Please enter all 6 digits of the OTP');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      const success = await onVerifyOtp(otpValue);
      if (success) {
        onVerificationSuccess();
      } else {
        setError('Invalid OTP. Please try again.');
      }
    } catch (err) {
      setError('An error occurred during verification. Please try again.');
      console.error('OTP verification error:', err);
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle OTP resend
  const handleResend = async () => {
    setIsResending(true);
    setError('');

    try {
      const success = await onResendOtp();
      if (success) {
        setCountdown(60);
        setCanResend(false);
      } else {
        setError('Failed to resend OTP. Please try again.');
      }
    } catch (err) {
      setError('An error occurred while resending OTP. Please try again.');
      console.error('OTP resend error:', err);
    } finally {
      setIsResending(false);
    }
  };

  // Determine if we're in a dialog context
  const isInDialog = purpose === 'password-reset';

  const content = (
    <div className='space-y-4'>
      <div>
        {!isInDialog && (
          <div className='mb-6'>
            <h3 className='text-xl mb-2 font-semibold text-black dark:text-white'>
              Verify Your Email
            </h3>
            <p className='text-sm text-gray-600 dark:text-gray-300'>
              We've sent a 6-digit verification code to{' '}
              <span className='font-semibold'>{email}</span>
            </p>
          </div>
        )}
        <Label htmlFor='otp-0' className='sr-only'>
          OTP Verification
        </Label>
        <div className='flex justify-center gap-2  sm:gap-3 max-w-full overflow-x-auto'>
          {otp.map((digit, index) => (
            <Input
              key={index}
              id={`otp-${index}`}
              type='text'
              inputMode='numeric'
              pattern='[0-9]*'
              maxLength={1}
              value={digit}
              onChange={(e) => handleOtpChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={handlePaste}
              className='w-10 h-12 sm:w-12 sm:h-14 text-center text-lg font-semibold bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-700 text-black dark:text-white focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400'
              autoFocus={index === 0}
            />
          ))}
        </div>
      </div>

      {error && (
        <div className='text-red-500 dark:text-red-400 text-sm mt-2 text-center'>
          {error}
        </div>
      )}

      <div className='text-sm text-gray-600 dark:text-gray-300 text-center'>
        Didn't receive the code?{' '}
        {canResend ? (
          <button
            onClick={handleResend}
            disabled={isResending}
            className='text-blue-600 dark:text-blue-400 hover:underline focus:outline-none font-medium'
          >
            {isResending ? 'Resending...' : 'Resend Code'}
          </button>
        ) : (
          <span>
            Resend in <span className='font-medium'>{countdown}s</span>
          </span>
        )}
      </div>
    </div>
  );

  const actions = (
    <div
      className={`flex ${
        isInDialog ? 'justify-center' : 'justify-between'
      } mt-6 gap-4`}
    >
      {!isInDialog && (
        <Button
          variant='outline'
          onClick={() => router.back()}
          disabled={isVerifying}
          className='border-blue-600 text-blue-600 dark:text-blue-400 dark:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950'
        >
          Back
        </Button>
      )}
      <Button
        onClick={handleVerify}
        disabled={otp.join('').length !== 6 || isVerifying}
        className={`bg-blue-600 hover:bg-blue-700 text-white ${
          isInDialog ? 'w-full sm:w-3/4' : ''
        }`}
      >
        {isVerifying ? (
          <>
            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
            Verifying...
          </>
        ) : (
          'Verify Email'
        )}
      </Button>
    </div>
  );

  // If we're in a dialog, just return the content and actions
  if (isInDialog) {
    return (
      <div>
        {content}
        {actions}
      </div>
    );
  }

  // Otherwise, wrap in a card
  return (
    <Card className='w-full max-w-md mx-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 shadow-lg'>
      {/* <CardHeader className='pb-2'>
        <CardTitle className='text-xl font-semibold text-black dark:text-white'>
          Verify Your Email
        </CardTitle>
        <CardDescription className='text-gray-600 dark:text-gray-300'>
          We've sent a 6-digit verification code to{' '}
          <span className='font-medium'>{email}</span>
        </CardDescription>
      </CardHeader> */}
      <CardContent className='pt-4'>{content}</CardContent>
      <CardFooter className='flex justify-center pb-6'>{actions}</CardFooter>
    </Card>
  );
}
