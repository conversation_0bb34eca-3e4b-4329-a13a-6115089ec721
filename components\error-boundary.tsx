'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * ErrorBoundary component to catch JavaScript errors in child component trees,
 * log errors, and display a fallback UI.
 * 
 * This helps prevent the whole app from crashing when a component fails,
 * improving the user experience and application resilience.
 */
export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  // Static method to derive state from error
  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  // Lifecycle method called after an error is caught
  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    console.error('Error boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });
    
    // You could log this to a monitoring service like Sentry here
    // if (typeof window !== 'undefined' && window.Sentry) {
    //   window.Sentry.captureException(error);
    // }
  }

  // Method to reset the error state and attempt recovery
  resetErrorBoundary = (): void => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined
    });
  };

  render(): ReactNode {
    const { hasError, error } = this.state;
    const { children, fallback } = this.props;

    // If there's an error, show the fallback UI
    if (hasError) {
      // Custom fallback is provided
      if (fallback) {
        return fallback;
      }

      // Default fallback UI
      return (
        <div className="p-6 rounded-lg border border-red-200 bg-red-50 flex flex-col items-center justify-center text-center">
          <h2 className="text-xl font-semibold text-red-700 mb-2">Something went wrong</h2>
          <p className="text-red-600 mb-4">
            {error?.message || 'An unexpected error occurred'}
          </p>
          <Button 
            variant="outline"
            className="border-red-500 text-red-600 hover:bg-red-100"
            onClick={this.resetErrorBoundary}
          >
            Try again
          </Button>
        </div>
      );
    }

    // If no error, render children normally
    return children;
  }
}

/**
 * withErrorBoundary is a higher-order component (HOC) to wrap components with an ErrorBoundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
): React.FC<P> {
  return (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );
}

export default ErrorBoundary;
