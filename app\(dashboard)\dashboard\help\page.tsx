'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { useUser } from '@/lib/auth';
import { use } from 'react';

export default function HelpPage() {
  const { userPromise } = useUser();
  const user = use(userPromise);
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Help & Support</h1>
      
      <Tabs defaultValue="faq" className="space-y-6">
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="faq">FAQ</TabsTrigger>
          <TabsTrigger value="contact">Contact Support</TabsTrigger>
        </TabsList>
        
        <TabsContent value="faq">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>
                Find answers to common questions about using the platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <AccordionTrigger>How do I update my profile information?</AccordionTrigger>
                  <AccordionContent>
                    You can update your profile information by going to the Settings page. 
                    Click on your profile picture in the sidebar, then select "Settings" from the dropdown menu.
                    On the Settings page, you can update your name, email, and password.
                  </AccordionContent>
                </AccordionItem>
                
                <AccordionItem value="item-2">
                  <AccordionTrigger>How do I bookmark a case study?</AccordionTrigger>
                  <AccordionContent>
                    To bookmark a case study, click on the bookmark icon in the top right corner of any case study card.
                    You can view all your bookmarked case studies by clicking on "Bookmarks" in the sidebar navigation.
                  </AccordionContent>
                </AccordionItem>
                
                <AccordionItem value="item-3">
                  <AccordionTrigger>How do I search for specific case studies?</AccordionTrigger>
                  <AccordionContent>
                    You can search for case studies using the search bar at the top of the dashboard.
                    Enter keywords related to the case study you're looking for, and the system will display matching results.
                  </AccordionContent>
                </AccordionItem>
                
                {user?.role === 'owner' && (
                  <>
                    <AccordionItem value="item-4">
                      <AccordionTrigger>How do I add a new case study?</AccordionTrigger>
                      <AccordionContent>
                        As an owner, you can add a new case study by clicking on "Add Case" in the sidebar navigation.
                        Fill out the form with the case study details and click "Save" to add it to the database.
                      </AccordionContent>
                    </AccordionItem>
                    
                    <AccordionItem value="item-5">
                      <AccordionTrigger>How do I import multiple case studies?</AccordionTrigger>
                      <AccordionContent>
                        You can import multiple case studies at once by clicking on "Import Cases" in the sidebar navigation.
                        Upload a CSV file with the case study data in the required format, and the system will import them automatically.
                      </AccordionContent>
                    </AccordionItem>
                  </>
                )}
                
                <AccordionItem value="item-6">
                  <AccordionTrigger>How do I log out of my account?</AccordionTrigger>
                  <AccordionContent>
                    To log out, click on your profile picture in the sidebar, then select "Logout" from the dropdown menu.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="contact">
          <Card>
            <CardHeader>
              <CardTitle>Contact Support</CardTitle>
              <CardDescription>
                Need help? Contact our support team for assistance.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>
                  If you need assistance or have questions that aren't covered in the FAQ,
                  please contact our support team using one of the methods below:
                </p>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Email Support</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">
                        Send an email to <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        Response time: Within 24 hours
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Phone Support</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">
                        Call us at <a href="tel:+1234567890" className="text-blue-600 hover:underline">+1 (234) 567-890</a>
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        Available Monday-Friday, 9am-5pm EST
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
