'use client';

import React from 'react';
import { useSubscription, useFeatureAccess } from '@/lib/hooks/use-subscription';
import { PlanFeatures } from '@/lib/auth/subscription-middleware';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, Lock, Zap, Crown } from 'lucide-react';
import Link from 'next/link';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  feature?: keyof PlanFeatures;
  fallback?: React.ReactNode;
  showUpgrade?: boolean;
}

/**
 * Component that guards content based on subscription status and features
 */
export function SubscriptionGuard({ 
  children, 
  feature, 
  fallback, 
  showUpgrade = true 
}: SubscriptionGuardProps) {
  const { hasActiveSubscription, isLoading, error, planName } = useSubscription();
  const { hasAccess } = useFeatureAccess(feature || 'maxUsers');

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load subscription data: {error}
        </AlertDescription>
      </Alert>
    );
  }

  // Check subscription status
  if (!hasActiveSubscription) {
    return fallback || (
      <SubscriptionRequired showUpgrade={showUpgrade} />
    );
  }

  // Check feature access if specified
  if (feature && !hasAccess) {
    return fallback || (
      <FeatureRestricted feature={feature} planName={planName} showUpgrade={showUpgrade} />
    );
  }

  return <>{children}</>;
}

/**
 * Component shown when subscription is required
 */
function SubscriptionRequired({ showUpgrade }: { showUpgrade: boolean }) {
  return (
    <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
          <Lock className="h-5 w-5" />
          Subscription Required
        </CardTitle>
        <CardDescription className="text-orange-700 dark:text-orange-300">
          You need an active subscription to access this feature.
        </CardDescription>
      </CardHeader>
      {showUpgrade && (
        <CardContent>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/pricing">View Plans</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/billing">Manage Subscription</Link>
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

/**
 * Component shown when feature is restricted by plan
 */
function FeatureRestricted({ 
  feature, 
  planName, 
  showUpgrade 
}: { 
  feature: keyof PlanFeatures; 
  planName: string | null; 
  showUpgrade: boolean; 
}) {
  const featureNames: Record<keyof PlanFeatures, string> = {
    maxUsers: 'Multiple Users',
    maxCaseStudies: 'Unlimited Case Studies',
    maxStorage: 'Additional Storage',
    advancedAnalytics: 'Advanced Analytics',
    apiAccess: 'API Access',
    customBranding: 'Custom Branding',
    prioritySupport: 'Priority Support',
  };

  return (
    <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-800 dark:text-blue-200">
          <Crown className="h-5 w-5" />
          Premium Feature
        </CardTitle>
        <CardDescription className="text-blue-700 dark:text-blue-300">
          {featureNames[feature]} is not available in your current plan.
          {planName && (
            <Badge variant="outline" className="ml-2">
              {planName}
            </Badge>
          )}
        </CardDescription>
      </CardHeader>
      {showUpgrade && (
        <CardContent>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/dashboard/billing">
                <Zap className="h-4 w-4 mr-2" />
                Upgrade Plan
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/pricing">Compare Plans</Link>
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

/**
 * Component for displaying plan usage and limits
 */
interface PlanUsageProps {
  limitType: 'maxUsers' | 'maxCaseStudies' | 'maxStorage';
  currentCount: number;
  label: string;
  unit?: string;
}

export function PlanUsage({ limitType, currentCount, label, unit = '' }: PlanUsageProps) {
  const { planFeatures } = useSubscription();
  
  const limit = planFeatures[limitType] as number;
  const isUnlimited = limit === -1;
  const percentage = isUnlimited ? 0 : Math.min((currentCount / limit) * 100, 100);
  const isNearLimit = percentage >= 80;
  const isAtLimit = percentage >= 100;

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium">{label}</span>
        <span className="text-sm text-muted-foreground">
          {currentCount}{unit} {!isUnlimited && `/ ${limit}${unit}`}
          {isUnlimited && <Badge variant="secondary" className="ml-2">Unlimited</Badge>}
        </span>
      </div>
      
      {!isUnlimited && (
        <>
          <Progress 
            value={percentage} 
            className={`h-2 ${isAtLimit ? 'bg-red-100' : isNearLimit ? 'bg-yellow-100' : 'bg-green-100'}`}
          />
          
          {isAtLimit && (
            <Alert variant="destructive" className="mt-2">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                You've reached your plan limit. Consider upgrading to continue.
              </AlertDescription>
            </Alert>
          )}
          
          {isNearLimit && !isAtLimit && (
            <Alert className="mt-2">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                You're approaching your plan limit ({Math.round(percentage)}% used).
              </AlertDescription>
            </Alert>
          )}
        </>
      )}
    </div>
  );
}

/**
 * Component for displaying subscription status
 */
export function SubscriptionStatus() {
  const { status, hasActiveSubscription, planName, isLoading } = useSubscription();

  if (isLoading) {
    return <div className="animate-pulse h-6 bg-gray-200 rounded"></div>;
  }

  const statusColors = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    trialing: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    past_due: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    canceled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    unpaid: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  };

  const statusColor = statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800';

  return (
    <div className="flex items-center gap-2">
      <Badge className={statusColor}>
        {status || 'No Subscription'}
      </Badge>
      {planName && (
        <Badge variant="outline">
          {planName}
        </Badge>
      )}
    </div>
  );
}
