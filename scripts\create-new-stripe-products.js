require('dotenv').config();
const Stripe = require('stripe');

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function createStripeProducts() {
  console.log('Creating new Stripe products and prices with USD currency...');

  try {
    // Create Starter product
    const starterProduct = await stripe.products.create({
      name: 'Starter Plan',
      description: 'Ideal for early explorers and small teams',
      active: true,
      metadata: {
        plan_type: 'starter',
        monthly_price: '12'
      }
    });
    console.log('Created Starter product:', starterProduct.id);

    // Create Starter price with USD currency
    const starterPrice = await stripe.prices.create({
      product: starterProduct.id,
      unit_amount: 1200, // $12 in cents
      currency: 'usd',
      recurring: {
        interval: 'month',
        trial_period_days: 14,
      },
      metadata: {
        plan_type: 'starter'
      }
    });
    console.log('Created Starter price:', starterPrice.id);

    // Create Pro product
    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'Ideal for teams ready to build strategic AI capabilities',
      active: true,
      metadata: {
        plan_type: 'pro',
        monthly_price: '60'
      }
    });
    console.log('Created Pro product:', proProduct.id);

    // Create Pro price with USD currency
    const proPrice = await stripe.prices.create({
      product: proProduct.id,
      unit_amount: 6000, // $60 in cents
      currency: 'usd',
      recurring: {
        interval: 'month',
        trial_period_days: 14,
      },
      metadata: {
        plan_type: 'pro'
      }
    });
    console.log('Created Pro price:', proPrice.id);

    console.log('\nSummary of created products and prices:');
    console.log('Starter Plan:');
    console.log('  Product ID:', starterProduct.id);
    console.log('  Price ID:', starterPrice.id);
    console.log('Pro Plan:');
    console.log('  Product ID:', proProduct.id);
    console.log('  Price ID:', proPrice.id);
    
    console.log('\nPlease update your pricing page and API routes with these new price IDs.');
  } catch (error) {
    console.error('Error creating Stripe products and prices:', error);
  }
}

createStripeProducts();
