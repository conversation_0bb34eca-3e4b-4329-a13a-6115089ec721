'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Pagination } from '@/components/ui/pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useUser } from '@/lib/auth';
import { getUser } from '@/lib/db/server-queries';
import {
  ArrowDown,
  DeleteIcon,
  RefreshCw,
  Trash,
  Trash2Icon,
} from 'lucide-react';
import React, { use, useEffect, useState } from 'react';

const roleColors: Record<string, string> = {
  owner: 'bg-red-600',
  member: 'bg-purple-600',
  teamMember: 'bg-yellow-500',
  viewer: 'bg-gray-500',
};
const roleLabels: Record<string, string> = {
  owner: 'Owner',
  member: 'Member',
  teamMember: 'Team Member',
  viewer: 'Viewer',
};

const page = () => {
  const [users, setUsers] = React.useState([]);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 0,
  });
  const [page, setPage] = useState(1);
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const { userPromise } = useUser();
  const currUser: any = use(userPromise);
  const isRealOwner =
    currUser?.role === 'owner' && currUser?.teamRole === 'owner';

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `/api/users?page=${page}&pageSize=${pagination.pageSize}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }
        const data = await response.json();
        console.log('Fetched users:', data);

        setUsers(data.users);
        const paginationData = data.pagination;
        setPagination({
          total: paginationData.totalCount,
          page: paginationData.page,
          pageSize: paginationData.pageSize,
          totalPages: paginationData.totalPages,
        });
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchUsers();
  }, [page]);

  const handleDeleteUser = async (userId: string) => {
    if (!isRealOwner) {
      alert('You do not have permission to delete users.');
      return;
    }
    if (!confirm('Are you sure you want to delete this user?')) {
      return;
    }
    alert('This feature will be available soon. Please check back later.');
    // Uncomment the code below when the delete API is ready
    setLoading(true);
    try {
      // const response = await fetch(`/api/users/${userId}`, {
      //   method: 'DELETE',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      // });
      // if (!response.ok) {
      //   throw new Error('Failed to delete user');
      // }
      // const data = await response.json();
      // console.log('User deleted:', data);
      // // Refresh the user list after deletion
      // setUsers((prevUsers) => prevUsers.filter((user: any) => user.id !== userId));
      // setPagination((prev) => ({
      //   ...prev,
      //   total: prev.total - 1,
      // }));
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('Failed to delete user. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='p-2 md:p-4 space-y-4'>
      <h1 className='text-2xl font-bold'>Users Management</h1>

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='!pl-0'>
                  <Button variant='ghost' className='flex items-center'>
                    Id
                  </Button>
                </TableHead>
                <TableHead className='!pl-4'>Fullname</TableHead>
                <TableHead className='!pl-4'>Email</TableHead>
                <TableHead className='!pl-4'>Industry</TableHead>
                <TableHead className='!pl-4'>Role</TableHead>
                <TableHead className='!pl-4'>Subscriptions</TableHead>
                <TableHead className='!pl-4'>isVerified</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading && (
                <TableRow>
                  <TableCell colSpan={8} className='text-center py-8'>
                    <div className='flex flex-col items-center justify-center'>
                      <RefreshCw className='h-8 w-8 animate-spin text-gray-400 mb-2' />
                      <p className='text-gray-500'>Loading Users...</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
              {users.map((user: any) => (
                <TableRow key={user.id} className='relative'>
                  <TableCell className='font-medium'>{user.id}</TableCell>
                  <TableCell className='font-medium'>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.industry}</TableCell>
                  <TableCell>
                    {(() => {
                      const color = roleColors[user.role] || 'bg-gray-400';
                      return (
                        <div
                          className={`${color} text-white px-1.5 py-1 w-24 text-sm text-center rounded cursor-auto`}
                        >
                          {user.role}
                        </div>
                      );
                    })()}
                  </TableCell>
                  <TableCell>{user.subscriptions.length}</TableCell>
                  <TableCell>
                    {user.isVerified ? (
                      <div className='bg-green-500 text-white px-2 w-10 py-1 text-center rounded cursor-auto'>
                        Yes
                      </div>
                    ) : (
                      <div className='bg-red-500 text-white px-2 w-10 py-1 text-center rounded cursor-auto'>
                        No
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant='ghost'
                      className={`flex items-center justify-center rounded-full text-red-500 ${
                        isRealOwner && !loading
                          ? 'hover:bg-red-100'
                          : 'cursor-not-allowed'
                      }`}
                      disabled={!isRealOwner || loading}
                      onClick={() => handleDeleteUser(user.id)}
                    >
                      <Trash className='h-4 w-4' color='red' />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      {pagination.totalPages > 0 && (
        <div className='flex justify-center mt-8 pb-8 mx-auto'>
          <Pagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default page;
