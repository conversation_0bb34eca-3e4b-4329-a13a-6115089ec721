import React, { lazy, Suspense, ComponentType } from 'react';

/**
 * Options for lazy loading a component
 */
interface LazyLoadOptions {
  /** Delay loading to prioritize critical components */
  delay?: number;
  /** Component to show while loading */
  fallback?: React.ReactNode;
  /** Whether to preload the component */
  preload?: boolean;
  /** Error boundary to wrap the component */
  errorBoundary?: boolean;
}

/**
 * Default loading component
 */
const DefaultLoader = () => (
  <div className="animate-pulse flex space-x-4 p-4">
    <div className="rounded-md bg-gray-200 h-24 w-full"></div>
  </div>
);

/**
 * Creates a lazy-loaded component with optimized loading behavior
 * 
 * @param factory Function that imports the component
 * @param options Lazy loading options
 * @returns Lazy-loaded component
 */
export function lazyLoad<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): React.LazyExoticComponent<T> {
  const {
    delay = 0,
    fallback = <DefaultLoader />,
    preload = false,
    errorBoundary = true,
  } = options;

  // Create a lazy component with optional delay
  const LazyComponent = lazy(() => {
    // Add optional delay to prioritize critical path rendering
    if (delay > 0) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(factory());
        }, delay);
      });
    }
    return factory();
  });

  // Preload the component if requested
  if (preload && typeof window !== 'undefined') {
    // Use requestIdleCallback if available, otherwise use setTimeout
    const schedulePreload = window.requestIdleCallback || window.setTimeout;
    schedulePreload(() => {
      factory();
    });
  }

  // Return the lazy component wrapped in a Suspense
  return LazyComponent as React.LazyExoticComponent<T>;
}

/**
 * Wraps a component in a Suspense with a loading fallback
 * 
 * @param Component The component to wrap
 * @param fallback Loading fallback (optional)
 * @returns Wrapped component
 */
export function withSuspense<P extends object>(
  Component: React.ComponentType<P>,
  fallback: React.ReactNode = <DefaultLoader />
): React.FC<P> {
  return (props: P) => (
    <Suspense fallback={fallback}>
      <Component {...props} />
    </Suspense>
  );
}

export default lazyLoad;
