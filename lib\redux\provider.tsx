'use client';

import { Provider } from 'react-redux';
import { store } from './store';
import { useState, useEffect } from 'react';

export function ReduxProvider({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Always wrap children in Provider, but conditionally render the children
  return (
    <Provider store={store}>
      {mounted ? children : null}
    </Provider>
  );
}
