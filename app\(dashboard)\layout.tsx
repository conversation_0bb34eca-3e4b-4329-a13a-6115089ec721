'use client';

import Link from 'next/link';
import { use } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { CircleIcon, LogOut, ChevronDown, Menu, Search } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useUser } from '@/lib/auth';
import { signOut } from '@/app/(login)/actions';
import { useRouter } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { ThemeToggle } from '@/components/theme-toggle';


function Header() {
  const { userPromise } = useUser();
  const user: any = use(userPromise);
  const router = useRouter();

  async function handleSignOut() {
    try {
      await signOut();
      router.refresh();
      router.push('/');
    } catch (error) {
      console.error('Error during sign out:', error);
      // Still try to redirect to home page even if there was an error
      router.push('/');
    }
  }

  return (
    <header className="w-full bg-card border-b border-border">
      {!user ? (
        // Non-authenticated header with container width
        <div className="container mx-auto">
          <div className="h-20 flex items-center">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
              <img
                src="https://res.cloudinary.com/dyiso4ohk/image/upload/v1743058860/image_16_guqxd0.png"
                alt=""
                width={120}  // Added width
                height={40}  // Added height
                className="object-contain"  // Ensures image maintains aspect ratio
              />
              </Link>
            </div>

            {/* Rest of the code remains unchanged */}
            <div className="flex items-center gap-6 ml-auto">
              <Link href="/about" className="text-muted-foreground hover:text-foreground transition-colors">
                About Us
              </Link>
              <Link href="/faq" className="text-muted-foreground hover:text-foreground transition-colors">
                FAQ
              </Link>
              <Link href="/pricing" className="text-muted-foreground hover:text-foreground transition-colors">
                Pricing
              </Link>
              <Link href="/contact" className="text-muted-foreground hover:text-foreground transition-colors">
                Contact
              </Link>
              <Link href="/sign-in" className="text-muted-foreground hover:text-foreground transition-colors">
                <Button variant="ghost" className="rounded-full">
                  Log in
                </Button>
              </Link>
              <Link href="/sign-up">
                <Button className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-full">
                  Sign up
                </Button>
              </Link>
            </div>
          </div>
        </div>
      ) : (
        // Authenticated header with sidebar width
        <div className="h-20 flex items-center">
          {/* Left section with logo - Fixed width to match sidebar */}
          <div className="w-[240px] min-w-[240px] px-4 flex items-center justify-between border-r border-border lg:justify-start lg:gap-3">
            {/* Mobile Menu Button */}
            <Button variant="ghost" size="icon" className="lg:hidden -ml-2">
              <Sheet>
                <SheetTrigger asChild>
                  <Menu className="h-5 w-5" />
                </SheetTrigger>
                <SheetContent side="left" className="w-[240px] p-0">
                  <SheetHeader className="px-6 py-4 border-b border-border">
                    <SheetTitle className="flex items-center">
                      <CircleIcon className="h-7 w-7 text-orange-500" />
                      <span className="ml-2 text-xl font-semibold text-foreground">Turinos</span>
                    </SheetTitle>
                  </SheetHeader>
                  <div className="py-4">
                    <nav className="space-y-1">
                      <Link
                        href={user?.role === 'owner' ? '/dashboard/owner' : '/dashboard/member'}
                        className="flex items-center px-6 py-3 text-sm font-medium text-foreground hover:bg-accent"
                      >
                        Dashboard
                      </Link>
                      {user?.role === 'owner' ? (
                        <>
                          <Link
                            href="/dashboard/add-case"
                            className="flex items-center px-6 py-3 text-sm font-medium text-foreground hover:bg-accent"
                          >
                            Add Case
                          </Link>
                          <Link
                            href="/dashboard/import-cases"
                            className="flex items-center px-6 py-3 text-sm font-medium text-foreground hover:bg-accent"
                          >
                            Import Cases
                          </Link>
                          <Link
                            href="/dashboard/categories"
                            className="flex items-center px-6 py-3 text-sm font-medium text-gray-900 hover:bg-gray-50"
                          >
                            Categories
                          </Link>
                          <Link
                            href="/dashboard/trending"
                            className="flex items-center px-6 py-3 text-sm font-medium text-gray-900 hover:bg-gray-50"
                          >
                            Trending Cases
                          </Link>
                        </>
                      ) : (
                        <>
                          <Link
                            href="/dashboard/trends"
                            className="flex items-center px-6 py-3 text-sm font-medium text-gray-900 hover:bg-gray-50"
                          >
                            Trends
                          </Link>
                          <Link
                            href="/dashboard/reports"
                            className="flex items-center px-6 py-3 text-sm font-medium text-gray-900 hover:bg-gray-50"
                          >
                            Reports
                          </Link>
                          <Link
                            href="/dashboard/ns-opinion"
                            className="flex items-center px-6 py-3 text-sm font-medium text-gray-900 hover:bg-gray-50"
                          >
                            NS Opinion
                          </Link>
                        </>
                      )}
                    </nav>
                  </div>
                </SheetContent>
              </Sheet>
            </Button>

            {/* Logo */}
            <Link href="/" className="flex items-center">
              <CircleIcon className="h-7 w-7 text-orange-500" />
              <span className="ml-2 text-xl font-semibold">Turinos</span>
            </Link>
          </div>

          {/* Rest of the code remains unchanged */}
          <div className="flex-1 px-4 flex items-center justify-between">
            {/* Search Bar - Hidden on Mobile */}
            <div className="hidden lg:block lg:flex-1 lg:max-w-[480px] xl:max-w-[640px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search..."
                  className="w-full h-9 bg-muted border-0 text-sm pl-10"
                />
              </div>
            </div>

            {/* Right section with user menu */}
            <div className="flex items-center gap-2 ml-auto">
              {/* Mobile Search Button */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="lg:hidden"
                  >
                    <Search className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="top" className="h-24">
                  <div className="pt-4 px-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="search"
                        placeholder="Search..."
                        className="w-full h-9 bg-muted border-0 text-sm pl-10"
                      />
                    </div>
                  </div>
                </SheetContent>
              </Sheet>

              {/* Theme Toggle */}
              <ThemeToggle className="mr-2" />

              {/* User Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-9 px-2">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-7 w-7">
                        <AvatarImage src={user.image || ''} alt={user.name || ''} />
                        <AvatarFallback className="bg-blue-500 text-white">
                          {user.name?.charAt(0) || user.email?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem>
                    <Link href={user?.role === 'owner' ? '/dashboard/owner' : '/dashboard/member'} className="flex items-center w-full">
                      Dashboard
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <form action={handleSignOut}>
                    <DropdownMenuItem asChild>
                      <button className="flex items-center w-full text-red-600">
                        <LogOut className="mr-2 h-4 w-4" />
                        Sign out
                      </button>
                    </DropdownMenuItem>
                  </form>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <section className="flex flex-col min-h-screen">
      {children}
    </section>
  );
}