'use client';

import React from 'react';
import Hero from '@/components/sections/Homepage/Hero';
import Features from '@/components/sections/Homepage/Features';
import Partners from '@/components/sections/Homepage/Partners';
import BusinessPotential from '@/components/sections/Homepage/BusinessPotential';
import Faq from '@/components/sections/Homepage/faq';
import Cta from '@/components/sections/Homepage/Cta';
import Footer from '@/components/sections/Homepage/Footer';
import CustomerTestimonial from '@/components/sections/Homepage/CustomerTestimonial';
import Navbar from '@/components/Navbar';

interface HomePageProps {}
const HomePage: React.FC<HomePageProps> = () => {

  return (
   <>
    <Navbar />
    <div className="w-full overflow-x-hidden">
      <Hero />
      <Features />
      <Partners />
      <BusinessPotential />
      <CustomerTestimonial />
      <Faq />
      <Cta />
      <Footer />
    </div>
   </>
  );
};

export default HomePage;