/**
 * Setup New Stripe Account <PERSON><PERSON><PERSON>
 * Creates the same pricing structure in a new Stripe account
 */

import <PERSON><PERSON> from 'stripe';
import * as dotenv from 'dotenv';
import * as readline from 'readline';

// Load environment variables
dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

function askConfirmation(question: string): Promise<boolean> {
  return new Promise((resolve) => {
    rl.question(`${question} (y/N): `, (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function setupNewStripeAccount(): Promise<void> {
  console.log('🆕 Setup New Stripe Account');
  console.log('============================\n');
  
  console.log('This script will create the same pricing structure in your new Stripe account:');
  console.log('📋 Starter Plan: 14-day trial → paid subscription');
  console.log('📋 Pro Plan: Higher tier paid subscription');
  console.log('📋 Enterprise: Contact Us (no Stripe product)\n');

  try {
    // Verify Stripe connection
    console.log('🔍 Verifying Stripe connection...');
    const account = await stripe.accounts.retrieve();
    console.log(`✅ Connected to Stripe account: ${account.display_name || account.id}`);
    console.log(`   Country: ${account.country}`);
    console.log(`   Currency: ${account.default_currency?.toUpperCase()}\n`);

    // Check if account is empty
    const existingProducts = await stripe.products.list({ limit: 5 });
    if (existingProducts.data.length > 0) {
      console.log(`⚠️  This Stripe account already has ${existingProducts.data.length} products.`);
      const continueAnyway = await askConfirmation('Do you want to continue and add more products?');
      if (!continueAnyway) {
        console.log('❌ Setup cancelled.');
        rl.close();
        return;
      }
    } else {
      console.log('✅ Stripe account is empty - perfect for setup!');
    }

    // Get pricing preferences
    console.log('\n💰 Pricing Configuration:');
    console.log('=========================');
    
    const useDefaultPricing = await askConfirmation('Use the same pricing as before? (Starter: $12/month, Pro: $60/month)');
    
    let starterPrice = 12;
    let starterYearlyPrice = 120;
    let proPrice = 60;
    let proYearlyPrice = 600;
    
    if (!useDefaultPricing) {
      const starterPriceInput = await askQuestion('Starter plan monthly price (USD): $');
      const starterYearlyInput = await askQuestion('Starter plan yearly price (USD): $');
      const proPriceInput = await askQuestion('Pro plan monthly price (USD): $');
      const proYearlyInput = await askQuestion('Pro plan yearly price (USD): $');
      
      starterPrice = parseFloat(starterPriceInput) || 12;
      starterYearlyPrice = parseFloat(starterYearlyInput) || 120;
      proPrice = parseFloat(proPriceInput) || 60;
      proYearlyPrice = parseFloat(proYearlyInput) || 600;
    }

    console.log('\n📋 Plan Structure to Create:');
    console.log('============================');
    console.log(`📋 Starter Plan:`);
    console.log(`   - 14-day free trial`);
    console.log(`   - $${starterPrice}/month after trial`);
    console.log(`   - $${starterYearlyPrice}/year after trial`);
    console.log(`📋 Pro Plan:`);
    console.log(`   - $${proPrice}/month`);
    console.log(`   - $${proYearlyPrice}/year`);
    console.log(`📋 Enterprise Plan:`);
    console.log(`   - Contact Us (no Stripe product)`);

    const confirmSetup = await askConfirmation('\nProceed with creating these plans?');
    if (!confirmSetup) {
      console.log('❌ Setup cancelled.');
      rl.close();
      return;
    }

    console.log('\n🚀 Creating plans...\n');

    // Create Starter Plan
    console.log('📋 Creating Starter Plan...');
    const starterProduct = await stripe.products.create({
      name: 'Starter Plan',
      description: 'Perfect for individuals getting started - 14 day free trial',
      metadata: {
        plan_type: 'starter',
        trial_days: '14',
        created_by: 'new-account-setup',
        created_at: new Date().toISOString()
      }
    });
    console.log(`   ✅ Created Starter product: ${starterProduct.id}`);

    // Starter monthly price
    const starterMonthlyPrice = await stripe.prices.create({
      product: starterProduct.id,
      currency: 'usd',
      unit_amount: Math.round(starterPrice * 100),
      recurring: {
        interval: 'month',
        trial_period_days: 14
      },
      metadata: {
        plan_type: 'starter',
        billing_interval: 'monthly',
        trial_days: '14'
      }
    });
    console.log(`   ✅ Created Starter monthly: $${starterPrice}/month with 14-day trial (${starterMonthlyPrice.id})`);

    // Starter yearly price
    const starterYearlyPriceObj = await stripe.prices.create({
      product: starterProduct.id,
      currency: 'usd',
      unit_amount: Math.round(starterYearlyPrice * 100),
      recurring: {
        interval: 'year',
        trial_period_days: 14
      },
      metadata: {
        plan_type: 'starter',
        billing_interval: 'yearly',
        trial_days: '14'
      }
    });
    console.log(`   ✅ Created Starter yearly: $${starterYearlyPrice}/year with 14-day trial (${starterYearlyPriceObj.id})`);

    // Create Pro Plan
    console.log('\n📋 Creating Pro Plan...');
    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'For teams and businesses ready to scale with AI',
      metadata: {
        plan_type: 'pro',
        created_by: 'new-account-setup',
        created_at: new Date().toISOString()
      }
    });
    console.log(`   ✅ Created Pro product: ${proProduct.id}`);

    // Pro monthly price
    const proMonthlyPrice = await stripe.prices.create({
      product: proProduct.id,
      currency: 'usd',
      unit_amount: Math.round(proPrice * 100),
      recurring: { interval: 'month' },
      metadata: {
        plan_type: 'pro',
        billing_interval: 'monthly'
      }
    });
    console.log(`   ✅ Created Pro monthly: $${proPrice}/month (${proMonthlyPrice.id})`);

    // Pro yearly price
    const proYearlyPriceObj = await stripe.prices.create({
      product: proProduct.id,
      currency: 'usd',
      unit_amount: Math.round(proYearlyPrice * 100),
      recurring: { interval: 'year' },
      metadata: {
        plan_type: 'pro',
        billing_interval: 'yearly'
      }
    });
    console.log(`   ✅ Created Pro yearly: $${proYearlyPrice}/year (${proYearlyPriceObj.id})`);

    // Generate environment variables
    console.log('\n📝 Environment Variables for .env file:');
    console.log('=======================================');
    console.log('# Stripe Product IDs');
    console.log(`STRIPE_STARTER_PRODUCT_ID=${starterProduct.id}`);
    console.log(`STRIPE_PRO_PRODUCT_ID=${proProduct.id}`);
    console.log('');
    console.log('# Stripe Price IDs');
    console.log(`STRIPE_STARTER_MONTHLY_PRICE_ID=${starterMonthlyPrice.id}`);
    console.log(`STRIPE_STARTER_YEARLY_PRICE_ID=${starterYearlyPriceObj.id}`);
    console.log(`STRIPE_PRO_MONTHLY_PRICE_ID=${proMonthlyPrice.id}`);
    console.log(`STRIPE_PRO_YEARLY_PRICE_ID=${proYearlyPriceObj.id}`);

    console.log('\n🎉 NEW STRIPE ACCOUNT SETUP COMPLETE!');
    console.log('=====================================');
    console.log('✅ Starter Plan created with 14-day trial');
    console.log('✅ Pro Plan created');
    console.log('✅ Environment variables generated');
    
    console.log('\n📋 Next Steps:');
    console.log('1. Copy the environment variables above to your .env file');
    console.log('2. Replace your old Stripe environment variables');
    console.log('3. Restart your application');
    console.log('4. Test the new plans in your billing page');
    console.log('5. Set up webhooks in your new Stripe account if needed');

  } catch (error: any) {
    console.error('\n❌ ERROR SETTING UP NEW ACCOUNT:', error.message);
    
    if (error.type === 'StripeAuthenticationError') {
      console.error('\n🔑 Authentication Error:');
      console.error('   Check your STRIPE_SECRET_KEY in .env file');
      console.error('   Make sure you\'re using the new account\'s secret key');
    }
    
  } finally {
    rl.close();
  }
}

// Run the script
if (require.main === module) {
  setupNewStripeAccount();
}

export { setupNewStripeAccount };
