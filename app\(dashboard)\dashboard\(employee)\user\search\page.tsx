'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ArrowRightIcon,
  MagnifyingGlassIcon,
  BarChartIcon,
  BookmarkIcon,
} from '@radix-ui/react-icons';
import { Search, ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useToast } from '@/components/ui/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { Pagination } from '@/components/ui/pagination';
import { updateCaseStudyViewsOnClick } from '@/lib/api/views';

interface CaseStudy {
  id: number;
  useCaseTitle: string;
  industry: string | null;
  role: string | null;
  featureImageUrl: string | null;
  previewImageUrl: string | null;
  introductionText: string | null;
  marketIntelligenceData: any;
  marketMetricsData: any;
  isBookmarked?: boolean;
  icons: {
    iconUrl: string;
    order: number;
  }[];
}

interface SearchResponse {
  caseStudies: CaseStudy[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  query: string;
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();

  const query = searchParams.get('q') || '';
  const page = parseInt(searchParams.get('page') || '1');

  const [searchQuery, setSearchQuery] = useState(query);
  const [results, setResults] = useState<CaseStudy[]>([]);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    pageSize: 12,
    totalPages: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch search results
  useEffect(() => {
    const fetchResults = async () => {
      if (!query) {
        setResults([]);
        setPagination({
          total: 0,
          page: 1,
          pageSize: 12,
          totalPages: 0,
        });
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(
          `/api/case-studies/search?q=${encodeURIComponent(
            query
          )}&page=${page}&pageSize=12`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch search results');
        }

        const data: SearchResponse = await response.json();

        // Get user's bookmarks
        const bookmarksResponse = await fetch('/api/bookmarks');
        if (bookmarksResponse.ok) {
          const bookmarks = await bookmarksResponse.json();
          const bookmarkedIds = new Set(
            bookmarks.map((b: any) => b.caseStudyId)
          );

          // Add isBookmarked property to case studies
          data.caseStudies = data.caseStudies.map((cs) => ({
            ...cs,
            isBookmarked: bookmarkedIds.has(cs.id),
          }));
        }

        setResults(data.caseStudies);
        setPagination(data.pagination);
      } catch (err) {
        console.error('Error fetching search results:', err);
        setError('Failed to fetch search results. Please try again.');
        toast({
          title: 'Error',
          description: 'Failed to fetch search results. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [query, page, toast]);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(
        `/dashboard/user/search?q=${encodeURIComponent(searchQuery.trim())}`
      );
    }
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    router.push(
      `/dashboard/user/search?q=${encodeURIComponent(query)}&page=${newPage}`
    );
  };

  console.log('Search results:', results);

  return (
    <div className='space-y-8 pb-12'>
      {/* Search Header */}
      <div className='bg-blue-50 p-6 rounded-lg'>
        <h1 className='text-2xl font-bold mb-4'>Search Case Studies</h1>

        <form onSubmit={handleSearch} className='flex gap-2'>
          <div className='relative flex-1'>
            <Input
              className='pl-10 pr-4 py-2 h-12'
              placeholder='Search across the use cases library'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <MagnifyingGlassIcon className='absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400' />
          </div>
          <Button type='submit' className='h-12 px-6'>
            Search
          </Button>
        </form>
      </div>

      {/* Search Results */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <h2 className='text-xl font-medium'>
            {query ? `Search Results for "${query}"` : 'Search Results'}
          </h2>
          <div className='text-sm text-gray-500'>
            {pagination.total > 0
              ? `${pagination.total} results found`
              : 'No results found'}
          </div>
        </div>

        {isLoading ? (
          // Loading skeleton
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i} className='overflow-hidden'>
                <Skeleton className='h-48 w-full' />
                <div className='p-4 space-y-3'>
                  <Skeleton className='h-4 w-3/4' />
                  <Skeleton className='h-4 w-1/2' />
                  <Skeleton className='h-20 w-full' />
                </div>
              </Card>
            ))}
          </div>
        ) : error ? (
          // Error message
          <div className='text-center py-12'>
            <p className='text-red-500'>{error}</p>
            <Button
              variant='outline'
              className='mt-4'
              onClick={() =>
                router.push(
                  `/dashboard/user/search?q=${encodeURIComponent(query)}`
                )
              }
            >
              Try Again
            </Button>
          </div>
        ) : results.length === 0 ? (
          // No results
          <div className='text-center py-12'>
            {query ? (
              <>
                <p className='text-gray-500'>
                  No case studies found matching "{query}"
                </p>
                <p className='text-gray-500 mt-2'>
                  Try different keywords or browse all case studies
                </p>
                <Button asChild className='mt-4'>
                  <Link href='/dashboard/member'>Browse All Case Studies</Link>
                </Button>
              </>
            ) : (
              <p className='text-gray-500'>
                Enter a search term to find case studies
              </p>
            )}
          </div>
        ) : (
          // Search results grid
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
            {results.map((caseStudy) => {
              const imageURL =
                caseStudy.featureImageUrl ||
                caseStudy.previewImageUrl ||
                caseStudy.icons[0]?.iconUrl;
              return (
                <Link
                  key={caseStudy.id}
                  href={`/dashboard/case-studies/${caseStudy.id}`}
                  className='block transition-transform hover:scale-[1.02]'
                  onClick={() => updateCaseStudyViewsOnClick(caseStudy.id)}
                >
                  <Card className='h-full overflow-hidden border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all'>
                    <div className='relative h-48 w-full overflow-hidden bg-gray-100'>
                      {imageURL ? (
                        <Image
                          src={imageURL}
                          alt={caseStudy.useCaseTitle || 'Case study'}
                          fill
                          className='object-cover'
                        />
                      ) : (
                        <div className='flex h-full w-full items-center justify-center bg-gray-200'>
                          <span className='text-gray-500'>No image</span>
                        </div>
                      )}

                      {/* Market Intelligence Badge */}
                      {Boolean(caseStudy.marketIntelligenceData) && (
                        <div className='absolute top-2 right-2'>
                          <Badge className='bg-blue-600 text-white flex items-center gap-1'>
                            <MagnifyingGlassIcon className='h-3 w-3' />
                            Market Intelligence
                          </Badge>
                        </div>
                      )}

                      {/* Bookmark Badge */}
                      {caseStudy.isBookmarked && (
                        <div className='absolute top-2 left-2'>
                          <Badge className='bg-green-600 text-white flex items-center gap-1 '>
                            <BookmarkIcon className='w-5' />
                          </Badge>
                        </div>
                      )}
                    </div>

                    <div className='p-5'>
                      <div className='mb-2 flex flex-wrap gap-2'>
                        {caseStudy.industry && (
                          <Badge variant='outline' className='bg-gray-100'>
                            {caseStudy.industry}
                          </Badge>
                        )}
                        {caseStudy.role && (
                          <Badge variant='outline' className='bg-gray-100'>
                            {caseStudy.role}
                          </Badge>
                        )}

                        {/* Market Metrics Badge */}
                        {Boolean(caseStudy.marketMetricsData) && (
                          <Badge
                            variant='outline'
                            className='bg-green-50 text-green-700 border-green-200 flex items-center gap-1'
                          >
                            <BarChartIcon className='h-3 w-3' />
                            Metrics
                          </Badge>
                        )}
                      </div>

                      <h3 className='text-xl font-semibold mb-2 line-clamp-2'>
                        {caseStudy.useCaseTitle}
                      </h3>

                      <p className='text-gray-600 mb-4 line-clamp-3'>
                        {caseStudy.introductionText ||
                          'No description available.'}
                      </p>

                      <div className='flex justify-end'>
                        <Button
                          variant='ghost'
                          className='text-blue-600 hover:text-blue-800 p-0 flex items-center gap-1'
                        >
                          View Case Study
                          <ArrowRightIcon className='h-4 w-4' />
                        </Button>
                      </div>
                    </div>
                  </Card>
                </Link>
              );
            })}
          </div>
        )}

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className='flex justify-center mt-8'>
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>
    </div>
  );
}
