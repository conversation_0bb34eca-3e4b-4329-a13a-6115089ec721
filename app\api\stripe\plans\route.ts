import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/payments/stripe';
import { getUser } from '@/lib/db/server-queries';

export async function GET(request: NextRequest) {
  try {
    console.log('[Stripe Plans API] Fetching plans...');

    // Validate Stripe configuration
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('[Stripe Plans API] STRIPE_SECRET_KEY not configured');
      return NextResponse.json(
        { error: 'Stripe not configured', details: 'STRIPE_SECRET_KEY environment variable is missing' },
        { status: 500 }
      );
    }

    // Try to get the current user, but don't require authentication
    // This allows non-logged-in users to see pricing plans
    const user = await getUser();
    // Note: We no longer return 401 if user is not authenticated
    // This allows public access to pricing plans

    // Fetch all active products from Stripe
    let products;
    try {
      products = await stripe.products.list({
        active: true,
        limit: 100,
      });
      console.log(`[Stripe Plans API] Found ${products.data.length} active products`);
    } catch (stripeError) {
      console.error('Error fetching products from Stripe:', stripeError);
      return NextResponse.json(
        { error: 'Failed to fetch products from Stripe', details: stripeError instanceof Error ? stripeError.message : 'Unknown error' },
        { status: 500 }
      );
    }

    // Check if products data is valid
    if (!products || !products.data || !Array.isArray(products.data)) {
      console.error('Invalid products data returned from Stripe');
      return NextResponse.json(
        { error: 'Invalid products data returned from Stripe' },
        { status: 500 }
      );
    }

    // Log environment variables for debugging (without exposing full values)
    console.log('[Stripe Plans API] Environment check:', {
      hasStarterProductId: !!process.env.STRIPE_STARTER_PRODUCT_ID,
      hasProProductId: !!process.env.STRIPE_PRO_PRODUCT_ID,
      starterProductIdPrefix: process.env.STRIPE_STARTER_PRODUCT_ID?.substring(0, 8) + '...',
      proProductIdPrefix: process.env.STRIPE_PRO_PRODUCT_ID?.substring(0, 8) + '...',
    });

    // Filter products that match our current environment variables or have plan_type metadata
    const planProducts = products.data.filter((product) => {
      // Check if this is one of our current products
      const isCurrentProduct =
        product.id === process.env.STRIPE_STARTER_PRODUCT_ID ||
        product.id === process.env.STRIPE_PRO_PRODUCT_ID;

      // Check if it has plan_type metadata
      const hasPlanType = product.metadata &&
        (product.metadata.plan_type === 'starter' || product.metadata.plan_type === 'pro');

      const isValid = isCurrentProduct || hasPlanType;

      console.log(`[Stripe Plans API] Product: ${product.name} (${product.id})`, {
        isCurrentProduct,
        hasPlanType,
        planType: product.metadata?.plan_type,
        isValid
      });

      return isValid;
    });

    console.log(`[Stripe Plans API] Found ${planProducts.length} valid plan products`);

    // If no valid products found, return error
    if (planProducts.length === 0) {
      console.error('No valid plan products found in Stripe. Please ensure products are properly configured.');
      return NextResponse.json(
        {
          error: 'No pricing plans found',
          details: 'Please ensure Stripe products are properly configured with plan_type metadata or environment variables are set correctly.'
        },
        { status: 404 }
      );
    }

    // For each product, fetch ALL its prices (not just monthly)
    const productsWithPrices = await Promise.all(
      planProducts.map(async (product) => {
        try {
          const prices = await stripe.prices.list({
            product: product.id,
            active: true,
            currency: 'usd',
            // Remove the recurring filter to get all prices including trial prices
          });

          console.log(`[Stripe Plans API] Product ${product.name} has ${prices.data.length} prices`);

          return {
            ...product,
            prices: prices.data,
          };
        } catch (priceError) {
          console.error(`Error fetching prices for product ${product.id}:`, priceError);
          // Return the product with an empty prices array
          return {
            ...product,
            prices: [],
          };
        }
      })
    );

    // Filter out products with no prices
    const productsWithValidPrices = productsWithPrices.filter(
      (product) => product.prices && product.prices.length > 0
    );

    console.log(`[Stripe Plans API] ${productsWithValidPrices.length} products have valid prices`);

    // If no products with valid prices found, return error
    if (productsWithValidPrices.length === 0) {
      console.error('No products with valid prices found in Stripe');
      return NextResponse.json(
        {
          error: 'No valid pricing found',
          details: 'Products found but no active prices configured. Please ensure prices are set up in Stripe.'
        },
        { status: 404 }
      );
    }

    // Sort products by plan type (starter first, then pro)
    productsWithValidPrices.sort((a, b) => {
      const order = ['starter', 'pro'];
      const aType = a.metadata?.plan_type || 'unknown';
      const bType = b.metadata?.plan_type || 'unknown';

      const aIndex = order.indexOf(aType);
      const bIndex = order.indexOf(bType);

      if (aIndex === -1) return 1;
      if (bIndex === -1) return -1;

      return aIndex - bIndex;
    });

    console.log(`[Stripe Plans API] Returning ${productsWithValidPrices.length} plans`);
    return NextResponse.json(productsWithValidPrices);

  } catch (error) {
    console.error('Error fetching plans:', error);
    return NextResponse.json(
      { error: 'Failed to fetch plans', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
