'use client';

import { use } from 'react';
import { useUser } from '@/lib/auth';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Send } from 'lucide-react';

export default function NSOpinionPage() {
  const { userPromise } = useUser();
  const user = use(userPromise);

  if (user?.role === 'owner') {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
        <p className="mt-2">This page is only accessible to regular users.</p>
      </div>
    );
  }

  // Sample previous opinions
  const previousOpinions = [
    {
      id: 1,
      question: "What are the legal implications of remote work contracts?",
      response: "Based on current labor laws, remote work contracts should address several key areas...",
      date: "2025-02-20",
      status: "answered"
    },
    {
      id: 2,
      question: "How does recent precedent affect intellectual property in AI?",
      response: "Recent court decisions have established new guidelines for AI-related IP...",
      date: "2025-02-18",
      status: "answered"
    }
  ];

  return (
    <div className="p-4 space-y-4">
      <h1 className="text-2xl font-bold">NS Opinion Service</h1>
      <p className="text-muted-foreground">Get expert legal opinions on your cases and queries</p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* New Question Form */}
        <Card>
          <CardHeader>
            <CardTitle>Ask a Question</CardTitle>
            <CardDescription>
              Submit your legal query for expert analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="subject">Subject</Label>
                <Input id="subject" placeholder="Brief subject of your query" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="question">Your Question</Label>
                <Textarea
                  id="question"
                  placeholder="Describe your legal question in detail..."
                  className="h-32"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="context">Additional Context</Label>
                <Textarea
                  id="context"
                  placeholder="Any relevant case details or background information..."
                  className="h-24"
                />
              </div>

              <Button className="w-full">
                <Send className="w-4 h-4 mr-2" />
                Submit Question
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Previous Opinions */}
        <Card>
          <CardHeader>
            <CardTitle>Previous Opinions</CardTitle>
            <CardDescription>
              Your recent opinion requests and responses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {previousOpinions.map((opinion) => (
                <Card key={opinion.id}>
                  <CardHeader className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <CardTitle className="text-sm font-medium">
                          {opinion.question}
                        </CardTitle>
                        <div className="text-xs text-muted-foreground">
                          {opinion.date}
                        </div>
                      </div>
                      <Badge variant="secondary">{opinion.status}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-sm text-muted-foreground">
                      {opinion.response}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
