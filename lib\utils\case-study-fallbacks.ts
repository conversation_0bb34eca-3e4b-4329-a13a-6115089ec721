import { CaseStudyData } from '@/components/CaseStudyCard/types';

/**
 * Default fallback values for case study data
 */
export const DEFAULT_CASE_STUDY_FALLBACKS = {
  title: 'Untitled Case Study',
  headerImage: '/placeholder-image.jpg',
  industry: 'General',
  role: 'All Roles',
  kpis: {
    claimCycleTime: 'Efficiency',
    straightThroughRate: 'Throughput',
    customerComplaintVolume: 'Customer Satisfaction',
  },
  introduction: {
    title: 'Introduction',
    text: 'This case study explores innovative approaches to business challenges.',
    transitionToChallange: 'Key Challenges:',
    transitionToQuestions: 'Key Questions:',
    problems: [
      'Challenge 1: Operational inefficiency',
      'Challenge 2: Customer experience gaps',
      'Challenge 3: Technology integration',
    ],
    problemIcons: [
      '/placeholder-icon.png',
      '/placeholder-icon.png',
      '/placeholder-icon.png',
    ],
    questions: [
      'How can we improve operational efficiency?',
      'What strategies enhance customer experience?',
      'How do we integrate new technologies effectively?',
    ],
    questionIcons: [
      '/placeholder-icon.png',
      '/placeholder-icon.png',
      '/placeholder-icon.png',
    ],
  },
  process: {
    title: 'Process Overview',
    steps: [
      {
        title: 'Analysis',
        description: 'Thorough analysis of current processes and pain points',
        icon: '/placeholder-icon.png',
      },
      {
        title: 'Strategy',
        description: 'Development of targeted strategies and solutions',
        icon: '/placeholder-icon.png',
      },
      {
        title: 'Implementation',
        description: 'Careful implementation with continuous monitoring',
        icon: '/placeholder-icon.png',
      },
    ],
  },
  solution: {
    title: 'Solution Blueprint',
    description:
      'A comprehensive approach to addressing the identified challenges:',
    items: [
      {
        title: 'Digital Transformation',
        description: 'Leveraging digital tools to streamline operations',
        icon: '/placeholder-icon.png',
      },
      {
        title: 'Process Optimization',
        description: 'Refining workflows for maximum efficiency',
        icon: '/placeholder-icon.png',
      },
      {
        title: 'Customer-Centric Design',
        description: 'Redesigning experiences with the customer in mind',
        icon: '/placeholder-icon.png',
      },
    ],
  },
  // impact: {
  //   title: 'Potential Impact',
  //   metrics: [
  //     {
  //       metric: 'Efficiency',
  //       value: '30%',
  //       description: 'Improvement in operational efficiency',
  //       icon: '/placeholder-icon.png',
  //     },
  //     {
  //       metric: 'Customer Satisfaction',
  //       value: '25%',
  //       description: 'Increase in customer satisfaction scores',
  //       icon: '/placeholder-icon.png',
  //     },
  //     {
  //       metric: 'Cost Reduction',
  //       value: '20%',
  //       description: 'Reduction in operational costs',
  //       icon: '/placeholder-icon.png',
  //     },
  //     {
  //       metric: 'Revenue Growth',
  //       value: '15%',
  //       description: 'Increase in revenue',
  //       icon: '/placeholder-icon.png',
  //     },
  //   ],
  // },
  impact: {
    title: 'Potential Impact',
    metrics: [
      {
        metric: 'Efficiency',
        value: '',
        description: '',
        icon: '/placeholder-icon.png',
      },
      {
        metric: 'Customer Satisfaction',
        value: '',
        description: '',
        icon: '/placeholder-icon.png',
      },
      {
        metric: 'Cost Reduction',
        value: '',
        description: '',
        icon: '/placeholder-icon.png',
      },
      {
        metric: 'Revenue Growth',
        value: '',
        description: '',
        icon: '/placeholder-icon.png',
      },
    ],
  },
  conclusion: {
    title: 'Conclusion',
    text: 'By implementing these strategic changes, organizations can achieve significant improvements in efficiency, customer satisfaction, and overall business performance.',
  },
};

/**
 * Ensures a case study has all required fields by filling in missing data with fallbacks
 * @param caseStudy The original case study data
 * @returns A complete case study with fallbacks for any missing data
 */
export function ensureCaseStudyData(
  caseStudy: Partial<CaseStudyData>
): CaseStudyData {
  const fallbacks = DEFAULT_CASE_STUDY_FALLBACKS;

  return {
    id: caseStudy.id || 'preview',
    title: caseStudy.title || fallbacks.title,
    headerImage: caseStudy.headerImage || fallbacks.headerImage,
    industry: caseStudy.industry || fallbacks.industry,
    role: caseStudy.role || fallbacks.role,
    vector: caseStudy.vector || '',

    // Handle KPIs with nested fallbacks
    kpis: {
      claimCycleTime:
        caseStudy.kpis?.claimCycleTime || fallbacks.kpis.claimCycleTime,
      straightThroughRate:
        caseStudy.kpis?.straightThroughRate ||
        fallbacks.kpis.straightThroughRate,
      customerComplaintVolume:
        caseStudy.kpis?.customerComplaintVolume ||
        fallbacks.kpis.customerComplaintVolume,
    },

    // Handle introduction with nested fallbacks
    introduction: {
      title: caseStudy.introduction?.title || fallbacks.introduction.title,
      text: caseStudy.introduction?.text || fallbacks.introduction.text,
      transitionToChallange:
        caseStudy.introduction?.transitionToChallange || 'Key Challenges:',
      transitionToQuestions:
        caseStudy.introduction?.transitionToQuestions || 'Key Questions:',
      problems: caseStudy.introduction?.problems?.length
        ? caseStudy.introduction.problems
        : fallbacks.introduction.problems,
      questions: caseStudy.introduction?.questions?.length
        ? caseStudy.introduction.questions
        : fallbacks.introduction.questions,
      // Preserve problem and question icons if they exist
      problemIcons: caseStudy.introduction?.problemIcons || [],
      questionIcons: caseStudy.introduction?.questionIcons || [],
    },

    // Handle process with nested fallbacks
    process: {
      title: caseStudy.process?.title || fallbacks.process.title,
      steps: caseStudy.process?.steps?.length
        ? caseStudy.process.steps.map((step, index) => ({
            title:
              step.title ||
              fallbacks.process.steps[
                Math.min(index, fallbacks.process.steps.length - 1)
              ].title,
            description:
              step.description ||
              fallbacks.process.steps[
                Math.min(index, fallbacks.process.steps.length - 1)
              ].description,
            icon:
              step.icon ||
              fallbacks.process.steps[
                Math.min(index, fallbacks.process.steps.length - 1)
              ].icon,
          }))
        : fallbacks.process.steps,
    },

    // Handle solution with nested fallbacks
    solution: {
      title: caseStudy.solution?.title || fallbacks.solution.title,
      description:
        caseStudy.solution?.description || fallbacks.solution.description,
      items: caseStudy.solution?.items?.length
        ? caseStudy.solution.items.map((item, index) => ({
            title:
              item.title ||
              fallbacks.solution.items[
                Math.min(index, fallbacks.solution.items.length - 1)
              ].title,
            description:
              item.description ||
              fallbacks.solution.items[
                Math.min(index, fallbacks.solution.items.length - 1)
              ].description,
            icon:
              item.icon ||
              fallbacks.solution.items[
                Math.min(index, fallbacks.solution.items.length - 1)
              ].icon,
          }))
        : fallbacks.solution.items,
    },

    // Handle impact with nested fallbacks
    impact: {
      title: caseStudy.impact?.title || fallbacks.impact.title,
      metrics: caseStudy.impact?.metrics?.length
        ? caseStudy.impact.metrics.map((metric, index) => ({
            metric:
              metric.metric ||
              fallbacks.impact.metrics[
                Math.min(index, fallbacks.impact.metrics.length - 1)
              ].metric,
            value:
              metric.value ||
              fallbacks.impact.metrics[
                Math.min(index, fallbacks.impact.metrics.length - 1)
              ].value,
            description:
              metric.description ||
              fallbacks.impact.metrics[
                Math.min(index, fallbacks.impact.metrics.length - 1)
              ].description,
            icon:
              metric.icon ||
              fallbacks.impact.metrics[
                Math.min(index, fallbacks.impact.metrics.length - 1)
              ].icon,
          }))
        : fallbacks.impact.metrics,
    },

    // Handle conclusion with nested fallbacks
    conclusion: {
      title: caseStudy.conclusion?.title || fallbacks.conclusion.title,
      text: caseStudy.conclusion?.text || fallbacks.conclusion.text,
      resultTitle: caseStudy.conclusion?.resultTitle || 'The result?',
      result: caseStudy.conclusion?.result || '',
    },

    // Pass through any market intelligence data if available
    marketIntelligence: caseStudy.marketIntelligence || [],
    marketMetrics: caseStudy.marketMetrics || {},
  };
}
