// Script to check the relation between case studies and users
require('dotenv').config();
const { Pool } = require('pg');

async function checkRelation() {
  // Create a connection pool
  const pool = new Pool({
    connectionString: process.env.POSTGRES_URL,
  });

  try {
    console.log('Checking case studies with user information...');
    
    // Get case studies with user_id
    const caseStudiesQuery = `
      SELECT cs.id, cs.use_case_title, cs.user_id, u.id as user_exists, u.name, u.email
      FROM case_studies cs
      LEFT JOIN users u ON cs.user_id = u.id
      ORDER BY cs.id DESC
      LIMIT 10;
    `;
    
    const result = await pool.query(caseStudiesQuery);
    
    console.log(`Found ${result.rows.length} case studies:`);
    result.rows.forEach(row => {
      console.log(`- ID: ${row.id}, Title: ${row.use_case_title}`);
      console.log(`  User ID: ${row.user_id || 'NULL'}`);
      console.log(`  User exists: ${row.user_exists ? 'YES' : 'NO'}`);
      if (row.user_exists) {
        console.log(`  User name: ${row.name || 'NULL'}`);
        console.log(`  User email: ${row.email || 'NULL'}`);
      }
      console.log('---');
    });
    
    // Check for case studies with user_id that doesn't exist in users table
    const orphanedQuery = `
      SELECT cs.id, cs.use_case_title, cs.user_id
      FROM case_studies cs
      LEFT JOIN users u ON cs.user_id = u.id
      WHERE cs.user_id IS NOT NULL AND u.id IS NULL;
    `;
    
    const orphanedResult = await pool.query(orphanedQuery);
    
    if (orphanedResult.rows.length > 0) {
      console.log('\nWARNING: Found case studies with user_id that does not exist in users table:');
      orphanedResult.rows.forEach(row => {
        console.log(`- ID: ${row.id}, Title: ${row.use_case_title}, Invalid User ID: ${row.user_id}`);
      });
    } else {
      console.log('\nNo orphaned case studies found. All user_id values reference valid users.');
    }
    
  } catch (error) {
    console.error('Error checking relation:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

// Run the check
checkRelation();
