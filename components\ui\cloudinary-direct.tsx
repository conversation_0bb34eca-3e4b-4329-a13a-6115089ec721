'use client';

import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ImageIcon } from '@radix-ui/react-icons';

interface CloudinaryDirectProps {
  onSuccess: (url: string) => void;
  onError?: (error: any) => void;
  folder?: string;
  resourceType?: 'image' | 'video' | 'raw' | 'auto';
  className?: string;
  children?: React.ReactNode;
  caseStudyId?: number;
  order?: number;
}

export function CloudinaryDirect({
  onSuccess,
  onError,
  folder = 'case-studies',
  resourceType = 'image',
  className,
  children,
  caseStudyId,
  order = 0,
}: CloudinaryDirectProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    console.log(`Starting direct upload for file: ${file.name}, size: ${file.size} bytes, type: ${file.type}`);
    setIsUploading(true);

    try {
      // Create a FormData object to send to our API
      const formData = new FormData();
      formData.append('file', file);

      // Determine the type based on the folder
      const uploadType = folder.includes('process') ? 'process' :
                        folder.includes('solution') ? 'solution' :
                        folder.includes('impact') ? 'impact' : 'header';

      console.log(`Uploading file as type: ${uploadType}`);
      formData.append('type', uploadType);
      formData.append('folder', folder);

      // Add case study ID and order if provided
      if (caseStudyId) {
        console.log(`Adding caseStudyId: ${caseStudyId}`);
        formData.append('caseStudyId', caseStudyId.toString());
      }

      console.log(`Adding order: ${order}`);
      formData.append('order', order.toString());

      console.log('Sending upload request to our API...');

      // Upload via our API endpoint
      const response = await fetch('/api/case-studies/upload-media', {
        method: 'POST',
        body: formData,
      });

      console.log(`API response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error:', errorText);
        throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Upload response:', data);

      if (data.url) {
        console.log(`Successfully uploaded file, URL: ${data.url}`);
        onSuccess(data.url);
      } else {
        console.error('No URL in response data:', data);
        throw new Error('No URL returned from upload');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      if (onError) onError(error);
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className={className}>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        disabled={isUploading}
        className="hidden"
        accept={resourceType === 'image' ? 'image/*' : resourceType === 'video' ? 'video/*' : '*'}
      />
      <div
        onClick={() => fileInputRef.current?.click()}
        className={`cursor-pointer ${isUploading ? 'opacity-50 pointer-events-none' : ''}`}
      >
        {children || (
          <Button
            type="button"
            variant="outline"
            className="flex items-center"
            disabled={isUploading}
          >
            <ImageIcon className="mr-2 h-4 w-4" />
            {isUploading ? 'Uploading...' : 'Upload'}
          </Button>
        )}
      </div>
    </div>
  );
}
