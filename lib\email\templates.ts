/**
 * Email template for team invitations to new users
 */
export function getTeamInvitationEmail(params: {
  teamName: string;
  inviterName: string;
  inviterEmail: string;
  role: string;
  inviteLink: string;
}): { subject: string; html: string; text: string } {
  const { teamName, inviterName, inviterEmail, role, inviteLink } = params;

  const subject = `Invitation to join ${teamName} on Turinos`;

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Team Invitation</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: #ffffff;
          border-left: 1px solid #e9ecef;
          border-right: 1px solid #e9ecef;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          font-size: 12px;
          color: #6c757d;
          border-radius: 0 0 5px 5px;
        }
        .button {
          display: inline-block;
          padding: 10px 20px;
          background-color: #4f46e5;
          color: #ffffff;
          text-decoration: none;
          border-radius: 5px;
          margin: 20px 0;
          font-weight: bold;
        }
        .highlight {
          font-weight: bold;
          color: #4f46e5;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>You've Been Invited!</h1>
        </div>
        <div class="content">
          <p>Hello,</p>
          <p>
            You have been invited by <span class="highlight">${inviterName || inviterEmail}</span>
            to join <span class="highlight">${teamName}</span> on Turinos as a
            <span class="highlight">${role}</span>.
          </p>
          <p>
            Turinos is a powerful AI platform that helps businesses transform their operations through data-driven insights and strategic recommendations.
          </p>
          <p>To accept this invitation, please click the button below:</p>
          <div style="text-align: center;">
            <a href="${inviteLink}" class="button">Accept Invitation</a>
          </div>
          <p>
            If the button doesn't work, you can copy and paste the following link into your browser:
          </p>
          <p>
            <a href="${inviteLink}">${inviteLink}</a>
          </p>
          <p>
            This invitation will expire in 7 days.
          </p>
          <p>
            If you have any questions, please contact ${inviterName || inviterEmail} directly.
          </p>
          <p>Welcome aboard!</p>
          <p>The Turinos Team</p>
        </div>
        <div class="footer">
          <p>
            If you received this email by mistake, please ignore it.
            No action is required on your part.
          </p>
          <p>&copy; ${new Date().getFullYear()} Turinos. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    You've Been Invited!

    Hello,

    You have been invited by ${inviterName || inviterEmail} to join ${teamName} on Turinos as a ${role}.

    Turinos is a powerful AI platform that helps businesses transform their operations through data-driven insights and strategic recommendations.

    To accept this invitation, please visit the following link:
    ${inviteLink}

    This invitation will expire in 7 days.

    If you have any questions, please contact ${inviterName || inviterEmail} directly.

    Welcome aboard!
    The Turinos Team

    If you received this email by mistake, please ignore it. No action is required on your part.

    © ${new Date().getFullYear()} Turinos. All rights reserved.
  `;

  return { subject, html, text };
}

/**
 * Email template for team invitations to existing users
 */
export function getExistingUserInvitationEmail(params: {
  teamName: string;
  inviterName: string;
  inviterEmail: string;
  role: string;
  inviteLink: string;
  userName: string;
}): { subject: string; html: string; text: string } {
  const { teamName, inviterName, inviterEmail, role, inviteLink, userName } = params;

  const subject = `You've been invited to join ${teamName} on Turinos`;

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Team Invitation</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: #ffffff;
          border-left: 1px solid #e9ecef;
          border-right: 1px solid #e9ecef;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          font-size: 12px;
          color: #6c757d;
          border-radius: 0 0 5px 5px;
        }
        .button {
          display: inline-block;
          padding: 10px 20px;
          background-color: #4f46e5;
          color: #ffffff;
          text-decoration: none;
          border-radius: 5px;
          margin: 20px 0;
          font-weight: bold;
        }
        .highlight {
          font-weight: bold;
          color: #4f46e5;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Team Invitation</h1>
        </div>
        <div class="content">
          <p>Hello ${userName},</p>
          <p>
            <span class="highlight">${inviterName || inviterEmail}</span> has invited you
            to join <span class="highlight">${teamName}</span> on Turinos as a
            <span class="highlight">${role}</span>.
          </p>
          <p>
            Since you already have an account on Turinos, you can accept this invitation
            by clicking the button below. You'll be able to access the team's resources immediately.
          </p>
          <div style="text-align: center;">
            <a href="${inviteLink}" class="button">Accept Invitation</a>
          </div>
          <p>
            If the button doesn't work, you can copy and paste the following link into your browser:
          </p>
          <p>
            <a href="${inviteLink}">${inviteLink}</a>
          </p>
          <p>
            This invitation will expire in 7 days.
          </p>
          <p>
            If you have any questions, please contact ${inviterName || inviterEmail} directly.
          </p>
          <p>Thank you for using Turinos!</p>
        </div>
        <div class="footer">
          <p>
            If you received this email by mistake, please ignore it.
            No action is required on your part.
          </p>
          <p>&copy; ${new Date().getFullYear()} Turinos. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Team Invitation

    Hello ${userName},

    ${inviterName || inviterEmail} has invited you to join ${teamName} on Turinos as a ${role}.

    Since you already have an account on Turinos, you can accept this invitation by visiting the following link:
    ${inviteLink}

    This invitation will expire in 7 days.

    If you have any questions, please contact ${inviterName || inviterEmail} directly.

    Thank you for using Turinos!

    If you received this email by mistake, please ignore it. No action is required on your part.

    © ${new Date().getFullYear()} Turinos. All rights reserved.
  `;

  return { subject, html, text };
}
