import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { Team, TeamMember, Invitation, User } from '@/lib/db/schema';

// Response types
export interface TeamResponse extends Team {
  teamMembers: (TeamMember & {
    user: Pick<User, 'id' | 'name' | 'email' | 'role'>;
  })[];
  invitations: Invitation[];
}

export interface TeamMemberResponse extends TeamMember {
  user: Pick<User, 'id' | 'name' | 'email' | 'role'>;
}

export interface InvitationResponse extends Invitation {
  inviter: Pick<User, 'id' | 'name' | 'email'>;
}

export interface InviteMemberRequest {
  email: string;
  role: 'member' | 'manager' | 'owner';
}

export interface RemoveMemberRequest {
  memberId: number;
}

export interface DeleteUserRequest {
  userId: number;
}

export interface CancelInvitationRequest {
  invitationId: number;
}

export interface UpdateTeamRequest {
  name: string;
}

export const teamApi = createApi({
  reducerPath: 'teamApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['Team', 'TeamMembers', 'Invitations'],
  endpoints: (builder) => ({
    // Get team data with members and invitations
    getTeam: builder.query<TeamResponse, void>({
      query: () => 'team',
      providesTags: ['Team', 'TeamMembers', 'Invitations'],
    }),

    // Invite a new team member
    inviteMember: builder.mutation<{ success: string }, InviteMemberRequest>({
      query: (data) => ({
        url: 'team/invite',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Invitations', 'Team'],
    }),

    // Remove a team member
    removeMember: builder.mutation<{ success: string }, RemoveMemberRequest>({
      query: (data) => ({
        url: 'team/members/remove',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['TeamMembers', 'Team'],
    }),

    // Delete a user completely
    deleteUser: builder.mutation<{ success: string }, DeleteUserRequest>({
      query: (data) => ({
        url: 'team/users/delete',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['TeamMembers', 'Team'],
    }),

    // Cancel an invitation
    cancelInvitation: builder.mutation<{ success: string }, CancelInvitationRequest>({
      query: (data) => ({
        url: 'team/invitations/cancel',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Invitations', 'Team'],
    }),

    // Update team details
    updateTeam: builder.mutation<TeamResponse, UpdateTeamRequest>({
      query: (data) => ({
        url: 'team',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Team'],
    }),
  }),
});

export const {
  useGetTeamQuery,
  useInviteMemberMutation,
  useRemoveMemberMutation,
  useDeleteUserMutation,
  useCancelInvitationMutation,
  useUpdateTeamMutation,
} = teamApi;
