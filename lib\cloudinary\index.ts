import { v2 as cloudinary } from 'cloudinary';
import { cloudinaryConfig } from './config';

// Configure Cloudinary
cloudinary.config({
  cloud_name: cloudinaryConfig.cloudName,
  api_key: cloudinaryConfig.apiKey,
  api_secret: cloudinaryConfig.apiSecret,
});

// Upload a buffer to Cloudinary
export const uploadToCloudinary = async (
  buffer: Buffer,
  options: {
    filename: string;
    folder: string;
    resourceType: 'image' | 'video' | 'raw' | 'auto';
  }
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const uploadStream = cloudinary.uploader.upload_stream(
      {
        folder: options.folder,
        public_id: options.filename.split('.')[0],
        resource_type: options.resourceType,
      },
      (error, result) => {
        if (error) {
          reject(error);
        } else {
          resolve(result?.secure_url || '');
        }
      }
    );

    uploadStream.end(buffer);
  });
};
