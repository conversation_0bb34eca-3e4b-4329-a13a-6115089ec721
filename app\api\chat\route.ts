import { NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { db } from '@/lib/db/drizzle';
import { caseStudies } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// Store chat sessions in memory (in a production app, this would be in a database)
const chatSessions = new Map<string, string>();

export async function POST(req: Request) {
  try {
    // Check authentication
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Parse the request body
    const body = await req.json();
    const { message, caseStudyId, caseStudyTitle, history, sessionId } = body;

    if (!message) {
      return new NextResponse('Message is required', { status: 400 });
    }

    // Validate case study ID
    if (!caseStudyId) {
      return new NextResponse(
        JSON.stringify({ error: 'Case study ID is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    try {
      // Check if we have a session ID for this case study and user
      const sessionKey = `${caseStudyId}-${session.user.id}`;
      let chatSessionId = sessionId || chatSessions.get(sessionKey);

      // If no session ID exists, initialize a new chat session
      if (!chatSessionId) {
        console.log(
          'Initializing new chat session for case study:',
          caseStudyId
        );

        // Fetch the case study data
        const caseStudyData = await db.query.caseStudies.findFirst({
          where: eq(caseStudies.id, parseInt(caseStudyId.toString())),
        });

        if (!caseStudyData) {
          return new NextResponse(
            JSON.stringify({ error: 'Case study not found' }),
            { status: 404, headers: { 'Content-Type': 'application/json' } }
          );
        }

        // Prepare the case study data for the API
        const caseStudyJson = JSON.stringify(caseStudyData);

        console.log('Case study data for chat initialization:');
        console.log('Case study ID:', caseStudyId);
        console.log('Case study title:', caseStudyData.useCaseTitle);

        // Initialize chat session
        const initRequestBody = {
          user_id: session.user.id.toString(), // Convert to string as required by API
          data_source: caseStudyJson, // Pass the full case study data as JSON string
        };

        console.log('Chat init request body:', initRequestBody);

        const initResponse = await fetch(
          'https://miuagent-api.leafcraftstudios.com/chat/init',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(initRequestBody),
            signal: AbortSignal.timeout(10000), // 10 second timeout
          }
        );

        if (!initResponse.ok) {
          const errorText = await initResponse.text();
          console.error('Error initializing chat session:', {
            status: initResponse.status,
            statusText: initResponse.statusText,
            body: errorText,
            requestBody: initRequestBody,
          });

          // Try to parse the error response
          let errorDetails = errorText;
          try {
            const errorJson = JSON.parse(errorText);
            errorDetails = errorJson.error || errorText;

            // If the error is about missing fields, provide more specific guidance
            if (errorDetails.includes('Missing required fields')) {
              console.error(
                'Missing required fields for chat initialization. Please check API documentation.'
              );
            }
          } catch (e) {
            // If parsing fails, use the raw text
          }

          return new NextResponse(
            JSON.stringify({
              error: 'Failed to initialize chat session',
              details: errorDetails,
              requestSent: initRequestBody,
            }),
            {
              status: initResponse.status,
              headers: { 'Content-Type': 'application/json' },
            }
          );
        }

        const initData = await initResponse.json();
        console.log('Chat initialization response:', initData);

        // The API might return the session ID in different formats
        // Let's check all possible locations
        chatSessionId =
          initData.session_id ||
          initData.sessionId ||
          initData.data?.session_id ||
          initData.message?.session_id ||
          initData.request_id; // Sometimes the request_id is used as session ID

        if (!chatSessionId) {
          console.error(
            'Failed to extract session ID from response:',
            initData
          );

          // If we can't find a session ID, we'll use a combination of case study ID and user ID
          // This is a fallback approach
          chatSessionId = `${caseStudyId}-${session.user.id}-${Date.now()}`;
          console.log('Using fallback session ID:', chatSessionId);
        }

        // Store the session ID for future use
        chatSessions.set(sessionKey, chatSessionId);
        console.log('Chat session initialized with ID:', chatSessionId);
      }

      // Format the conversation history for the API
      let formattedHistory = '';
      if (history && history.length > 0) {
        formattedHistory = history
          .map(
            (msg: any) =>
              `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`
          )
          .join('\n\n');
      }

      // Now send the message using the case study ID, user ID, and session ID if available
      const requestBody = {
        id: caseStudyId.toString(), // Case study ID as string
        user_id: session.user.id.toString(), // User ID as string
        message, // The user's message
        session_id: chatSessionId, // Include the session ID

        // Add more context to help the AI understand the question better
        context: `This is a question about case study #${caseStudyId}: "${caseStudyTitle}".
                 The user is asking: "${message}".
                 Please provide a detailed and specific answer based on the case study data.

                 Previous conversation:
                 ${formattedHistory}`,
      };

      console.log('Sending message to MIUAgent API:', {
        ...requestBody,
        caseStudyId,
        sessionKey,
      });

      const apiResponse = await fetch(
        'https://miuagent-api.leafcraftstudios.com/chat',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
          signal: AbortSignal.timeout(15000), // 15 second timeout
        }
      );

      if (!apiResponse.ok) {
        const errorText = await apiResponse.text();
        console.error('Error from MIUAgent API:', {
          status: apiResponse.status,
          statusText: apiResponse.statusText,
          body: errorText,
          requestBody,
          sessionId: chatSessionId,
        });

        // Try to parse the error response
        let errorDetails = errorText;
        try {
          const errorJson = JSON.parse(errorText);
          errorDetails = errorJson.error || errorText;
        } catch (e) {
          // If parsing fails, use the raw text
        }

        // If session not found, remove it from our cache and try again
        if (
          apiResponse.status === 404 &&
          errorText.includes('Chat session not found')
        ) {
          console.log(
            'Chat session not found, removing from cache:',
            sessionKey
          );
          chatSessions.delete(sessionKey);
          return new NextResponse(
            JSON.stringify({
              error: 'Session expired, please try again',
              retry: true,
            }),
            {
              status: 404,
              headers: { 'Content-Type': 'application/json' },
            }
          );
        }

        return new NextResponse(
          JSON.stringify({
            error: 'Failed to get response from AI service',
            details: errorDetails,
            requestSent: requestBody,
          }),
          {
            status: apiResponse.status,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }

      // Get the raw response text first
      const responseText = await apiResponse.text();

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        console.error('Error parsing API response as JSON:', e);
        // If parsing fails, use the raw text as the response
        return NextResponse.json({
          response: responseText,
          success: true,
        });
      }

      // Store the session ID in our map for future use
      if (data.session_id) {
        const sessionKey = `${caseStudyId}-${session.user.id}`;
        chatSessions.set(sessionKey, data.session_id);
      }

      // Ensure we're returning the complete response
      // Add additional metadata to help the client process the response
      const enhancedResponse = {
        ...data,
        success: true,
        timestamp: new Date().toISOString(),
        // Include the raw response text as a fallback
        rawResponse:
          responseText.length > 50000
            ? 'Response too large to include in raw form'
            : responseText,
        // Ensure session_id is included if available
        session_id: data.session_id || chatSessionId,
      };

      return NextResponse.json(enhancedResponse);
    } catch (fetchError) {
      console.error('Fetch error when calling MIUAgent API:', fetchError);
      return new NextResponse(
        JSON.stringify({
          error: 'Error connecting to AI service',
          message:
            fetchError instanceof Error ? fetchError.message : 'Unknown error',
        }),
        {
          status: 503, // Service Unavailable
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
  } catch (error) {
    console.error('Error in chat API route:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
