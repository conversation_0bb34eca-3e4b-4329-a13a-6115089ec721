'use client';

import Image, { ImageProps, StaticImageData } from 'next/image';
import type { StaticImport } from 'next/dist/shared/lib/get-img-props';
import { useState, useEffect } from 'react';

interface SafeImageProps extends Omit<ImageProps, 'onError'> {
  fallbackSrc?: string;
}

/**
 * A wrapper around Next.js Image component that handles errors gracefully
 * and falls back to a placeholder image if the source image fails to load.
 */
export function SafeImage({
  src,
  alt,
  fallbackSrc = '/placeholder-image.jpg',
  ...props
}: SafeImageProps) {
  // Check if src is a valid URL or path
  const isValidSrc = (url: string | StaticImport) => {
    if (typeof url !== 'string') return true; // StaticImport is valid
    if (!url) return false;
    if (url.startsWith('/')) return true; // Local path
    if (url.startsWith('data:')) return true; // Data URL
    if (url.startsWith('blob:')) return true; // Blob URL

    // Special handling for Cloudinary URLs
    if (
      typeof url === 'string' &&
      (url.includes('res.cloudinary.com') ||
        url.includes('cloudinary.com') ||
        url.includes('cloudinary.com%2F'))
    ) {
      // URL encoded version
      // Ensure Cloudinary URLs use https
      if (url.startsWith('http://')) {
        url = url.replace('http://', 'https://');
      }
      return true;
    }

    // Check if it's a valid URL
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  // Use fallback immediately if src is invalid
  const initialSrc = isValidSrc(src) ? src : fallbackSrc;
  const [imgSrc, setImgSrc] = useState(initialSrc);
  const [error, setError] = useState(!isValidSrc(src));
  const [loading, setLoading] = useState(true);

  // Add timeout to detect slow-loading images
  useEffect(() => {
    if (!error && imgSrc !== fallbackSrc) {
      const timeoutId = setTimeout(() => {
        setImgSrc(fallbackSrc);
        setError(true);
        setLoading(false);
      }, 800000); // 8 second timeout

      return () => clearTimeout(timeoutId); // Cleanup on unmount or src change
    }
  }, [imgSrc, fallbackSrc, error]);

  // Update imgSrc when src prop changes
  useEffect(() => {
    if (!error && src !== imgSrc) {
      if (isValidSrc(src)) {
        setImgSrc(src);
      } else {
        setImgSrc(fallbackSrc);
        setError(true);
      }
    }
  }, [src, error, fallbackSrc, imgSrc]);

  // Handle image load error
  const handleError = () => {
    if (!error) {
      // Try to fix common Cloudinary URL issues
      if (
        typeof imgSrc === 'string' &&
        (imgSrc.includes('cloudinary.com') ||
          imgSrc.includes('res.cloudinary.com'))
      ) {
        try {
          let fixedUrl = imgSrc;
          if (fixedUrl.startsWith('http://')) {
            fixedUrl = fixedUrl.replace('http://', 'https://');
          }
          if (fixedUrl.includes('/upload') && !fixedUrl.includes('/upload/')) {
            fixedUrl = fixedUrl.replace('/upload', '/upload/');
          }
          if (fixedUrl.includes(' ')) {
            fixedUrl = fixedUrl.replace(/ /g, '%20');
          }
          if (fixedUrl !== imgSrc) {
            setImgSrc(fixedUrl);
            return;
          }
        } catch (e) {
          // Error handling
        }
      }

      // Ensure the fallback path is absolute
      let absoluteFallbackPath = fallbackSrc;
      if (!absoluteFallbackPath.startsWith('/')) {
        absoluteFallbackPath = '/' + absoluteFallbackPath;
      }

      setImgSrc(absoluteFallbackPath);
      setError(true);
      setLoading(false);
    }
  };

  // Handle image load success
  const handleLoad = () => {
    setLoading(false);
  };

  if (
    typeof imgSrc === 'string' &&
    (imgSrc.startsWith('data:') || imgSrc.startsWith('blob:'))
  ) {
    return (
      <img
        src={imgSrc}
        alt={alt}
        onError={handleError}
        onLoad={handleLoad}
        {...(props as any)}
      />
    );
  }

  return (
    <Image
      {...props}
      src={imgSrc}
      alt={alt}
      onError={handleError}
      onLoad={handleLoad}
    />
  );
}
