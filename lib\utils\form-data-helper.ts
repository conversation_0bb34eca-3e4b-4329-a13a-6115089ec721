/**
 * Helper functions for working with FormData in a more type-safe way
 */

/**
 * Creates a FormData object for file upload
 * @param file The file to upload
 * @param type The type of file (e.g., 'header', 'icon')
 * @param additionalFields Optional additional fields to add to the FormData
 * @returns FormData object ready for upload
 */
export function createUploadFormData(
  file: File,
  type: string,
  additionalFields?: Record<string, string>
): FormData {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);
  
  // Add any additional fields
  if (additionalFields) {
    Object.entries(additionalFields).forEach(([key, value]) => {
      formData.append(key, value);
    });
  }
  
  return formData;
}

/**
 * Creates a FormData object for case study import
 * @param file The CSV file to import
 * @returns FormData object ready for import
 */
export function createImportFormData(file: File): FormData {
  const formData = new FormData();
  formData.append('file', file);
  return formData;
}
