# Stripe Pricing Setup Guide

This guide will help you set up pricing in any Stripe account (development, staging, or production).

## 📋 Current Pricing Structure

- **Starter Plan**: $12/month with 14-day free trial, 10 case studies limit
- **Pro Plan**: $60/month or $600/year, unlimited case studies  
- **Enterprise Plan**: Contact for pricing, unlimited case studies

## 🚀 Quick Setup for New Stripe Account

### Step 1: Set Environment Variables

Make sure you have your Stripe secret key in your environment:

```bash
# In your .env file
STRIPE_SECRET_KEY=sk_test_... # or sk_live_... for production
```

### Step 2: Run the Setup Script

```bash
node scripts/setup-stripe-pricing.js
```

This script will:
- ✅ Create all products and prices in Stripe
- ✅ Generate environment variables for your .env file
- ✅ Provide SQL commands to update your database
- ✅ Show a complete summary

### Step 3: Update Environment Variables

Copy the generated environment variables to your `.env` file:

```bash
# Stripe Product IDs
STRIPE_STARTER_PRODUCT_ID=prod_...
STRIPE_PRO_PRODUCT_ID=prod_...
STRIPE_ENTERPRISE_PRODUCT_ID=prod_...

# Stripe Price IDs
STRIPE_STARTER_PRICE_ID_MONTHLY=price_...
STRIPE_PRO_PRICE_ID_MONTHLY=price_...
STRIPE_PRO_PRICE_ID_YEARLY=price_...
```

### Step 4: Update Database

Option A - Use the automated script:
1. Update the `STRIPE_IDS` object in `scripts/update-database-with-new-stripe-ids.ts`
2. Run: `npx tsx scripts/update-database-with-new-stripe-ids.ts`

Option B - Run SQL manually:
Copy and run the SQL commands provided by the setup script.

### Step 5: Restart and Test

```bash
npm run dev
```

Visit `/pricing` to test the new pricing setup.

## 🔧 Manual Setup (Alternative)

If you prefer to set up manually:

### 1. Create Stripe Products

```bash
# Create individual products
node scripts/create-stripe-products.js
```

### 2. Update Pro Plan Pricing

```bash
# Create $60 Pro plan prices
node scripts/create-correct-pro-price.js
```

### 3. Update Database Schema

```bash
# Update database with new IDs
npm run db:update-plans-schema
```

## 📁 Available Scripts

| Script | Purpose |
|--------|---------|
| `setup-stripe-pricing.js` | **Complete setup** - Creates everything from scratch |
| `create-stripe-products.js` | Creates basic products with default pricing |
| `create-correct-pro-price.js` | Creates $60 Pro plan prices |
| `update-database-with-new-stripe-ids.ts` | Updates database with new Stripe IDs |
| `test-purchase-flow.js` | Tests the purchase API endpoints |

## 🎯 Production Deployment Checklist

### Before Deployment:

- [ ] Switch to live Stripe keys (`sk_live_...`)
- [ ] Run `setup-stripe-pricing.js` with live keys
- [ ] Update production environment variables
- [ ] Update production database with new Stripe IDs
- [ ] Test purchase flow in production

### Environment Variables for Production:

```bash
# Production Stripe Keys
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...

# Webhook (set up in Stripe Dashboard)
STRIPE_WEBHOOK_SECRET=whsec_...

# Generated Product/Price IDs
STRIPE_STARTER_PRODUCT_ID=prod_...
STRIPE_PRO_PRODUCT_ID=prod_...
STRIPE_ENTERPRISE_PRODUCT_ID=prod_...
STRIPE_STARTER_PRICE_ID_MONTHLY=price_...
STRIPE_PRO_PRICE_ID_MONTHLY=price_...
STRIPE_PRO_PRICE_ID_YEARLY=price_...
```

## 🔍 Testing

### Test Purchase Flow:

```bash
# Test API endpoints
node scripts/test-purchase-flow.js

# Manual testing:
# 1. Visit /pricing
# 2. Try purchasing as unauthenticated user
# 3. Sign in and try purchasing
# 4. Test with existing subscription
```

### Verify Database:

```sql
SELECT name, price_monthly, price_yearly, stripe_product_id, stripe_price_id_monthly 
FROM plans 
WHERE name IN ('starter', 'pro', 'enterprise');
```

## 🆘 Troubleshooting

### "Price is inactive" Error:
- Run `setup-stripe-pricing.js` to create new active prices
- Update database with new price IDs

### "Product not found" Error:
- Verify Stripe product IDs in environment variables
- Check if products exist in your Stripe dashboard

### Database Connection Error:
- Verify `POSTGRES_URL` environment variable
- Check database connection and permissions

## 📞 Support

If you encounter issues:
1. Check the Stripe Dashboard for created products/prices
2. Verify environment variables are set correctly
3. Test with the provided test scripts
4. Check application logs for detailed error messages

---

**Note**: Always test in Stripe's test mode before deploying to production!
