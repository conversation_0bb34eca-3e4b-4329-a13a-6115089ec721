/**
 * Stripe Plan Cleanup and Recreation Script
 * This script will:
 * 1. List all existing products and prices in Stripe
 * 2. Archive old products and prices
 * 3. Create fresh plans matching our pricing structure
 */

import <PERSON><PERSON> from 'stripe';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// Define our CORRECT plan structure based on actual pricing
const PLAN_STRUCTURE = {
  starter: {
    name: 'Starter Plan',
    description: 'Perfect for individuals getting started with AI use cases - 14 day free trial',
    features: [
      '14-day free trial',
      'Basic AI use case access',
      'Limited case studies',
      'Community support'
    ],
    pricing: {
      trial: { amount: 0, interval: 'month', trial_period_days: 14 } // Free trial for 14 days
    }
  },
  pro: {
    name: 'Pro Plan',
    description: 'For teams and businesses ready to scale with AI',
    features: [
      'Full access to all features',
      'Unlimited case studies',
      'Advanced analytics',
      'Priority support',
      'Team collaboration'
    ],
    pricing: {
      monthly: { amount: 2900, interval: 'month' }, // $29.00 - UPDATE THIS WITH YOUR ACTUAL PRICE
      yearly: { amount: 29000, interval: 'year' }   // $290.00 - UPDATE THIS WITH YOUR ACTUAL PRICE
    }
  }
  // Note: Enterprise is "Contact Us" only - no Stripe product needed
};

interface StripeAuditResult {
  products: Stripe.Product[];
  prices: Stripe.Price[];
  activeSubscriptions: Stripe.Subscription[];
}

/**
 * Audit existing Stripe data
 */
async function auditStripeData(): Promise<StripeAuditResult> {
  console.log('🔍 Auditing existing Stripe data...\n');

  // Get all products
  const products = await stripe.products.list({ limit: 100 });
  console.log(`Found ${products.data.length} products:`);
  products.data.forEach(product => {
    console.log(`  - ${product.name} (${product.id}) - ${product.active ? 'Active' : 'Inactive'}`);
  });

  // Get all prices
  const prices = await stripe.prices.list({ limit: 100 });
  console.log(`\nFound ${prices.data.length} prices:`);
  prices.data.forEach(price => {
    const amount = price.unit_amount ? `$${(price.unit_amount / 100).toFixed(2)}` : 'Free';
    console.log(`  - ${amount}/${price.recurring?.interval || 'one-time'} (${price.id}) - ${price.active ? 'Active' : 'Inactive'}`);
  });

  // Get active subscriptions
  const subscriptions = await stripe.subscriptions.list({
    status: 'active',
    limit: 100
  });
  console.log(`\nFound ${subscriptions.data.length} active subscriptions:`);
  subscriptions.data.forEach(sub => {
    console.log(`  - ${sub.id} - Customer: ${sub.customer}`);
  });

  return {
    products: products.data,
    prices: prices.data,
    activeSubscriptions: subscriptions.data
  };
}

/**
 * Archive old products and prices
 */
async function archiveOldPlans(auditResult: StripeAuditResult): Promise<void> {
  console.log('\n🗑️  Archiving old products and prices...\n');

  // Check for active subscriptions
  if (auditResult.activeSubscriptions.length > 0) {
    console.log('⚠️  WARNING: Found active subscriptions!');
    console.log('   You should migrate these subscriptions before archiving products.');
    console.log('   Active subscriptions:');
    auditResult.activeSubscriptions.forEach(sub => {
      console.log(`   - ${sub.id} (Customer: ${sub.customer})`);
    });
    console.log('\n   Proceeding with archival anyway...\n');
  }

  // Archive all active prices first
  for (const price of auditResult.prices) {
    if (price.active) {
      try {
        await stripe.prices.update(price.id, { active: false });
        console.log(`✅ Archived price: ${price.id}`);
      } catch (error: any) {
        console.log(`❌ Failed to archive price ${price.id}: ${error.message}`);
      }
    }
  }

  // Archive all active products
  for (const product of auditResult.products) {
    if (product.active) {
      try {
        await stripe.products.update(product.id, { active: false });
        console.log(`✅ Archived product: ${product.name} (${product.id})`);
      } catch (error: any) {
        console.log(`❌ Failed to archive product ${product.id}: ${error.message}`);
      }
    }
  }
}

/**
 * Create new plans according to our structure
 */
async function createNewPlans(): Promise<{ [key: string]: { productId: string; prices: { [key: string]: string } } }> {
  console.log('\n🆕 Creating new plans...\n');

  const createdPlans: { [key: string]: { productId: string; prices: { [key: string]: string } } } = {};

  for (const [planKey, planData] of Object.entries(PLAN_STRUCTURE)) {
    console.log(`Creating ${planData.name}...`);

    try {
      // Create product
      const product = await stripe.products.create({
        name: planData.name,
        description: planData.description,
        metadata: {
          plan_type: planKey,
          features: JSON.stringify(planData.features),
          created_by: 'morphx-cleanup-script',
          created_at: new Date().toISOString()
        }
      });

      console.log(`✅ Created product: ${product.name} (${product.id})`);

      createdPlans[planKey] = {
        productId: product.id,
        prices: {}
      };

      // Create prices for each billing interval
      for (const [interval, pricingData] of Object.entries(planData.pricing)) {
        try {
          const priceParams: Stripe.PriceCreateParams = {
            product: product.id,
            currency: 'usd',
            metadata: {
              plan_type: planKey,
              billing_interval: interval,
              created_by: 'morphx-cleanup-script'
            }
          };

          if (pricingData.amount === 0) {
            // Free plan or trial - create a recurring price of $0
            priceParams.unit_amount = 0;
            priceParams.recurring = {
              interval: pricingData.interval as 'month' | 'year',
              ...(pricingData.trial_period_days && { trial_period_days: pricingData.trial_period_days })
            };
          } else {
            // Paid plan - create recurring price
            priceParams.unit_amount = pricingData.amount;
            priceParams.recurring = {
              interval: pricingData.interval as 'month' | 'year'
            };
          }

          const price = await stripe.prices.create(priceParams);

          const displayAmount = pricingData.amount === 0 ? 'Free' : `$${(pricingData.amount / 100).toFixed(2)}`;
          const trialInfo = pricingData.trial_period_days ? ` (${pricingData.trial_period_days}-day trial)` : '';
          console.log(`  ✅ Created ${interval} price: ${displayAmount}${trialInfo} (${price.id})`);

          createdPlans[planKey].prices[interval] = price.id;

        } catch (error: any) {
          console.log(`  ❌ Failed to create ${interval} price for ${planKey}: ${error.message}`);
        }
      }

    } catch (error: any) {
      console.log(`❌ Failed to create product for ${planKey}: ${error.message}`);
    }

    console.log(''); // Empty line for readability
  }

  return createdPlans;
}

/**
 * Generate environment variables for the new plans
 */
function generateEnvVariables(createdPlans: { [key: string]: { productId: string; prices: { [key: string]: string } } }): void {
  console.log('\n📝 Environment Variables for .env file:\n');
  console.log('# Stripe Product IDs');

  for (const [planKey, planData] of Object.entries(createdPlans)) {
    const envKey = `STRIPE_${planKey.toUpperCase()}_PRODUCT_ID`;
    console.log(`${envKey}=${planData.productId}`);
  }

  console.log('\n# Stripe Price IDs');
  for (const [planKey, planData] of Object.entries(createdPlans)) {
    for (const [interval, priceId] of Object.entries(planData.prices)) {
      const envKey = `STRIPE_${planKey.toUpperCase()}_${interval.toUpperCase()}_PRICE_ID`;
      console.log(`${envKey}=${priceId}`);
    }
  }

  console.log('\n📋 Copy these variables to your .env file and restart your application.');
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 Starting Stripe Plan Cleanup and Recreation\n');
    console.log('This script will:');
    console.log('1. Audit existing Stripe products and prices');
    console.log('2. Archive old products and prices');
    console.log('3. Create fresh plans matching your pricing structure');
    console.log('4. Generate environment variables\n');

    // Confirm before proceeding
    console.log('⚠️  WARNING: This will archive ALL existing products and prices in Stripe!');
    console.log('   Make sure you have backed up any important data.\n');

    // In a real script, you'd want to add a confirmation prompt here
    // For now, we'll proceed automatically

    // Step 1: Audit existing data
    const auditResult = await auditStripeData();

    // Step 2: Archive old plans
    await archiveOldPlans(auditResult);

    // Step 3: Create new plans
    const createdPlans = await createNewPlans();

    // Step 4: Generate environment variables
    generateEnvVariables(createdPlans);

    console.log('\n✅ Stripe plan cleanup and recreation completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Update your .env file with the new environment variables');
    console.log('2. Restart your application');
    console.log('3. Test the new plans in your application');
    console.log('4. If you have active subscriptions, migrate them to the new plans');

  } catch (error: any) {
    console.error('\n❌ Error during plan cleanup:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

export { main as cleanupStripePlans, auditStripeData, archiveOldPlans, createNewPlans };
