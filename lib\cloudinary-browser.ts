'use client';

/**
 * Cloudinary utilities for browser environments
 * This file contains only browser-compatible functions
 */

/**
 * Checks if a URL is a Cloudinary URL
 */
export function isCloudinaryUrl(url: string): boolean {
  if (!url) return false;

  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'daf8hefrg';
  return url.includes(`res.cloudinary.com/${cloudName}/`);
}

/**
 * Gets the public ID from a Cloudinary URL
 */
export function getPublicIdFromUrl(url: string): string | null {
  if (!isCloudinaryUrl(url)) return null;

  // Extract the public ID from the URL
  // Format: https://res.cloudinary.com/cloud-name/image/upload/v1234567890/folder/public-id.ext
  const regex = /\/v\d+\/(.+)$/;
  const match = url.match(regex);

  if (match && match[1]) {
    // Remove file extension
    return match[1].replace(/\.[^/.]+$/, '');
  }

  return null;
}

/**
 * Generates a Cloudinary URL with transformations
 * @param url The original Cloudinary URL
 * @param transformations The transformations to apply
 */
export function generateTransformedUrl(url: string, transformations: string[]): string {
  if (!isCloudinaryUrl(url)) return url;

  // Find the upload part in the URL
  const uploadIndex = url.indexOf('/upload/');
  if (uploadIndex === -1) return url;

  // Insert transformations after /upload/
  const transformationString = transformations.join(',');
  const transformedUrl = url.slice(0, uploadIndex + 8) +
                         transformationString + '/' +
                         url.slice(uploadIndex + 8);

  return transformedUrl;
}

/**
 * Generates a responsive image URL for different screen sizes
 * @param url The original Cloudinary URL
 * @param width The desired width
 */
export function getResponsiveImageUrl(url: string, width: number): string {
  return generateTransformedUrl(url, [`w_${width}`]);
}

/**
 * Direct upload to Cloudinary using unsigned upload preset
 * This function works in the browser
 */
export async function uploadToCloudinaryBrowser(
  file: File,
  options: {
    folder?: string;
    publicId?: string;
    resourceType?: 'image' | 'video' | 'raw' | 'auto';
    uploadPreset?: string;
  } = {}
): Promise<string> {
  try {
    // Create a FormData object
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', options.uploadPreset || 'Path4ai');
    formData.append('folder', options.folder || 'case-studies');

    if (options.publicId) {
      formData.append('public_id', options.publicId);
    }

    // Upload directly to Cloudinary
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'daf8hefrg';
    const resourceType = options.resourceType || 'image';
    const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName}/${resourceType}/upload`;

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Upload failed: ${errorText}`);
    }

    const result = await response.json();
    return result.secure_url;
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
    throw error;
  }
}
