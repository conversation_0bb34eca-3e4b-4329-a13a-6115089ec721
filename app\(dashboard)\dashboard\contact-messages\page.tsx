'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MessageSquare, 
  Eye, 
  Mail, 
  CheckCircle, 
  Clock, 
  Search,
  RefreshCw
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format } from 'date-fns';

type ContactMessage = {
  id: number;
  name: string;
  email: string;
  inquiryType: string;
  subject: string;
  status: string;
  createdAt: string;
};

export default function ContactMessagesPage() {
  const router = useRouter();
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  const [filteredMessages, setFilteredMessages] = useState<ContactMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('all');

  // Fetch contact messages
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/contact-messages');
        if (!response.ok) {
          throw new Error('Failed to fetch contact messages');
        }
        const data = await response.json();
        setMessages(data.messages);
        setFilteredMessages(data.messages);
      } catch (error) {
        console.error('Error fetching contact messages:', error);
        // Use sample data for now
        const sampleData = generateSampleData();
        setMessages(sampleData);
        setFilteredMessages(sampleData);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMessages();
  }, []);

  // Filter messages based on search query, status, and type
  useEffect(() => {
    let filtered = [...messages];

    // Filter by tab (status group)
    if (activeTab === 'unread') {
      filtered = filtered.filter(message => message.status === 'unread');
    } else if (activeTab === 'read') {
      filtered = filtered.filter(message => message.status === 'read');
    } else if (activeTab === 'replied') {
      filtered = filtered.filter(message => message.status === 'replied');
    }

    // Filter by status if not 'all'
    if (statusFilter !== 'all') {
      filtered = filtered.filter(message => message.status === statusFilter);
    }

    // Filter by inquiry type if not 'all'
    if (typeFilter !== 'all') {
      filtered = filtered.filter(message => message.inquiryType === typeFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        message =>
          message.name.toLowerCase().includes(query) ||
          message.email.toLowerCase().includes(query) ||
          message.subject.toLowerCase().includes(query)
      );
    }

    setFilteredMessages(filtered);
  }, [messages, searchQuery, statusFilter, typeFilter, activeTab]);

  // Generate sample data for development
  const generateSampleData = (): ContactMessage[] => {
    const inquiryTypes = ['general', 'sales', 'support', 'partnership'];
    const statuses = ['unread', 'read', 'replied'];
    const subjects = [
      'Question about pricing',
      'Technical support needed',
      'Partnership opportunity',
      'Feature request',
      'Account issue',
      'Billing question',
      'General inquiry',
      'Demo request',
    ];

    return Array.from({ length: 15 }, (_, i) => ({
      id: i + 1,
      name: `User ${i + 1}`,
      email: `user${i + 1}@example.com`,
      inquiryType: inquiryTypes[Math.floor(Math.random() * inquiryTypes.length)],
      subject: subjects[Math.floor(Math.random() * subjects.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      createdAt: new Date(
        Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000
      ).toISOString(),
    }));
  };

  // Handle view message
  const handleViewMessage = (id: number) => {
    router.push(`/dashboard/contact-messages/${id}`);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'unread':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">
            <Clock className="mr-1 h-3 w-3" />
            Unread
          </Badge>
        );
      case 'read':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">
            <Eye className="mr-1 h-3 w-3" />
            Read
          </Badge>
        );
      case 'replied':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Replied
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200">
            {status}
          </Badge>
        );
    }
  };

  // Get inquiry type badge
  const getInquiryTypeBadge = (type: string) => {
    switch (type) {
      case 'general':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200">
            General
          </Badge>
        );
      case 'sales':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200">
            Sales
          </Badge>
        );
      case 'support':
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200">
            Support
          </Badge>
        );
      case 'partnership':
        return (
          <Badge variant="outline" className="bg-indigo-50 text-indigo-600 border-indigo-200">
            Partnership
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200">
            {type}
          </Badge>
        );
    }
  };

  // Count messages by status
  const countByStatus = (status: string) => {
    if (status === 'all') {
      return messages.length;
    }
    return messages.filter(message => message.status === status).length;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Contact Messages</h1>
        <Button 
          variant="outline" 
          className="flex items-center gap-2"
          onClick={() => {
            setIsLoading(true);
            setTimeout(() => {
              setIsLoading(false);
            }, 500);
          }}
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      <Tabs 
        defaultValue="all" 
        className="space-y-4"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="all" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              All
              <Badge variant="secondary" className="ml-1">
                {countByStatus('all')}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="unread" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Unread
              <Badge variant="secondary" className="ml-1">
                {countByStatus('unread')}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="read" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Read
              <Badge variant="secondary" className="ml-1">
                {countByStatus('read')}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="replied" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Replied
              <Badge variant="secondary" className="ml-1">
                {countByStatus('replied')}
              </Badge>
            </TabsTrigger>
          </TabsList>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Contact Messages</CardTitle>
            <CardDescription>
              View and manage messages from the contact form.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name, email, or subject..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex gap-4">
                <Select
                  value={statusFilter}
                  onValueChange={setStatusFilter}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="unread">Unread</SelectItem>
                    <SelectItem value="read">Read</SelectItem>
                    <SelectItem value="replied">Replied</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={typeFilter}
                  onValueChange={setTypeFilter}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="sales">Sales</SelectItem>
                    <SelectItem value="support">Support</SelectItem>
                    <SelectItem value="partnership">Partnership</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center">
                          <RefreshCw className="h-8 w-8 animate-spin text-gray-400 mb-2" />
                          <p className="text-gray-500">Loading messages...</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredMessages.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center">
                          <MessageSquare className="h-8 w-8 text-gray-400 mb-2" />
                          <p className="text-gray-500">No messages found</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredMessages.map((message) => (
                      <TableRow key={message.id}>
                        <TableCell className="font-medium">{message.name}</TableCell>
                        <TableCell>{message.email}</TableCell>
                        <TableCell className="max-w-[200px] truncate">
                          {message.subject}
                        </TableCell>
                        <TableCell>{getInquiryTypeBadge(message.inquiryType)}</TableCell>
                        <TableCell>{getStatusBadge(message.status)}</TableCell>
                        <TableCell>
                          {format(new Date(message.createdAt), 'MMM d, yyyy')}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewMessage(message.id)}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </Tabs>
    </div>
  );
}
