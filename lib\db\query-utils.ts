import { LRUCache } from 'lru-cache';
import { db } from './drizzle';
import { eq, and, SQL } from 'drizzle-orm';
import { users, teams, teamMembers } from './schema';

// Set up LRU cache for query results
// This reduces database load for frequently accessed data
const queryCache = new LRUCache<string, any>({
  // Maximum size of cache - adjust based on memory constraints
  max: 100,
  // TTL in milliseconds (10 minutes)
  ttl: 1000 * 60 * 10,
});

/**
 * Generates a cache key from query parameters
 * @param namespace - Namespace for the query (usually table name)
 * @param operation - Operation type (e.g., 'findById', 'getAll')
 * @param params - Query parameters
 * @returns Cache key string
 */
export function generateCacheKey(namespace: string, operation: string, params: any = {}): string {
  return `${namespace}:${operation}:${JSON.stringify(params)}`;
}

/**
 * Executes a database query with caching
 * @param queryFn - Function that executes the actual database query
 * @param cacheKey - Cache key to use for storing the result
 * @param ttl - Optional TTL override in milliseconds
 * @returns Query result
 */
export async function cachedQuery<T>(
  queryFn: () => Promise<T>,
  cacheKey: string,
  ttl?: number
): Promise<T> {
  // Check if we have a cached result
  const cachedResult = queryCache.get(cacheKey) as T | undefined;
  
  if (cachedResult !== undefined) {
    return cachedResult;
  }
  
  // Execute the query
  const result = await queryFn();
  
  // Cache the result
  if (result !== null && result !== undefined) {
    queryCache.set(cacheKey, result, { ttl });
  }
  
  return result;
}

/**
 * Invalidates cache entries containing the specified key pattern
 * @param keyPattern - Pattern to match against cache keys
 */
export function invalidateCache(keyPattern: string): void {
  const keysToInvalidate: string[] = [];
  
  // Find all keys that match the pattern
  queryCache.forEach((_, key) => {
    if (key.includes(keyPattern)) {
      keysToInvalidate.push(key);
    }
  });
  
  // Delete each matched key
  keysToInvalidate.forEach(key => {
    queryCache.delete(key);
  });
}

/**
 * Find a user by ID with caching
 * @param userId - User ID
 * @returns User object or null
 */
export async function findUserById(userId: number) {
  const cacheKey = generateCacheKey('users', 'findById', { id: userId });
  
  return cachedQuery(
    async () => {
      const result = await db
        .select()
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);
      
      return result.length > 0 ? result[0] : null;
    },
    cacheKey
  );
}

/**
 * Find a team by ID with caching
 * @param teamId - Team ID
 * @returns Team object or null
 */
export async function findTeamById(teamId: number) {
  const cacheKey = generateCacheKey('teams', 'findById', { id: teamId });
  
  return cachedQuery(
    async () => {
      const result = await db
        .select()
        .from(teams)
        .where(eq(teams.id, teamId))
        .limit(1);
      
      return result.length > 0 ? result[0] : null;
    },
    cacheKey
  );
}

/**
 * Get team members for a team with caching
 * @param teamId - Team ID
 * @returns Array of team members
 */
export async function getTeamMembers(teamId: number) {
  const cacheKey = generateCacheKey('teamMembers', 'getByTeamId', { teamId });
  
  return cachedQuery(
    async () => {
      return await db.query.teamMembers.findMany({
        where: eq(teamMembers.teamId, teamId),
        with: {
          user: {
            columns: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
      });
    },
    cacheKey
  );
}

/**
 * Execute a batch of queries in a transaction for better performance
 * @param queries - Array of query functions to execute
 * @returns Array of query results
 */
export async function batchQueries<T>(queries: (() => Promise<T>)[]): Promise<T[]> {
  // Execute queries in parallel for better performance
  return Promise.all(queries.map(queryFn => queryFn()));
}

/**
 * Helper to perform a query with retry logic
 * @param queryFn - Query function to execute
 * @param options - Retry options
 * @returns Query result
 */
export async function queryWithRetry<T>(
  queryFn: () => Promise<T>,
  options: {
    retries?: number;
    retryDelay?: number;
    exponentialBackoff?: boolean;
  } = {}
): Promise<T> {
  const {
    retries = 3,
    retryDelay = 300,
    exponentialBackoff = true,
  } = options;
  
  let lastError: any;
  
  for (let attempt = 0; attempt < retries + 1; attempt++) {
    try {
      return await queryFn();
    } catch (error) {
      lastError = error;
      
      // Don't wait after the last attempt
      if (attempt < retries) {
        const delay = exponentialBackoff
          ? retryDelay * Math.pow(2, attempt)
          : retryDelay;
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  // If we got here, all attempts failed
  throw lastError;
}

/**
 * Utility function to run an expensive query with proper monitoring
 * Logs performance metrics and adds appropriate headers to identify slow queries
 * @param queryFn - Query function to execute
 * @param queryName - Name of the query for logging
 * @returns Query result
 */
export async function monitoredQuery<T>(
  queryFn: () => Promise<T>,
  queryName: string
): Promise<T> {
  const startTime = performance.now();
  
  try {
    // Execute the query
    const result = await queryFn();
    
    // Calculate execution time
    const executionTime = performance.now() - startTime;
    
    // Log slow queries (over 100ms)
    if (executionTime > 100) {
      console.warn(`Slow query detected: ${queryName} took ${executionTime.toFixed(2)}ms`);
      // Here you could send metrics to a monitoring service
    }
    
    return result;
  } catch (error) {
    // Calculate execution time even for failed queries
    const executionTime = performance.now() - startTime;
    
    // Log error with execution time
    console.error(`Query error in ${queryName} after ${executionTime.toFixed(2)}ms:`, error);
    
    // Re-throw the error to be handled by the caller
    throw error;
  }
}

/**
 * Clear the entire query cache
 * Useful when deploying new versions or after schema changes
 */
export function clearQueryCache(): void {
  queryCache.clear();
}

/**
 * Get query cache statistics
 * Useful for monitoring cache performance
 */
export function getQueryCacheStats() {
  return {
    size: queryCache.size,
    maxSize: queryCache.max,
    itemCount: queryCache.size,
  };
}
