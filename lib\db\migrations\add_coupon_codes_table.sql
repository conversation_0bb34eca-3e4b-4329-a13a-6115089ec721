-- Add coupon codes table
CREATE TABLE IF NOT EXISTS "coupon_codes" (
  "id" SERIAL PRIMARY KEY,
  "code" VARCHAR(50) NOT NULL UNIQUE,
  "discount_type" VARCHAR(20) NOT NULL, -- 'percentage' or 'fixed'
  "discount_amount" NUMERIC NOT NULL, -- percentage or fixed amount
  "description" TEXT,
  "max_uses" INTEGER, -- NULL means unlimited
  "current_uses" INTEGER NOT NULL DEFAULT 0,
  "valid_from" TIMESTAMP NOT NULL DEFAULT NOW(),
  "valid_until" TIMESTAMP, -- NULL means no expiration
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "created_by" INTEGER REFERENCES "users"("id"),
  "is_active" BOOLEAN NOT NULL DEFAULT TRUE
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS "coupon_codes_code_idx" ON "coupon_codes"("code");
