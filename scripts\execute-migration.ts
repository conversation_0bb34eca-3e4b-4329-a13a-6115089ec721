import { db } from '../lib/db/drizzle';
import fs from 'fs';
import path from 'path';
import { sql } from 'drizzle-orm';

async function executeMigration() {
  try {
    console.log('Executing migration to add solution4 and solution5 fields...');
    
    // Execute the SQL directly
    await db.execute(sql`
      ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "solution_4_title" TEXT;
      ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "solution_4_description" TEXT;
      ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "solution_5_title" TEXT;
      ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "solution_5_description" TEXT;
    `);
    
    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error executing migration:', error);
    process.exit(1);
  }
}

executeMigration();
