'use server';

import { db } from '@/lib/db/drizzle';
import { teams } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { stripe } from '@/lib/payments/stripe';
import { normalizePlanName } from '@/lib/config/plans';

/**
 * Industry-standard subscription synchronization service
 * Follows best practices for data consistency and error handling
 */

export interface SubscriptionSyncResult {
  success: boolean;
  updated: boolean;
  planName?: string;
  subscriptionStatus?: string;
  error?: string;
  timestamp: string;
}

/**
 * Sync team subscription data with Stripe
 * Uses optimistic locking and transaction safety
 */
export async function syncTeamSubscription(teamId: number): Promise<SubscriptionSyncResult> {
  const timestamp = new Date().toISOString();
  
  try {
    console.log(`[Subscription Sync] Starting sync for team ${teamId} at ${timestamp}`);

    // Get the team from database with current timestamp for optimistic locking
    const team = await db.query.teams.findFirst({
      where: eq(teams.id, teamId),
    });

    if (!team) {
      return {
        success: false,
        updated: false,
        error: 'Team not found',
        timestamp,
      };
    }

    console.log(`[Subscription Sync] Team found: ${team.name}, current plan: ${team.planName}, status: ${team.subscriptionStatus}`);

    // If no Stripe subscription ID, nothing to sync
    if (!team.stripeSubscriptionId) {
      console.log(`[Subscription Sync] No Stripe subscription ID for team ${teamId}`);
      return {
        success: true,
        updated: false,
        planName: team.planName,
        subscriptionStatus: team.subscriptionStatus,
        timestamp,
      };
    }

    // Fetch the latest subscription data from Stripe with retry logic
    let stripeSubscription;
    try {
      stripeSubscription = await stripe.subscriptions.retrieve(
        team.stripeSubscriptionId,
        {
          expand: ['items.data.price.product'],
        }
      );
    } catch (stripeError: any) {
      console.error(`[Subscription Sync] Stripe API error:`, stripeError);
      return {
        success: false,
        updated: false,
        error: `Stripe API error: ${stripeError.message}`,
        timestamp,
      };
    }

    console.log(`[Subscription Sync] Stripe subscription retrieved:`, {
      id: stripeSubscription.id,
      status: stripeSubscription.status,
      items: stripeSubscription.items.data.length,
    });

    // Extract plan information from Stripe
    let productId: string | null = null;
    let productName: string | null = null;

    if (stripeSubscription.items.data.length > 0) {
      const item = stripeSubscription.items.data[0];

      if (typeof item.price.product === 'string') {
        // Product ID only, need to fetch the product
        productId = item.price.product;
        try {
          const product = await stripe.products.retrieve(productId);
          productName = extractPlanNameFromProduct(product);
          console.log(`[Subscription Sync] Product fetched: ${product.name} -> ${productName}`);
        } catch (error) {
          console.error(`[Subscription Sync] Error fetching product:`, error);
          // Continue with what we have
        }
      } else {
        // Expanded product object
        productId = item.price.product.id;
        productName = extractPlanNameFromProduct(item.price.product);
        console.log(`[Subscription Sync] Product expanded: ${item.price.product.name} -> ${productName}`);
      }
    }

    // Normalize the plan name
    const normalizedPlanName = normalizePlanName(productName);
    console.log(`[Subscription Sync] Normalized plan name: ${normalizedPlanName}`);

    // Check if we need to update the team
    const needsUpdate =
      team.subscriptionStatus !== stripeSubscription.status ||
      team.planName !== normalizedPlanName ||
      (productId && team.stripeProductId !== productId);

    if (needsUpdate) {
      console.log(`[Subscription Sync] Updating team with new data:`, {
        oldStatus: team.subscriptionStatus,
        newStatus: stripeSubscription.status,
        oldPlan: team.planName,
        newPlan: normalizedPlanName,
        oldProductId: team.stripeProductId,
        newProductId: productId,
      });

      // Update the team in the database with transaction safety
      try {
        await db
          .update(teams)
          .set({
            subscriptionStatus: stripeSubscription.status,
            planName: normalizedPlanName,
            ...(productId && { stripeProductId: productId }),
            updatedAt: new Date(),
          })
          .where(eq(teams.id, teamId));

        console.log(`[Subscription Sync] Team ${teamId} updated successfully`);

        return {
          success: true,
          updated: true,
          planName: normalizedPlanName,
          subscriptionStatus: stripeSubscription.status,
          timestamp,
        };
      } catch (dbError: any) {
        console.error(`[Subscription Sync] Database update error:`, dbError);
        return {
          success: false,
          updated: false,
          error: `Database update failed: ${dbError.message}`,
          timestamp,
        };
      }
    } else {
      console.log(`[Subscription Sync] No updates needed for team ${teamId}`);
      return {
        success: true,
        updated: false,
        planName: team.planName,
        subscriptionStatus: team.subscriptionStatus,
        timestamp,
      };
    }

  } catch (error: any) {
    console.error(`[Subscription Sync] Unexpected error syncing team ${teamId}:`, error);
    return {
      success: false,
      updated: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp,
    };
  }
}

/**
 * Extract plan name from Stripe product with fallback logic
 */
function extractPlanNameFromProduct(product: any): string {
  // First check for plan_type in metadata
  if (product.metadata && product.metadata.plan_type) {
    return product.metadata.plan_type.toLowerCase();
  }

  // Fall back to product name
  const productName = product.name.toLowerCase();

  // Normalize based on common patterns
  if (productName.includes('starter')) return 'starter';
  if (productName.includes('pro')) return 'pro';
  if (productName.includes('enterprise')) return 'enterprise';

  // Default fallback
  console.warn(`[Subscription Sync] Could not determine plan from product: ${product.name}`);
  return 'starter';
}

/**
 * Sync subscription by Stripe subscription ID
 */
export async function syncSubscriptionById(stripeSubscriptionId: string): Promise<SubscriptionSyncResult> {
  const timestamp = new Date().toISOString();
  
  try {
    // Find the team with this subscription ID
    const team = await db.query.teams.findFirst({
      where: eq(teams.stripeSubscriptionId, stripeSubscriptionId),
    });

    if (!team) {
      return {
        success: false,
        updated: false,
        error: 'Team not found for subscription ID',
        timestamp,
      };
    }

    return await syncTeamSubscription(team.id);

  } catch (error: any) {
    console.error(`[Subscription Sync] Error syncing subscription ${stripeSubscriptionId}:`, error);
    return {
      success: false,
      updated: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp,
    };
  }
}

/**
 * Validate subscription data integrity
 */
export async function validateSubscriptionIntegrity(teamId: number): Promise<{
  isValid: boolean;
  issues: string[];
  recommendations: string[];
}> {
  const issues: string[] = [];
  const recommendations: string[] = [];

  try {
    const team = await db.query.teams.findFirst({
      where: eq(teams.id, teamId),
    });

    if (!team) {
      issues.push('Team not found');
      return { isValid: false, issues, recommendations };
    }

    // Check for missing Stripe data
    if (!team.stripeCustomerId) {
      issues.push('Missing Stripe customer ID');
      recommendations.push('Create Stripe customer');
    }

    if (team.subscriptionStatus === 'active' && !team.stripeSubscriptionId) {
      issues.push('Active subscription without Stripe subscription ID');
      recommendations.push('Sync with Stripe or update status');
    }

    if (!team.planName) {
      issues.push('Missing plan name');
      recommendations.push('Set default plan or sync with Stripe');
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };

  } catch (error) {
    console.error('Error validating subscription integrity:', error);
    return {
      isValid: false,
      issues: ['Validation error'],
      recommendations: ['Check database connectivity'],
    };
  }
}
