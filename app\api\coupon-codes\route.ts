import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { couponCodes } from '@/lib/db/schema';
import { eq, and, isNull, gt, lte } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';
import { z } from 'zod';

// Schema for validating coupon code creation/update
const couponCodeSchema = z.object({
  code: z.string().min(3).max(50),
  discountType: z.enum(['percentage', 'fixed']),
  discountAmount: z.number().positive(),
  description: z.string().optional(),
  maxUses: z.number().int().positive().optional().nullable(),
  // More flexible date validation
  validFrom: z.string().refine(
    (val) => {
      try {
        new Date(val);
        return true;
      } catch (e) {
        return false;
      }
    },
    { message: 'Invalid date format for validFrom' }
  ),
  // More flexible date validation with nullable
  validUntil: z
    .string()
    .refine(
      (val) => {
        try {
          new Date(val);
          return true;
        } catch (e) {
          return false;
        }
      },
      { message: 'Invalid date format for validUntil' }
    )
    .nullable()
    .optional(),
  isActive: z.boolean().optional(),
});

// GET all coupon codes (admin only)
export async function GET(request: NextRequest) {
  try {
    const user = await getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated. Please sign in.' },
        { status: 401 }
      );
    }

    if (user.role !== 'owner' && user.role !== 'teamMember') {
      return NextResponse.json(
        { error: 'Only owners and team members can manage coupon codes.' },
        { status: 403 }
      );
    }

    try {
      const allCoupons = await db
        .select()
        .from(couponCodes)
        .orderBy(couponCodes.createdAt);
      return NextResponse.json(allCoupons);
    } catch (dbError) {
      console.error('Database error fetching coupon codes:', dbError);
      return NextResponse.json(
        { error: 'Database error fetching coupon codes' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error fetching coupon codes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch coupon codes' },
      { status: 500 }
    );
  }
}

// POST create a new coupon code (admin only)
export async function POST(request: NextRequest) {
  try {
    const user = await getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated. Please sign in.' },
        { status: 401 }
      );
    }

    if (user.role !== 'owner' && user.role !== 'teamMember') {
      return NextResponse.json(
        { error: 'Only owners and team members can manage coupon codes.' },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate the request body
    let validatedData;
    try {
      validatedData = couponCodeSchema.parse(body);
      console.log('Validated data:', validatedData);
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        console.error('Validation error:', validationError.errors);
        return NextResponse.json(
          {
            error: 'Validation error',
            details: validationError.errors,
          },
          { status: 400 }
        );
      }
      throw validationError;
    }

    try {
      const newCoupon = await db
        .insert(couponCodes)
        .values({
          code: validatedData.code.toUpperCase(),
          discountType: validatedData.discountType,
          discountAmount: validatedData.discountAmount,
          description: validatedData.description,
          maxUses: validatedData.maxUses,
          validFrom: validatedData.validFrom
            ? new Date(validatedData.validFrom)
            : new Date(),
          validUntil: validatedData.validUntil
            ? new Date(validatedData.validUntil)
            : null,
          createdBy: user.id,
          isActive: validatedData.isActive ?? true,
        })
        .returning();

      return NextResponse.json(newCoupon[0], { status: 201 });
    } catch (dbError) {
      console.error('Database error creating coupon code:', dbError);

      // Check for duplicate code error
      if (dbError.message && dbError.message.includes('duplicate key')) {
        return NextResponse.json(
          { error: 'A coupon with this code already exists' },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { error: 'Database error creating coupon code' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error creating coupon code:', error);

    // This catch block should only handle non-validation errors now
    // since we're handling validation errors earlier
    return NextResponse.json(
      {
        error: 'Failed to create coupon code',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
