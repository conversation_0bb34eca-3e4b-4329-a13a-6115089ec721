import Stripe from 'stripe';
import { Team, userSubscriptions, subscriptionHistory } from '@/lib/db/schema';
import { getUser } from '@/lib/db/server-queries';
import {
  getTeamByStripeCustomerId,
  updateTeamSubscription
} from '@/lib/db/queries';
import { db } from '@/lib/db/drizzle';
import { eq } from 'drizzle-orm';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-01-27.acacia'
});

// Function to update an existing subscription
export async function updateSubscription({
  team,
  priceId,
  isUpgrade,
  isDowngrade
}: {
  team: Team;
  priceId: string;
  isUpgrade?: boolean;
  isDowngrade?: boolean;
}): Promise<string> {
  try {
    // If the team doesn't have a Stripe customer ID, create one
    if (!team.stripeCustomerId) {
      console.log('Team does not have a Stripe customer ID, creating one...');

      // Get the current user
      const user = await getUser();
      if (!user) {
        throw new Error('User not found');
      }

      // Create a new Stripe customer
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name || user.email,
        metadata: {
          teamId: team.id.toString(),
          userId: user.id.toString()
        }
      });

      console.log('Created new Stripe customer:', customer.id);

      // Update the team with the new Stripe customer ID
      await updateTeamSubscription(team.id, {
        stripeCustomerId: customer.id
      });

      // Update the local team object
      team.stripeCustomerId = customer.id;

      console.log('Updated team with new Stripe customer ID');
    }

    console.log('Updating subscription for team:', {
      teamId: team.id,
      stripeCustomerId: team.stripeCustomerId,
      stripeSubscriptionId: team.stripeSubscriptionId,
      currentPlan: team.planName,
      newPriceId: priceId
    });

    // Check if the team has an active subscription
    if (team.stripeSubscriptionId) {
      try {
        // Get the current subscription with expanded product information
        const subscription = await stripe.subscriptions.retrieve(team.stripeSubscriptionId, {
          expand: ['items.data.price.product']
        });

        // Only update if the subscription is active or trialing
        if (subscription.status === 'active' || subscription.status === 'trialing') {
          console.log('Updating existing subscription:', subscription.id);

          // Check if this is an upgrade or downgrade
          const currentPriceId = subscription.items.data[0].price.id;

          // Get product details for the current price
          let currentProductName = '';
          try {
            const currentPrice = await stripe.prices.retrieve(currentPriceId, {
              expand: ['product']
            });

            if (typeof currentPrice.product === 'object' && 'name' in currentPrice.product) {
              currentProductName = currentPrice.product.name.toLowerCase();
            }
          } catch (error) {
            console.error('Error retrieving current price details:', error);
          }

          // Get product details for the new price
          let newProductName = '';
          try {
            const newPrice = await stripe.prices.retrieve(priceId, {
              expand: ['product']
            });

            if (typeof newPrice.product === 'object' && 'name' in newPrice.product) {
              newProductName = newPrice.product.name.toLowerCase();
            }
          } catch (error) {
            console.error('Error retrieving new price details:', error);
          }

          console.log('Product names:', { currentProductName, newProductName });

          // Determine if this is an upgrade or downgrade based on provided flags or product names
          const currentPlan = team.planName?.toLowerCase() || '';

          // Use provided flags if available, otherwise determine from product names
          const planIsUpgrade = isUpgrade !== undefined ? isUpgrade :
            (currentPlan === 'starter' && (priceId.includes('pro') || newProductName.includes('pro'))) ||
            (currentProductName.includes('starter') && (priceId.includes('pro') || newProductName.includes('pro')));

          const planIsDowngrade = isDowngrade !== undefined ? isDowngrade :
            (currentPlan === 'pro' && (priceId.includes('starter') || newProductName.includes('starter'))) ||
            (currentProductName.includes('pro') && (priceId.includes('starter') || newProductName.includes('starter')));

          console.log('Plan change details:', {
            currentPriceId,
            currentPlan,
            newPriceId: priceId,
            isUpgrade: planIsUpgrade,
            isDowngrade: planIsDowngrade
          });

          // Update the subscription with the new price
          const updatedSubscription = await stripe.subscriptions.update(team.stripeSubscriptionId, {
            items: [
              {
                id: subscription.items.data[0].id,
                price: priceId,
              },
            ],
            // For upgrades, prorate immediately
            // For downgrades, apply at the end of the billing period
            proration_behavior: planIsUpgrade ? 'create_prorations' : 'none',
            payment_behavior: 'allow_incomplete',
            // For downgrades, set to take effect at period end
            cancel_at_period_end: planIsDowngrade,
          });

          console.log('Subscription updated:', {
            id: updatedSubscription.id,
            status: updatedSubscription.status,
            currentPeriodEnd: new Date(updatedSubscription.current_period_end * 1000),
            cancelAtPeriodEnd: updatedSubscription.cancel_at_period_end
          });

          // Create a billing portal session for the customer to review the changes
          const portalSession = await stripe.billingPortal.sessions.create({
            customer: team.stripeCustomerId,
            return_url: `${process.env.BASE_URL}/dashboard/billing`,
          });

          return portalSession.url;
        } else {
          console.log('Subscription exists but is not active:', subscription.status);
          // Fall through to create a new subscription
        }
      } catch (error) {
        console.error('Error retrieving or updating subscription:', error);
        // If there's an error (e.g., subscription not found), create a new one
      }
    }

    // If we get here, either there's no subscription or it's not active
    // Create a new checkout session instead
    console.log('Creating new checkout session for customer:', team.stripeCustomerId);

    // Determine if we should include a trial period
    // Only include trial for new subscriptions, not for reactivations or plan changes
    // Also check if user has subscription history to prevent trial abuse
    const hasSubscriptionHistory = await checkSubscriptionHistory(team.id);
    const shouldIncludeTrial = !team.stripeSubscriptionId && !hasSubscriptionHistory;

    console.log('Should include trial period:', shouldIncludeTrial, {
      hasStripeSubscriptionId: !!team.stripeSubscriptionId,
      hasSubscriptionHistory
    });

    // Create the checkout session
    const sessionParams: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'subscription',
      success_url: `${process.env.BASE_URL}/api/stripe/checkout?session_id={CHECKOUT_SESSION_ID}&redirect_to=member`,
      cancel_url: `${process.env.BASE_URL}/dashboard/billing`,
      customer: team.stripeCustomerId,
      client_reference_id: team.id.toString(),
      allow_promotion_codes: true,
      currency: 'usd'
    };

    // Only add trial period for new subscriptions
    if (shouldIncludeTrial) {
      sessionParams.subscription_data = {
        trial_period_days: 14 // 14-day free trial as mentioned in the pricing page
      };
    }

    const session = await stripe.checkout.sessions.create(sessionParams);

    return session.url || `${process.env.BASE_URL || ''}/dashboard/billing`;
  } catch (error) {
    console.error('Error updating subscription:', error);
    throw error;
  }
}

// Function to create a new checkout session
export async function createCheckoutSession({
  team,
  priceId
}: {
  team: Team | null;
  priceId: string;
}): Promise<string> {
  const user = await getUser();

  if (!team || !user) {
    return `${process.env.BASE_URL || ''}/sign-up?redirect=checkout&priceId=${priceId}`;
  }

  // For new customers or customers without an active subscription
  // Determine if we should include a trial period
  // Only include trial for new customers without a subscription ID and no subscription history
  const hasSubscriptionHistory = team ? await checkSubscriptionHistory(team.id) : false;
  const shouldIncludeTrial = !team?.stripeSubscriptionId && !hasSubscriptionHistory;

  console.log('Should include trial period:', shouldIncludeTrial, {
    hasStripeSubscriptionId: !!team?.stripeSubscriptionId,
    hasSubscriptionHistory
  });

  // Create the checkout session
  const sessionParams: Stripe.Checkout.SessionCreateParams = {
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1
      }
    ],
    mode: 'subscription',
    success_url: `${process.env.BASE_URL}/api/stripe/checkout?session_id={CHECKOUT_SESSION_ID}&redirect_to=member`,
    cancel_url: `${process.env.BASE_URL}/pricing`,
    customer: team?.stripeCustomerId || undefined,
    client_reference_id: user.id.toString(),
    allow_promotion_codes: true,
    currency: 'usd' // Explicitly set currency to USD
  };

  // Only add trial period for new subscriptions
  if (shouldIncludeTrial) {
    sessionParams.subscription_data = {
      trial_period_days: 14 // 14-day free trial as mentioned in the pricing page
    };
  }

  const session = await stripe.checkout.sessions.create(sessionParams);

  return session.url || `${process.env.BASE_URL || ''}/pricing`;
}

export async function createCustomerPortalSession(
  team: Team & { returnUrl?: string }
): Promise<Stripe.BillingPortal.Session> {
  // If the team doesn't have a Stripe customer ID, create one
  if (!team.stripeCustomerId) {
    console.log('Team does not have a Stripe customer ID, creating one...');

    // Get the current user
    const user = await getUser();
    if (!user) {
      throw new Error('User not found');
    }

    // Create a new Stripe customer
    const customer = await stripe.customers.create({
      email: user.email,
      name: user.name || user.email,
      metadata: {
        teamId: team.id.toString(),
        userId: user.id.toString()
      }
    });

    console.log('Created new Stripe customer:', customer.id);

    // Update the team with the new Stripe customer ID
    await updateTeamSubscription(team.id, {
      stripeCustomerId: customer.id
    });

    // Update the local team object
    team.stripeCustomerId = customer.id;

    console.log('Updated team with new Stripe customer ID');
  }

  // If the team doesn't have a product ID, we can't create a portal session
  if (!team.stripeProductId) {
    throw new Error('Team does not have a Stripe product ID. Please subscribe to a plan first.');
  }

  // Instead of creating a new configuration, we'll use the Stripe customer portal directly
  // This approach bypasses the configuration issues and lets Stripe handle the subscription identification
  console.log('Creating customer portal session directly...');

  // Log the team's subscription details for debugging
  console.log('Team subscription details:', {
    teamId: team.id,
    stripeCustomerId: team.stripeCustomerId,
    stripeSubscriptionId: team.stripeSubscriptionId,
    stripeProductId: team.stripeProductId,
    planName: team.planName,
    subscriptionStatus: team.subscriptionStatus
  });

  // Verify the subscription in Stripe
  try {
    if (team.stripeSubscriptionId) {
      const subscription = await stripe.subscriptions.retrieve(team.stripeSubscriptionId, {
        expand: ['items.data.price.product']
      });

      console.log('Current Stripe subscription:', {
        id: subscription.id,
        status: subscription.status,
        items: subscription.items.data.map(item => ({
          priceId: item.price.id,
          productId: typeof item.price.product === 'string'
            ? item.price.product
            : (item.price.product as Stripe.Product).id,
          productName: typeof item.price.product === 'string'
            ? 'unknown'
            : (item.price.product as Stripe.Product).name
        }))
      });
    }
  } catch (error) {
    console.error('Error retrieving subscription from Stripe:', error);
  }

  // First, prioritize the current product if it exists
  const productConfigurations = [];

  if (team.stripeProductId) {
    console.log(`Prioritizing current product: ${team.stripeProductId}`);

    // Get prices for the current product
    const currentProductPrices = await stripe.prices.list({
      product: team.stripeProductId,
      active: true,
      limit: 100
    });

    if (currentProductPrices.data.length > 0) {
      // Get the product details to log the name
      try {
        const product = await stripe.products.retrieve(team.stripeProductId);
        console.log(`Added current product ${product.name} (${team.stripeProductId}) with ${currentProductPrices.data.length} prices`);
      } catch (error) {
        console.log(`Added current product ${team.stripeProductId} with ${currentProductPrices.data.length} prices`);
      }

      productConfigurations.push({
        product: team.stripeProductId,
        prices: currentProductPrices.data.map(price => price.id)
      });
    }
  }

  // If we need more products, get the most relevant ones
  // Stripe has a limit of 10 products in the portal configuration
  if (productConfigurations.length < 2) {
    console.log('Getting additional products for the portal configuration');

    // Get products that are specifically for subscription plans (starter, pro)
    const planProducts = await stripe.products.list({
      active: true,
      limit: 10
    });

    // Filter and sort products to prioritize subscription plan products
    const filteredProducts = planProducts.data
      .filter(product =>
        // Skip the current product as we've already added it
        product.id !== team.stripeProductId &&
        // Prioritize products with names containing 'starter' or 'pro'
        (product.name.toLowerCase().includes('starter') ||
         product.name.toLowerCase().includes('pro'))
      )
      .slice(0, 9); // Limit to 9 additional products (10 total with current product)

    console.log(`Found ${filteredProducts.length} additional relevant products`);

    // Add each relevant product
    for (const product of filteredProducts) {
      // Get prices for this product
      const prices = await stripe.prices.list({
        product: product.id,
        active: true,
        limit: 100
      });

      if (prices.data.length > 0) {
        productConfigurations.push({
          product: product.id,
          prices: prices.data.map(price => price.id)
        });

        console.log(`Added product ${product.name} (${product.id}) with ${prices.data.length} prices`);

        // Stop if we've reached the limit
        if (productConfigurations.length >= 10) {
          console.log('Reached the maximum of 10 products for portal configuration');
          break;
        }
      }
    }
  }

  // If no products were found, try a different approach
  if (productConfigurations.length === 0) {
    console.log('No products found yet, trying a different approach');

    // Get all active prices
    const allPrices = await stripe.prices.list({
      active: true,
      limit: 100,
      expand: ['data.product']
    });

    console.log(`Found ${allPrices.data.length} active prices`);

    // Group prices by product
    const pricesByProduct: Record<string, string[]> = {};

    for (const price of allPrices.data) {
      if (price.product) {
        const productId = typeof price.product === 'string' ? price.product : price.product.id;

        if (!pricesByProduct[productId]) {
          pricesByProduct[productId] = [];
        }

        pricesByProduct[productId].push(price.id);
      }
    }

    // Sort products by number of prices (descending)
    const sortedProducts = Object.entries(pricesByProduct)
      .sort(([, pricesA], [, pricesB]) => pricesB.length - pricesA.length)
      .slice(0, 10); // Limit to 10 products

    console.log(`Found ${sortedProducts.length} products with prices`);

    // Add each product with its prices
    for (const [productId, priceIds] of sortedProducts) {
      if (priceIds.length > 0) {
        productConfigurations.push({
          product: productId,
          prices: priceIds
        });

        console.log(`Added product ${productId} with ${priceIds.length} prices`);
      }

      // Stop if we've reached the limit
      if (productConfigurations.length >= 10) {
        console.log('Reached the maximum of 10 products for portal configuration');
        break;
      }
    }
  }

  // If still no products, throw an error
  if (productConfigurations.length === 0) {
    throw new Error('No products or prices found in Stripe. Please create products and prices in your Stripe dashboard.');
  }

  // Create the configuration with the product configurations
  const configuration = await stripe.billingPortal.configurations.create({
    business_profile: {
      headline: 'Manage your subscription'
    },
    features: {
      payment_method_update: {
        enabled: true
      },
      customer_update: {
        enabled: true,
        allowed_updates: ['email', 'name'],
      },
      subscription_update: {
        enabled: true,
        default_allowed_updates: ['price', 'quantity', 'promotion_code'],
        proration_behavior: 'create_prorations',
        products: productConfigurations
      },
      subscription_cancel: {
        enabled: true,
        mode: 'at_period_end',
        cancellation_reason: {
          enabled: true,
          options: [
            'too_expensive',
            'missing_features',
            'switched_service',
            'unused',
            'other'
          ]
        }
      },
      invoice_history: {
        enabled: true
      }
    }
  });

  // Use the provided returnUrl or fall back to the default
  const returnUrl = team.returnUrl || `${process.env.NEXT_PUBLIC_APP_URL || process.env.BASE_URL}/dashboard/billing?fromStripe=true`;

  return stripe.billingPortal.sessions.create({
    customer: team.stripeCustomerId,
    return_url: returnUrl,
    configuration: configuration.id
  });
}

export async function handleSubscriptionChange(
  subscription: Stripe.Subscription
) {
  const customerId = subscription.customer as string;
  const subscriptionId = subscription.id;
  const status = subscription.status;

  console.log('Handling subscription change:', {
    customerId,
    subscriptionId,
    status,
    items: subscription.items.data.length
  });

  // Expand the subscription to get product details
  try {
    // Retrieve the subscription with expanded product details
    const expandedSubscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['items.data.price.product']
    });

    // Log the expanded subscription details
    console.log('Expanded subscription details:', {
      id: expandedSubscription.id,
      status: expandedSubscription.status,
      items: expandedSubscription.items.data.map(item => ({
        priceId: item.price.id,
        productId: typeof item.price.product === 'string'
          ? item.price.product
          : (item.price.product as Stripe.Product).id,
        productName: typeof item.price.product === 'string'
          ? 'unknown'
          : (item.price.product as Stripe.Product).name
      }))
    });

    // Use the expanded subscription for further processing
    subscription = expandedSubscription;
  } catch (error) {
    console.error('Error retrieving expanded subscription:', error);
  }

  const team = await getTeamByStripeCustomerId(customerId);

  if (!team) {
    console.error('Team not found for Stripe customer:', customerId);
    return;
  }

  console.log('Found team:', {
    teamId: team.id,
    currentPlan: team.planName,
    currentStatus: team.subscriptionStatus,
    stripeProductId: team.stripeProductId,
    stripeSubscriptionId: team.stripeSubscriptionId
  });

  // Check if this is the most recent subscription
  // This is crucial to prevent older webhook events from overriding newer ones
  if (team.stripeSubscriptionId && team.stripeSubscriptionId !== subscriptionId) {
    try {
      // Try to get the current subscription from Stripe
      const currentSubscription = await stripe.subscriptions.retrieve(team.stripeSubscriptionId);

      // If the current subscription is newer than the one in the webhook, don't update
      if (new Date(currentSubscription.created) > new Date(subscription.created)) {
        console.log('Ignoring older subscription update:', {
          webhookSubscriptionId: subscriptionId,
          webhookCreated: new Date(subscription.created),
          currentSubscriptionId: team.stripeSubscriptionId,
          currentCreated: new Date(currentSubscription.created)
        });
        return;
      }
    } catch (error) {
      // If we can't retrieve the current subscription, it might have been deleted
      // In this case, we should proceed with the update
      console.log('Could not retrieve current subscription, proceeding with update:', error);
    }
  }

  if (status === 'active' || status === 'trialing') {
    const item = subscription.items.data[0];
    if (!item || !item.price || !item.price.product) {
      console.error('Invalid subscription data - missing price or product:', subscription);
      return;
    }

    // Get product details to get the correct plan name
    let productName;
    let productId;

    try {
      if (typeof item.price.product === 'string') {
        productId = item.price.product;
        const product = await stripe.products.retrieve(productId);

        // Map the product name to our plan names
        const productNameRaw = product.name?.toLowerCase() || 'unknown';

        // Normalize the plan name to match our expected values
        if (productNameRaw.includes('starter')) {
          productName = 'starter';
        } else if (productNameRaw.includes('pro')) {
          productName = 'pro';
        } else {
          productName = productNameRaw;
        }

        console.log('Retrieved product details:', {
          productId,
          productName: productNameRaw,
          normalizedName: productName
        });
      } else {
        productId = item.price.product.id;
        // Check if it's a deleted product
        if ('name' in item.price.product) {
          const productNameRaw = item.price.product.name.toLowerCase();

          // Normalize the plan name
          if (productNameRaw.includes('starter')) {
            productName = 'starter';
          } else if (productNameRaw.includes('pro')) {
            productName = 'pro';
          } else {
            productName = productNameRaw;
          }

          console.log('Using embedded product details:', {
            productId,
            productName: productNameRaw,
            normalizedName: productName
          });
        } else {
          productName = 'unknown';
          console.log('Product has no name property:', item.price.product);
        }
      }
    } catch (error) {
      console.error('Error retrieving product details:', error);
      productName = 'unknown';
      productId = typeof item.price.product === 'string' ? item.price.product : item.price.product.id;
    }

    console.log('Updating team subscription to active/trialing:', {
      subscriptionId,
      productId,
      planName: productName,
      status
    });

    // Update the database with the new subscription details
    await updateTeamSubscription(team.id, {
      stripeSubscriptionId: subscriptionId,
      stripeProductId: productId,
      planName: productName,
      subscriptionStatus: status
    });

    // Log the update for debugging
    console.log(`Updated team ${team.id} with subscription status: ${status}, plan: ${productName}`);

    console.log('Successfully updated team subscription in database');
  } else if (status === 'canceled' || status === 'unpaid' || status === 'incomplete_expired' || status === 'incomplete') {
    console.log('Updating team subscription to inactive:', {
      status
    });

    // For inactive subscriptions, clear the subscription details
    await updateTeamSubscription(team.id, {
      stripeSubscriptionId: null,
      stripeProductId: null,
      planName: null,
      subscriptionStatus: status
    });

    console.log('Successfully cleared team subscription in database');
  } else {
    console.log('Unhandled subscription status:', status);
  }
}

export async function getStripePrices() {
  const prices = await stripe.prices.list({
    expand: ['data.product'],
    active: true,
    type: 'recurring'
  });

  return prices.data.map((price) => ({
    id: price.id,
    productId:
      typeof price.product === 'string' ? price.product : price.product.id,
    unitAmount: price.unit_amount,
    currency: price.currency,
    interval: price.recurring?.interval,
    trialPeriodDays: price.recurring?.trial_period_days
  }));
}

export async function getStripeProducts() {
  const products = await stripe.products.list({
    active: true,
    expand: ['data.default_price']
  });

  return products.data.map((product) => ({
    id: product.id,
    name: product.name,
    description: product.description,
    defaultPriceId:
      typeof product.default_price === 'string'
        ? product.default_price
        : product.default_price?.id
  }));
}

/**
 * Check if team has any subscription history to prevent trial abuse
 */
async function checkSubscriptionHistory(teamId: number): Promise<boolean> {
  try {
    // Check for any existing or past subscriptions
    const existingSubscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.teamId, teamId),
    });

    // Check subscription history
    const historyRecord = await db.query.subscriptionHistory.findFirst({
      where: eq(subscriptionHistory.teamId, teamId),
    });

    // If there's any record of subscriptions, they've had a subscription before
    return !!(existingSubscription || historyRecord);
  } catch (error) {
    console.error('[Stripe Service] Error checking subscription history:', error);
    // If we can't check history, err on the side of caution and don't give trial
    return true;
  }
}
