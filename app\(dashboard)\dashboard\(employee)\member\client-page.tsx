'use client';

import { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { CloudinaryImage } from '@/components/ui/cloudinary-image';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Hand, Lock } from 'lucide-react';
import { SearchBarWrapper } from '@/components/SearchBarWrapper';
import { BookmarkButton } from '@/components/BookmarkButton';
import { useRouter } from 'next/navigation';
import { useMutation } from '@tanstack/react-query';
import { db } from '@/lib/db/drizzle';
import { updateCaseStudyViewsOnClick } from '@/lib/api/views';

// Define types for our case studies
type CaseStudy = {
  id: number;
  useCaseTitle: string;
  introductionText: string | null;
  industry: string | null;
  role: string | null;
  featureImageUrl: string | null;
  previewImageUrl: string | null;
  marketIntelligenceData: any;
  marketMetricsData: any;
  isBookmarked: boolean;
  icons?: {
    iconUrl: string;
    iconType: string;
  }[];
};

type CaseStudyWithMatchType = CaseStudy & {
  matchType?: 'role' | 'industry';
};

type MemberDashboardProps = {
  trendingCaseStudies: CaseStudy[];
  recentCaseStudies: CaseStudy[];
  bookmarkedCaseStudies: CaseStudy[];
  industrySpecificCaseStudies: CaseStudy[];
  relatedCaseStudies: CaseStudyWithMatchType[];
  roleRelatedCaseStudies: CaseStudyWithMatchType[];
  industryRelatedCaseStudies: CaseStudyWithMatchType[];
  roleSpecificCaseStudies: Record<string, CaseStudy[]>;
  vectorSpecificCaseStudies: Record<string, CaseStudy[]>;
  predefinedRoles: string[];
  predefinedVectors: string[];
  userIndustry: string;
  userRole?: string | null;
  userName?: string | null;
};

export default function MemberDashboardClient({
  trendingCaseStudies,
  recentCaseStudies,
  bookmarkedCaseStudies,
  industrySpecificCaseStudies,
  relatedCaseStudies,
  roleRelatedCaseStudies,
  industryRelatedCaseStudies,
  roleSpecificCaseStudies,
  vectorSpecificCaseStudies,
  predefinedRoles,
  predefinedVectors,
  userIndustry,
  userRole,
  userName,
}: MemberDashboardProps) {
  const router = useRouter();

  // State for sliding functionality
  const [trendingStartIndex, setTrendingStartIndex] = useState(0);
  const [recentStartIndex, setRecentStartIndex] = useState(0);
  const [industryStartIndex, setIndustryStartIndex] = useState(0);
  const [relatedStartIndex, setRelatedStartIndex] = useState(0);

  // Create state for each role's sliding functionality
  const roleStartIndices = predefinedRoles.reduce((acc, role) => {
    acc[role] = 0;
    return acc;
  }, {} as Record<string, number>);

  const [roleStartIndexMap, setRoleStartIndexMap] =
    useState<Record<string, number>>(roleStartIndices);

  // Create state for each vector's sliding functionality
  const vectorStartIndices = predefinedVectors.reduce((acc, vector) => {
    acc[vector] = 0;
    return acc;
  }, {} as Record<string, number>);

  const [vectorStartIndexMap, setVectorStartIndexMap] =
    useState<Record<string, number>>(vectorStartIndices);

  // State for subscription data
  const [planName, setPlanName] = useState<string | null>(null);
  const [isProPlan, setIsProPlan] = useState(false);
  const [caseStudiesLimit, setCaseStudiesLimit] = useState(10); // Default limit for starter plan

  // Function to fetch subscription data and log it
  const fetchSubscriptionData = async () => {
    try {
      // Fetch team data including subscription information using the correct endpoint
      // Using the same endpoint as the billing page
      const response = await fetch('/api/user/team');

      // Log the raw response for debugging
      console.log('Member Dashboard - Raw response status:', response.status);

      // Check if the response is OK before trying to parse JSON
      if (!response.ok) {
        const text = await response.text();
        console.error('Member Dashboard - API Error:', response.status, text.substring(0, 200) + '...');
        return;
      }

      try {
        const data = await response.json();

        // Log the complete team data for comparison with billing page
        console.log('Member Dashboard - Team Data:', data);

        // Log specific subscription details for debugging the discrepancy
        console.log('Member Dashboard - Plan Name:', data.planName);
        console.log('Member Dashboard - Subscription Status:', data.subscriptionStatus);
        console.log('Member Dashboard - Stripe Customer ID:', data.stripeCustomerId);
        console.log('Member Dashboard - Stripe Subscription ID:', data.stripeSubscriptionId);

        // Check if there's a discrepancy with what's shown in the billing page
        console.log('Member Dashboard - IMPORTANT: If this plan name differs from what is shown in the billing page, there is a data inconsistency');

        // Store plan information in state
        setPlanName(data.planName);

        // Check if user has Pro plan (case-insensitive check)
        const isPro = data.planName?.toLowerCase() === 'pro';
        setIsProPlan(isPro);

        // Set case study limit based on plan
        setCaseStudiesLimit(isPro ? Infinity : 10); // Pro has unlimited, Starter has 10
      } catch (jsonError) {
        console.error('Member Dashboard - JSON parsing error:', jsonError);
        const text = await response.text();
        console.error('Member Dashboard - Response was not valid JSON:', text.substring(0, 200) + '...');
      }
    } catch (error) {
      console.error('Member Dashboard - Network error fetching team data:', error);
    }
  };

  // Function to manually sync subscription data with Stripe
  const handleRefreshSubscription = async () => {
    try {
      // Use the sync service for better reliability
      const response = await fetch('/api/subscriptions/sync', {
        method: 'POST',
      });

      // Log the raw response for debugging
      console.log('Member Dashboard - Sync raw response status:', response.status);

      // Check if the response is OK before trying to parse JSON
      if (!response.ok) {
        const text = await response.text();
        console.error('Member Dashboard - Sync API Error:', response.status, text.substring(0, 200) + '...');
        return;
      }

      try {
        const data = await response.json();
        console.log('Member Dashboard - Sync response:', data);

        if (data.success) {
          // After sync, get the updated team data
          await fetchSubscriptionData();

          // Show validation issues if any
          if (data.validation && !data.validation.isValid) {
            console.warn('Member Dashboard - Subscription validation issues:', data.validation.issues);
          }
        }
      } catch (jsonError) {
        console.error('Member Dashboard - Sync JSON parsing error:', jsonError);
        const text = await response.text();
        console.error('Member Dashboard - Sync response was not valid JSON:', text.substring(0, 200) + '...');
      }
    } catch (error) {
      console.error('Member Dashboard - Network error syncing subscription data:', error);
    }
  };

  // Fetch subscription data on component mount
  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  // Call handleRefreshSubscription to manually sync data
  useEffect(() => {
    // Small delay to ensure initial fetch completes first
    const timer = setTimeout(() => {
      // Log before attempting to refresh
      console.log('Member Dashboard - Attempting to refresh subscription data');
      handleRefreshSubscription();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Number of items to display per page
  const itemsPerPage = 4;

  // Handler functions for sliding
  const slideTrending = (direction: 'left' | 'right') => {
    if (direction === 'left') {
      setTrendingStartIndex(Math.max(0, trendingStartIndex - itemsPerPage));
    } else {
      setTrendingStartIndex(
        Math.min(
          trendingCaseStudies.length - itemsPerPage,
          trendingStartIndex + itemsPerPage
        )
      );
    }
  };

  const slideRecent = (direction: 'left' | 'right') => {
    if (direction === 'left') {
      setRecentStartIndex(Math.max(0, recentStartIndex - itemsPerPage));
    } else {
      setRecentStartIndex(
        Math.min(
          recentCaseStudies.length - itemsPerPage,
          recentStartIndex + itemsPerPage
        )
      );
    }
  };

  const slideIndustry = (direction: 'left' | 'right') => {
    if (direction === 'left') {
      setIndustryStartIndex(Math.max(0, industryStartIndex - itemsPerPage));
    } else {
      setIndustryStartIndex(
        Math.min(
          industrySpecificCaseStudies.length - itemsPerPage,
          industryStartIndex + itemsPerPage
        )
      );
    }
  };

  const slideRelated = (direction: 'left' | 'right') => {
    if (direction === 'left') {
      setRelatedStartIndex(Math.max(0, relatedStartIndex - itemsPerPage));
    } else {
      setRelatedStartIndex(
        Math.min(
          relatedCaseStudies.length - itemsPerPage,
          relatedStartIndex + itemsPerPage
        )
      );
    }
  };

  // Function to slide a specific role's case studies
  const slideRoleSpecific = (role: string, direction: 'left' | 'right') => {
    const currentIndex = roleStartIndexMap[role];
    const caseStudiesForRole = roleSpecificCaseStudies[role] || [];

    let newIndex;
    if (direction === 'left') {
      newIndex = Math.max(0, currentIndex - itemsPerPage);
    } else {
      newIndex = Math.min(
        caseStudiesForRole.length - itemsPerPage,
        currentIndex + itemsPerPage
      );
    }

    setRoleStartIndexMap({
      ...roleStartIndexMap,
      [role]: newIndex,
    });
  };

  // Function to slide a specific vector's case studies
  const slideVectorSpecific = (vector: string, direction: 'left' | 'right') => {
    const currentIndex = vectorStartIndexMap[vector];
    const caseStudiesForVector = vectorSpecificCaseStudies[vector] || [];

    let newIndex;
    if (direction === 'left') {
      newIndex = Math.max(0, currentIndex - itemsPerPage);
    } else {
      newIndex = Math.min(
        caseStudiesForVector.length - itemsPerPage,
        currentIndex + itemsPerPage
      );
    }

    setVectorStartIndexMap({
      ...vectorStartIndexMap,
      [vector]: newIndex,
    });
  };

  // updates views onClick case study

  // Calculate and track all unique case studies across all categories
  const { totalCaseStudies, accessibleCaseStudies } = useMemo(() => {
    const allCaseStudies = new Set<number>();
    const accessibleIds = new Set<number>();

    // Helper function to add case studies to both sets
    const addCaseStudy = (cs: CaseStudy) => {
      allCaseStudies.add(cs.id);
      // Only add to accessible if we haven't reached the limit yet
      if (accessibleIds.size < caseStudiesLimit) {
        accessibleIds.add(cs.id);
      }
    };

    // Add case studies from priority categories first
    // This ensures the most important case studies are accessible

    // 1. First add trending case studies
    trendingCaseStudies.forEach(addCaseStudy);

    // 2. Then add industry specific case studies
    industrySpecificCaseStudies.forEach(addCaseStudy);

    // 3. Then add recent case studies
    recentCaseStudies.forEach(addCaseStudy);

    // 4. Then add related case studies
    relatedCaseStudies.forEach(addCaseStudy);

    // 5. Then add from role specific case studies
    Object.values(roleSpecificCaseStudies).forEach(caseStudies => {
      caseStudies.forEach(addCaseStudy);
    });

    // 6. Finally add from vector specific case studies
    Object.values(vectorSpecificCaseStudies).forEach(caseStudies => {
      caseStudies.forEach(addCaseStudy);
    });

    return {
      totalCaseStudies: allCaseStudies.size,
      accessibleCaseStudies: accessibleIds
    };
  }, [trendingCaseStudies, recentCaseStudies, industrySpecificCaseStudies, relatedCaseStudies, roleSpecificCaseStudies, vectorSpecificCaseStudies, caseStudiesLimit]);

  // Function to handle upgrade click
  const handleUpgradeClick = () => {
    router.push('/dashboard/billing');
  };

  // Helper function to check if a case study should be locked
  const shouldLockCaseStudy = (caseStudyId: number) => {
    if (isProPlan) return false; // Pro plan has unlimited access

    // For starter plan, check if this case study is in our accessible set
    // If it's not in the set, it should be locked
    return !accessibleCaseStudies.has(caseStudyId);
  };

  // Helper function to render a case study card
  const renderCaseStudyCard = (caseStudy: CaseStudy) => {
    const isLocked = shouldLockCaseStudy(caseStudy.id);

    // Handle click based on whether the case study is locked
    const handleCardClick = () => {
      if (isLocked) {
        // Redirect to billing page for upgrade
        handleUpgradeClick();
      } else {
        // Normal behavior - update views and navigate to case study
        updateCaseStudyViewsOnClick(caseStudy.id);
        router.push(`/dashboard/case-studies/${caseStudy.id}`);
      }
    };

    return (
      <div
        key={caseStudy.id}
        className={`relative block transition-transform ${isLocked ? 'opacity-80' : 'hover:scale-[1.02]'}`}
        onClick={handleCardClick}
      >
        <Card className={`h-full overflow-hidden shadow-sm ${isLocked ? 'border-amber-300' : 'hover:shadow-md'} transition-all flex flex-col cursor-pointer`}>
          <div className='relative h-48 w-full overflow-hidden bg-gray-100'>
            {caseStudy.featureImageUrl ||
            caseStudy.previewImageUrl ||
            (caseStudy.icons &&
              caseStudy.icons.find((icon) => icon.iconType === 'icon')
                ?.iconUrl) ? (
              <CloudinaryImage
                src={
                  caseStudy.featureImageUrl ||
                  caseStudy.previewImageUrl ||
                  (caseStudy.icons &&
                    caseStudy.icons.find((icon) => icon.iconType === 'icon')
                      ?.iconUrl) ||
                  ''
                }
                alt={caseStudy.useCaseTitle || 'Case study'}
                width={400}
                height={300}
                className='object-cover w-full h-full'
                fallbackSrc='/placeholder-image.jpg'
              />
            ) : (
              <div className='flex h-full w-full items-center justify-center bg-gray-200'>
                <span className='text-gray-500'>No image</span>
              </div>
            )}

            {/* Bookmark button - kept outside the Link to prevent event propagation issues */}
            <div
              className='absolute top-2 right-2 z-10'
              onClick={(e) => e.stopPropagation()}
            >
              <BookmarkButton
                caseStudyId={caseStudy.id}
                initialIsBookmarked={caseStudy.isBookmarked || false}
                compact={true}
              />
            </div>
          </div>

          <div className='p-5 flex flex-col flex-grow'>
            {/* Show role badge and lock icon if locked */}
            <div className='mb-2 flex justify-between items-center'>
              <div>
                {caseStudy.role && (
                  <Badge
                    variant='secondary'
                    className='bg-gray-100 dark:bg-gray-800 border-0'
                  >
                    {caseStudy.role}
                  </Badge>
                )}
              </div>
              {isLocked && (
                <Badge variant='outline' className='bg-amber-50 text-amber-600 border-amber-200'>
                  <Lock size={12} className="mr-1" /> Pro Only
                </Badge>
              )}
            </div>

            <h3 className='text-base font-semibold line-clamp-2'>
              {caseStudy.useCaseTitle}
            </h3>

            {isLocked && (
              <div className="mt-3 pt-2 border-t border-amber-200">
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full text-amber-600 border-amber-300 hover:bg-amber-50"
                  onClick={handleUpgradeClick}
                >
                  <Lock size={14} className="mr-1" /> Upgrade to Pro
                </Button>
              </div>
            )}
          </div>
        </Card>
      </div>
    );
  };

  return (
    <div className='space-y-8 pb-12'>
      {/* Video Hero Section */}
      <div className='relative rounded-lg overflow-hidden h-[600px]'>
        {/* Video */}
        <video
          className='absolute inset-0 w-full h-full object-cover'
          autoPlay
          muted
          loop
          src='https://res.cloudinary.com/dyiso4ohk/video/upload/v1746252746/6913518_Motion_Graphics_Motion_Graphic_3840x2160_hvynby.mp4'
        >
          Your browser does not support the video tag.
        </video>

        {/* Overlay with content */}
        <div className='absolute inset-0 bg-black/50 flex flex-col items-center justify-center text-white px-4'>
          <h1 className='text-4xl font-bold mb-6 text-center'>
            Explore new ways to Innovate
          </h1>

          {/* Search bar */}
          <div className='w-full max-w-xl py-4'>
            <SearchBarWrapper userRole='member' dropdownPosition='bottom' />
          </div>
        </div>
      </div>

      {/* Welcome message with subscription info */}
      <div className='flex flex-col md:flex-row items-start md:items-center justify-between gap-4 bg-white p-4 rounded-lg shadow-sm border'>
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='icon' className='rounded-full'>
            <Hand className='h-5 w-5' />
          </Button>
          <div>
            <h2 className='text-xl font-medium'>
              {userName
                ? `Welcome, ${userName || 'Aboard'}!`
                : 'Welcome! Your dashboard is ready.'}
            </h2>
            {!isProPlan && (
              <div className="text-sm text-amber-600 flex items-center mt-1">
                <Lock size={14} className="mr-1" />
                <span>Starter Plan: Access limited to {caseStudiesLimit} of {totalCaseStudies} case studies</span>
              </div>
            )}
            {isProPlan && (
              <div className="text-sm text-emerald-600 flex items-center mt-1">
                <Badge variant="outline" className="bg-emerald-50 text-emerald-600 border-emerald-200 mr-2">PRO</Badge>
                <span>Unlimited case study access</span>
              </div>
            )}
          </div>
        </div>

        {!isProPlan && (
          <Button
            onClick={handleUpgradeClick}
            className="bg-amber-500 hover:bg-amber-600 text-white"
          >
            Upgrade to Pro
          </Button>
        )}
      </div>

      {/* Trending Case Studies Section */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <h2 className='text-xl font-medium'>Trending Case Studies</h2>
            <Button
              size='icon'
              variant='ghost'
              className='rounded-full w-6 h-6 bg-gray-100'
            >
              <span className='text-xs text-blue-600 font-medium'>i</span>
            </Button>
          </div>
          <div className='flex gap-2'>
            <Button
              size='icon'
              variant='outline'
              className='rounded-full w-8 h-8'
              onClick={() => slideTrending('left')}
              disabled={
                trendingStartIndex === 0 ||
                trendingCaseStudies.length <= itemsPerPage
              }
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>
            <Button
              size='icon'
              variant='outline'
              className='rounded-full w-8 h-8'
              onClick={() => slideTrending('right')}
              disabled={
                trendingStartIndex >=
                  trendingCaseStudies.length - itemsPerPage ||
                trendingCaseStudies.length <= itemsPerPage
              }
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
          {trendingCaseStudies.length > 0 ? (
            trendingCaseStudies
              .slice(trendingStartIndex, trendingStartIndex + itemsPerPage)
              .map(renderCaseStudyCard)
          ) : (
            <div className='col-span-4 text-center py-8'>
              <p className='text-gray-500'>
                No trending case studies available.
              </p>
            </div>
          )}
        </div>
      </div>



      {/* Industry Specific Case Studies Section */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <h2 className='text-xl font-medium'>
              {userIndustry && userIndustry !== 'Other'
                ? `${userIndustry} Use Cases`
                : 'Industry Insights'}
            </h2>
            <Button
              size='icon'
              variant='ghost'
              className='rounded-full w-6 h-6 bg-gray-100'
            >
              <span className='text-xs text-blue-600 font-medium'>i</span>
            </Button>
          </div>
          <div className='flex gap-2'>
            <Button
              size='icon'
              variant='outline'
              className='rounded-full w-8 h-8'
              onClick={() => slideIndustry('left')}
              disabled={
                industryStartIndex === 0 ||
                industrySpecificCaseStudies.length <= itemsPerPage
              }
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>
            <Button
              size='icon'
              variant='outline'
              className='rounded-full w-8 h-8'
              onClick={() => slideIndustry('right')}
              disabled={
                industryStartIndex >=
                  industrySpecificCaseStudies.length - itemsPerPage ||
                industrySpecificCaseStudies.length <= itemsPerPage
              }
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
          {industrySpecificCaseStudies.length > 0 ? (
            industrySpecificCaseStudies
              .slice(industryStartIndex, industryStartIndex + itemsPerPage)
              .map(renderCaseStudyCard)
          ) : (
            <div className='col-span-4 text-center py-8'>
              {userIndustry && userIndustry !== 'Other' ? (
                <p className='text-gray-500'>
                  We couldn't find exact matches for the {userIndustry}{' '}
                  industry.
                </p>
              ) : (
                <p className='text-gray-500'>
                  To see industry-specific case studies, please update your
                  profile with your industry.
                </p>
              )}
              <Button asChild className='mt-4'>
                <Link href='/dashboard/user/case-studies'>
                  Browse All Case Studies
                </Link>
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Recently Added Case Studies Section */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <h2 className='text-xl font-medium'>Recently Added</h2>
            <Button
              size='icon'
              variant='ghost'
              className='rounded-full w-6 h-6 bg-gray-100'
            >
              <span className='text-xs text-blue-600 font-medium'>i</span>
            </Button>
          </div>
          <div className='flex gap-2'>
            <Button
              size='icon'
              variant='outline'
              className='rounded-full w-8 h-8'
              onClick={() => slideRecent('left')}
              disabled={
                recentStartIndex === 0 ||
                recentCaseStudies.length <= itemsPerPage
              }
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>
            <Button
              size='icon'
              variant='outline'
              className='rounded-full w-8 h-8'
              onClick={() => slideRecent('right')}
              disabled={
                recentStartIndex >= recentCaseStudies.length - itemsPerPage ||
                recentCaseStudies.length <= itemsPerPage
              }
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
          {recentCaseStudies.length > 0 ? (
            recentCaseStudies
              .slice(recentStartIndex, recentStartIndex + itemsPerPage)
              .map(renderCaseStudyCard)
          ) : (
            <div className='col-span-4 text-center py-8'>
              <p className='text-gray-500'>No recent case studies available.</p>
            </div>
          )}
        </div>
      </div>

      {/* Role-Specific Case Studies Sections - One for each role */}
      {predefinedRoles.map((role) => {
        const caseStudiesForRole = roleSpecificCaseStudies[role] || [];
        const startIndex = roleStartIndexMap[role];

        // Skip rendering this section if there are no case studies for this role
        if (caseStudiesForRole.length === 0) return null;

        return (
          <div key={`role-${role}`} className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <h2 className='text-xl font-medium'>{role} Use Cases</h2>
                <Button
                  size='icon'
                  variant='ghost'
                  className='rounded-full w-6 h-6 bg-gray-100'
                >
                  <span className='text-xs text-blue-600 font-medium'>i</span>
                </Button>
              </div>
              <div className='flex gap-2'>
                <Button
                  size='icon'
                  variant='outline'
                  className='rounded-full w-8 h-8'
                  onClick={() => slideRoleSpecific(role, 'left')}
                  disabled={
                    startIndex === 0 ||
                    caseStudiesForRole.length <= itemsPerPage
                  }
                >
                  <ChevronLeft className='h-4 w-4' />
                </Button>
                <Button
                  size='icon'
                  variant='outline'
                  className='rounded-full w-8 h-8'
                  onClick={() => slideRoleSpecific(role, 'right')}
                  disabled={
                    startIndex >= caseStudiesForRole.length - itemsPerPage ||
                    caseStudiesForRole.length <= itemsPerPage
                  }
                >
                  <ChevronRight className='h-4 w-4' />
                </Button>
              </div>
            </div>

            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
              {caseStudiesForRole.length > 0 ? (
                caseStudiesForRole
                  .slice(startIndex, startIndex + itemsPerPage)
                  .map(renderCaseStudyCard)
              ) : (
                <div className='col-span-4 text-center py-8'>
                  <p className='text-gray-500'>
                    No case studies found for the {role} role.
                  </p>
                  <Button asChild className='mt-4'>
                    <Link href='/dashboard/user/case-studies'>
                      Browse All Case Studies
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        );
      })}

      {/* Vector-Specific Case Studies Sections - One for each vector */}
      {predefinedVectors.map((vector) => {
        const caseStudiesForVector = vectorSpecificCaseStudies[vector] || [];
        const startIndex = vectorStartIndexMap[vector];

        // Skip rendering this section if there are no case studies for this vector
        if (caseStudiesForVector.length === 0) return null;

        return (
          <div key={`vector-${vector}`} className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <h2 className='text-xl font-medium'>{vector} Use Cases</h2>
                <Button
                  size='icon'
                  variant='ghost'
                  className='rounded-full w-6 h-6 bg-gray-100'
                >
                  <span className='text-xs text-blue-600 font-medium'>i</span>
                </Button>
              </div>
              <div className='flex gap-2'>
                <Button
                  size='icon'
                  variant='outline'
                  className='rounded-full w-8 h-8'
                  onClick={() => slideVectorSpecific(vector, 'left')}
                  disabled={
                    startIndex === 0 ||
                    caseStudiesForVector.length <= itemsPerPage
                  }
                >
                  <ChevronLeft className='h-4 w-4' />
                </Button>
                <Button
                  size='icon'
                  variant='outline'
                  className='rounded-full w-8 h-8'
                  onClick={() => slideVectorSpecific(vector, 'right')}
                  disabled={
                    startIndex >= caseStudiesForVector.length - itemsPerPage ||
                    caseStudiesForVector.length <= itemsPerPage
                  }
                >
                  <ChevronRight className='h-4 w-4' />
                </Button>
              </div>
            </div>

            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
              {caseStudiesForVector.length > 0 ? (
                caseStudiesForVector
                  .slice(startIndex, startIndex + itemsPerPage)
                  .map(renderCaseStudyCard)
              ) : (
                <div className='col-span-4 text-center py-8'>
                  <p className='text-gray-500'>
                    No case studies found for {vector}.
                  </p>
                  <Button asChild className='mt-4'>
                    <Link href='/dashboard/user/case-studies'>
                      Browse All Case Studies
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        );
      })}

      {/* Your Bookmarks Section */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <h2 className='text-xl font-medium'>Your Bookmarks</h2>
            <Button
              size='icon'
              variant='ghost'
              className='rounded-full w-6 h-6 bg-gray-100'
            >
              <span className='text-xs text-blue-600 font-medium'>i</span>
            </Button>
          </div>
          {bookmarkedCaseStudies.length > 4 && (
            <Link href='/dashboard/trends'>
              <Button variant='outline' className='text-blue-600'>
                View All
              </Button>
            </Link>
          )}
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
          {bookmarkedCaseStudies.length > 0 ? (
            bookmarkedCaseStudies.slice(0, 4).map(renderCaseStudyCard)
          ) : (
            <div className='col-span-4 text-center py-8'>
              <p className='text-gray-500'>
                You haven't bookmarked any case studies yet.
              </p>
              <Button asChild className='mt-4'>
                <Link href='/dashboard/user/case-studies'>
                  Browse Case Studies
                </Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
