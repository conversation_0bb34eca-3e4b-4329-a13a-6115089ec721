import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { teams } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { stripe } from '@/lib/payments/stripe';

// GET /api/subscriptions/force-refresh - Force refresh subscription data from Stripe
export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's team
    const team = await getTeamForUser(user.id);
    if (!team) {
      return NextResponse.json({ error: 'Team not found' }, { status: 404 });
    }

    // If the team doesn't have a Stripe subscription ID, there's nothing to refresh
    if (!team.stripeSubscriptionId) {
      return NextResponse.json({ 
        message: 'No active subscription to refresh',
        team: {
          id: team.id,
          name: team.name,
          planName: team.planName,
          subscriptionStatus: team.subscriptionStatus,
        }
      });
    }

    // Fetch the subscription directly from Stripe
    try {
      const stripeSubscription = await stripe.subscriptions.retrieve(
        team.stripeSubscriptionId,
        {
          expand: ['items.data.price.product'],
        }
      );

      console.log('Force refreshed subscription from Stripe:', {
        id: stripeSubscription.id,
        status: stripeSubscription.status,
        items: stripeSubscription.items.data.length
      });

      // Get product details
      let productId = null;
      let productName = null;

      if (stripeSubscription.items.data.length > 0) {
        const item = stripeSubscription.items.data[0];
        if (item.price && item.price.product) {
          if (typeof item.price.product === 'string') {
            productId = item.price.product;
            try {
              const product = await stripe.products.retrieve(productId);
              
              // First check for plan_type in metadata
              if (product.metadata && product.metadata.plan_type) {
                productName = product.metadata.plan_type.toLowerCase();
                console.log('Using plan_type from product metadata:', productName);
              } else {
                // Fall back to product name
                productName = product.name.toLowerCase();
                console.log('Using product name (no metadata):', productName);
                
                // Normalize the plan name based on product name
                if (productName.includes('starter')) {
                  productName = 'starter';
                } else if (productName.includes('pro')) {
                  productName = 'pro';
                }
              }
              
              console.log('Retrieved product from Stripe:', {
                id: productId,
                name: productName,
                metadata: product.metadata
              });
            } catch (error) {
              console.error('Error retrieving product:', error);
            }
          } else {
            productId = item.price.product.id;
            
            // First check for plan_type in metadata
            if (item.price.product.metadata && item.price.product.metadata.plan_type) {
              productName = item.price.product.metadata.plan_type.toLowerCase();
              console.log('Using plan_type from expanded product metadata:', productName);
            } else {
              // Fall back to product name
              productName = item.price.product.name.toLowerCase();
              console.log('Using expanded product name (no metadata):', productName);
              
              // Normalize the plan name based on product name
              if (productName.includes('starter')) {
                productName = 'starter';
              } else if (productName.includes('pro')) {
                productName = 'pro';
              }
            }
            
            console.log('Using expanded product from Stripe:', {
              id: productId,
              name: productName,
              metadata: item.price.product.metadata
            });
          }
        }
      }

      // Always update the team record with the latest data from Stripe
      console.log('Updating team record with force-refreshed data:', {
        subscriptionStatus: stripeSubscription.status,
        productId,
        productName
      });

      await db
        .update(teams)
        .set({
          subscriptionStatus: stripeSubscription.status,
          ...(productId && { stripeProductId: productId }),
          ...(productName && { planName: productName }),
          updatedAt: new Date(),
        })
        .where(eq(teams.id, team.id));

      // Return the updated team data
      return NextResponse.json({
        message: 'Subscription data refreshed from Stripe',
        subscription: {
          id: stripeSubscription.id,
          status: stripeSubscription.status,
          productId,
          productName
        },
        team: {
          id: team.id,
          name: team.name,
          planName: productName || team.planName,
          subscriptionStatus: stripeSubscription.status,
          stripeSubscriptionId: team.stripeSubscriptionId,
          stripeCustomerId: team.stripeCustomerId,
        },
      });
    } catch (error) {
      console.error('Error retrieving subscription from Stripe:', error);
      return NextResponse.json({ 
        error: 'Failed to retrieve subscription from Stripe',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error force-refreshing subscription:', error);
    return NextResponse.json({ 
      error: 'Failed to force-refresh subscription',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
