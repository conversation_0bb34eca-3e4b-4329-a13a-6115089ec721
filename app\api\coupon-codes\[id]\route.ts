import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { couponCodes } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';
import { z } from 'zod';

// Schema for validating coupon code updates
const couponCodeUpdateSchema = z.object({
  code: z.string().min(3).max(50).optional(),
  discountType: z.enum(['percentage', 'fixed']).optional(),
  discountAmount: z.number().positive().optional(),
  description: z.string().optional(),
  maxUses: z.number().int().positive().optional().nullable(),
  // More flexible date validation
  validFrom: z.string().refine(
    (val) => {
      try {
        new Date(val);
        return true;
      } catch (e) {
        return false;
      }
    },
    { message: "Invalid date format for validFrom" }
  ).optional(),
  // More flexible date validation with nullable
  validUntil: z.string().refine(
    (val) => {
      try {
        new Date(val);
        return true;
      } catch (e) {
        return false;
      }
    },
    { message: "Invalid date format for validUntil" }
  ).nullable().optional(),
  isActive: z.boolean().optional(),
});

// GET a specific coupon code by ID (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated. Please sign in.' }, { status: 401 });
    }

    if (user.role !== 'owner') {
      return NextResponse.json({ error: 'Only owners can manage coupon codes.' }, { status: 403 });
    }

    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    try {
      const coupon = await db.select().from(couponCodes).where(eq(couponCodes.id, id)).limit(1);

      if (!coupon.length) {
        return NextResponse.json({ error: 'Coupon code not found' }, { status: 404 });
      }

      return NextResponse.json(coupon[0]);
    } catch (dbError) {
      console.error('Database error fetching coupon code:', dbError);
      return NextResponse.json({ error: 'Database error fetching coupon code' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error fetching coupon code:', error);
    return NextResponse.json({ error: 'Failed to fetch coupon code' }, { status: 500 });
  }
}

// PATCH update a coupon code (admin only)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated. Please sign in.' }, { status: 401 });
    }

    if (user.role !== 'owner') {
      return NextResponse.json({ error: 'Only owners can manage coupon codes.' }, { status: 403 });
    }

    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    const body = await request.json();

    // Validate the request body
    let validatedData;
    try {
      validatedData = couponCodeUpdateSchema.parse(body);
      console.log('Validated data for update:', validatedData);
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        console.error('Validation error:', validationError.errors);
        return NextResponse.json({
          error: 'Validation error',
          details: validationError.errors
        }, { status: 400 });
      }
      throw validationError;
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (validatedData.code !== undefined) {
      updateData.code = validatedData.code.toUpperCase();
    }

    if (validatedData.discountType !== undefined) {
      updateData.discountType = validatedData.discountType;
    }

    if (validatedData.discountAmount !== undefined) {
      updateData.discountAmount = validatedData.discountAmount;
    }

    if (validatedData.description !== undefined) {
      updateData.description = validatedData.description;
    }

    if (validatedData.maxUses !== undefined) {
      updateData.maxUses = validatedData.maxUses;
    }

    if (validatedData.validFrom !== undefined) {
      updateData.validFrom = new Date(validatedData.validFrom);
    }

    if (validatedData.validUntil !== undefined) {
      updateData.validUntil = validatedData.validUntil ? new Date(validatedData.validUntil) : null;
    }

    if (validatedData.isActive !== undefined) {
      updateData.isActive = validatedData.isActive;
    }

    try {
      const updatedCoupon = await db.update(couponCodes)
        .set(updateData)
        .where(eq(couponCodes.id, id))
        .returning();

      if (!updatedCoupon.length) {
        return NextResponse.json({ error: 'Coupon code not found' }, { status: 404 });
      }

      return NextResponse.json(updatedCoupon[0]);
    } catch (dbError) {
      console.error('Database error updating coupon code:', dbError);
      return NextResponse.json({ error: 'Database error updating coupon code' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error updating coupon code:', error);

    // This catch block should only handle non-validation errors now
    // since we're handling validation errors earlier
    return NextResponse.json({
      error: 'Failed to update coupon code',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// DELETE a coupon code (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated. Please sign in.' }, { status: 401 });
    }

    if (user.role !== 'owner') {
      return NextResponse.json({ error: 'Only owners can manage coupon codes.' }, { status: 403 });
    }

    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    try {
      const deletedCoupon = await db.delete(couponCodes)
        .where(eq(couponCodes.id, id))
        .returning();

      if (!deletedCoupon.length) {
        return NextResponse.json({ error: 'Coupon code not found' }, { status: 404 });
      }

      return NextResponse.json({ message: 'Coupon code deleted successfully' });
    } catch (dbError) {
      console.error('Database error deleting coupon code:', dbError);
      return NextResponse.json({ error: 'Database error deleting coupon code' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error deleting coupon code:', error);
    return NextResponse.json({ error: 'Failed to delete coupon code' }, { status: 500 });
  }
}
