import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { teamMembers, invitations, users } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { eq, and } from 'drizzle-orm';
import { sendTeamInvitation } from '@/lib/email/send-invitation';
import { sendExistingUserInvitation } from '@/lib/email/send-invitation';

// POST /api/team/invite - Invite a new team member
export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a team owner
    const userTeamMember = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, session.user.id),
        eq(teamMembers.role, 'owner')
      ),
      with: {
        team: true,
      },
    });

    if (!userTeamMember?.team) {
      return NextResponse.json(
        { error: 'Team not found or you are not an owner' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { email, role } = body;

    if (!email || !role) {
      return NextResponse.json(
        { error: 'Email and role are required' },
        { status: 400 }
      );
    }

    if (role !== 'member' && role !== 'owner' && role !== 'viewer') {
      return NextResponse.json({ error: 'Invalid role' }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, email),
    });

    if (existingUser) {
      // Check if user is already a member of the team
      const existingMember = await db.query.teamMembers.findFirst({
        where: and(
          eq(teamMembers.userId, existingUser.id),
          eq(teamMembers.teamId, userTeamMember.team.id)
        ),
      });

      if (existingMember) {
        // If the user is already a member but with a different role, update their role
        if (existingMember.role !== role) {
          await db
            .update(teamMembers)
            .set({ role })
            .where(eq(teamMembers.id, existingMember.id));

          // Send notification email about role change
          await sendExistingUserInvitation(0, existingUser);

          return NextResponse.json({
            success: `User's role has been updated to ${role}`,
            roleUpdated: true,
          });
        }

        return NextResponse.json(
          { error: 'User is already a member of this team' },
          { status: 400 }
        );
      }
    }

    // Check if there's an existing invitation
    const existingInvitation = await db.query.invitations.findFirst({
      where: and(
        eq(invitations.email, email),
        eq(invitations.teamId, userTeamMember.team.id),
        eq(invitations.status, 'pending')
      ),
    });

    if (existingInvitation) {
      return NextResponse.json(
        { error: 'An invitation has already been sent to this email' },
        { status: 400 }
      );
    }

    // Create a new invitation
    const newInvitation = await db
      .insert(invitations)
      .values({
        teamId: userTeamMember.team.id,
        email,
        role,
        invitedBy: session.user.id,
        status: 'pending',
      })
      .returning();

    let emailSent = false;

    // If the user already exists, send them a special email for existing users
    if (existingUser) {
      // Check if the user is already a member of the team
      const existingMember = await db.query.teamMembers.findFirst({
        where: and(
          eq(teamMembers.userId, existingUser.id),
          eq(teamMembers.teamId, userTeamMember.team.id)
        ),
      });

      if (existingMember) {
        // If the user is already a member, update their role
        if (existingMember.role !== role) {
          await db
            .update(teamMembers)
            .set({ role })
            .where(eq(teamMembers.id, existingMember.id));

          // If the role is owner, also update the user's global role
          // if (role === 'owner') {
          await db
            .update(users)
            .set({ role: role === 'member' ? 'teamMember' : role })
            .where(eq(users.id, existingUser.id));

          //   console.log(`Updated user ${existingUser.id} global role to owner`);
          // }

          emailSent = await sendExistingUserInvitation(
            newInvitation[0].id,
            existingUser
          );

          // Mark the invitation as accepted
          await db
            .update(invitations)
            .set({ status: 'accepted' })
            .where(eq(invitations.id, newInvitation[0].id));

          return NextResponse.json({
            success: `User's role has been updated to ${role}`,
            roleUpdated: true,
            emailSent,
          });
        } else {
          // User already has the requested role
          return NextResponse.json(
            {
              error:
                'User is already a member of this team with the requested role',
            },
            { status: 400 }
          );
        }
      } else {
        // If the user exists but is not a team member, add them directly to the team
        await db.insert(teamMembers).values({
          userId: existingUser.id,
          teamId: userTeamMember.team.id,
          role,
        });

        // If the role is owner, also update the user's global role
        // if (role === 'owner') {
        await db
          .update(users)
          .set({ role: role === 'member' ? 'teamMember' : role })
          .where(eq(users.id, existingUser.id));

        //   console.log(`Updated user ${existingUser.id} global role to owner`);
        // }

        emailSent = await sendExistingUserInvitation(
          newInvitation[0].id,
          existingUser
        );

        // Mark the invitation as accepted
        await db
          .update(invitations)
          .set({ status: 'accepted' })
          .where(eq(invitations.id, newInvitation[0].id));

        return NextResponse.json({
          success: emailSent
            ? 'User already exists and has been added to the team. Notification email sent.'
            : 'User already exists and has been added to the team, but notification email could not be sent.',
          userAdded: true,
          emailSent,
        });
      }
    } else {
      // Send regular invitation email for new users
      emailSent = await sendTeamInvitation(newInvitation[0].id);

      return NextResponse.json({
        success: emailSent
          ? 'Invitation sent successfully'
          : 'Invitation created but email could not be sent',
        invitation: newInvitation[0],
        emailSent,
      });
    }
  } catch (error) {
    console.error('Error inviting team member:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
