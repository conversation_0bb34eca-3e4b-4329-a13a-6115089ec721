'use client';

import { useState, use, useEffect } from 'react';
import { useUser } from '@/lib/auth';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, RefreshCw, AlertTriangle, Lock, Crown } from 'lucide-react';
import { getValidationErrorMessage } from '@/lib/auth/post-payment-handler';

export default function BillingPage() {
  const { userPromise } = useUser();
  const user = use(userPromise);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const [teamData, setTeamData] = useState<any>(null);
  const [isLoadingTeam, setIsLoadingTeam] = useState(true);
  const [subscriptionError, setSubscriptionError] = useState<{
    type: string;
    feature?: string;
    redirect?: string;
  } | null>(null);

  // Redirect owners to dashboard as they shouldn't access billing
  if (user?.role === 'owner') {
    router.push('/dashboard/owner');
    return null;
  }

  // Fetch team data - placeholder, actual implementation is below

  // Handle manual refresh of subscription data
  const handleRefreshSubscription = async () => {
    setIsLoading(true);
    try {
      // Use the new sync service for better reliability
      const response = await fetch('/api/subscriptions/sync', {
        method: 'POST',
      });

      const data = await response.json();
console.log('Sync response:', data);
      if (data.success) {
        // After sync, get the updated team data
        await fetchTeamData(false);
        toast({
          title: 'Sync Successful',
          description: data.message || 'Subscription information synced with Stripe.',
          variant: 'default',
        });

        // Show validation issues if any
        if (data.validation && !data.validation.isValid) {
          console.warn('Subscription validation issues:', data.validation.issues);
        }
      } else {
        toast({
          title: 'Sync Failed',
          description: data.error || 'Failed to sync subscription information.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error syncing subscription data:', error);
      toast({
        title: 'Error',
        description: 'Failed to sync subscription information.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Simplified: Only handle subscription data refresh
  const refreshSubscriptionData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/user/subscription', {
        method: 'POST', // POST to trigger refresh
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Subscription Refreshed',
          description: 'Your subscription data has been updated.',
          variant: 'default',
        });

        // Refresh the page data
        await fetchTeamData(false);
      } else {
        toast({
          title: 'Refresh Failed',
          description: data.message || 'Failed to refresh subscription data.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error refreshing subscription:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh subscription data.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle customer portal access
  const handleManageSubscription = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/subscriptions/upgrade', {
        method: 'PUT',
      });

      const data = await response.json();

      if (data.success && data.portalUrl) {
        window.location.href = data.portalUrl;
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to open billing portal.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error opening billing portal:', error);
      toast({
        title: 'Error',
        description: 'Failed to open billing portal.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Define fetchTeamData function before using it in useEffect
  const fetchTeamData = async (showLoadingState = true) => {
    try {
      if (showLoadingState) {
        setIsLoadingTeam(true);
      }

      // Use the subscription API for consistent data
      const response = await fetch('/api/user/subscription');
      if (response.ok) {
        const data = await response.json();

        // Handle the case where user has no subscription
        if (!data.hasSubscription) {
          console.log('Billing Page - No subscription found, redirecting to pricing');
          if (data.redirectTo) {
            router.push(data.redirectTo);
          }
          return;
        }

        // Extract team data from subscription response
        const teamData = {
          id: data.team?.id,
          name: data.team?.name,
          planName: data.subscription?.planName,
          subscriptionStatus: data.subscription?.status,
          stripeCustomerId: data.subscription?.stripeCustomerId,
          stripeSubscriptionId: data.subscription?.stripeSubscriptionId,
          dataInconsistency: data.subscription?.dataInconsistency
        };

        console.log('Billing Page - Subscription data from API:', {
          hasSubscription: data.hasSubscription,
          planName: data.subscription?.planName,
          subscriptionStatus: data.subscription?.status,
          stripeCustomerId: data.subscription?.stripeCustomerId,
          stripeSubscriptionId: data.subscription?.stripeSubscriptionId,
          dataInconsistency: data.subscription?.dataInconsistency
        });

        // Set the team data state
        setTeamData(teamData);

        // Log team data for debugging
        console.log('Billing Page - Final team data:', teamData);
        console.log('Billing Page - Subscription status:', teamData.subscriptionStatus);
        console.log('Billing Page - Plan name:', teamData.planName);
      } else {
        console.error('Failed to fetch team data');
        toast({
          title: 'Error',
          description: 'Failed to fetch subscription information. Please refresh the page.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching team data:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while loading your subscription details.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingTeam(false);
    }
  };

  // Initialize the component
  useEffect(() => {
    // Define an async function inside the useEffect
    const initializeComponent = async () => {
      // Check for error parameters in URL (from redirects)
      const url = new URL(window.location.href);
      const errorParam = url.searchParams.get('error');
      const messageParam = url.searchParams.get('message');
      const fromStripe = url.searchParams.get('fromStripe') === 'true';
      const paymentSuccess = url.searchParams.get('payment_success') === 'true';

      if (errorParam) {
        if (errorParam === 'already-subscribed') {
          toast({
            title: 'Already Subscribed',
            description: 'You are already subscribed to this plan.',
            variant: 'default',
          });
        } else if (errorParam === 'subscription-failed') {
          toast({
            title: 'Subscription Failed',
            description: messageParam || 'Failed to update subscription. Please try again.',
            variant: 'destructive',
          });
        } else if (errorParam === 'subscription-required') {
          setSubscriptionError({
            type: 'subscription-required',
            redirect: url.searchParams.get('redirect') || undefined,
          });
        } else if (errorParam === 'feature-restricted') {
          setSubscriptionError({
            type: 'feature-restricted',
            feature: url.searchParams.get('feature') || undefined,
            redirect: url.searchParams.get('redirect') || undefined,
          });
        } else if (errorParam === 'past-due') {
          setSubscriptionError({
            type: 'past-due',
            redirect: url.searchParams.get('redirect') || undefined,
          });
        } else if (errorParam === 'inactive') {
          setSubscriptionError({
            type: 'inactive',
            redirect: url.searchParams.get('redirect') || undefined,
          });
        } else if (errorParam === 'validation-failed') {
          const errorMessage = getValidationErrorMessage('validation-failed');
          toast({
            title: 'Validation Error',
            description: errorMessage,
            variant: 'destructive',
          });
        } else if (errorParam === 'no-team') {
          const errorMessage = getValidationErrorMessage('no-team');
          toast({
            title: 'Team Not Found',
            description: errorMessage,
            variant: 'destructive',
          });
        }

        // Remove the error parameters from the URL
        url.searchParams.delete('error');
        url.searchParams.delete('message');
        url.searchParams.delete('feature');
        url.searchParams.delete('redirect');
        window.history.replaceState({}, '', url.toString());
      }

      // Handle payment success
      if (paymentSuccess) {
        toast({
          title: 'Payment Successful!',
          description: 'Your subscription has been activated. Welcome to your new plan!',
          variant: 'default',
        });

        // Remove the payment_success parameter from the URL
        url.searchParams.delete('payment_success');
        window.history.replaceState({}, '', url.toString());

        // Force refresh subscription data
        await fetchTeamData();
      }

      // If coming back from Stripe, force a refresh of subscription data
      if (fromStripe) {
        console.log('Detected return from Stripe, forcing subscription data refresh');

        // Show a toast to let the user know we're refreshing
        toast({
          title: 'Refreshing Subscription',
          description: 'Updating your subscription information from Stripe...',
          variant: 'default',
        });

        // Remove the fromStripe parameter from the URL
        url.searchParams.delete('fromStripe');
        window.history.replaceState({}, '', url.toString());

        // Force a hard refresh from Stripe first
        try {
          const response = await fetch(`/api/subscriptions/force-refresh?_=${new Date().getTime()}`);
          if (response.ok) {
            console.log('Successfully force-refreshed subscription data from Stripe');
          } else {
            console.warn('Failed to force-refresh subscription data from Stripe');
          }
        } catch (error) {
          console.error('Error force-refreshing subscription data:', error);
        }

        // Then fetch the team data
        await fetchTeamData();

        // Set a timeout to refresh again after 3 seconds
        // This is because sometimes Stripe webhooks take a moment to process
        setTimeout(() => {
          console.log('Performing second refresh after Stripe return');
          fetchTeamData(false);
        }, 3000);
      } else {
        // Normal page load, just fetch data once
        await fetchTeamData();
      }
    };

    // Call the async function
    initializeComponent();

    // Set up an interval to refresh the team data every 30 seconds
    // This is less frequent but still ensures the UI stays reasonably in sync
    const intervalId = setInterval(() => fetchTeamData(false), 30000);

    // Clean up the interval when the component unmounts
    return () => clearInterval(intervalId);
  }, [toast]); // Only include toast as a dependency





  const currentPlan = teamData?.planName?.toLowerCase() || null;
  const subscriptionStatus = teamData?.subscriptionStatus || null;

  // Determine if the user has an active subscription
  const hasActiveSubscription = subscriptionStatus === 'active' || subscriptionStatus === 'trialing';

  // Determine if the user has a Stripe customer ID
  const hasStripeCustomerId = !!teamData?.stripeCustomerId;

  // Log subscription status for debugging
  console.log('Subscription status:', {
    hasActiveSubscription,
    hasStripeCustomerId,
    currentPlan,
    status: subscriptionStatus
  });

  // Function to render subscription error alerts
  const renderSubscriptionError = () => {
    if (!subscriptionError) return null;

    const featureNames: Record<string, string> = {
      advancedAnalytics: 'Advanced Analytics',
      apiAccess: 'API Access',
      customBranding: 'Custom Branding',
      prioritySupport: 'Priority Support',
      maxUsers: 'Multiple Users',
      maxCaseStudies: 'Unlimited Case Studies',
      maxStorage: 'Additional Storage',
    };

    switch (subscriptionError.type) {
      case 'subscription-required':
        return (
          <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
            <Lock className="h-4 w-4" />
            <AlertTitle className="text-orange-800 dark:text-orange-200">
              Subscription Required
            </AlertTitle>
            <AlertDescription className="text-orange-700 dark:text-orange-300">
              You need an active subscription to access this feature.
              {subscriptionError.redirect && (
                <span className="block mt-2 text-sm">
                  You were trying to access: <code className="bg-orange-100 dark:bg-orange-900 px-1 rounded">{subscriptionError.redirect}</code>
                </span>
              )}
            </AlertDescription>
          </Alert>
        );

      case 'feature-restricted':
        return (
          <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
            <Crown className="h-4 w-4" />
            <AlertTitle className="text-blue-800 dark:text-blue-200">
              Premium Feature Required
            </AlertTitle>
            <AlertDescription className="text-blue-700 dark:text-blue-300">
              {subscriptionError.feature && featureNames[subscriptionError.feature]
                ? `${featureNames[subscriptionError.feature]} is not available in your current plan.`
                : 'This feature is not available in your current plan.'
              }
              {subscriptionError.redirect && (
                <span className="block mt-2 text-sm">
                  You were trying to access: <code className="bg-blue-100 dark:bg-blue-900 px-1 rounded">{subscriptionError.redirect}</code>
                </span>
              )}
            </AlertDescription>
          </Alert>
        );

      case 'past-due':
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Payment Past Due</AlertTitle>
            <AlertDescription>
              Your subscription payment is past due. Please update your payment method to continue using premium features.
              {subscriptionError.redirect && (
                <span className="block mt-2 text-sm">
                  You were trying to access: <code className="bg-red-100 dark:bg-red-900 px-1 rounded">{subscriptionError.redirect}</code>
                </span>
              )}
            </AlertDescription>
          </Alert>
        );

      case 'inactive':
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Subscription Inactive</AlertTitle>
            <AlertDescription>
              Your subscription is inactive. Please subscribe to a plan to access premium features.
              {subscriptionError.redirect && (
                <span className="block mt-2 text-sm">
                  You were trying to access: <code className="bg-red-100 dark:bg-red-900 px-1 rounded">{subscriptionError.redirect}</code>
                </span>
              )}
            </AlertDescription>
          </Alert>
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8 dark:text-white">Billing & Subscription</h1>

      {/* Subscription Error Alert */}
      {subscriptionError && (
        <div className="mb-6">
          {renderSubscriptionError()}
        </div>
      )}

      {isLoadingTeam ? (
        <div className="flex items-center justify-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-primary" />
        </div>
      ) : (
        <div className="space-y-8">
          {/* Current Plan */}
          <Card className="dark:bg-black dark:border-white/20">
            <CardHeader>
              <CardTitle className="dark:text-white">Current Plan</CardTitle>
              <CardDescription className="dark:text-white">
                Manage your subscription and billing details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground dark:text-white/70">Plan</h3>
                    <p className="text-lg font-semibold dark:text-white">
                      {currentPlan ? currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1) : 'No active plan'}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground dark:text-white/70">Status</h3>
                    <p className="text-lg font-semibold capitalize dark:text-white">
                      {subscriptionStatus || 'Not subscribed'}
                    </p>
                  </div>
                </div>

                {hasActiveSubscription ? (
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button
                      variant="outline"
                      onClick={handleManageSubscription}
                      disabled={isLoading}
                      className="dark:bg-black dark:text-white dark:hover:bg-white/10 dark:border-white/20"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        'Manage Subscription'
                      )}
                    </Button>
                    <Button
                      variant="secondary"
                      onClick={handleRefreshSubscription}
                      disabled={isLoading}
                      title="Refresh subscription data from Stripe"
                      className="dark:bg-white/10 dark:text-white dark:hover:bg-white/20"
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                    <div className="text-xs text-muted-foreground dark:text-white/70 mt-1">
                      <em>Note: If you made changes in Stripe, click the refresh button to see the latest status.</em>
                    </div>
                  </div>
                ) : hasStripeCustomerId ? (
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground dark:text-white/70">
                      Your subscription is {subscriptionStatus || 'inactive'}. You can reactivate or change your plan below.
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Button
                        variant="outline"
                        onClick={handleManageSubscription}
                        disabled={isLoading}
                        className="dark:bg-black dark:text-white dark:hover:bg-white/10 dark:border-white/20"
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Loading...
                          </>
                        ) : (
                          'Manage Billing'
                        )}
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={handleRefreshSubscription}
                        disabled={isLoading}
                        title="Refresh subscription data from Stripe"
                        className="dark:bg-white/10 dark:text-white dark:hover:bg-white/20"
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground dark:text-white/70">
                    No subscription found. Select a plan below to get started.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Simple Plan Information - No Upgrade/Downgrade */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-2xl font-bold mb-6 dark:text-white">Plan Information</h2>
            <div className="text-center py-8">
              <div className="mb-4">
                <Crown className="h-12 w-12 mx-auto text-yellow-500 mb-2" />
                <h3 className="text-xl font-semibold dark:text-white">
                  {teamData?.planName ? `${teamData.planName.charAt(0).toUpperCase() + teamData.planName.slice(1)} Plan` : 'No Plan'}
                </h3>
              </div>

              {!teamData?.planName && !hasActiveSubscription ? (
                <div className="space-y-4">
                  <p className="text-gray-600 dark:text-gray-400">
                    You don't have an active subscription plan.
                  </p>
                  <Button
                    onClick={() => router.push('/pricing')}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Choose a Plan
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {teamData?.planName ? (
                    <>
                      <p className="text-gray-600 dark:text-gray-400">
                        You're currently on the {teamData.planName} plan.
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        To change your plan, please contact our support team.
                      </p>
                    </>
                  ) : hasActiveSubscription ? (
                    <>
                      <p className="text-gray-600 dark:text-gray-400">
                        You have an active subscription, but plan details are not synced.
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Click "Sync Subscription" to update your plan information.
                      </p>
                    </>
                  ) : (
                    <p className="text-gray-600 dark:text-gray-400">
                      Loading subscription information...
                    </p>
                  )}

                  {teamData?.dataInconsistency && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        ⚠️ Data inconsistency detected. Your plan information may need to be synced.
                      </p>
                    </div>
                  )}

                  <div className="flex gap-3 justify-center">
                    <Button
                      variant="outline"
                      onClick={() => router.push('/contact')}
                    >
                      Contact Support
                    </Button>
                    <Button
                      variant="outline"
                      onClick={refreshSubscriptionData}
                      disabled={isLoading}
                    >
                      {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
                      {hasActiveSubscription && !teamData?.planName ? 'Sync Subscription' : 'Refresh Data'}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
