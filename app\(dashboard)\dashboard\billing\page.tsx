'use client';

import { useState, use, useEffect } from 'react';
import { useUser } from '@/lib/auth';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { PlanUpgrade } from '@/components/PlanUpgrade/PlanUpgrade';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, RefreshCw, AlertTriangle, Lock, Crown, CheckCircle } from 'lucide-react';
import { getValidationErrorMessage } from '@/lib/auth/post-payment-handler';

export default function BillingPage() {
  const { userPromise } = useUser();
  const user = use(userPromise);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const [teamData, setTeamData] = useState<any>(null);
  const [isLoadingTeam, setIsLoadingTeam] = useState(true);
  const [subscriptionError, setSubscriptionError] = useState<{
    type: string;
    feature?: string;
    redirect?: string;
  } | null>(null);

  // Redirect owners to dashboard as they shouldn't access billing
  if (user?.role === 'owner') {
    router.push('/dashboard/owner');
    return null;
  }

  // Fetch team data - placeholder, actual implementation is below

  // Handle manual refresh of subscription data
  const handleRefreshSubscription = async () => {
    setIsLoading(true);
    try {
      // Use the new sync service for better reliability
      const response = await fetch('/api/subscriptions/sync', {
        method: 'POST',
      });

      const data = await response.json();
console.log('Sync response:', data);
      if (data.success) {
        // After sync, get the updated team data
        await fetchTeamData(false);
        toast({
          title: 'Sync Successful',
          description: data.message || 'Subscription information synced with Stripe.',
          variant: 'default',
        });

        // Show validation issues if any
        if (data.validation && !data.validation.isValid) {
          console.warn('Subscription validation issues:', data.validation.issues);
        }
      } else {
        toast({
          title: 'Sync Failed',
          description: data.error || 'Failed to sync subscription information.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error syncing subscription data:', error);
      toast({
        title: 'Error',
        description: 'Failed to sync subscription information.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle subscription upgrade with industry best practices
  const handleUpgrade = async (plan: 'starter' | 'pro' | 'enterprise', billingCycle: 'monthly' | 'yearly' = 'monthly') => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/subscriptions/upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan, billingCycle }),
      });

      const data = await response.json();

      if (data.success) {
        if (data.requiresPayment && data.checkoutUrl) {
          // Redirect to Stripe Checkout
          window.location.href = data.checkoutUrl;
        } else {
          // Subscription updated without payment
          toast({
            title: 'Upgrade Successful',
            description: data.message,
            variant: 'default',
          });

          // Refresh data after upgrade
          await fetchTeamData(false);
        }
      } else {
        toast({
          title: 'Upgrade Failed',
          description: data.message || 'Failed to upgrade subscription.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      toast({
        title: 'Error',
        description: 'Failed to process upgrade request.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle customer portal access
  const handleManageSubscription = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/subscriptions/upgrade', {
        method: 'PUT',
      });

      const data = await response.json();

      if (data.success && data.portalUrl) {
        window.location.href = data.portalUrl;
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to open billing portal.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error opening billing portal:', error);
      toast({
        title: 'Error',
        description: 'Failed to open billing portal.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Define fetchTeamData function before using it in useEffect
  const fetchTeamData = async (showLoadingState = true) => {
    try {
      if (showLoadingState) {
        setIsLoadingTeam(true);
      }

      // First, try to get the team data
      const response = await fetch('/api/user/team');
      if (response.ok) {
        const data = await response.json();

        console.log('Team data from API:', {
          teamId: data.id,
          planName: data.planName,
          subscriptionStatus: data.subscriptionStatus,
          stripeCustomerId: data.stripeCustomerId,
          stripeSubscriptionId: data.stripeSubscriptionId,
          stripeProductId: data.stripeProductId
        });

        // Also fetch the subscription details from Stripe
        try {
          // Add a cache-busting parameter to ensure we get fresh data
          const timestamp = new Date().getTime();
          const subscriptionResponse = await fetch(`/api/subscriptions?_=${timestamp}`);
          if (subscriptionResponse.ok) {
            const subscriptionData = await subscriptionResponse.json();

            console.log('Subscription data from API:', {
              subscription: subscriptionData.subscription,
              team: subscriptionData.team
            });

            // If we have Stripe subscription data, use it to update our local state
            if (subscriptionData.stripeSubscription) {
              console.log('Stripe subscription data:', {
                id: subscriptionData.stripeSubscription.id,
                status: subscriptionData.stripeSubscription.status,
                items: subscriptionData.stripeSubscription.items.data.length
              });

              // Update the team data with the latest subscription status from Stripe
              data.subscriptionStatus = subscriptionData.stripeSubscription.status;

              // Get the plan name from the product
              const item = subscriptionData.stripeSubscription.items.data[0];
              if (item && item.price) {
                console.log('Subscription item:', {
                  priceId: item.price.id,
                  product: item.price.product
                });

                if (item.price.product) {
                  let productName;

                  if (typeof item.price.product === 'string') {
                    // Just log the product ID
                    console.log('Product ID from Stripe:', item.price.product);

                    // Use the price ID to determine the plan name as a fallback
                    if (item.price.id.includes('starter')) {
                      productName = 'starter';
                    } else if (item.price.id.includes('pro')) {
                      productName = 'pro';
                    }
                  } else if ('name' in item.price.product) {
                    // Use the product name
                    const rawName = item.price.product.name.toLowerCase();
                    console.log('Product name from Stripe:', rawName);

                    if (rawName.includes('starter')) {
                      productName = 'starter';
                    } else if (rawName.includes('pro')) {
                      productName = 'pro';
                    } else {
                      productName = rawName;
                    }
                  }

                  if (productName) {
                    console.log('Setting plan name to:', productName);
                    data.planName = productName;
                  }
                } else {
                  // Fallback to price ID
                  console.log('No product in price, using price ID:', item.price.id);
                  if (item.price.id.includes('starter')) {
                    data.planName = 'starter';
                  } else if (item.price.id.includes('pro')) {
                    data.planName = 'pro';
                  }
                }
              }
            }
          }
        } catch (subscriptionError) {
          console.error('Error fetching subscription data:', subscriptionError);
        }

        setTeamData(data);

        // Log team data for debugging
        console.log('Team data:', data);
        console.log('Subscription status:', data.subscriptionStatus);
        console.log('Plan name:', data.planName);
      } else {
        console.error('Failed to fetch team data');
        toast({
          title: 'Error',
          description: 'Failed to fetch subscription information. Please refresh the page.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching team data:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while loading your subscription details.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingTeam(false);
    }
  };

  // Initialize the component
  useEffect(() => {
    // Define an async function inside the useEffect
    const initializeComponent = async () => {
      // Check for error parameters in URL (from redirects)
      const url = new URL(window.location.href);
      const errorParam = url.searchParams.get('error');
      const messageParam = url.searchParams.get('message');
      const fromStripe = url.searchParams.get('fromStripe') === 'true';
      const paymentSuccess = url.searchParams.get('payment_success') === 'true';

      if (errorParam) {
        if (errorParam === 'already-subscribed') {
          toast({
            title: 'Already Subscribed',
            description: 'You are already subscribed to this plan.',
            variant: 'default',
          });
        } else if (errorParam === 'subscription-failed') {
          toast({
            title: 'Subscription Failed',
            description: messageParam || 'Failed to update subscription. Please try again.',
            variant: 'destructive',
          });
        } else if (errorParam === 'subscription-required') {
          setSubscriptionError({
            type: 'subscription-required',
            redirect: url.searchParams.get('redirect') || undefined,
          });
        } else if (errorParam === 'feature-restricted') {
          setSubscriptionError({
            type: 'feature-restricted',
            feature: url.searchParams.get('feature') || undefined,
            redirect: url.searchParams.get('redirect') || undefined,
          });
        } else if (errorParam === 'past-due') {
          setSubscriptionError({
            type: 'past-due',
            redirect: url.searchParams.get('redirect') || undefined,
          });
        } else if (errorParam === 'inactive') {
          setSubscriptionError({
            type: 'inactive',
            redirect: url.searchParams.get('redirect') || undefined,
          });
        } else if (errorParam === 'validation-failed') {
          const errorMessage = getValidationErrorMessage('validation-failed');
          toast({
            title: 'Validation Error',
            description: errorMessage,
            variant: 'destructive',
          });
        } else if (errorParam === 'no-team') {
          const errorMessage = getValidationErrorMessage('no-team');
          toast({
            title: 'Team Not Found',
            description: errorMessage,
            variant: 'destructive',
          });
        }

        // Remove the error parameters from the URL
        url.searchParams.delete('error');
        url.searchParams.delete('message');
        url.searchParams.delete('feature');
        url.searchParams.delete('redirect');
        window.history.replaceState({}, '', url.toString());
      }

      // Handle payment success
      if (paymentSuccess) {
        toast({
          title: 'Payment Successful!',
          description: 'Your subscription has been activated. Welcome to your new plan!',
          variant: 'default',
        });

        // Remove the payment_success parameter from the URL
        url.searchParams.delete('payment_success');
        window.history.replaceState({}, '', url.toString());

        // Force refresh subscription data
        await fetchTeamData();
      }

      // If coming back from Stripe, force a refresh of subscription data
      if (fromStripe) {
        console.log('Detected return from Stripe, forcing subscription data refresh');

        // Show a toast to let the user know we're refreshing
        toast({
          title: 'Refreshing Subscription',
          description: 'Updating your subscription information from Stripe...',
          variant: 'default',
        });

        // Remove the fromStripe parameter from the URL
        url.searchParams.delete('fromStripe');
        window.history.replaceState({}, '', url.toString());

        // Force a hard refresh from Stripe first
        try {
          const response = await fetch(`/api/subscriptions/force-refresh?_=${new Date().getTime()}`);
          if (response.ok) {
            console.log('Successfully force-refreshed subscription data from Stripe');
          } else {
            console.warn('Failed to force-refresh subscription data from Stripe');
          }
        } catch (error) {
          console.error('Error force-refreshing subscription data:', error);
        }

        // Then fetch the team data
        await fetchTeamData();

        // Set a timeout to refresh again after 3 seconds
        // This is because sometimes Stripe webhooks take a moment to process
        setTimeout(() => {
          console.log('Performing second refresh after Stripe return');
          fetchTeamData(false);
        }, 3000);
      } else {
        // Normal page load, just fetch data once
        await fetchTeamData();
      }
    };

    // Call the async function
    initializeComponent();

    // Set up an interval to refresh the team data every 30 seconds
    // This is less frequent but still ensures the UI stays reasonably in sync
    const intervalId = setInterval(() => fetchTeamData(false), 30000);

    // Clean up the interval when the component unmounts
    return () => clearInterval(intervalId);
  }, [toast]); // Only include toast as a dependency

  // Handle plan upgrade/downgrade with industry best practices
  const handlePlanChange = async (planId: string, priceId?: string) => {
    // Don't allow changing to the same plan
    if (teamData?.planName?.toLowerCase() === planId &&
        (teamData?.subscriptionStatus === 'active' || teamData?.subscriptionStatus === 'trialing')) {
      toast({
        title: 'Already Subscribed',
        description: `You are already subscribed to the ${planId.charAt(0).toUpperCase() + planId.slice(1)} plan.`,
        variant: 'default',
      });
      return false;
    }

    // Determine if this is an upgrade or downgrade
    const currentPlan = teamData?.planName?.toLowerCase();
    const planOrder = ['starter', 'pro', 'enterprise'];
    const currentIndex = planOrder.indexOf(currentPlan || 'starter');
    const targetIndex = planOrder.indexOf(planId);

    const isUpgrade = targetIndex > currentIndex;
    const isDowngrade = targetIndex < currentIndex;

    // For downgrades, confirm with the user
    if (isDowngrade) {
      const confirmed = window.confirm(
        `Are you sure you want to downgrade from ${currentPlan?.charAt(0).toUpperCase() + currentPlan?.slice(1)} to ${planId.charAt(0).toUpperCase() + planId.slice(1)}? You will lose access to premium features at the end of your current billing period.`
      );
      if (!confirmed) {
        return false;
      }
    }

    // For upgrades, confirm with the user
    if (isUpgrade) {
      const confirmed = window.confirm(
        `Are you sure you want to upgrade to ${planId.charAt(0).toUpperCase() + planId.slice(1)}? You will be charged the difference immediately with proration.`
      );
      if (!confirmed) {
        return false;
      }
    }

    // Use the new upgrade service
    await handleUpgrade(planId as 'starter' | 'pro' | 'enterprise', 'monthly');
    return true;
  };



  const currentPlan = teamData?.planName?.toLowerCase() || null;
  const subscriptionStatus = teamData?.subscriptionStatus || null;

  // Determine if the user has an active subscription
  const hasActiveSubscription = subscriptionStatus === 'active' || subscriptionStatus === 'trialing';

  // Determine if the user has a Stripe customer ID
  const hasStripeCustomerId = !!teamData?.stripeCustomerId;

  // Log subscription status for debugging
  console.log('Subscription status:', {
    hasActiveSubscription,
    hasStripeCustomerId,
    currentPlan,
    status: subscriptionStatus
  });

  // Function to render subscription error alerts
  const renderSubscriptionError = () => {
    if (!subscriptionError) return null;

    const featureNames: Record<string, string> = {
      advancedAnalytics: 'Advanced Analytics',
      apiAccess: 'API Access',
      customBranding: 'Custom Branding',
      prioritySupport: 'Priority Support',
      maxUsers: 'Multiple Users',
      maxCaseStudies: 'Unlimited Case Studies',
      maxStorage: 'Additional Storage',
    };

    switch (subscriptionError.type) {
      case 'subscription-required':
        return (
          <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
            <Lock className="h-4 w-4" />
            <AlertTitle className="text-orange-800 dark:text-orange-200">
              Subscription Required
            </AlertTitle>
            <AlertDescription className="text-orange-700 dark:text-orange-300">
              You need an active subscription to access this feature.
              {subscriptionError.redirect && (
                <span className="block mt-2 text-sm">
                  You were trying to access: <code className="bg-orange-100 dark:bg-orange-900 px-1 rounded">{subscriptionError.redirect}</code>
                </span>
              )}
            </AlertDescription>
          </Alert>
        );

      case 'feature-restricted':
        return (
          <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
            <Crown className="h-4 w-4" />
            <AlertTitle className="text-blue-800 dark:text-blue-200">
              Premium Feature Required
            </AlertTitle>
            <AlertDescription className="text-blue-700 dark:text-blue-300">
              {subscriptionError.feature && featureNames[subscriptionError.feature]
                ? `${featureNames[subscriptionError.feature]} is not available in your current plan.`
                : 'This feature is not available in your current plan.'
              }
              {subscriptionError.redirect && (
                <span className="block mt-2 text-sm">
                  You were trying to access: <code className="bg-blue-100 dark:bg-blue-900 px-1 rounded">{subscriptionError.redirect}</code>
                </span>
              )}
            </AlertDescription>
          </Alert>
        );

      case 'past-due':
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Payment Past Due</AlertTitle>
            <AlertDescription>
              Your subscription payment is past due. Please update your payment method to continue using premium features.
              {subscriptionError.redirect && (
                <span className="block mt-2 text-sm">
                  You were trying to access: <code className="bg-red-100 dark:bg-red-900 px-1 rounded">{subscriptionError.redirect}</code>
                </span>
              )}
            </AlertDescription>
          </Alert>
        );

      case 'inactive':
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Subscription Inactive</AlertTitle>
            <AlertDescription>
              Your subscription is inactive. Please subscribe to a plan to access premium features.
              {subscriptionError.redirect && (
                <span className="block mt-2 text-sm">
                  You were trying to access: <code className="bg-red-100 dark:bg-red-900 px-1 rounded">{subscriptionError.redirect}</code>
                </span>
              )}
            </AlertDescription>
          </Alert>
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8 dark:text-white">Billing & Subscription</h1>

      {/* Subscription Error Alert */}
      {subscriptionError && (
        <div className="mb-6">
          {renderSubscriptionError()}
        </div>
      )}

      {isLoadingTeam ? (
        <div className="flex items-center justify-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-primary" />
        </div>
      ) : (
        <div className="space-y-8">
          {/* Current Plan */}
          <Card className="dark:bg-black dark:border-white/20">
            <CardHeader>
              <CardTitle className="dark:text-white">Current Plan</CardTitle>
              <CardDescription className="dark:text-white">
                Manage your subscription and billing details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground dark:text-white/70">Plan</h3>
                    <p className="text-lg font-semibold dark:text-white">
                      {currentPlan ? currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1) : 'No active plan'}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground dark:text-white/70">Status</h3>
                    <p className="text-lg font-semibold capitalize dark:text-white">
                      {subscriptionStatus || 'Not subscribed'}
                    </p>
                  </div>
                </div>

                {hasActiveSubscription ? (
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button
                      variant="outline"
                      onClick={handleManageSubscription}
                      disabled={isLoading}
                      className="dark:bg-black dark:text-white dark:hover:bg-white/10 dark:border-white/20"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        'Manage Subscription'
                      )}
                    </Button>
                    <Button
                      variant="secondary"
                      onClick={handleRefreshSubscription}
                      disabled={isLoading}
                      title="Refresh subscription data from Stripe"
                      className="dark:bg-white/10 dark:text-white dark:hover:bg-white/20"
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                    <div className="text-xs text-muted-foreground dark:text-white/70 mt-1">
                      <em>Note: If you made changes in Stripe, click the refresh button to see the latest status.</em>
                    </div>
                  </div>
                ) : hasStripeCustomerId ? (
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground dark:text-white/70">
                      Your subscription is {subscriptionStatus || 'inactive'}. You can reactivate or change your plan below.
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Button
                        variant="outline"
                        onClick={handleManageSubscription}
                        disabled={isLoading}
                        className="dark:bg-black dark:text-white dark:hover:bg-white/10 dark:border-white/20"
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Loading...
                          </>
                        ) : (
                          'Manage Billing'
                        )}
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={handleRefreshSubscription}
                        disabled={isLoading}
                        title="Refresh subscription data from Stripe"
                        className="dark:bg-white/10 dark:text-white dark:hover:bg-white/20"
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground dark:text-white/70">
                    No subscription found. Select a plan below to get started.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Available Plans */}
          <div>
            <h2 className="text-2xl font-bold mb-6 dark:text-white">Available Plans</h2>
            <PlanUpgrade
              currentPlan={currentPlan}
              onUpgrade={handlePlanChange}
              subscriptionStatus={subscriptionStatus}
              hasStripeCustomerId={hasStripeCustomerId}
            />
          </div>
        </div>
      )}
    </div>
  );
}
