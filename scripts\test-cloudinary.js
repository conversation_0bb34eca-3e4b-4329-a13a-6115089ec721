// This script tests the Cloudinary upload functionality
const { v2: cloudinary } = require('cloudinary');
const fs = require('fs');
const path = require('path');

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'daf8hefrg',
  api_key: process.env.CLOUDINARY_API_KEY || '614559198916115',
  api_secret: process.env.CLOUDINARY_API_SECRET || 'OV5jQ9RurrjEubiLdOTpUB0VMFo',
});

// Log Cloudinary configuration
console.log('\n========== CLOUDINARY CONFIG ==========');
console.log(`Cloud name: ${cloudinary.config().cloud_name}`);
console.log(`API key: ${cloudinary.config().api_key}`);
console.log(`API secret: ${cloudinary.config().api_secret ? '***' + cloudinary.config().api_secret.toString().substring(cloudinary.config().api_secret.toString().length - 4) : 'Not set'}`);
console.log('========== CLOUDINARY CONFIG END ==========\n');

// Test file path - use a sample image from the public folder
const testFilePath = path.join(__dirname, '..', 'public', 'placeholder-image.jpg');

// Function to test direct upload
async function testDirectUpload() {
  console.log('\n========== TESTING DIRECT UPLOAD ==========');
  console.log(`Test file: ${testFilePath}`);

  try {
    // Read the file
    const fileBuffer = fs.readFileSync(testFilePath);

    // Upload to Cloudinary using the upload_stream method
    const uploadResult = await new Promise((resolve, reject) => {
      const uploadOptions = {
        folder: 'test-uploads',
        upload_preset: 'Path4ai', // Use your upload preset name
      };

      console.log('Upload options:', uploadOptions);

      const uploadStream = cloudinary.uploader.upload_stream(
        uploadOptions,
        (error, result) => {
          if (error) {
            console.error('Error in upload stream:', error);
            reject(error);
            return;
          }

          resolve(result);
        }
      );

      // Write buffer to stream
      uploadStream.write(fileBuffer);
      uploadStream.end();
    });

    console.log('Upload successful!');
    console.log('Result:', {
      url: uploadResult.secure_url,
      public_id: uploadResult.public_id,
      format: uploadResult.format,
      bytes: uploadResult.bytes
    });

    console.log('========== DIRECT UPLOAD TEST COMPLETE ==========\n');
    return uploadResult.secure_url;
  } catch (error) {
    console.error('Error testing direct upload:', error);
    console.log('========== DIRECT UPLOAD TEST FAILED ==========\n');
    throw error;
  }
}

// Function to test signed upload
async function testSignedUpload() {
  console.log('\n========== TESTING SIGNED UPLOAD ==========');
  console.log(`Test file: ${testFilePath}`);

  try {
    // Read the file
    const fileBuffer = fs.readFileSync(testFilePath);

    // Upload to Cloudinary using the uploader.upload method
    const uploadResult = await cloudinary.uploader.upload(testFilePath, {
      folder: 'test-uploads',
    });

    console.log('Upload successful!');
    console.log('Result:', {
      url: uploadResult.secure_url,
      public_id: uploadResult.public_id,
      format: uploadResult.format,
      bytes: uploadResult.bytes
    });

    console.log('========== SIGNED UPLOAD TEST COMPLETE ==========\n');
    return uploadResult.secure_url;
  } catch (error) {
    console.error('Error testing signed upload:', error);
    console.log('========== SIGNED UPLOAD TEST FAILED ==========\n');
    throw error;
  }
}

// Run the tests
async function runTests() {
  try {
    // Test direct upload
    const directUploadUrl = await testDirectUpload();
    console.log(`Direct upload URL: ${directUploadUrl}`);

    // Test signed upload
    const signedUploadUrl = await testSignedUpload();
    console.log(`Signed upload URL: ${signedUploadUrl}`);

    console.log('\n========== ALL TESTS COMPLETED SUCCESSFULLY ==========');
  } catch (error) {
    console.error('Test failed:', error);
    console.log('\n========== TESTS FAILED ==========');
    process.exit(1);
  }
}

// Run the tests
runTests();
