import { NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { db } from '@/lib/db/drizzle';
import {
  caseStudies,
  caseStudyIcons,
  type NewCaseStudy,
} from '@/lib/db/schema';
import { parse } from 'csv-parse/sync';
import { isValidUrl } from '@/lib/utils/url-validator';

export async function POST(req: Request) {
  try {
    // Authentication check
    const session = await getSession();
    console.log(
      'CSV Import - Session:',
      JSON.stringify({
        hasSession: !!session,
        hasUser: !!session?.user,
        userId: session?.user?.id,
        userRole: session?.user?.role,
      })
    );

    if (!session?.user?.id) {
      console.log('CSV Import - Unauthorized: No valid user ID in session');
      return new NextResponse(
        JSON.stringify({
          error: 'Unauthorized',
          message: 'You must be logged in to import case studies',
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    console.log('CSV Import - Authenticated user ID:', session.user.id);

    // Get form data
    let formData;
    try {
      formData = await req.formData();
    } catch (error) {
      console.error('Error parsing form data:', error);
      return new NextResponse(
        JSON.stringify({
          error: 'Invalid request',
          message: 'Could not parse form data',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate file exists
    const file = formData.get('file') as File;
    if (!file) {
      return new NextResponse(
        JSON.stringify({
          error: 'Missing file',
          message: 'No file was uploaded',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      return new NextResponse(
        JSON.stringify({
          error: 'Invalid file type',
          message: 'Only CSV files are supported',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate file size (10MB max)
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > MAX_FILE_SIZE) {
      return new NextResponse(
        JSON.stringify({
          error: 'File too large',
          message: 'File size exceeds the maximum limit of 10MB',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Read and parse CSV file
    let text;
    try {
      text = await file.text();
    } catch (error) {
      console.error('Error reading file:', error);
      return new NextResponse(
        JSON.stringify({
          error: 'File read error',
          message: 'Could not read the uploaded file',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Parse CSV
    let records;
    try {
      records = parse(text, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
        // Add encoding handling options
        bom: true, // Handle Byte Order Mark
        relax_column_count: true, // Be more forgiving with column counts
      });

      // Clean up encoding issues in the parsed records
      records = records.map((record: Record<string, any>) => {
        const cleanedRecord: Record<string, any> = {};
        Object.keys(record).forEach((key) => {
          if (typeof record[key] === 'string') {
            // Clean up common encoding issues
            let cleanedValue = record[key]
              .replace(/�/g, '') // Replace replacement character
              .replace(/ï¿½/g, '') // Common CSV encoding issue
              .replace(/Â/g, '') // Non-breaking space encoding issue
              .replace(/â€™/g, "'") // Smart single quote
              .replace(/â€œ/g, '"') // Smart opening double quote
              .replace(/â€/g, '"') // Smart closing double quote
              .replace(/â€"/g, '—') // Em dash
              .replace(/â€"/g, '–'); // En dash

            cleanedRecord[key] = cleanedValue.trim();
          } else {
            cleanedRecord[key] = record[key];
          }
        });
        return cleanedRecord;
      });

      console.log('Cleaned CSV records:', records.length);
    } catch (error) {
      console.error('Error parsing CSV:', error);
      return new NextResponse(
        JSON.stringify({
          error: 'CSV parse error',
          message:
            'Could not parse the CSV file. Please check the format and try again.',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate CSV has records
    if (!records || records.length === 0) {
      return new NextResponse(
        JSON.stringify({
          error: 'Empty CSV',
          message: 'The CSV file contains no records',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate required fields
    const firstRecord = records[0];
    if (!firstRecord.use_case_title) {
      return new NextResponse(
        JSON.stringify({
          error: 'Missing required fields',
          message:
            'The CSV file is missing required fields. Please download the template and try again.',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const results = [];
    const errors = [];
    const batchSize = 10; // Process in batches to avoid overwhelming the database

    // Process records in batches
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);

      // Process each record in the batch
      for (const record of batch) {
        try {
          // Validate required fields for this record
          if (!record.use_case_title) {
            throw new Error('Missing required field: use_case_title');
          }

          // Convert CSV fields to match our schema
          const caseStudyData: NewCaseStudy = {
            useCaseTitle: String(record.use_case_title),
            industry: record.industry ? String(record.industry) : null,
            role: record.role ? String(record.role) : null,
            vector: record.vector ? String(record.vector) : null,
            // Handle potentially impacted KPIs - can be in multiple columns or comma-separated
            potentiallyImpactedKpis: record.potentially_impacted_kpis
              ? String(record.potentially_impacted_kpis)
              : // Check if we have individual KPI columns
              record.potentially_impacted_kpis_1 ||
                record.potentially_impacted_kpis_2 ||
                record.potentially_impacted_kpis_3
              ? [
                  record.potentially_impacted_kpis_1
                    ? String(record.potentially_impacted_kpis_1)
                    : '',
                  record.potentially_impacted_kpis_2
                    ? String(record.potentially_impacted_kpis_2)
                    : '',
                  record.potentially_impacted_kpis_3
                    ? String(record.potentially_impacted_kpis_3)
                    : '',
                ]
                  .filter(Boolean)
                  .join(', ')
              : null,
            // Introduction fields
            introductionTitle: record.introduction_title
              ? String(record.introduction_title)
              : null,
            introductionText: record.introduction_text
              ? String(record.introduction_text)
              : null,
            transitionToChallange: record.transition_to_challange
              ? String(record.transition_to_challange)
              : null,
            // Challenge fields - handle multiple columns
            challange1: record.challange_1 ? String(record.challange_1) : null,
            challange2: record.challange_2 ? String(record.challange_2) : null,
            challange3: record.challange_3 ? String(record.challange_3) : null,
            // Questions fields
            transitionToQuestions: record.transition_to_questions
              ? String(record.transition_to_questions)
              : null,
            question1: record.question_1 ? String(record.question_1) : null,
            question2: record.question_2 ? String(record.question_2) : null,
            question3: record.question_3 ? String(record.question_3) : null,
            // Legacy fields - keeping for backward compatibility
            solution1: record.solution_1 ? String(record.solution_1) : null,
            solution2: record.solution_2 ? String(record.solution_2) : null,
            solution3: record.solution_3 ? String(record.solution_3) : null,
            solution4: record.solution_4 ? String(record.solution_4) : null,
            solution5: record.solution_5 ? String(record.solution_5) : null,
            // Impact fields - qualitative descriptions
            potentialImpact1: record.potential_impact_1_qualitative
              ? String(record.potential_impact_1_qualitative)
              : record.potential_impact_1
              ? String(record.potential_impact_1)
              : null,
            potentialImpact2: record.potential_impact_2_qualitative
              ? String(record.potential_impact_2_qualitative)
              : record.potential_impact_2
              ? String(record.potential_impact_2)
              : null,
            potentialImpact3: record.potential_impact_3_qualitative
              ? String(record.potential_impact_3_qualitative)
              : record.potential_impact_3
              ? String(record.potential_impact_3)
              : null,
            potentialImpact4: record.potential_impact_4_qualitative
              ? String(record.potential_impact_4_qualitative)
              : record.potential_impact_4
              ? String(record.potential_impact_4)
              : null,

            // Impact fields - quantitative values
            impactValue1: record.potential_impact_1_quantitative
              ? String(record.potential_impact_1_quantitative)
              : null,
            impactValue2: record.potential_impact_2_quantitative
              ? String(record.potential_impact_2_quantitative)
              : null,
            impactValue3: record.potential_impact_3_quantitative
              ? String(record.potential_impact_3_quantitative)
              : null,
            impactValue4: record.potential_impact_4_quantitative
              ? String(record.potential_impact_4_quantitative)
              : null,

            // Impact fields - metric names (use potentiallyImpactedKpis if available)
            impactMetric1: record.potentially_impacted_kpis_1
              ? String(record.potentially_impacted_kpis_1)
              : null,
            impactMetric2: record.potentially_impacted_kpis_2
              ? String(record.potentially_impacted_kpis_2)
              : null,
            impactMetric3: record.potentially_impacted_kpis_3
              ? String(record.potentially_impacted_kpis_3)
              : null,
            impactMetric4:
              record.potentially_impacted_kpis &&
              record.potentially_impacted_kpis.split(',').length > 3
                ? String(record.potentially_impacted_kpis.split(',')[3])
                : null,
            // Conclusion fields
            conclusionTitle: record.conclusion_title
              ? String(record.conclusion_title)
              : null,
            conclusionText: record.conclusion_text
              ? String(record.conclusion_text)
              : null,
            conclusionResult: record.conclusion_result_dec
              ? String(record.conclusion_result_dec)
              : null,
            conclusionResultTitle: record.conclusion_result_title
              ? String(record.conclusion_result_title)
              : null,
            // Process fields
            processTitle: record.process_title
              ? String(record.process_title)
              : null,
            processStep1Title: record.process_step_1_title
              ? String(record.process_step_1_title)
              : null,
            processStep1Description: record.process_step_1_description
              ? String(record.process_step_1_description)
              : null,
            processStep2Title: record.process_step_2_title
              ? String(record.process_step_2_title)
              : null,
            processStep2Description: record.process_step_2_description
              ? String(record.process_step_2_description)
              : null,
            processStep3Title: record.process_step_3_title
              ? String(record.process_step_3_title)
              : null,
            processStep3Description: record.process_step_3_description
              ? String(record.process_step_3_description)
              : null,
            // Solution fields
            solutionTitle: record.solution_title
              ? String(record.solution_title)
              : null,
            solutionDescription: record.solution_description
              ? String(record.solution_description)
              : null,
            solution1Title: record.solution_1_title
              ? String(record.solution_1_title)
              : null,
            solution1Description: record.solution_1_description
              ? String(record.solution_1_description)
              : null,
            solution2Title: record.solution_2_title
              ? String(record.solution_2_title)
              : null,
            solution2Description: record.solution_2_description
              ? String(record.solution_2_description)
              : null,
            solution3Title: record.solution_3_title
              ? String(record.solution_3_title)
              : null,
            solution3Description: record.solution_3_description
              ? String(record.solution_3_description)
              : null,
            solution4Title: record.solution_4_title
              ? String(record.solution_4_title)
              : null,
            solution4Description: record.solution_4_description
              ? String(record.solution_4_description)
              : null,
            solution5Title: record.solution_5_title
              ? String(record.solution_5_title)
              : null,
            solution5Description: record.solution_5_description
              ? String(record.solution_5_description)
              : null,
            // Impact fields
            impactTitle: record.impact_title
              ? String(record.impact_title)
              : null,
            // Set feature image URL from header_image_url
            featureImageUrl: record.header_image_url
              ? String(record.header_image_url)
              : null,
          };

          // Insert case study with transaction to ensure atomicity
          let caseStudy;
          try {
            // Use a transaction to ensure both case study and icons are inserted together
            await db.transaction(async (tx) => {
              // Add the user ID from the session to the case study data
              console.log(
                'CSV Import - User ID from session:',
                session.user.id
              );
              console.log(
                'CSV Import - Case study data before insert:',
                JSON.stringify({
                  title: caseStudyData.useCaseTitle,
                  industry: caseStudyData.industry,
                  role: caseStudyData.role,
                })
              );

              // Create a new object with the user ID explicitly set
              const dataWithUserId = {
                ...caseStudyData,
                userId: session.user.id,
              };

              console.log(
                'CSV Import - Data with userId:',
                JSON.stringify({
                  userId: dataWithUserId.userId,
                })
              );

              const result = await tx
                .insert(caseStudies)
                .values(dataWithUserId)
                .returning();

              console.log(
                'CSV Import - Case study created with ID:',
                result[0].id
              );
              console.log(
                'CSV Import - Returned case study data:',
                JSON.stringify({
                  id: result[0].id,
                  title: result[0].useCaseTitle,
                  userId: result[0].userId,
                })
              );
              caseStudy = result[0];

              // Handle image URLs from CSV
              const imageInserts = [];

              // Header image
              if (
                record.header_image_url &&
                isValidUrl(record.header_image_url)
              ) {
                imageInserts.push({
                  caseStudyId: caseStudy.id,
                  iconType: 'header',
                  iconUrl: record.header_image_url,
                  order: 0,
                });
              }

              // Icon image (thumbnail)
              if (record.icon_image_url && isValidUrl(record.icon_image_url)) {
                imageInserts.push({
                  caseStudyId: caseStudy.id,
                  iconType: 'icon',
                  iconUrl: record.icon_image_url,
                  order: 0,
                });
              }

              // Impact icon images - handle new format with _icon_image_url suffix
              for (let i = 1; i <= 4; i++) {
                const iconUrl = record[`potential_impact_${i}_icon_image_url`];
                if (iconUrl && isValidUrl(iconUrl)) {
                  imageInserts.push({
                    caseStudyId: caseStudy.id,
                    iconType: 'impact',
                    iconUrl,
                    order: i - 1,
                  });
                }
              }

              // Process step icons
              for (let i = 1; i <= 3; i++) {
                const iconUrl = record[`process_step_${i}_icon_url`];
                if (iconUrl && isValidUrl(iconUrl)) {
                  imageInserts.push({
                    caseStudyId: caseStudy.id,
                    iconType: 'process',
                    iconUrl,
                    order: i - 1,
                  });
                }
              }

              // Solution icons
              for (let i = 1; i <= 5; i++) {
                const iconUrl = record[`solution_${i}_icon_url`];
                if (iconUrl && isValidUrl(iconUrl)) {
                  imageInserts.push({
                    caseStudyId: caseStudy.id,
                    iconType: 'solution',
                    iconUrl,
                    order: i - 1,
                  });
                }
              }

              // Impact icons
              for (let i = 1; i <= 4; i++) {
                const iconUrl = record[`impact_${i}_icon_url`];
                if (iconUrl && isValidUrl(iconUrl)) {
                  imageInserts.push({
                    caseStudyId: caseStudy.id,
                    iconType: 'impact',
                    iconUrl,
                    order: i - 1,
                  });
                }
              }

              // Insert all icons if any
              if (imageInserts.length > 0) {
                await tx.insert(caseStudyIcons).values(imageInserts);
              }
            });

            results.push(caseStudy);
          } catch (dbError) {
            console.error(
              'Database error during case study insertion:',
              dbError
            );
            throw new Error(
              `Database error: ${
                dbError instanceof Error
                  ? dbError.message
                  : 'Unknown database error'
              }`
            );
          }
        } catch (error) {
          // Add detailed error information
          const rowNumber = i + batch.indexOf(record) + 1; // 1-based row number for user-friendly messages
          errors.push({
            row: record,
            rowNumber,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    }

    // Return appropriate response based on results
    if (results.length === 0 && errors.length > 0) {
      // All imports failed
      return new NextResponse(
        JSON.stringify({
          success: false,
          message: 'All case studies failed to import',
          imported: 0,
          total: records.length,
          errors,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    } else {
      // Some or all imports succeeded
      return NextResponse.json({
        success: true,
        message:
          errors.length > 0
            ? `Imported ${results.length} of ${records.length} case studies with ${errors.length} errors`
            : 'All case studies imported successfully',
        imported: results.length,
        total: records.length,
        errors: errors.length > 0 ? errors : undefined,
      });
    }
  } catch (error) {
    // Log the error with detailed information
    console.error('Unhandled error importing case studies:', error);
    console.error(
      'Error stack:',
      error instanceof Error ? error.stack : 'No stack trace available'
    );

    // Return a structured error response
    return new NextResponse(
      JSON.stringify({
        error: 'Server error',
        message: 'An unexpected error occurred while importing case studies',
        details: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}

// Using the isValidUrl function from utils/url-validator.ts
