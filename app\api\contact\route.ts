import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { contactMessages } from '@/lib/db/schema';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    const requiredFields = ['name', 'email', 'subject', 'message', 'inquiryType'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Insert the contact message into the database
    const result = await db.insert(contactMessages).values({
      name: data.name,
      email: data.email,
      phone: data.phone || null,
      company: data.company || null,
      inquiryType: data.inquiryType,
      subject: data.subject,
      message: data.message,
      status: 'unread',
    }).returning({ id: contactMessages.id });

    return NextResponse.json({
      success: true,
      message: 'Contact message submitted successfully',
      id: result[0].id
    });
  } catch (error) {
    console.error('Error submitting contact message:', error);
    return NextResponse.json(
      { error: 'Failed to submit contact message' },
      { status: 500 }
    );
  }
}
