/**
 * Fix Starter Plan Pricing
 * Updates the Starter plan to charge $12/month after 14-day trial
 */

import <PERSON><PERSON> from 'stripe';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

async function fixStarterPricing(): Promise<void> {
  console.log('🔧 Fixing Starter Plan Pricing');
  console.log('==============================\n');
  
  console.log('Current issue: Starter plan is $0/month after trial');
  console.log('Required fix: Starter plan should be $12/month after 14-day trial\n');

  try {
    const STARTER_PRODUCT_ID = process.env.STRIPE_STARTER_PRODUCT_ID;
    const STARTER_TRIAL_PRICE_ID = process.env.STRIPE_STARTER_TRIAL_PRICE_ID;

    if (!STARTER_PRODUCT_ID || !STARTER_TRIAL_PRICE_ID) {
      console.error('❌ Missing environment variables:');
      console.error('   STRIPE_STARTER_PRODUCT_ID:', STARTER_PRODUCT_ID);
      console.error('   STRIPE_STARTER_TRIAL_PRICE_ID:', STARTER_TRIAL_PRICE_ID);
      return;
    }

    console.log('📋 Current Starter Plan:');
    console.log(`   Product ID: ${STARTER_PRODUCT_ID}`);
    console.log(`   Current Price ID: ${STARTER_TRIAL_PRICE_ID}`);

    // Step 1: Archive the current incorrect price
    console.log('\n🗑️  Step 1: Archiving current $0/month price...');
    
    try {
      await stripe.prices.update(STARTER_TRIAL_PRICE_ID, { active: false });
      console.log(`   ✅ Archived old price: ${STARTER_TRIAL_PRICE_ID}`);
    } catch (error: any) {
      console.log(`   ⚠️  Could not archive old price: ${error.message}`);
    }

    // Step 2: Create new correct price ($12/month with 14-day trial)
    console.log('\n🆕 Step 2: Creating new $12/month price with 14-day trial...');
    
    const newStarterPrice = await stripe.prices.create({
      product: STARTER_PRODUCT_ID,
      currency: 'usd',
      unit_amount: 1200, // $12.00 in cents
      recurring: {
        interval: 'month',
        trial_period_days: 14
      },
      metadata: {
        plan_type: 'starter',
        billing_interval: 'monthly',
        trial_days: '14',
        created_by: 'fix-starter-pricing-script'
      }
    });

    console.log(`   ✅ Created new Starter price: $12/month with 14-day trial`);
    console.log(`   📋 New Price ID: ${newStarterPrice.id}`);

    // Step 3: Create yearly option for Starter (optional)
    console.log('\n🆕 Step 3: Creating yearly Starter price...');
    
    const yearlyStarterPrice = await stripe.prices.create({
      product: STARTER_PRODUCT_ID,
      currency: 'usd',
      unit_amount: 12000, // $120/year (10 months price for 12 months)
      recurring: {
        interval: 'year',
        trial_period_days: 14
      },
      metadata: {
        plan_type: 'starter',
        billing_interval: 'yearly',
        trial_days: '14',
        created_by: 'fix-starter-pricing-script'
      }
    });

    console.log(`   ✅ Created yearly Starter price: $120/year with 14-day trial`);
    console.log(`   📋 Yearly Price ID: ${yearlyStarterPrice.id}`);

    // Step 4: Update environment variables
    console.log('\n📝 Step 4: Updated Environment Variables');
    console.log('=======================================');
    console.log('Replace these in your .env file:\n');
    
    console.log('# Updated Starter Plan Price IDs');
    console.log(`STRIPE_STARTER_MONTHLY_PRICE_ID=${newStarterPrice.id}`);
    console.log(`STRIPE_STARTER_YEARLY_PRICE_ID=${yearlyStarterPrice.id}`);
    console.log('');
    console.log('# Remove this old variable:');
    console.log(`# STRIPE_STARTER_TRIAL_PRICE_ID=${STARTER_TRIAL_PRICE_ID} (archived)`);

    // Step 5: Summary
    console.log('\n🎉 STARTER PLAN PRICING FIXED!');
    console.log('==============================');
    console.log('✅ Old $0/month price archived');
    console.log('✅ New $12/month price created (with 14-day trial)');
    console.log('✅ New $120/year price created (with 14-day trial)');
    console.log('✅ Environment variables generated');
    
    console.log('\n📋 Current Plan Structure:');
    console.log('┌─────────────┬──────────────────┬─────────────────────┐');
    console.log('│ Plan        │ Price            │ Trial Period        │');
    console.log('├─────────────┼──────────────────┼─────────────────────┤');
    console.log('│ Starter     │ $12/month        │ 14 days free        │');
    console.log('│ Starter     │ $120/year        │ 14 days free        │');
    console.log('│ Pro         │ $60/month        │ No trial            │');
    console.log('│ Pro         │ $600/year        │ No trial            │');
    console.log('│ Enterprise  │ Contact Us       │ N/A                 │');
    console.log('└─────────────┴──────────────────┴─────────────────────┘');

    console.log('\n📋 Next Steps:');
    console.log('1. Update your .env file with the new environment variables');
    console.log('2. Restart your application');
    console.log('3. Test the Starter plan signup flow');
    console.log('4. Verify 14-day trial works correctly');
    console.log('5. Confirm $12/month billing starts after trial');

  } catch (error: any) {
    console.error('\n❌ ERROR FIXING STARTER PRICING:', error.message);
    console.error('Stack trace:', error.stack);
    
    if (error.type === 'StripeAuthenticationError') {
      console.error('\n🔑 Authentication Error:');
      console.error('   Check your STRIPE_SECRET_KEY in .env file');
    }
    
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  fixStarterPricing();
}

export { fixStarterPricing };
