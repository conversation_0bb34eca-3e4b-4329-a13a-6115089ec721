'use client';

import Image from 'next/image';
import Link from 'next/link';

export default function Footer() {
  return (
    <footer className='bg-white md:pt-16'>
      <div className='container mx-auto px-4 md:px-6 lg:px-8 bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border border-[#EAEFF4] rounded-3xl'>
        <div className='rounded-3xl p-12 relative'>
          <div className='grid grid-cols-1 md:grid-cols-12 gap-12'>
            {/* Logo and Description */}
            <div className='md:col-span-4'>
              <div className='flex items-center h-[60px] overflow-hidden gap-2 mb-4 select-none'>
                <Image
                  src='/images/logos/main-logo.svg'
                  alt='Turinos AI Logo'
                  width={180}
                  height={60}
                  className='w-[180px] h-auto object-contain'
                />
              </div>
              <p className='text-gray-600'>
                Gateway to AI-fy your business
                <br />
                with insights that inspire action
              </p>
              <div className='flex gap-6 mt-6'>
                <a
                  href='https://www.linkedin.com/company/turin-os/'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-gray-400 hover:text-gray-600 transition-colors duration-300'
                >
                  <svg
                    className='h-5 w-5'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z' />
                  </svg>
                </a>
                {/* <a href="#" className="text-gray-400 hover:text-gray-600 transition-colors duration-300">
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z"/>
                  </svg>
                </a> */}
                {/* <a href="#" className="text-gray-400 hover:text-gray-600 transition-colors duration-300">
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </a> */}
              </div>
            </div>

            {/* Menu */}
            <div className='md:col-span-3'>
              <h3 className='text-lg font-semibold mb-6 text-gray-900'>Menu</h3>
              <ul className='space-y-4'>
                <li>
                  <Link
                    href='/about'
                    className='text-gray-600 hover:text-gray-900 transition-colors duration-300'
                  >
                    About Us
                  </Link>
                </li>
                {/* <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors duration-300">Solutions</Link></li> */}
                <li>
                  <Link
                    href='/pricing'
                    className='text-gray-600 hover:text-gray-900 transition-colors duration-300'
                  >
                    Pricing
                  </Link>
                </li>
                <li>
                  <Link
                    href='/contact'
                    className='text-gray-600 hover:text-gray-900 transition-colors duration-300'
                  >
                    Contact Us
                  </Link>
                </li>
              </ul>
            </div>

            {/* Coffee Table Book */}
            <div className='md:col-span-5'>
              <h3 className='text-lg font-semibold mb-4 text-gray-900'>
                CEO Strategy Playbook
              </h3>
              <p className='text-gray-600 mb-6'>
                Get insights ready to reshape your boardroom thinking
              </p>
              <div className='flex md:gap-4 gap-2 '>
                <input
                  type='email'
                  placeholder='Enter your email to get latest Coffee Table Book'
                  className='flex-1 px-4 py-3 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300'
                />
                <button className='px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-300'>
                  Send
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
