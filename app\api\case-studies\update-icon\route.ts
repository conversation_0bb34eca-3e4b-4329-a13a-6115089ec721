import { NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { db } from '@/lib/db/drizzle';
import { caseStudyIcons } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const formData = await req.formData();
    const type = formData.get('type') as string; // 'header', 'icon', 'process', 'solution', 'impact'
    const caseStudyId = formData.get('caseStudyId') ? Number(formData.get('caseStudyId')) : null;
    const order = formData.get('order') ? Number(formData.get('order')) : 0;
    const url = formData.get('url') as string;

    if (!type || !caseStudyId || !url) {
      return new NextResponse('Missing required fields', { status: 400 });
    }

    console.log('========== UPDATE ICON REQUEST ==========');
    console.log(`Type: ${type}`);
    console.log(`Case Study ID: ${caseStudyId}`);
    console.log(`Order: ${order}`);
    console.log(`URL: ${url}`);
    console.log('=========================================');

    // Check if icon already exists
    const existingIcons = await db
      .select()
      .from(caseStudyIcons)
      .where(
        and(
          eq(caseStudyIcons.caseStudyId, caseStudyId),
          eq(caseStudyIcons.iconType, type),
          eq(caseStudyIcons.order, order)
        )
      );

    if (existingIcons.length > 0) {
      // Update existing icon
      await db
        .update(caseStudyIcons)
        .set({ iconUrl: url })
        .where(
          and(
            eq(caseStudyIcons.caseStudyId, caseStudyId),
            eq(caseStudyIcons.iconType, type),
            eq(caseStudyIcons.order, order)
          )
        );
      console.log('✅ Icon updated successfully');
    } else {
      // Insert new icon
      await db.insert(caseStudyIcons).values({
        caseStudyId,
        iconType: type,
        iconUrl: url,
        order,
      });
      console.log('✅ Icon inserted successfully');
    }

    return NextResponse.json({
      success: true,
      message: 'Icon updated successfully',
    });
  } catch (error) {
    console.error('Error updating icon:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}
