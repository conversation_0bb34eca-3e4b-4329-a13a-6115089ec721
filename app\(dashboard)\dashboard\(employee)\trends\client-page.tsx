'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRightIcon, MagnifyingGlassIcon, BarChartIcon, BookmarkIcon } from '@radix-ui/react-icons';
import { BookmarkButton } from '@/components/BookmarkButton';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

interface BookmarkItem {
  id: number;
  caseStudyId: number;
  caseStudy: {
    id: number;
    useCaseTitle: string;
    industry?: string;
    role?: string;
    introductionText?: string;
    featureImageUrl?: string;
    previewImageUrl?: string;
    marketIntelligenceData?: any;
    marketMetricsData?: any;
    icons?: {
      iconType: string;
      iconUrl: string;
    }[];
  };
}

interface BookmarksClientPageProps {
  initialBookmarks: BookmarkItem[];
}

export default function BookmarksClientPage({ initialBookmarks }: BookmarksClientPageProps) {
  const [bookmarks, setBookmarks] = useState<BookmarkItem[]>(initialBookmarks);

  // This function will be called when a bookmark is removed
  const handleBookmarkRemoved = (caseStudyId: number) => {
    setBookmarks(prev => prev.filter(bookmark => bookmark.caseStudyId !== caseStudyId));
  };

  // Custom BookmarkButton component specifically for the bookmarks page
  function CustomBookmarkButton({ bookmark, onRemove }: { bookmark: BookmarkItem, onRemove: (id: number) => void }) {
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter();
    const { toast } = useToast();

    const toggleBookmark = async (e: React.MouseEvent) => {
      e.preventDefault(); // Prevent the link from navigating
      e.stopPropagation(); // Prevent event bubbling

      setIsLoading(true);
      try {
        // Always DELETE since we're on the bookmarks page
        const response = await fetch('/api/bookmarks', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ caseStudyId: bookmark.caseStudy.id }),
        });

        if (response.ok) {
          toast({
            title: 'Bookmark removed',
            description: 'Case study removed from bookmarks',
          });
          // Call the onRemove callback to update the UI
          onRemove(bookmark.caseStudy.id);
          router.refresh();
        } else {
          console.error('Failed to remove bookmark', await response.text());
          toast({
            title: 'Error',
            description: 'Failed to remove bookmark',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error removing bookmark:', error);
        toast({
          title: 'Error',
          description: 'An error occurred while removing bookmark',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    return (
      <Button
        onClick={toggleBookmark}
        disabled={isLoading}
        variant="default"
        size="sm"
        className="rounded-full w-8 h-8 p-0 bg-blue-500 text-white"
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <BookmarkIcon className="h-4 w-4" />
        )}
      </Button>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Your Bookmarked Case Studies</h1>
      </div>

      {bookmarks.length === 0 ? (
        <div className="text-center py-12">
          <BookmarkIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h2 className="text-xl font-medium text-gray-900 mb-2">No bookmarks yet</h2>
          <p className="text-gray-500 mb-6">You haven't bookmarked any case studies yet.</p>
          <Button asChild>
            <Link href="/dashboard/member">Browse Case Studies</Link>
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {bookmarks.map((bookmark) => (
            <div key={bookmark.id} className="relative">
              <Link
                href={`/dashboard/case-studies/${bookmark.caseStudy.id}`}
                className="block transition-transform hover:scale-[1.02]"
              >
                <Card className="h-full overflow-hidden border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all">
                  <div className="relative h-48 w-full overflow-hidden bg-gray-100">
                    {bookmark.caseStudy.featureImageUrl || bookmark.caseStudy.previewImageUrl || (bookmark.caseStudy.icons && bookmark.caseStudy.icons.find(icon => icon.iconType === 'icon')?.iconUrl) ? (
                      <Image
                        src={bookmark.caseStudy.featureImageUrl || bookmark.caseStudy.previewImageUrl || (bookmark.caseStudy.icons && bookmark.caseStudy.icons.find(icon => icon.iconType === 'icon')?.iconUrl) || '/placeholder-image.jpg'}
                        alt={bookmark.caseStudy.useCaseTitle || 'Case study'}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-gray-200">
                        <span className="text-gray-500">No image</span>
                      </div>
                    )}

                    {/* Market Intelligence Badge */}
                    {bookmark.caseStudy.marketIntelligenceData && (
                      <div className="absolute top-2 left-2">
                        <Badge className="bg-green-600 text-white flex items-center gap-1">
                          <MagnifyingGlassIcon className="h-3 w-3" />
                          Market Intelligence
                        </Badge>
                      </div>
                    )}
                  </div>

                  <CardContent className="p-5">
                    <div className="mb-2 flex flex-wrap gap-2">
                      {bookmark.caseStudy.industry && (
                        <Badge variant="outline" className="bg-gray-100">
                          {bookmark.caseStudy.industry}
                        </Badge>
                      )}
                      {bookmark.caseStudy.role && (
                        <Badge variant="outline" className="bg-gray-100">
                          {bookmark.caseStudy.role}
                        </Badge>
                      )}

                      {/* Market Metrics Badge */}
                      {bookmark.caseStudy.marketMetricsData && (
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
                          <BarChartIcon className="h-3 w-3" />
                          Metrics
                        </Badge>
                      )}
                    </div>

                    <h3 className="text-xl font-semibold mb-2 line-clamp-2">
                      {bookmark.caseStudy.useCaseTitle}
                    </h3>

                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {bookmark.caseStudy.introductionText || 'No description available.'}
                    </p>

                    <div className="flex justify-end">
                      <Button variant="ghost" className="text-blue-600 hover:text-blue-800 p-0 flex items-center gap-1">
                        View Case Study
                        <ArrowRightIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              {/* Bookmark Button - positioned absolutely on top of the card */}
              <div className="absolute top-2 right-2 z-10" onClick={(e) => e.stopPropagation()}>
                <CustomBookmarkButton
                  bookmark={bookmark}
                  onRemove={handleBookmarkRemoved}
                />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
