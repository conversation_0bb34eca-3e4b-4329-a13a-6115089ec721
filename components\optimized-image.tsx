'use client';

import { useState, useEffect, memo } from 'react';
import Image, { ImageProps } from 'next/image';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends Omit<ImageProps, 'onLoad' | 'onError' | 'loading'> {
  fallbackSrc?: string;
  lowQualitySrc?: string;
  preload?: boolean;
  lazyBoundary?: string;
}

/**
 * OptimizedImage component extends Next.js Image with:
 * - Progressive loading with low-quality placeholder
 * - Error handling with fallback image
 * - Optional preloading for critical images
 * - Automatic handling of WebP format
 * - Appropriate size optimizations
 */
function OptimizedImageComponent({
  src,
  alt,
  width,
  height,
  className,
  style,
  fallbackSrc = '/images/fallback-image.jpg',
  lowQualitySrc,
  preload = false,
  lazyBoundary = '200px',
  priority = false,
  ...props
}: OptimizedImageProps) {
  const [imgSrc, setImgSrc] = useState<string | typeof src>(lowQualitySrc || src);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);

  // If preload is true, use Next.js priority feature
  const shouldPrioritize = preload || priority;

  // Reset state when src changes
  useEffect(() => {
    setImgSrc(lowQualitySrc || src);
    setIsLoaded(false);
    setIsError(false);
  }, [src, lowQualitySrc]);

  // Add image preload tag for critical images
  useEffect(() => {
    if (typeof window !== 'undefined' && shouldPrioritize && typeof src === 'string') {
      const linkElement = document.createElement('link');
      linkElement.rel = 'preload';
      linkElement.as = 'image';
      linkElement.href = src as string;
      document.head.appendChild(linkElement);

      return () => {
        document.head.removeChild(linkElement);
      };
    }
  }, [src, shouldPrioritize]);

  // Handle successful image load
  const handleLoad = () => {
    setIsLoaded(true);
    if (lowQualitySrc) {
      setImgSrc(src);
    }
  };

  // Handle image load error
  const handleError = () => {
    setIsError(true);
    setImgSrc(fallbackSrc);
  };

  return (
    <div className={cn('relative overflow-hidden', className)} style={style}>
      <Image
        src={isError ? fallbackSrc : imgSrc}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          'transition-opacity duration-500',
          isLoaded ? 'opacity-100' : 'opacity-0',
          className
        )}
        onLoadingComplete={handleLoad}
        onError={handleError}
        loading={shouldPrioritize ? 'eager' : 'lazy'}
        priority={shouldPrioritize}
        sizes={
          // Default sizes attribute for responsive images
          props.sizes ||
          `(max-width: 640px) 100vw, 
           (max-width: 1024px) 50vw, 
           33vw`
        }
        {...props}
      />
      
      {/* Show low quality placeholder while loading */}
      {!isLoaded && lowQualitySrc && (
        <div className="absolute inset-0 bg-gray-100 animate-pulse" />
      )}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export const OptimizedImage = memo(OptimizedImageComponent);

export default OptimizedImage;
