'use client';

import { useState, useEffect, useCallback } from 'react';
import { PlanFeatures, PLAN_CONFIG, getPlanFeatures, isActiveSubscription } from '@/lib/config/plans';

// Types for subscription data
export interface SubscriptionData {
  status: string | null;
  planName: string | null;
  hasActiveSubscription: boolean;
  planFeatures: PlanFeatures;
  isLoading: boolean;
  error: string | null;
}

// Types for plan limit checking
export interface PlanLimitResult {
  allowed: boolean;
  limit: number;
  current: number;
  message?: string;
}

/**
 * Custom hook for subscription management
 */
export function useSubscription() {
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    status: null,
    planName: null,
    hasActiveSubscription: false,
    planFeatures: PLAN_CONFIG.starter,
    isLoading: true,
    error: null,
  });

  /**
   * Fetch subscription data from API
   */
  const fetchSubscriptionData = useCallback(async () => {
    try {
      setSubscriptionData(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await fetch('/api/subscriptions');
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Please sign in to view subscription data');
        }
        throw new Error('Failed to fetch subscription data');
      }

      const data = await response.json();

      // Handle case where no subscription exists
      if (!data.subscription && !data.team) {
        setSubscriptionData({
          status: null,
          planName: null,
          hasActiveSubscription: false,
          planFeatures: PLAN_CONFIG.starter,
          isLoading: false,
          error: null,
        });
        return;
      }

      // Determine plan features using centralized function
      const planName = data.team?.planName;
      const planFeatures = getPlanFeatures(planName);

      // Check if subscription is active
      const status = data.team?.subscriptionStatus;
      const hasActiveSubscription = isActiveSubscription(status);

      setSubscriptionData({
        status,
        planName: data.team?.planName || null,
        hasActiveSubscription,
        planFeatures,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.error('Error fetching subscription data:', error);
      setSubscriptionData(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
    }
  }, []);

  /**
   * Check if user has access to a specific feature
   */
  const hasFeatureAccess = useCallback((feature: keyof PlanFeatures): boolean => {
    if (subscriptionData.isLoading) return false;
    if (!subscriptionData.hasActiveSubscription) return false;
    return subscriptionData.planFeatures[feature] as boolean;
  }, [subscriptionData]);

  /**
   * Check plan limits for a specific resource
   */
  const checkPlanLimit = useCallback(async (
    limitType: 'maxUsers' | 'maxCaseStudies' | 'maxStorage',
    currentCount: number
  ): Promise<PlanLimitResult> => {
    try {
      const response = await fetch('/api/subscriptions/check-limit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ limitType, currentCount }),
      });

      if (!response.ok) {
        throw new Error('Failed to check plan limit');
      }

      const result = await response.json();
      return {
        allowed: result.allowed,
        limit: result.limit,
        current: currentCount,
        message: result.message,
      };
    } catch (error) {
      console.error('Error checking plan limit:', error);
      return {
        allowed: false,
        limit: 0,
        current: currentCount,
        message: 'Error checking plan limits',
      };
    }
  }, []);

  /**
   * Get upgrade suggestions based on current usage
   */
  const getUpgradeSuggestions = useCallback((usage: {
    userCount?: number;
    caseStudyCount?: number;
    storageUsed?: number;
  }): string[] => {
    const suggestions: string[] = [];
    const { planFeatures } = subscriptionData;

    if (usage.userCount && planFeatures.maxUsers !== -1 && usage.userCount >= planFeatures.maxUsers) {
      suggestions.push('Upgrade to add more team members');
    }

    if (usage.caseStudyCount && planFeatures.maxCaseStudies && planFeatures.maxCaseStudies !== -1 && usage.caseStudyCount >= planFeatures.maxCaseStudies) {
      suggestions.push('Upgrade to create more case studies');
    }

    if (usage.storageUsed && planFeatures.maxStorage && planFeatures.maxStorage !== -1 && usage.storageUsed >= planFeatures.maxStorage * 0.8) {
      suggestions.push('Upgrade for more storage space');
    }

    if (!planFeatures.advancedAnalytics) {
      suggestions.push('Upgrade for advanced analytics');
    }

    if (!planFeatures.apiAccess) {
      suggestions.push('Upgrade for API access');
    }

    return suggestions;
  }, [subscriptionData]);

  /**
   * Refresh subscription data
   */
  const refresh = useCallback(() => {
    fetchSubscriptionData();
  }, [fetchSubscriptionData]);

  // Initial data fetch
  useEffect(() => {
    fetchSubscriptionData();
  }, [fetchSubscriptionData]);

  return {
    ...subscriptionData,
    hasFeatureAccess,
    checkPlanLimit,
    getUpgradeSuggestions,
    refresh,
  };
}

/**
 * Hook for checking specific feature access
 */
export function useFeatureAccess(feature: keyof PlanFeatures) {
  const { hasFeatureAccess, isLoading } = useSubscription();

  return {
    hasAccess: hasFeatureAccess(feature),
    isLoading,
  };
}

/**
 * Hook for plan limit checking with real-time validation
 */
export function usePlanLimit(
  limitType: 'maxUsers' | 'maxCaseStudies' | 'maxStorage',
  currentCount: number
) {
  const [limitResult, setLimitResult] = useState<PlanLimitResult | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const { checkPlanLimit, planFeatures } = useSubscription();

  const checkLimit = useCallback(async () => {
    setIsChecking(true);
    try {
      const result = await checkPlanLimit(limitType, currentCount);
      setLimitResult(result);
    } catch (error) {
      console.error('Error checking limit:', error);
    } finally {
      setIsChecking(false);
    }
  }, [checkPlanLimit, limitType, currentCount]);

  useEffect(() => {
    checkLimit();
  }, [checkLimit]);

  // Calculate percentage used
  const percentageUsed = limitResult && limitResult.limit > 0
    ? Math.min((currentCount / limitResult.limit) * 100, 100)
    : 0;

  return {
    ...limitResult,
    percentageUsed,
    isChecking,
    recheck: checkLimit,
  };
}

/**
 * Hook for subscription status with automatic refresh
 */
export function useSubscriptionStatus() {
  const { status, hasActiveSubscription, isLoading, refresh } = useSubscription();

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(refresh, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [refresh]);

  return {
    status,
    hasActiveSubscription,
    isLoading,
    refresh,
  };
}
