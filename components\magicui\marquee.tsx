import { cn } from "@/lib/utils";
import { HTMLAttributes } from "react";

interface MarqueeProps extends HTMLAttributes<HTMLDivElement> {
  /**
   * Optional CSS class name to apply custom styles
   */
  className?: string;
  /**
   * Whether to reverse the animation direction
   * @default false
   */
  reverse?: boolean;
  /**
   * Whether to pause the animation on hover
   * @default false
   */
  pauseOnHover?: boolean;
  /**
   * Content to be displayed in the marquee
   */
  children: React.ReactNode;
  /**
   * Whether to animate vertically instead of horizontally
   * @default false
   */
  vertical?: boolean;
  /**
   * Speed of the animation
   * @default 'normal'
   */
  speed?: "slow" | "normal" | "fast";
  /**
   * Whether to fade the content
   * @default false
   */
  fade?: boolean;
}

export function Marquee({
  children,
  pauseOnHover = false,
  vertical = false,
  reverse = false,
  fade = false,
  speed = "normal",
  className,
  ...props
}: MarqueeProps) {
  const duration = {
    slow: "40s",
    normal: "30s",
    fast: "20s",
  };

  return (
    <div
      className={cn(
        "group relative flex min-w-full overflow-hidden",
        vertical ? "flex-col" : "flex-row",
        className
      )}
      {...props}
    >
      <div
        className={cn(
          "flex min-w-full flex-none justify-around",
          vertical ? "flex-col" : "flex-row",
          pauseOnHover && "group-hover:[animation-play-state:paused]",
          vertical
            ? "animate-marquee-vertical-infinite"
            : "animate-marquee-infinite",
          {
            "flex-col-reverse": vertical && reverse,
            "flex-row-reverse": !vertical && reverse,
          }
        )}
        style={{
          animationDuration: duration[speed],
        }}
      >
        {children}
      </div>
      <div
        aria-hidden="true"
        className={cn(
          "flex min-w-full flex-none justify-around",
          vertical ? "flex-col" : "flex-row",
          pauseOnHover && "group-hover:[animation-play-state:paused]",
          vertical
            ? "animate-marquee-vertical-infinite"
            : "animate-marquee-infinite",
          {
            "flex-col-reverse": vertical && reverse,
            "flex-row-reverse": !vertical && reverse,
          }
        )}
        style={{
          animationDuration: duration[speed],
        }}
      >
        {children}
      </div>
    </div>
  );
}
