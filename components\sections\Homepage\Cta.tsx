import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function Cta() {
  const router = useRouter();
  return (
    <section className='relative pb-16 md:py-20 w-full overflow-hidden bg-white'>
      <div className='container mx-auto px-6 lg:px-8 '>
        <div className='max-w-4xl mx-auto'>
          <div className='bg-[#EAEFF4] rounded-2xl p-12 relative'>
            <div className='absolute inset-0 border-2 border-[#EAEFF4] rounded-2xl -m-2 pointer-events-none'></div>

            <div className='text-center space-y-6 '>
              <div className='inline-block'>
                <Badge
                  variant='secondary'
                  className='px-4 py-1.5 text-sm bg-white/95 backdrop-blur-sm border-none shadow-md text-gray-600 drop-shadow-md'
                >
                  Lead the Future of AI Execution
                </Badge>
              </div>
              <h2 className='text-4xl font-bold text-gray-900'>
                Ready to Turn AI Ambition Into Action?
              </h2>
              <p className='text-gray-600 text-lg max-w-2xl mx-auto text-center'>
                Sign up now to access real-time AI signals, curated use cases,
                and execution-ready insights—everything CXOs need to lead AI
                transformation with clarity and confidence.
              </p>
              <div className='flex items-center justify-center gap-4 pt-4'>
                <Button
                  variant='outline'
                  className='px-8 py-2 text-base'
                  onClick={() => router.push('/sign-in')}
                >
                  Login
                </Button>
                <Button
                  className='bg-blue-500 hover:bg-blue-600 px-8 py-2 text-base'
                  onClick={() => router.push('/sign-up')}
                >
                  Sign Up
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
