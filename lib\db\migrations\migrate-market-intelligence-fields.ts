import { db } from '../drizzle';
import { sql } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

async function migrateMarketIntelligenceFields() {
  console.log('Running migration to add market intelligence fields...');

  try {
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'add-market-intelligence-fields.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL statements
    await db.execute(sql.raw(sqlContent));

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateMarketIntelligenceFields().catch(console.error);
