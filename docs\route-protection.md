# Route Protection Documentation

This document outlines the route protection implementation in the application, which prevents owners from accessing member routes and members from accessing owner routes.

## Overview

The application implements a multi-layered approach to route protection:

1. **Edge Middleware**: Intercepts requests at the network edge and redirects unauthorized users
2. **Server-Side Layout Protection**: Validates user roles in layout components
3. **Client-Side Validation**: Provides immediate feedback to users

## Implementation Details

### 1. Edge Middleware

The middleware (`middleware.ts`) intercepts all requests and performs the following checks:

- Redirects unauthenticated users from protected routes to the sign-in page
- Redirects authenticated users from auth routes to their appropriate dashboard
- Validates user roles against route patterns and redirects users attempting to access unauthorized routes

```typescript
// Role-based access control for protected routes
if (isProtectedRoute && sessionCookie) {
  try {
    const parsed = await verifyToken(sessionCookie.value);
    const role = parsed.user?.role || 'member';
    
    // Check if user is trying to access a route they don't have permission for
    const isOwnerRoute = matchesPattern(pathname, OWNER_ROUTE_PATTERNS);
    const isMemberRoute = matchesPattern(pathname, MEMBER_ROUTE_PATTERNS);
    
    // If it's an owner route but user is not an owner
    if (isOwnerRoute && role !== 'owner') {
      console.log(`Middleware: Access denied - ${role} attempting to access owner route: ${pathname}`);
      return NextResponse.redirect(new URL('/dashboard/member', request.url));
    }
    
    // If it's a member route but user is not a member
    if (isMemberRoute && role !== 'member') {
      console.log(`Middleware: Access denied - ${role} attempting to access member route: ${pathname}`);
      return NextResponse.redirect(new URL('/dashboard/owner', request.url));
    }
  } catch (error) {
    console.error('Error verifying token for role-based access:', error);
  }
}
```

### 2. Server-Side Layout Protection

Layout components for owner and member routes validate user roles using server components:

**Owner Layout (`app/(dashboard)/dashboard/(owner)/layout.tsx`)**:
```typescript
'use server';

import { protectOwnerRoute } from '@/lib/auth/route-protection';

export default async function OwnerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Protect all owner routes
  await protectOwnerRoute();
  
  return <>{children}</>;
}
```

**Member Layout (`app/(dashboard)/dashboard/(employee)/layout.tsx`)**:
```typescript
'use server';

import { protectMemberRoute } from '@/lib/auth/route-protection';

export default async function MemberLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Protect all member routes
  await protectMemberRoute();
  
  return <>{children}</>;
}
```

### 3. Route Protection Utilities

The `lib/auth/route-protection.ts` file provides utility functions for route protection:

```typescript
/**
 * Middleware to protect owner routes from non-owner users
 */
export async function protectOwnerRoute() {
  const user = await getUser();
  
  if (!user) {
    redirect('/sign-in');
  }
  
  if (user.role !== 'owner') {
    console.log('Access denied: Non-owner attempting to access owner route');
    redirect('/dashboard/member');
  }
}

/**
 * Middleware to protect member routes from non-member users
 */
export async function protectMemberRoute() {
  const user = await getUser();
  
  if (!user) {
    redirect('/sign-in');
  }
  
  if (user.role !== 'member') {
    console.log('Access denied: Non-member attempting to access member route');
    redirect('/dashboard/owner');
  }
}
```

## Route Patterns

The application defines route patterns for different user roles:

**Owner Routes**:
- `/dashboard/owner`
- `/dashboard/add-case`
- `/dashboard/import-cases`
- `/dashboard/categories`
- `/dashboard/team`
- `/dashboard/case-studies`

**Member Routes**:
- `/dashboard/member`
- `/dashboard/trends`
- `/dashboard/reports`
- `/dashboard/user`

## Security Considerations

1. **Defense in Depth**: Multiple layers of protection ensure that even if one layer fails, others will prevent unauthorized access
2. **Logging**: Unauthorized access attempts are logged for security monitoring
3. **Clear Redirects**: Users are redirected to appropriate pages with proper permissions
4. **No Information Leakage**: Error messages don't reveal sensitive information about the application structure

## Best Practices

1. Always use the route protection utilities for new routes
2. Keep the route patterns updated when adding new routes
3. Test route protection when implementing new features
4. Monitor logs for unauthorized access attempts
