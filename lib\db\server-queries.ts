'use server';

import { desc, and, eq, isNull } from 'drizzle-orm';
import { db } from './drizzle';
import { activityLogs, teamMembers, teams, users } from './schema';
import { cookies } from 'next/headers';
import { verifyToken } from '@/lib/auth/session';

/**
 * Get the currently authenticated user
 * This function can only be used in Server Components or Server Actions
 */
export async function getUser(
  mode: null | 'member-access' | 'admin-access' | undefined = null
) {
  const sessionCookie = (await cookies()).get('session');
  if (!sessionCookie || !sessionCookie.value) {
    console.log('getUser - No session cookie found');
    return null;
  }

  const sessionData = await verifyToken(sessionCookie.value);

  if (
    !sessionData ||
    !sessionData.user ||
    typeof sessionData.user.id !== 'number'
  ) {
    console.log('getUser - Invalid session data');
    return null;
  }

  if (new Date(sessionData.expires) < new Date()) {
    console.log('getUser - Session expired');
    return null;
  }

  // Get user with team membership information
  const userResults = await db
    .select({
      id: users.id,
      companyName: users.companyName,
      industry: users.industry,
      role: users.role,
      name: users.name,
      email: users.email,
      isVerified: users.isVerified,
      createdAt: users.createdAt,
      updatedAt: users.updatedAt,
      teamId: teamMembers.teamId,
    })
    .from(users)
    .leftJoin(teamMembers, eq(users.id, teamMembers.userId))
    .where(and(eq(users.id, sessionData.user.id)))
    .limit(1);

  if (userResults.length === 0) {
    console.log('getUser - User not found in database');
    return null;
  }

  let targetUser: any = userResults[0];
  targetUser = {
    ...targetUser,
    teamRole: targetUser.role,
    role:
      (targetUser.role === 'owner' ||
        targetUser.role === 'teamMember' ||
        targetUser.role === 'viewer') &&
      mode !== 'member-access'
        ? 'owner'
        : 'member',
  };

  return targetUser;
}

/**
 * Get activity logs for the current user
 * This function can only be used in Server Components or Server Actions
 */
export async function getActivityLogs() {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  return await db
    .select({
      id: activityLogs.id,
      action: activityLogs.action,
      timestamp: activityLogs.timestamp,
      ipAddress: activityLogs.ipAddress,
      userName: users.name,
    })
    .from(activityLogs)
    .leftJoin(users, eq(activityLogs.userId, users.id))
    .where(eq(activityLogs.userId, user.id))
    .orderBy(desc(activityLogs.timestamp))
    .limit(10);
}

/**
 * Get team data for a user
 * This function can only be used in Server Components or Server Actions
 */
export async function getTeamForUser(userId: number) {
  // First, find the team ID for this user
  const memberRecord = await db
    .select({
      teamId: teamMembers.teamId,
    })
    .from(teamMembers)
    .where(eq(teamMembers.userId, userId))
    .limit(1);

  if (!memberRecord || memberRecord.length === 0 || !memberRecord[0].teamId) {
    console.log('getTeamForUser - No team found for user:', userId);
    return null;
  }

  // Now get the team with all its members
  const teamData = await db
    .select()
    .from(teams)
    .where(eq(teams.id, memberRecord[0].teamId))
    .limit(1);

  if (!teamData || teamData.length === 0) {
    console.log('getTeamForUser - Team not found:', memberRecord[0].teamId);
    return null;
  }

  return teamData[0];
}
