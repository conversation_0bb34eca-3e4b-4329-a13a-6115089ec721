/**
 * Interactive Stripe Plan Management Script
 * Safer version with user confirmations and step-by-step execution
 */

import <PERSON><PERSON> from 'stripe';
import * as dotenv from 'dotenv';
import * as readline from 'readline';

// Load environment variables
dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function to ask yes/no questions
function askQuestion(question: string): Promise<boolean> {
  return new Promise((resolve) => {
    rl.question(`${question} (y/N): `, (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// Helper function to ask for input
function askInput(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

/**
 * Step 1: Audit and display current Stripe data
 */
async function auditCurrentPlans(): Promise<void> {
  console.log('\n🔍 STEP 1: Auditing Current Stripe Plans\n');

  try {
    // Get products
    const products = await stripe.products.list({ limit: 100 });
    console.log(`📦 Found ${products.data.length} products:`);
    products.data.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name} (${product.id})`);
      console.log(`     Status: ${product.active ? '✅ Active' : '❌ Inactive'}`);
      console.log(`     Created: ${new Date(product.created * 1000).toLocaleDateString()}`);
      if (product.description) {
        console.log(`     Description: ${product.description}`);
      }
      console.log('');
    });

    // Get prices
    const prices = await stripe.prices.list({ limit: 100 });
    console.log(`💰 Found ${prices.data.length} prices:`);
    prices.data.forEach((price, index) => {
      const amount = price.unit_amount ? `$${(price.unit_amount / 100).toFixed(2)}` : 'Free';
      const interval = price.recurring?.interval || 'one-time';
      console.log(`  ${index + 1}. ${amount}/${interval} (${price.id})`);
      console.log(`     Status: ${price.active ? '✅ Active' : '❌ Inactive'}`);
      console.log(`     Product: ${price.product}`);
      console.log('');
    });

    // Get active subscriptions
    const subscriptions = await stripe.subscriptions.list({
      status: 'active',
      limit: 100
    });
    console.log(`🔄 Found ${subscriptions.data.length} active subscriptions:`);
    if (subscriptions.data.length > 0) {
      subscriptions.data.forEach((sub, index) => {
        console.log(`  ${index + 1}. ${sub.id}`);
        console.log(`     Customer: ${sub.customer}`);
        console.log(`     Status: ${sub.status}`);
        console.log(`     Current period: ${new Date(sub.current_period_start * 1000).toLocaleDateString()} - ${new Date(sub.current_period_end * 1000).toLocaleDateString()}`);
        console.log('');
      });
    } else {
      console.log('  No active subscriptions found ✅');
    }

  } catch (error: any) {
    console.error('❌ Error auditing Stripe data:', error.message);
    throw error;
  }
}

/**
 * Step 2: Show what will be created
 */
async function showNewPlanStructure(): Promise<void> {
  console.log('\n🆕 STEP 2: New Plan Structure to be Created\n');

  console.log('📋 STARTER PLAN');
  console.log('  • 14-day free trial');
  console.log('  • Basic AI use case access');
  console.log('  • Limited case studies');
  console.log('  • Community support');
  console.log('  💰 Pricing: Free trial for 14 days');
  console.log('');

  console.log('📋 PRO PLAN');
  console.log('  • Full access to all features');
  console.log('  • Unlimited case studies');
  console.log('  • Advanced analytics');
  console.log('  • Priority support');
  console.log('  • Team collaboration');
  console.log('  💰 Pricing: $29/month or $290/year (UPDATE WITH YOUR ACTUAL PRICES)');
  console.log('');

  console.log('📋 ENTERPRISE PLAN');
  console.log('  • Custom pricing and features');
  console.log('  • Dedicated account management');
  console.log('  • Custom integrations');
  console.log('  • SLA guarantees');
  console.log('  💰 Pricing: Contact Us (No Stripe product - handled separately)');
  console.log('');

  console.log('⚠️  NOTE: Please update the Pro plan pricing in the script with your actual prices!');
  console.log('');
}

/**
 * Step 3: Archive old plans with confirmation
 */
async function archiveOldPlansInteractive(): Promise<void> {
  console.log('\n🗑️  STEP 3: Archive Old Plans\n');

  const shouldArchive = await askQuestion('Do you want to archive ALL existing products and prices?');

  if (!shouldArchive) {
    console.log('❌ Skipping archival. Exiting...');
    return;
  }

  console.log('\n⚠️  FINAL WARNING: This will archive ALL existing Stripe products and prices!');
  const finalConfirm = await askQuestion('Are you absolutely sure you want to continue?');

  if (!finalConfirm) {
    console.log('❌ Operation cancelled.');
    return;
  }

  console.log('\n🔄 Archiving old plans...');

  try {
    // Archive prices first
    const prices = await stripe.prices.list({ limit: 100 });
    for (const price of prices.data) {
      if (price.active) {
        await stripe.prices.update(price.id, { active: false });
        console.log(`✅ Archived price: ${price.id}`);
      }
    }

    // Archive products
    const products = await stripe.products.list({ limit: 100 });
    for (const product of products.data) {
      if (product.active) {
        await stripe.products.update(product.id, { active: false });
        console.log(`✅ Archived product: ${product.name} (${product.id})`);
      }
    }

    console.log('\n✅ All old plans archived successfully!');

  } catch (error: any) {
    console.error('❌ Error archiving plans:', error.message);
    throw error;
  }
}

/**
 * Step 4: Create new plans
 */
async function createNewPlansInteractive(): Promise<void> {
  console.log('\n🆕 STEP 4: Create New Plans\n');

  const shouldCreate = await askQuestion('Do you want to create the new plan structure?');

  if (!shouldCreate) {
    console.log('❌ Skipping plan creation.');
    return;
  }

  console.log('\n🔄 Creating new plans...');

  const plans = [
    {
      key: 'starter',
      name: 'Starter Plan',
      description: 'Perfect for individuals getting started with AI use cases - 14 day free trial',
      prices: [
        { interval: 'month', amount: 0, trial_period_days: 14 }
      ]
    },
    {
      key: 'pro',
      name: 'Pro Plan',
      description: 'For teams and businesses ready to scale with AI',
      prices: [
        { interval: 'month', amount: 2900 }, // $29.00 - UPDATE WITH YOUR ACTUAL PRICE
        { interval: 'year', amount: 29000 }  // $290.00 - UPDATE WITH YOUR ACTUAL PRICE
      ]
    }
    // Note: Enterprise is "Contact Us" only - no Stripe product needed
  ];

  const envVars: string[] = [];

  for (const plan of plans) {
    console.log(`\n📦 Creating ${plan.name}...`);

    try {
      // Create product
      const product = await stripe.products.create({
        name: plan.name,
        description: plan.description,
        metadata: {
          plan_type: plan.key,
          created_by: 'morphx-interactive-script',
          created_at: new Date().toISOString()
        }
      });

      console.log(`✅ Created product: ${product.name} (${product.id})`);
      envVars.push(`STRIPE_${plan.key.toUpperCase()}_PRODUCT_ID=${product.id}`);

      // Create prices
      for (const priceData of plan.prices) {
        const priceParams: Stripe.PriceCreateParams = {
          product: product.id,
          currency: 'usd',
          metadata: {
            plan_type: plan.key,
            billing_interval: priceData.interval,
            created_by: 'morphx-interactive-script'
          }
        };

        if (priceData.amount === 0) {
          // Free plan or trial - create a recurring price of $0
          priceParams.unit_amount = 0;
          priceParams.recurring = {
            interval: priceData.interval as 'month' | 'year',
            ...(priceData.trial_period_days && { trial_period_days: priceData.trial_period_days })
          };
        } else {
          // Paid plan - create recurring price
          priceParams.unit_amount = priceData.amount;
          priceParams.recurring = {
            interval: priceData.interval as 'month' | 'year'
          };
        }

        const price = await stripe.prices.create(priceParams);

        const displayAmount = priceData.amount === 0 ? 'Free' : `$${(priceData.amount / 100).toFixed(2)}`;
        const trialInfo = priceData.trial_period_days ? ` (${priceData.trial_period_days}-day trial)` : '';
        console.log(`  ✅ Created ${priceData.interval}ly price: ${displayAmount}${trialInfo} (${price.id})`);

        envVars.push(`STRIPE_${plan.key.toUpperCase()}_${priceData.interval.toUpperCase()}LY_PRICE_ID=${price.id}`);
      }

    } catch (error: any) {
      console.error(`❌ Error creating ${plan.name}:`, error.message);
    }
  }

  console.log('\n📝 Environment Variables:');
  console.log('Add these to your .env file:\n');
  envVars.forEach(envVar => console.log(envVar));

  console.log('\n✅ All new plans created successfully!');
}

/**
 * Main interactive function
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 Interactive Stripe Plan Management');
    console.log('=====================================\n');

    console.log('This script will help you:');
    console.log('1. Audit your current Stripe plans');
    console.log('2. Show the new plan structure');
    console.log('3. Archive old plans (with confirmation)');
    console.log('4. Create new plans matching your pricing');
    console.log('');

    // Step 1: Audit current plans
    await auditCurrentPlans();

    const continueStep2 = await askQuestion('\nDo you want to continue to see the new plan structure?');
    if (!continueStep2) {
      console.log('👋 Goodbye!');
      rl.close();
      return;
    }

    // Step 2: Show new plan structure
    await showNewPlanStructure();

    const continueStep3 = await askQuestion('\nDo you want to continue to archive old plans?');
    if (!continueStep3) {
      console.log('👋 Goodbye!');
      rl.close();
      return;
    }

    // Step 3: Archive old plans
    await archiveOldPlansInteractive();

    const continueStep4 = await askQuestion('\nDo you want to continue to create new plans?');
    if (!continueStep4) {
      console.log('👋 Goodbye!');
      rl.close();
      return;
    }

    // Step 4: Create new plans
    await createNewPlansInteractive();

    console.log('\n🎉 Plan management completed!');
    console.log('\nNext steps:');
    console.log('1. Update your .env file with the new environment variables');
    console.log('2. Restart your application');
    console.log('3. Test the new plans');

  } catch (error: any) {
    console.error('\n❌ Error:', error.message);
  } finally {
    rl.close();
  }
}

// Run the script
if (require.main === module) {
  main();
}

export { main as runInteractiveCleanup };
