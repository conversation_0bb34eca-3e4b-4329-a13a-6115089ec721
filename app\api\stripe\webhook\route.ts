import Stripe from 'stripe';
import { handleSubscriptionChange, stripe } from '@/lib/payments/stripe';
import { NextRequest, NextResponse } from 'next/server';
import { handleStripeSubscriptionUpdate } from '@/lib/services/subscription-service';
import { syncSubscriptionById } from '@/lib/services/subscription-sync';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  const payload = await request.text();
  const signature = request.headers.get('stripe-signature') as string;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(payload, signature, webhookSecret);
  } catch (err) {
    console.error('Webhook signature verification failed.', err);
    return NextResponse.json(
      { error: 'Webhook signature verification failed.' },
      { status: 400 }
    );
  }

  try {
    console.log(`Processing webhook event: ${event.type}`);

    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        const subscription = event.data.object as Stripe.Subscription;

        console.log('Subscription event received:', {
          id: subscription.id,
          status: subscription.status,
          customerId: subscription.customer,
          items: subscription.items.data.length
        });

        // Use the centralized sync service for better reliability
        try {
          console.log(`[Webhook] Syncing subscription ${subscription.id} using sync service`);
          const syncResult = await syncSubscriptionById(subscription.id);

          if (syncResult.success) {
            console.log(`[Webhook] Subscription sync successful: ${syncResult.planName} (${syncResult.subscriptionStatus})`);
          } else {
            console.error(`[Webhook] Subscription sync failed: ${syncResult.error}`);
          }
        } catch (error) {
          console.error('[Webhook] Error in subscription sync service:', error);
        }

        // Keep the old handlers for backward compatibility
        try {
          await handleSubscriptionChange(subscription);
        } catch (error) {
          console.error('Error in legacy subscription handler:', error);
        }

        try {
          if (subscription.items.data.length > 0) {
            const item = subscription.items.data[0];
            const priceId = item.price.id;

            // Get the product ID
            let productId;
            if (typeof item.price.product === 'string') {
              productId = item.price.product;
            } else {
              productId = item.price.product.id;
            }

            await handleStripeSubscriptionUpdate(
              subscription.id,
              subscription.status,
              subscription.customer as string,
              priceId,
              productId,
              subscription.current_period_start,
              subscription.current_period_end,
              subscription.cancel_at_period_end,
              subscription.canceled_at,
              subscription.trial_start,
              subscription.trial_end
            );

            console.log('Subscription updated successfully with legacy handler');
          } else {
            console.error('Subscription has no items');
          }
        } catch (error) {
          console.error('Error in legacy subscription service:', error);
        }
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error(`Error handling webhook event:`, error);
    return NextResponse.json(
      { error: 'Error handling webhook event' },
      { status: 500 }
    );
  }
}
