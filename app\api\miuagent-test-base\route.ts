import { NextResponse } from 'next/server';

export async function GET(req: Request) {
  try {
    console.log('Testing base MIUAgent API connection...');
    
    // Try to connect to the base domain
    const response = await fetch('https://miuagent-api.leafcraftstudios.com', {
      method: 'GET',
    });
    
    console.log('Response status:', response.status);
    
    let responseText = await response.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      responseData = { text: responseText };
    }
    
    return NextResponse.json({
      success: true,
      message: 'Base domain test completed',
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
      data: responseData,
    });
  } catch (error) {
    console.error('Error testing base domain:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
