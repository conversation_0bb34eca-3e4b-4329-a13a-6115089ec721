'use client';

import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ImageIcon } from '@radix-ui/react-icons';
import { Loader2 } from 'lucide-react';

interface CloudinaryUploaderProps {
  onSuccess: (url: string) => void;
  onError?: (error: any) => void;
  folder?: string;
  resourceType?: 'image' | 'video' | 'raw' | 'auto';
  className?: string;
  children?: React.ReactNode;
  buttonText?: string;
  variant?: 'default' | 'outline' | 'ghost';
}

export function CloudinaryUploader({
  onSuccess,
  onError,
  folder = 'case-studies',
  resourceType = 'image',
  className,
  children,
  buttonText = 'Upload Image',
  variant = 'outline',
}: CloudinaryUploaderProps) {
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    console.log(`Starting upload for file: ${file.name}, size: ${file.size} bytes, type: ${file.type}`);
    setIsLoading(true);

    try {
      // Create a FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Use unsigned upload with preset - hardcode the values to ensure they work
      // IMPORTANT: The upload preset must be created in the Cloudinary dashboard as an "unsigned" preset
      const uploadPreset = 'Path4ai'; // Make sure this matches EXACTLY with the preset name in Cloudinary
      formData.append('upload_preset', uploadPreset);
      formData.append('folder', folder);

      console.log('Sending upload request to Cloudinary with preset:', uploadPreset);

      // Upload directly to Cloudinary - hardcode the cloud name
      const cloudName = 'daf8hefrg'; // Make sure this matches EXACTLY with your Cloudinary cloud name
      console.log('Using cloud name:', cloudName);

      const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/${resourceType}/upload`, {
        method: 'POST',
        body: formData,
      });

      console.log(`Cloudinary response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Cloudinary error response:', errorText);
        throw new Error(`Failed to upload file: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Upload response data:', data);

      if (data.secure_url) {
        console.log(`Successfully uploaded file, URL: ${data.secure_url}`);
        onSuccess(data.secure_url);
      } else {
        console.error('No URL in response data:', data);
        throw new Error('No URL returned from upload');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      if (onError) onError(error);
    } finally {
      setIsLoading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className={className}>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        disabled={isLoading}
        className="hidden"
        accept={resourceType === 'image' ? 'image/*' : resourceType === 'video' ? 'video/*' : '*'}
      />
      {children ? (
        <div
          onClick={() => fileInputRef.current?.click()}
          className={`cursor-pointer ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
        >
          {children}
        </div>
      ) : (
        <Button
          type="button"
          variant={variant}
          onClick={() => fileInputRef.current?.click()}
          disabled={isLoading}
          className="flex items-center"
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <ImageIcon className="mr-2 h-4 w-4" />
          )}
          {isLoading ? 'Uploading...' : buttonText}
        </Button>
      )}
    </div>
  );
}
