'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface InvitationDetails {
  id: number;
  email: string;
  role: string;
  teamName: string;
  teamId: number;
}

export default function InvitationProcessor() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const inviteId = searchParams.get('inviteId');

  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [invitation, setInvitation] = useState<InvitationDetails | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);

  // Fetch the current user's email
  useEffect(() => {
    const fetchUserEmail = async () => {
      try {
        const response = await fetch('/api/auth/user');
        if (response.ok) {
          const data = await response.json();
          setUserEmail(data.email);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchUserEmail();
  }, []);

  // Fetch invitation details
  useEffect(() => {
    if (!inviteId) return;

    const fetchInvitation = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/invitations/${inviteId}`);

        if (!response.ok) {
          const errorData = await response.json();
          setError(errorData.error || 'Failed to fetch invitation details');
          return;
        }

        const data = await response.json();
        setInvitation(data);
      } catch (error) {
        console.error('Error fetching invitation:', error);
        setError('An error occurred while fetching the invitation');
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [inviteId]);

  const handleAcceptInvitation = async () => {
    if (!invitation) return;

    try {
      setProcessing(true);
      const response = await fetch('/api/invitations/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invitationId: invitation.id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process invitation');
      }

      toast({
        title: 'Success',
        description: `You have joined ${invitation.teamName} as a ${invitation.role}`,
      });

      // Refresh the page to update the UI with the new role
      router.refresh();

      // Redirect to the team page
      router.push('/dashboard/team');
    } catch (error: any) {
      console.error('Error accepting invitation:', error);
      setError(error.message || 'An error occurred while processing the invitation');
      toast({
        title: 'Error',
        description: error.message || 'Failed to process invitation',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  // If no invitation ID is provided, don't render anything
  if (!inviteId) {
    return null;
  }

  // Show loading state
  if (loading) {
    return (
      <Card className="w-full max-w-md mx-auto mt-8">
        <CardHeader>
          <CardTitle>Processing Invitation</CardTitle>
          <CardDescription>Please wait while we fetch the invitation details</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-6">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  // Show error state
  if (error) {
    return (
      <Card className="w-full max-w-md mx-auto mt-8">
        <CardHeader>
          <CardTitle className="text-red-500">Invitation Error</CardTitle>
          <CardDescription>There was a problem with this invitation</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-red-500">
            <XCircle className="h-5 w-5" />
            <p>{error}</p>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </CardFooter>
      </Card>
    );
  }

  // Show invitation details
  if (invitation) {
    // Check if the invitation is for the current user
    const isForCurrentUser = userEmail && userEmail === invitation.email;

    return (
      <Card className="w-full max-w-md mx-auto mt-8">
        <CardHeader>
          <CardTitle>Team Invitation</CardTitle>
          <CardDescription>You've been invited to join a team</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-1">
            <p className="text-sm font-medium">Team</p>
            <p className="text-lg font-bold">{invitation.teamName}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium">Role</p>
            <p className="text-lg font-bold capitalize">{invitation.role}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium">Invited Email</p>
            <p className="text-lg">{invitation.email}</p>
          </div>

          {!isForCurrentUser && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mt-4">
              <p className="text-yellow-800 text-sm">
                This invitation is for <strong>{invitation.email}</strong>, but you are currently logged in as <strong>{userEmail}</strong>.
                Please log out and sign in with the correct email to accept this invitation.
              </p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/dashboard')}>
            Cancel
          </Button>
          <Button
            onClick={handleAcceptInvitation}
            disabled={processing || !isForCurrentUser}
          >
            {processing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Accept Invitation
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return null;
}
