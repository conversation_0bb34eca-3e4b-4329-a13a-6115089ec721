import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { invitations, teamMembers, users, activityLogs, ActivityType } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { eq, and } from 'drizzle-orm';

// POST /api/invitations/process - Process an invitation for an existing user
export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { invitationId } = body;

    if (!invitationId) {
      return NextResponse.json({ error: 'Invitation ID is required' }, { status: 400 });
    }

    // Get the invitation
    const invitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, invitationId),
      with: {
        team: true,
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found' }, { status: 404 });
    }

    // Check if the invitation is for the current user
    const currentUser = await db.query.users.findFirst({
      where: eq(users.id, session.user.id),
    });

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    if (currentUser.email !== invitation.email) {
      return NextResponse.json({ error: 'This invitation is not for you' }, { status: 403 });
    }

    // Check if the user is already a member of the team
    const existingMembership = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, session.user.id),
        eq(teamMembers.teamId, invitation.teamId)
      ),
    });

    if (existingMembership) {
      // Update the role if it's different
      if (existingMembership.role !== invitation.role) {
        await db
          .update(teamMembers)
          .set({ role: invitation.role })
          .where(eq(teamMembers.id, existingMembership.id));

        // Log the role change
        await db.insert(activityLogs).values({
          teamId: invitation.teamId,
          userId: session.user.id,
          action: ActivityType.ROLE_CHANGED,
        });

        console.log(`Updated user ${session.user.id} role to ${invitation.role} in team ${invitation.teamId}`);
      } else {
        console.log(`User ${session.user.id} already has role ${invitation.role} in team ${invitation.teamId}`);
      }
    } else {
      // Add the user to the team with the specified role
      await db.insert(teamMembers).values({
        userId: session.user.id,
        teamId: invitation.teamId,
        role: invitation.role,
      });

      // Log the join
      await db.insert(activityLogs).values({
        teamId: invitation.teamId,
        userId: session.user.id,
        action: ActivityType.JOIN_TEAM,
      });

      console.log(`Added user ${session.user.id} with role ${invitation.role} to team ${invitation.teamId}`);
    }

    // If the invitation role is 'owner', update the user's role in the users table
    if (invitation.role === 'owner') {
      await db
        .update(users)
        .set({ role: 'owner' })
        .where(eq(users.id, session.user.id));

      console.log(`Updated user ${session.user.id} global role to owner`);
    }

    // Mark the invitation as accepted
    await db
      .update(invitations)
      .set({ status: 'accepted' })
      .where(eq(invitations.id, invitationId));

    console.log(`Marked invitation ${invitationId} as accepted`);

    return NextResponse.json({
      success: 'Invitation processed successfully',
      teamId: invitation.teamId,
      role: invitation.role
    });
  } catch (error) {
    console.error('Error processing invitation:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
