# Batch Processing with MIUAgent API

This document explains how the batch processing system works to send case studies to the MIUAgent API for market intelligence data enrichment.

## Overview

The system automatically processes case studies by sending them to the MIUAgent API during nighttime. The API analyzes the case studies and returns enriched market intelligence data, which is then used to update the case studies in the database.

## How It Works

1. A cron job is scheduled to run at 12 PM every day
2. The cron job triggers the `/api/cron/process-cases` endpoint
3. This endpoint calls the batch processing API at `/api/case-studies/process-batch`
4. The batch processing API:
   - Fetches unprocessed case studies from the database
   - Sends them to the MIUAgent API
   - Updates the database with the returned data

## Configuration

The following environment variables need to be set:

```
# MIUAgent API configuration
MIUAGENT_API_URL=https://miuagent-api.leafcraftstudios.com
MIUAGENT_API_KEY=your_miuagent_api_key

# Batch processing configuration
BATCH_PROCESSING_API_KEY=your_batch_processing_api_key
CRON_SECRET=your_cron_job_secret
```

## Setting Up the Cron Job

To set up the cron job, you need to configure it on your server. Here's an example using crontab:

```bash
# Run at 12 PM every day
0 12 * * * curl -X GET https://your-domain.com/api/cron/process-cases -H "Authorization: Bearer your_cron_secret"
```

## Database Schema

The case studies table has been updated with the following fields to track AI processing:

- `aiProcessed`: Timestamp of when the case study was last processed by the AI
- `aiProcessingStatus`: Status of the AI processing (success, error)
- `aiProcessingError`: Error message if the AI processing failed
- `aiRequestId`: Request ID from the MIUAgent API

Additionally, the following fields have been added to store market intelligence data:

- `marketIntelligenceData`: JSON data containing market intelligence sections
- `marketMetricsData`: JSON data containing market metrics
- `marketSize`: Market size value in dollars
- `marketCAGR`: Compound Annual Growth Rate
- `marketROI`: Return on Investment range

## Testing

To test the batch processing system:

1. Make sure you have set up the environment variables
2. Run the following command to trigger the batch processing manually:

```bash
curl -X GET http://localhost:3000/api/cron/process-cases -H "Authorization: Bearer your_cron_secret"
```

## Troubleshooting

If you encounter issues with the batch processing:

1. Check the server logs for error messages
2. Verify that the MIUAgent API key is valid
3. Make sure the database schema has been updated with the AI processing fields
4. Check that the cron job is properly configured and running
