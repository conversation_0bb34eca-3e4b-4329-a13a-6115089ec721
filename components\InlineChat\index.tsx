'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send, Loader2, Bo<PERSON>, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface InlineChatProps {
  caseStudyId?: string | number;
  caseStudyTitle?: string;
  caseStudyContent?: string;
}

export const InlineChat: React.FC<InlineChatProps> = ({
  caseStudyId,
  caseStudyTitle,
  caseStudyContent,
}) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      role: 'assistant',
      content: `Hello! I'm your AI assistant for "${
        caseStudyTitle || 'this case study'
      }". You can ask me questions about:

• Key insights and findings
• Implementation strategies
• Similar case studies
• Industry trends related to this topic
• Technical details or explanations

How can I assist you today?`,
      timestamp: new Date(),
    },
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatMessagesRef = useRef<HTMLDivElement>(null);

  function scrollToBottom() {
    chatMessagesRef.current?.scrollTo({
      top: chatMessagesRef.current.scrollHeight + 100,
      behavior: 'smooth',
    });
  }

  const handleSendMessage = async () => {
    if (!input.trim()) return;

    // Add user message to chat
    const userMessage: Message = {
      role: 'user',
      content: input,
      timestamp: new Date(),
    };

    // Store the current input for use in the API call
    const currentInput = input;

    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    console.log('Sending message:', {
      message: currentInput,
      caseStudyId,
      caseStudyTitle,
      sessionId,
    });

    try {
      // Send message to our API route that forwards to MIUAgent API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentInput, // Use the stored input
          caseStudyId: caseStudyId || null,
          caseStudyTitle: caseStudyTitle || 'AI Agents Case Study',
          caseStudyContent: caseStudyContent || null,
          history: messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
          sessionId: sessionId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Error response from API:', {
          status: response.status,
          statusText: response.statusText,
          data,
        });
        throw new Error(
          data.error ||
            `Failed to get response from AI: ${response.status} ${response.statusText}`
        );
      }

      if (data.error) {
        console.error('Error in API response:', data.error);
        throw new Error(data.error);
      }

      // Process the response
      console.log('Received response from API:', data);

      // Handle retry case (session expired)
      if (data.retry) {
        console.log('Session expired, retrying...');
        setSessionId(null);
        handleSendMessage();
        return;
      }

      // Check if the response was successful
      // We'll be more lenient here since we've already handled success in the API
      if (data.success === false) {
        console.error('Error in API response:', data);
        throw new Error(data.error || 'Failed to get response from AI');
      }

      // Extract the message from the data structure
      let responseText = '';

      console.log('Full API response data:', data);

      // Check for message in the data.data.message structure (most common format from the API)
      if (data.data && data.data.message) {
        responseText = data.data.message;
        console.log('Found message in data.data.message:', responseText);
      }
      // Check for direct message property
      else if (data.message) {
        responseText = data.message;
        console.log('Found message in data.message:', responseText);
      }
      // Check for response property
      else if (data.response) {
        responseText = data.response;
        console.log('Found message in data.response:', responseText);
      }
      // Check for raw response
      else if (data.rawResponse) {
        responseText = data.rawResponse;
        console.log('Using raw response:', responseText);
      }
      // If all else fails, try to extract from the conversation array
      else if (
        data.data &&
        data.data.conversation &&
        Array.isArray(data.data.conversation)
      ) {
        // Find the last assistant message in the conversation
        const assistantMessages = data.data.conversation.filter(
          (msg: any) =>
            msg && (msg.role === 'assistant' || msg.role === 'system')
        );

        if (assistantMessages.length > 0) {
          // Get the last assistant message
          const lastMessage = assistantMessages[assistantMessages.length - 1];
          if (lastMessage && lastMessage.content) {
            responseText = lastMessage.content;
            console.log(
              'Extracted message from conversation array:',
              responseText
            );
          }
        }
      }

      // If we still don't have a message, stringify the data as a last resort
      if (!responseText) {
        try {
          responseText = JSON.stringify(data, null, 2);
          console.log('Stringified entire response as fallback');
        } catch (e) {
          responseText =
            "Received response from API but couldn't extract the message.";
          console.error('Error stringifying response:', e);
        }
      }

      console.log('Final response text to display:', responseText);

      // Add AI response to chat
      const aiMessage: Message = {
        role: 'assistant',
        content: responseText,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, aiMessage]);

      // Store the session ID if it was returned in the response
      if (data.session_id) {
        const sessionKey = `${caseStudyId}-${Date.now()}`;
        console.log(
          'Storing session ID:',
          data.session_id,
          'for key:',
          sessionKey
        );
        setSessionId(data.session_id);
      }

      if (chatMessagesRef.current) {
        scrollToBottom();
      }
    } catch (error) {
      console.error('Error communicating with AI:', error);

      // Get detailed error message if available
      let errorContent =
        "I'm sorry, I encountered an error while processing your request. This could be due to a connection issue or the AI service being temporarily unavailable. Please try again in a moment.";

      if (error instanceof Error) {
        console.error('Error details:', error.message);

        // If it's a response error with details
        if ('details' in (error as any)) {
          errorContent += `\n\nError details: ${(error as any).details}`;
        }
      }

      // Add error message
      const errorMessage: Message = {
        role: 'assistant',
        content: errorContent,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className='w-full rounded-xl border border-gray-100 dark:border-gray-900 bg-white dark:bg-black shadow-sm max-w-full flex flex-col h-full'>
      {/* Chat Header */}
      <div className='bg-gradient-to-r from-blue-600 to-blue-800 text-white py-2 px-3 flex-shrink-0'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center'>
            <div className='bg-white/20 p-1.5 rounded-full mr-2'>
              <Bot className='h-4 w-4' />
            </div>
            <div>
              <h3 className='font-medium text-sm'>AI Assistant</h3>
              <p className='text-xs text-blue-100'>
                Ask me anything about this case study
              </p>
            </div>
          </div>
          <div className='bg-blue-500 px-2 py-0.5 rounded-full text-xs font-medium'>
            MIUAgent
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div
        className='p-3 overflow-y-auto space-y-3 bg-white dark:bg-black scrollbar-thin scrollbar-thumb-blue-300 dark:scrollbar-thumb-blue-700 scrollbar-track-transparent scrollbar-thumb-rounded-full flex-grow min-h-[200px]'
        ref={chatMessagesRef}
      >
        {messages.map((message, index) => (
          <div
            key={index}
            className={cn(
              'flex items-start gap-3',
              message.role === 'user' ? 'justify-end' : 'justify-start'
            )}
          >
            {message.role === 'assistant' && (
              <div className='bg-blue-100 dark:bg-blue-900 p-2 rounded-full flex-shrink-0 mt-1'>
                <Bot className='h-5 w-5 text-blue-600 dark:text-blue-400' />
              </div>
            )}

            <div
              className={cn(
                'rounded-2xl p-3 sm:p-4 shadow-sm max-w-[85%] sm:max-w-[75%]',
                message.role === 'user'
                  ? 'bg-blue-500 text-white rounded-tr-none'
                  : 'bg-white dark:bg-black text-black dark:text-white rounded-tl-none border border-gray-100 dark:border-gray-900'
              )}
            >
              {message.role === 'assistant' && message.content.includes('•') ? (
                <div className='whitespace-pre-wrap text-sm leading-relaxed'>
                  {message.content.split('•').map((part, i) => {
                    if (i === 0) return <p key={i}>{part}</p>;
                    return (
                      <div key={i} className='flex items-start mt-2'>
                        <span className='mr-2 text-blue-500 dark:text-blue-400 font-bold'>
                          •
                        </span>
                        <span>{part}</span>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className='whitespace-pre-wrap text-sm leading-relaxed prose prose-sm dark:prose-invert max-w-none'>
                  <ReactMarkdown>{message.content}</ReactMarkdown>
                </div>
              )}
              <div className='text-xs opacity-70 mt-2 text-right'>
                {message.timestamp.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </div>
            </div>

            {message.role === 'user' && (
              <div className='bg-blue-600 p-2 rounded-full flex-shrink-0 mt-1'>
                <User className='h-5 w-5 text-white' />
              </div>
            )}
          </div>
        ))}

        {isLoading && (
          <div className='flex items-center justify-center py-4 px-6 bg-white dark:bg-black rounded-xl shadow-sm max-w-[85%] mx-auto border border-gray-100 dark:border-gray-900'>
            <div className='flex items-center gap-3'>
              <div className='relative'>
                <div className='h-8 w-8 rounded-full border-2 border-blue-500 dark:border-blue-400 border-t-transparent animate-spin'></div>
                <Bot className='h-4 w-4 text-blue-500 dark:text-blue-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2' />
              </div>
              <span className='text-sm text-black dark:text-white font-medium'>
                AI is thinking...
              </span>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <div className='p-2 border-t border-gray-100 dark:border-gray-900 bg-white dark:bg-black flex-shrink-0'>
        <div className='flex items-center gap-1 bg-white dark:bg-black rounded-lg p-1 pl-2 border border-gray-100 dark:border-gray-900 focus-within:border-blue-300 dark:focus-within:border-blue-700 focus-within:ring-1 focus-within:ring-blue-100 dark:focus-within:ring-blue-900 transition-all shadow-sm'>
          <div className='flex-1 relative'>
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder='Ask a question...'
              className='flex-1 min-h-8 resize-none border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 py-1 pr-1 text-sm text-black dark:text-white'
              rows={1}
              disabled={isLoading}
            />
            {!isLoading && !input.trim() && (
              <div className='absolute right-1 bottom-1 flex flex-wrap gap-1 max-w-[calc(100%-20px)]'>
                <div
                  className='text-[10px] bg-gray-100 dark:bg-gray-900 text-blue-600 dark:text-blue-400 px-1 py-0.5 rounded-full cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 transition-colors border border-gray-100 dark:border-gray-900'
                  onClick={() => setInput('Key insights?')}
                >
                  Insights
                </div>
                <div
                  className='text-[10px] bg-gray-100 dark:bg-gray-900 text-blue-600 dark:text-blue-400 px-1 py-0.5 rounded-full cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 transition-colors border border-gray-100 dark:border-gray-900'
                  onClick={() => setInput('Implementation steps?')}
                >
                  Implementation
                </div>
              </div>
            )}
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={isLoading || !input.trim()}
            size='sm'
            className={cn(
              'rounded-lg px-2 py-1 transition-all h-8 w-8',
              isLoading || !input.trim()
                ? 'bg-blue-200 dark:bg-blue-900 text-blue-400 dark:text-blue-600'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            )}
          >
            {isLoading ? (
              <Loader2 className='h-4 w-4 animate-spin' />
            ) : (
              <Send className='h-4 w-4' />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default InlineChat;
