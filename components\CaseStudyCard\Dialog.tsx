import * as Dialog from '@radix-ui/react-dialog';
import { motion, AnimatePresence } from 'framer-motion';
import { CaseStudyCard } from './Card';
import { Cross2Icon } from '@radix-ui/react-icons';
import type { CaseStudyData } from './types';

interface CaseStudyDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  data: CaseStudyData;
}

export function CaseStudyDialog({ isOpen, onOpenChange, data }: CaseStudyDialogProps) {
  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <AnimatePresence>
        {isOpen && (
          <Dialog.Portal forceMount>
            <Dialog.Overlay asChild>
              <motion.div
                className="fixed inset-0 bg-black/50 z-50"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              />
            </Dialog.Overlay>
            <Dialog.Content asChild>
              <motion.div
                className="fixed inset-x-4 bottom-0 top-20 bg-white rounded-t-xl overflow-auto z-50"
                initial={{ y: '100%' }}
                animate={{ y: 0 }}
                exit={{ y: '100%' }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              >
                <div className="sticky top-0 flex justify-end p-4 bg-white/80 backdrop-blur-sm border-b">
                  <Dialog.Close className="rounded-full p-2 hover:bg-gray-100 transition-colors">
                    <Cross2Icon className="h-4 w-4" />
                  </Dialog.Close>
                </div>
                <div className="p-4">
                  <CaseStudyCard {...data} />
                </div>
              </motion.div>
            </Dialog.Content>
          </Dialog.Portal>
        )}
      </AnimatePresence>
    </Dialog.Root>
  );
} 