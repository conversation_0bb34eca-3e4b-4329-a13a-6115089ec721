// Force dynamic rendering since we use cookies
export const dynamic = 'force-dynamic';

import { Button } from '@/components/ui/button';
import { ArrowLeftIcon, SpeakerQuietIcon } from '@radix-ui/react-icons';
import { CaseStudyCard } from '@/components/CaseStudyCard/Card';
import React from 'react';
import { db } from '@/lib/db/drizzle';
import { caseStudies, bookmarks } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import Link from 'next/link';
import { SimilarCaseStudies } from '@/components/SimilarCaseStudies';
import { formatCaseStudyForDisplay } from '@/lib/utils/format-case-study';
import { getUser } from '@/lib/db/server-queries';
import { BookmarkButton } from '@/components/BookmarkButton';
import { DeleteButton } from '@/components/DeleteButton';

// Type for the full case study response
type FullCaseStudyResponse = typeof caseStudies.$inferSelect & {
  icons: any[];
  headerImage?: string;
};

// Define the correct type for params according to Next.js
type PageProps = {
  params: { id: string };
};

export default async function SharedCaseStudyDetailPage({ params }: PageProps) {
  // Parse the ID from params
  const id = parseInt(params.id);

  // Get the current user
  const user = await getUser();
  const userId = user?.id;
  const userRole = user?.role || 'member';
  const isOwner = userRole === 'owner';
  const isActualOwner = !(
    user?.teamRole === 'teamMember' || user?.teamRole === 'viewer'
  );

  if (!userId) {
    return (
      <div className='lg:p-6 text-center'>
        <h1 className='text-2xl font-bold text-red-600'>
          Authentication Error
        </h1>
        <p className='mt-2'>Please sign in to view this page.</p>
      </div>
    );
  }

  // Fetch the case study data directly from the database
  const caseStudyData = await db.query.caseStudies.findFirst({
    where: eq(caseStudies.id, id),
    with: {
      icons: {
        orderBy: (icons, { asc }) => [asc(icons.order)],
      },
    },
  });
  // console.log('Raw Case Study Data fetched from server: ', caseStudyData);
  // Handle case study not found with a more user-friendly message
  if (!caseStudyData) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen p-4'>
        <div className='bg-red-50 border border-red-200 rounded-lg p-6 max-w-md w-full text-center'>
          <h1 className='text-2xl font-bold mb-2 text-red-700'>
            Case Study Not Found
          </h1>
          <p className='text-gray-600 mb-6'>
            This case study may have been deleted or doesn't exist. If you were
            expecting to see a case study here, please contact support.
          </p>
          <Link
            href={isOwner ? '/dashboard/case-studies' : '/dashboard/member'}
          >
            <Button className='w-full'>
              <ArrowLeftIcon className='mr-2 h-4 w-4' />
              {isOwner ? 'Back to Case Studies' : 'Back to Dashboard'}
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  // Check if the case study is bookmarked by the user
  const bookmarkData = await db.query.bookmarks.findFirst({
    where: and(eq(bookmarks.userId, userId), eq(bookmarks.caseStudyId, id)),
  });

  const isBookmarked = !!bookmarkData;

  // Cast the data to our extended type
  const caseStudy = caseStudyData as unknown as FullCaseStudyResponse;

  // Determine the back link based on user role
  const backLink = isOwner ? '/dashboard/case-studies' : '/dashboard/member';
  const backText = isOwner ? 'Back to Case Studies' : 'Back to Dashboard';

  return (
    <div className='container mx-auto'>
      <div className='flex justify-between items-center mb-6'>
        <Button variant='outline' asChild>
          <Link href={backLink}>
            <ArrowLeftIcon className='mr-2 h-4 w-4' />
            {backText}
          </Link>
        </Button>
        <div className='flex gap-2 items-center'>
          {/* Show bookmark button only for members */}
          {(!isOwner as any) && (
            <BookmarkButton
              caseStudyId={id}
              initialIsBookmarked={isBookmarked}
              compact={true}
            />
          )}

          {/* Show MUI Report button for all users if market data exists */}
          {(caseStudy.marketIntelligenceData ||
            caseStudy.marketMetricsData) && (
            <Button
              variant='outline'
              className='bg-blue-500 hover:bg-blue-600 text-white'
              asChild
            >
              <Link href={`/dashboard/reports/${id}/view`}>
                View MIU Report
              </Link>
            </Button>
          )}

          {/* Show Process with AI button only for owners */}
          {/* {isOwner && !caseStudy.aiProcessed && (
            <form action={`/api/case-studies/${id}/process`} method="post">
              <Button
                type="submit"
                variant="outline"
                className="bg-green-500 hover:bg-green-600 text-white"
              >
                <SpeakerQuietIcon className="mr-2 h-4 w-4" />
                Process with AI
              </Button>
            </form>
          )} */}

          {/* Show delete button only for owners */}
          {isOwner && isActualOwner && (
            <DeleteButton
              caseStudyId={id}
              size='default'
              variant='outline'
              className='text-red-500'
            />
          )}
        </div>
      </div>

      {/* Process the case study data with fallbacks for missing fields */}
      <CaseStudyCard
        {...formatCaseStudyForDisplay(caseStudy)}
        isOwner={isOwner}
      />

      {/* Similar Case Studies Section */}
      <div className='mt-12'>
        {/* <h2 className="text-2xl font-bold mb-6">Similar Case Studies</h2> */}
        <SimilarCaseStudies
          currentCaseStudyId={id}
          industry={caseStudy.industry || ''}
          role={caseStudy.role || ''}
        />
      </div>
    </div>
  );
}
