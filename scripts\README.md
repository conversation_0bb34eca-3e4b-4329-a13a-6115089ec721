# Stripe Plan Management Scripts

This directory contains scripts to manage your Stripe plans and ensure they match your application's pricing structure.

## 🎯 Purpose

These scripts help you:
1. **Audit** your current Stripe setup
2. **Clean up** old/unused products and prices
3. **Create** fresh plans that match your pricing structure
4. **Migrate** from old plans to new ones safely

## 📋 Available Scripts

### 1. `npm run stripe:audit`
**Safe to run** - Only reads data, makes no changes.

```bash
npm run stripe:audit
```

**What it does:**
- Shows all products, prices, and subscriptions in your Stripe account
- Displays account information
- Lists active subscriptions (important before cleanup)
- Provides a summary of your current setup

**Use this first** to understand what's currently in your Stripe account.

### 2. `npm run stripe:interactive`
**Interactive and safe** - Asks for confirmation before making changes.

```bash
npm run stripe:interactive
```

**What it does:**
- Step-by-step guided process
- Shows current plans and what will be created
- Asks for confirmation before each destructive action
- Archives old plans and creates new ones
- Generates environment variables for your .env file

**Recommended approach** for most users.

### 3. `npm run stripe:cleanup`
**Automated** - Runs without prompts (use with caution).

```bash
npm run stripe:cleanup
```

**What it does:**
- Automatically audits, archives, and creates plans
- No user interaction required
- Generates environment variables
- **Use only if you're sure about the changes**

## 🏗️ New Plan Structure

The scripts will create these plans in Stripe:

### 📋 Starter Plan (Free)
- **Price**: Free
- **Features**: 1 user, 10 case studies, basic features
- **Stripe Products**: 
  - `STRIPE_STARTER_PRODUCT_ID`
  - `STRIPE_STARTER_MONTHLY_PRICE_ID` (Free)
  - `STRIPE_STARTER_YEARLY_PRICE_ID` (Free)

### 📋 Pro Plan
- **Price**: $29/month or $290/year (2 months free)
- **Features**: 3 users, 100 case studies, advanced features
- **Stripe Products**:
  - `STRIPE_PRO_PRODUCT_ID`
  - `STRIPE_PRO_MONTHLY_PRICE_ID` ($29.00)
  - `STRIPE_PRO_YEARLY_PRICE_ID` ($290.00)

### 📋 Enterprise Plan
- **Price**: $99/month or $990/year (2 months free)
- **Features**: Unlimited users, all features, dedicated support
- **Stripe Products**:
  - `STRIPE_ENTERPRISE_PRODUCT_ID`
  - `STRIPE_ENTERPRISE_MONTHLY_PRICE_ID` ($99.00)
  - `STRIPE_ENTERPRISE_YEARLY_PRICE_ID` ($990.00)

## ⚠️ Important Warnings

### Before Running Cleanup Scripts:

1. **Backup Important Data**: Export any important subscription data
2. **Check Active Subscriptions**: Run `npm run stripe:audit` first
3. **Understand Impact**: Archiving products affects existing subscriptions
4. **Test Environment**: Consider running on test data first

### If You Have Active Subscriptions:

The scripts will show you active subscriptions before proceeding. You have options:

1. **Migrate Subscriptions**: Update existing subscriptions to new plans
2. **Let Them Expire**: Allow current subscriptions to run their course
3. **Manual Handling**: Handle each subscription individually

## 🚀 Step-by-Step Process

### Recommended Workflow:

1. **Audit Current Setup**
   ```bash
   npm run stripe:audit
   ```

2. **Run Interactive Cleanup**
   ```bash
   npm run stripe:interactive
   ```

3. **Update Environment Variables**
   - Copy the generated environment variables
   - Add them to your `.env` file
   - Remove old Stripe environment variables

4. **Restart Application**
   ```bash
   npm run dev
   ```

5. **Test New Plans**
   - Visit `/dashboard/billing`
   - Test upgrade flows
   - Verify plan features work correctly

## 📝 Environment Variables

After running the scripts, you'll get environment variables like:

```env
# Stripe Product IDs
STRIPE_STARTER_PRODUCT_ID=prod_xxxxx
STRIPE_PRO_PRODUCT_ID=prod_xxxxx
STRIPE_ENTERPRISE_PRODUCT_ID=prod_xxxxx

# Stripe Price IDs
STRIPE_STARTER_MONTHLY_PRICE_ID=price_xxxxx
STRIPE_STARTER_YEARLY_PRICE_ID=price_xxxxx
STRIPE_PRO_MONTHLY_PRICE_ID=price_xxxxx
STRIPE_PRO_YEARLY_PRICE_ID=price_xxxxx
STRIPE_ENTERPRISE_MONTHLY_PRICE_ID=price_xxxxx
STRIPE_ENTERPRISE_YEARLY_PRICE_ID=price_xxxxx
```

## 🔧 Troubleshooting

### Common Issues:

1. **Authentication Error**
   - Check your `STRIPE_SECRET_KEY` in `.env`
   - Ensure you're using the correct key (test vs live)

2. **Permission Error**
   - Verify your Stripe API key has the necessary permissions
   - Check if you're using a restricted key

3. **Active Subscriptions**
   - The script will warn you about active subscriptions
   - Consider migrating them before cleanup

4. **Environment Variables**
   - Make sure to update your `.env` file with new variables
   - Restart your application after updating

### Getting Help:

If you encounter issues:
1. Check the console output for detailed error messages
2. Run `npm run stripe:audit` to see current state
3. Verify your Stripe API keys are correct
4. Check Stripe dashboard for any issues

## 🎯 After Running Scripts

1. **Update .env file** with new environment variables
2. **Restart your application**
3. **Test the billing page** (`/dashboard/billing`)
4. **Test upgrade flows** for each plan
5. **Verify webhook handling** still works
6. **Check subscription sync** functionality

## 📚 Related Files

- `lib/config/plans.ts` - Plan configuration and features
- `lib/services/subscription-upgrade.ts` - Upgrade service
- `app/api/subscriptions/upgrade/route.ts` - Upgrade API
- `app/(dashboard)/dashboard/billing/page.tsx` - Billing page

## 🔒 Security Notes

- These scripts use your Stripe secret key
- Never commit your `.env` file to version control
- Test on Stripe test environment first if possible
- Keep backups of important subscription data
