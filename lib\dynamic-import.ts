/**
 * Utilities for optimized dynamic imports and code splitting.
 * These utilities help reduce initial bundle size and
 * improve application load performance.
 */

import { lazy, ComponentType } from 'react';

// Type for dynamic import options
interface DynamicImportOptions {
  // Whether to preload the component
  preload?: boolean;
  // Whether to prefetch the component
  prefetch?: boolean;
  // Delay in milliseconds before loading
  loadDelay?: number;
  // Component display name for debugging
  displayName?: string;
}

// Cache for preloaded modules
const preloadCache = new Map<string, Promise<any>>();

/**
 * Dynamically import a module with preloading and prefetching options
 * 
 * @param importFn Dynamic import function
 * @param options Import options
 * @returns Promise resolving to the imported module
 */
export function dynamicImport<T>(
  importFn: () => Promise<T>,
  options: DynamicImportOptions = {}
): () => Promise<T> {
  const {
    preload = false,
    prefetch = false,
    loadDelay = 0,
    displayName = 'DynamicModule',
  } = options;

  // Generate a unique key for this import
  const importKey = displayName;
  
  // Function to perform the actual import
  const doImport = (): Promise<T> => {
    // Add delay if needed
    if (loadDelay > 0) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(importFn());
        }, loadDelay);
      });
    }
    
    return importFn();
  };
  
  // Preload the module if requested
  if (preload && typeof window !== 'undefined') {
    if (!preloadCache.has(importKey)) {
      preloadCache.set(importKey, doImport());
    }
  }
  
  // Prefetch the module if requested
  if (prefetch && !preload && typeof window !== 'undefined') {
    // Use requestIdleCallback for prefetching
    const prefetchFn = () => {
      if (!preloadCache.has(importKey)) {
        preloadCache.set(importKey, doImport());
      }
    };
    
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(prefetchFn, { timeout: 2000 });
    } else {
      setTimeout(prefetchFn, 1000);
    }
  }
  
  // Return a function that returns the import promise
  return () => {
    if (preloadCache.has(importKey)) {
      return preloadCache.get(importKey)!;
    }
    
    const promise = doImport();
    preloadCache.set(importKey, promise);
    return promise;
  };
}

/**
 * Dynamically import a React component with optimizations
 * 
 * @param importFn Dynamic import function
 * @param options Import options
 * @returns Lazy-loaded React component
 */
export function dynamicComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: DynamicImportOptions = {}
): T {
  const enhancedImport = dynamicImport(importFn, options);
  
  // Create lazy component
  const LazyComponent = lazy(enhancedImport);
  
  // Set display name for debugging
  const displayName = options.displayName || 'DynamicComponent';
  Object.defineProperty(LazyComponent, 'displayName', {
    value: displayName,
  });
  
  return LazyComponent as unknown as T;
}

/**
 * Preload multiple modules for critical paths
 * 
 * @param imports Array of import functions to preload
 */
export function preloadModules(
  imports: Array<() => Promise<any>>
): void {
  if (typeof window === 'undefined') return;
  
  const preload = () => {
    imports.forEach(importFn => {
      dynamicImport(importFn, { preload: true })();
    });
  };
  
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(preload, { timeout: 2000 });
  } else {
    setTimeout(preload, 1000);
  }
}

/**
 * Create a dynamic import with automatic retry logic
 * Useful for handling network issues during lazy loading
 * 
 * @param importFn Dynamic import function
 * @param retries Number of retries
 * @param retryDelay Delay between retries in milliseconds
 * @returns Function that returns import promise with retries
 */
export function importWithRetry<T>(
  importFn: () => Promise<T>,
  retries: number = 3,
  retryDelay: number = 1000
): () => Promise<T> {
  return () => {
    return new Promise<T>((resolve, reject) => {
      let attempts = 0;
      
      const tryImport = () => {
        attempts++;
        importFn()
          .then(resolve)
          .catch(error => {
            if (attempts < retries) {
              console.warn(`Dynamic import failed (attempt ${attempts}). Retrying...`);
              setTimeout(tryImport, retryDelay);
            } else {
              console.error(`Dynamic import failed after ${retries} retries`);
              reject(error);
            }
          });
      };
      
      tryImport();
    });
  };
}

export default dynamicImport;
