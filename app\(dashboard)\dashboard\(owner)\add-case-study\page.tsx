'use client';

import { useState } from 'react';
import { CaseStudyCard } from '@/components/CaseStudyCard/Card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@radix-ui/react-tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useRouter } from 'next/navigation';
import { CaseStudyData, useCreateCaseStudyMutation } from '@/lib/redux/api/caseStudiesApi';
import { CloudinaryUploader } from '@/components/ui/cloudinary-uploader';
import { toast } from '@/components/ui/use-toast';
import { createUploadFormData } from '@/lib/utils/form-data-helper';

export default function AddCaseStudyPage() {
  const router = useRouter();
  const [createCaseStudy, { isLoading: isCreating }] = useCreateCaseStudyMutation();
  const [isUploading, setIsUploading] = useState(false);
  const [previewData, setPreviewData] = useState<CaseStudyData>({
    useCaseTitle: 'Architecting a Faster, Leaner Claims Process Using Intelligence',
    headerImage: '/demo-header.jpg',
    industry: 'Insurance',
    role: 'CIO',
    potentiallyImpactedKpis: 'Claims Cycle Time, Straight Through Processing Rate, Customer Complaint Volume',
    kpis: {
      claimCycleTime: 'Claims Cycle Time',
      straightThroughRate: 'Straight Through Processing Rate',
      customerComplaintVolume: 'Customer Complaint Volume'
    },
    introduction: {
      title: 'Where the Experience Often Breaks First',
      text: 'Claims are the frontline of customer experience in insurance. It\'s where promises are tested - and often, where they fall apart.',
      problems: [
        'Claims take weeks instead of days',
        'Customers lack transparency and updates',
        'Staff are bogged down with manual tasks and reconciliation work',
      ],
      questions: [
        'Can we move faster without compromising accuracy?',
        'How do we eliminate friction for both customers and agents?',
        'What would it take to turn claims into a strategic advantage?',
      ],
    },
    process: {
      title: 'How It All Comes Together: The Claims Engine, Rewired',
      steps: [
        {
          title: 'Intake',
          description: 'Customers submit claims digitally with supporting documents, guided by smart forms',
          icon: '/icons/intake.svg'
        },
        {
          title: 'Intelligence',
          description: 'AI processes documents, predicts liability, flags potential fraud, and recommends next steps',
          icon: '/icons/intelligence.svg'
        },
        {
          title: 'Resolution',
          description: 'Teams get AI-driven insights to handle claims faster and more accurately',
          icon: '/icons/resolution.svg'
        },
      ],
    },
    solution: {
      title: 'Solution Blueprint: Reimagining Claims with AI',
      description: 'Insurers can build into their claims process with a modular, scalable approach:',
      items: [
        {
          title: 'Smart Digital Intake',
          description: 'Enable digital claim submissions through mobile-friendly guided form logic',
          icon: '/icons/digital-intake.svg'
        },
        {
          title: 'AI-Driven Triage & Assessment',
          description: 'Use trained models to analyze claim data, detect anomalies, and prioritize routing',
          icon: '/icons/ai-triage.svg'
        },
        {
          title: 'Real-Time Decision Support',
          description: 'Deliver actionable insights to adjusters for faster claims based on policy logic',
          icon: '/icons/decision-support.svg'
        },
        {
          title: 'Human-in-the-Loop Controls',
          description: 'Retain human oversight for sensitive or complex cases with transparency into AI recommendations',
          icon: '/icons/human-loop.svg'
        },
        {
          title: 'Continuous Learning',
          description: 'Feed outcomes back into the models to improve accuracy and risk scoring over time',
          icon: '/icons/continuous-learning.svg'
        },
      ],
    },
    impact: {
      title: 'Potential Impact: From Lagging to Leading',
      metrics: [
        {
          metric: 'Claims Cycle Time',
          value: 'Up to 50%',
          description: 'reduction in average claim cycle time',
          icon: '/icons/cycle-time.svg'
        },
        {
          metric: 'Straight Through Processing',
          value: 'Up to 70%',
          description: 'increase in straight through processing (STP) rates',
          icon: '/icons/processing.svg'
        },
        {
          metric: 'Customer Complaints',
          value: 'Up to 40%',
          description: 'decrease in customer complaints related to delays or communication breakdowns',
          icon: '/icons/complaints.svg'
        },
        {
          metric: 'Processing Time',
          value: '20 to 30%',
          description: 'reduction in average claim cycle time',
          icon: '/icons/time.svg'
        },
      ],
    },
    conclusion: {
      title: 'The New Standard in Claims',
      text: 'Insurance doesn\'t need more complexity - it needs precision, clarity, and speed. By embedding AI into the claims journey, insurers can shift from reactive firefighting to proactive, insight-driven resolution. This isn\'t about replacing people - it\'s about equipping them with smarter tools and faster answers.',
    },
  });

  const showToast = (title: string, description: string, variant: 'default' | 'destructive' = 'default') => {
    toast({
      title,
      description,
      variant,
    });
  };

  const handleHeaderImageUploadSuccess = (url: string) => {
    setPreviewData(prev => ({
      ...prev,
      headerImage: url
    }));
    showToast('Success', 'Header image uploaded successfully');
  };

  const handleHeaderImageUploadError = (error: any) => {
    console.error('Error uploading header image:', error);
    showToast('Error', 'Failed to upload header image', 'destructive');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createCaseStudy(previewData).unwrap();
      showToast('Success', 'Case study created successfully');
      router.push('/dashboard/case-studies');
    } catch (error) {
      showToast('Error', 'Failed to create case study', 'destructive');
    }
  };

  return (
    <div className="container mx-auto py-6">
        <Tabs defaultValue="edit" className="space-y-6">
          <div className="flex items-center justify-between">
            <TabsList className="bg-gray-100 p-1 rounded-lg">
              <TabsTrigger value="edit" className="px-4 py-2 rounded">
                Edit
              </TabsTrigger>
              <TabsTrigger value="preview" className="px-4 py-2 rounded">
                Preview
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="edit">
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-lg font-semibold">Basic Information</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Title</label>
                    <Input
                      value={previewData.useCaseTitle}
                      onChange={(e) =>
                        setPreviewData((prev) => ({
                          ...prev,
                          useCaseTitle: e.target.value,
                        }))
                      }
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Industry</label>
                    <Select
                      value={previewData.industry}
                      onValueChange={(value) =>
                        setPreviewData((prev) => ({
                          ...prev,
                          industry: value,
                        }))
                      }
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="retail-ecommerce">Retail & E-commerce</SelectItem>
                        <SelectItem value="mining-natural-resources">Mining & Natural Resources</SelectItem>
                        <SelectItem value="healthcare-pharmaceuticals">Healthcare-Pharmaceuticals</SelectItem>
                        <SelectItem value="fmcg-cpg-consumer-goods">FMCG-CPG & Consumer Goods</SelectItem>
                        <SelectItem value="energy-utilities">Energy & Utilities</SelectItem>
                        <SelectItem value="construction-real-estate">Construction & Real-Estate</SelectItem>
                        <SelectItem value="manufacturing">Manufacturing</SelectItem>
                        <SelectItem value="media-entertainment-gaming">Media-Entertainment and Gaming</SelectItem>
                        <SelectItem value="banking-financial-services">Banking & Financial Services</SelectItem>
                        <SelectItem value="agriculture-dairy-farming">Agriculture & Dairy Farming</SelectItem>
                        <SelectItem value="legal-professional-services">Legal & Professional Services</SelectItem>
                        <SelectItem value="travel-transportation-logistics">Travel, Transportation logistics</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Role</label>
                    <Input
                      value={previewData.role}
                      onChange={(e) =>
                        setPreviewData((prev) => ({
                          ...prev,
                          role: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Vector</label>
                    <Input
                      value={previewData.vector}
                      onChange={(e) =>
                        setPreviewData((prev) => ({
                          ...prev,
                          vector: e.target.value,
                        }))
                      }
                      placeholder="Cost, Revenue, etc."
                    />
                  </div>
                </div>
                <div className="space-y-2 mt-4">
                  <label className="block text-sm font-medium mb-1">Potentially Impacted KPIs</label>
                  <Input
                    value={previewData.potentiallyImpactedKpis || ''}
                    onChange={(e) =>
                      setPreviewData((prev) => ({
                        ...prev,
                        potentiallyImpactedKpis: e.target.value,
                      }))
                    }
                    placeholder="Enter KPIs separated by commas (e.g., KPI 1, KPI 2, KPI 3)"
                  />
                  <p className="text-xs text-muted-foreground mt-1">Enter KPIs separated by commas (e.g., "Manual Error Rate, Time to Complete Reports, Compliance Score")</p>
                </div>

                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium mb-1">Header Image</label>
                    <div className="flex flex-col space-y-2">
                      {previewData.headerImage && (
                        <div className="relative h-40 bg-muted rounded-md overflow-hidden mb-2">
                          <img
                            src={previewData.headerImage}
                            alt="Header Preview"
                            className="object-cover w-full h-full"
                          />
                        </div>
                      )}
                      <CloudinaryUploader
                        onSuccess={handleHeaderImageUploadSuccess}
                        onError={handleHeaderImageUploadError}
                        folder="case-studies/headers"
                        resourceType="image"
                        buttonText="Upload Header Image"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium mb-1">Thumbnail Image</label>
                    <div className="flex flex-col space-y-2">
                      {previewData.thumbnailImage && (
                        <div className="relative h-40 bg-muted rounded-md overflow-hidden mb-2">
                          <img
                            src={previewData.thumbnailImage}
                            alt="Thumbnail Preview"
                            className="object-cover w-full h-full"
                          />
                        </div>
                      )}
                      <CloudinaryUploader
                        onSuccess={(url) => {
                          setPreviewData((prev) => ({
                            ...prev,
                            thumbnailImage: url,
                          }));
                          showToast('Success', 'Thumbnail image uploaded successfully');
                        }}
                        onError={(error) => {
                          console.error('Error uploading thumbnail image:', error);
                          showToast('Error', 'Failed to upload thumbnail image', 'destructive');
                        }}
                        folder="case-studies/thumbnails"
                        resourceType="image"
                        buttonText="Upload Thumbnail Image"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Introduction Section */}
              <div className="space-y-4 border-t pt-6 mt-6">
                <h2 className="text-lg font-semibold">Introduction</h2>
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Introduction Title</label>
                  <Input
                    value={previewData.introduction?.title || ''}
                    onChange={(e) =>
                      setPreviewData((prev) => ({
                        ...prev,
                        introduction: {
                          ...prev.introduction,
                          title: e.target.value,
                          text: prev.introduction?.text || '',
                          problems: prev.introduction?.problems || [],
                          questions: prev.introduction?.questions || []
                        }
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Introduction Text</label>
                  <Textarea
                    value={previewData.introduction?.text || ''}
                    onChange={(e) =>
                      setPreviewData((prev) => ({
                        ...prev,
                        introduction: {
                          ...prev.introduction,
                          text: e.target.value,
                          title: prev.introduction?.title || '',
                          problems: prev.introduction?.problems || [],
                          questions: prev.introduction?.questions || []
                        }
                      }))
                    }
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Challenges</label>
                  {[0, 1, 2].map((index) => (
                    <div key={`problem-${index}`} className="mt-2">
                      <label className="block text-sm font-medium mb-1">Challenge {index + 1}</label>
                      <Textarea
                        value={previewData.introduction?.problems?.[index] || ''}
                        onChange={(e) => {
                          const newProblems = [...(previewData.introduction?.problems || [])];
                          while (newProblems.length <= index) {
                            newProblems.push('');
                          }
                          newProblems[index] = e.target.value;
                          setPreviewData((prev) => ({
                            ...prev,
                            introduction: {
                              ...prev.introduction,
                              problems: newProblems,
                              title: prev.introduction?.title || '',
                              text: prev.introduction?.text || '',
                              questions: prev.introduction?.questions || []
                            }
                          }));
                        }}
                        rows={2}
                      />
                    </div>
                  ))}
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Questions</label>
                  {[0, 1, 2].map((index) => (
                    <div key={`question-${index}`} className="mt-2">
                      <label className="block text-sm font-medium mb-1">Question {index + 1}</label>
                      <Textarea
                        value={previewData.introduction?.questions?.[index] || ''}
                        onChange={(e) => {
                          const newQuestions = [...(previewData.introduction?.questions || [])];
                          while (newQuestions.length <= index) {
                            newQuestions.push('');
                          }
                          newQuestions[index] = e.target.value;
                          setPreviewData((prev) => ({
                            ...prev,
                            introduction: {
                              ...prev.introduction,
                              questions: newQuestions,
                              title: prev.introduction?.title || '',
                              text: prev.introduction?.text || '',
                              problems: prev.introduction?.problems || []
                            }
                          }));
                        }}
                        rows={2}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Process Section */}
              <div className="space-y-4 border-t pt-6 mt-6">
                <h2 className="text-lg font-semibold">Process</h2>
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Process Title</label>
                  <Input
                    value={previewData.process?.title || ''}
                    onChange={(e) =>
                      setPreviewData((prev) => ({
                        ...prev,
                        process: {
                          ...prev.process,
                          title: e.target.value,
                          steps: prev.process?.steps || []
                        }
                      }))
                    }
                  />
                </div>

                {[0, 1, 2].map((index) => (
                  <div key={`step-${index}`} className="p-4 border rounded-md space-y-3 mt-4">
                    <h4 className="font-medium">Step {index + 1}</h4>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Title</label>
                      <Input
                        value={previewData.process?.steps?.[index]?.title || ''}
                        onChange={(e) => {
                          const newSteps = [...(previewData.process?.steps || [])];
                          while (newSteps.length <= index) {
                            newSteps.push({ title: '', description: '', icon: '' });
                          }
                          newSteps[index] = { ...newSteps[index], title: e.target.value };
                          setPreviewData((prev) => ({
                            ...prev,
                            process: {
                              ...prev.process,
                              steps: newSteps,
                              title: prev.process?.title || ''
                            }
                          }));
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Description</label>
                      <Textarea
                        value={previewData.process?.steps?.[index]?.description || ''}
                        onChange={(e) => {
                          const newSteps = [...(previewData.process?.steps || [])];
                          while (newSteps.length <= index) {
                            newSteps.push({ title: '', description: '', icon: '' });
                          }
                          newSteps[index] = { ...newSteps[index], description: e.target.value };
                          setPreviewData((prev) => ({
                            ...prev,
                            process: {
                              ...prev.process,
                              steps: newSteps,
                              title: prev.process?.title || ''
                            }
                          }));
                        }}
                        rows={3}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Icon</label>
                      <div className="flex flex-col space-y-2">
                        {previewData.process?.steps?.[index]?.icon && (
                          <div className="flex items-center mt-2">
                            <div className="w-16 h-16 bg-muted rounded-md overflow-hidden">
                              <img
                                src={previewData.process.steps[index].icon}
                                alt={`Step ${index + 1} Icon`}
                                className="object-contain w-full h-full"
                              />
                            </div>
                          </div>
                        )}
                        <CloudinaryUploader
                          onSuccess={(url) => {
                            const newSteps = [...(previewData.process?.steps || [])];
                            while (newSteps.length <= index) {
                              newSteps.push({ title: '', description: '', icon: '' });
                            }
                            newSteps[index] = { ...newSteps[index], icon: url };
                            setPreviewData((prev) => ({
                              ...prev,
                              process: {
                                ...prev.process,
                                steps: newSteps,
                                title: prev.process?.title || ''
                              }
                            }));
                            showToast('Success', `Process step ${index + 1} icon uploaded successfully`);
                          }}
                          onError={(error) => {
                            console.error(`Error uploading process step ${index + 1} icon:`, error);
                            showToast('Error', `Failed to upload process step ${index + 1} icon`, 'destructive');
                          }}
                          folder={`case-studies/process/${index}`}
                          resourceType="image"
                          buttonText="Upload Icon"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Solution Section */}
              <div className="space-y-4 border-t pt-6 mt-6">
                <h2 className="text-lg font-semibold">Solution</h2>
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Solution Title</label>
                  <Input
                    value={previewData.solution?.title || ''}
                    onChange={(e) =>
                      setPreviewData((prev) => ({
                        ...prev,
                        solution: {
                          ...prev.solution,
                          title: e.target.value,
                          description: prev.solution?.description || '',
                          items: prev.solution?.items || []
                        }
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Solution Description</label>
                  <Textarea
                    value={previewData.solution?.description || ''}
                    onChange={(e) =>
                      setPreviewData((prev) => ({
                        ...prev,
                        solution: {
                          ...prev.solution,
                          description: e.target.value,
                          title: prev.solution?.title || '',
                          items: prev.solution?.items || []
                        }
                      }))
                    }
                    rows={3}
                  />
                </div>

                {[0, 1, 2, 3, 4].map((index) => (
                  <div key={`solution-${index}`} className="p-4 border rounded-md space-y-3 mt-4">
                    <h4 className="font-medium">Solution Item {index + 1}</h4>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Title</label>
                      <Input
                        value={previewData.solution?.items?.[index]?.title || ''}
                        onChange={(e) => {
                          const newItems = [...(previewData.solution?.items || [])];
                          while (newItems.length <= index) {
                            newItems.push({ title: '', description: '', icon: '' });
                          }
                          newItems[index] = { ...newItems[index], title: e.target.value };
                          setPreviewData((prev) => ({
                            ...prev,
                            solution: {
                              ...prev.solution,
                              items: newItems,
                              title: prev.solution?.title || '',
                              description: prev.solution?.description || ''
                            }
                          }));
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Description</label>
                      <Textarea
                        value={previewData.solution?.items?.[index]?.description || ''}
                        onChange={(e) => {
                          const newItems = [...(previewData.solution?.items || [])];
                          while (newItems.length <= index) {
                            newItems.push({ title: '', description: '', icon: '' });
                          }
                          newItems[index] = { ...newItems[index], description: e.target.value };
                          setPreviewData((prev) => ({
                            ...prev,
                            solution: {
                              ...prev.solution,
                              items: newItems,
                              title: prev.solution?.title || '',
                              description: prev.solution?.description || ''
                            }
                          }));
                        }}
                        rows={3}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Icon</label>
                      <div className="flex flex-col space-y-2">
                        {previewData.solution?.items?.[index]?.icon && (
                          <div className="flex items-center mt-2">
                            <div className="w-16 h-16 bg-muted rounded-md overflow-hidden">
                              <img
                                src={previewData.solution.items[index].icon}
                                alt={`Solution ${index + 1} Icon`}
                                className="object-contain w-full h-full"
                              />
                            </div>
                          </div>
                        )}
                        <CloudinaryUploader
                          onSuccess={(url) => {
                            const newItems = [...(previewData.solution?.items || [])];
                            while (newItems.length <= index) {
                              newItems.push({ title: '', description: '', icon: '' });
                            }
                            newItems[index] = { ...newItems[index], icon: url };
                            setPreviewData((prev) => ({
                              ...prev,
                              solution: {
                                ...prev.solution,
                                items: newItems,
                                title: prev.solution?.title || '',
                                description: prev.solution?.description || ''
                              }
                            }));
                            showToast('Success', `Solution item ${index + 1} icon uploaded successfully`);
                          }}
                          onError={(error) => {
                            console.error(`Error uploading solution item ${index + 1} icon:`, error);
                            showToast('Error', `Failed to upload solution item ${index + 1} icon`, 'destructive');
                          }}
                          folder={`case-studies/solution/${index}`}
                          resourceType="image"
                          buttonText="Upload Icon"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Impact Section */}
              <div className="space-y-4 border-t pt-6 mt-6">
                <h2 className="text-lg font-semibold">Impact</h2>
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Impact Title</label>
                  <Input
                    value={previewData.impact?.title || ''}
                    onChange={(e) =>
                      setPreviewData((prev) => ({
                        ...prev,
                        impact: {
                          ...prev.impact,
                          title: e.target.value,
                          metrics: prev.impact?.metrics || []
                        }
                      }))
                    }
                  />
                </div>

                {[0, 1, 2, 3].map((index) => (
                  <div key={`impact-${index}`} className="p-4 border rounded-md space-y-3 mt-4">
                    <h4 className="font-medium">Impact Metric {index + 1}</h4>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Metric</label>
                      <Input
                        value={previewData.impact?.metrics?.[index]?.metric || ''}
                        onChange={(e) => {
                          const newMetrics = [...(previewData.impact?.metrics || [])];
                          while (newMetrics.length <= index) {
                            newMetrics.push({ metric: '', value: '', description: '', icon: '' });
                          }
                          newMetrics[index] = { ...newMetrics[index], metric: e.target.value };
                          setPreviewData((prev) => ({
                            ...prev,
                            impact: {
                              ...prev.impact,
                              metrics: newMetrics,
                              title: prev.impact?.title || ''
                            }
                          }));
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Value</label>
                      <Input
                        value={previewData.impact?.metrics?.[index]?.value || ''}
                        onChange={(e) => {
                          const newMetrics = [...(previewData.impact?.metrics || [])];
                          while (newMetrics.length <= index) {
                            newMetrics.push({ metric: '', value: '', description: '', icon: '' });
                          }
                          newMetrics[index] = { ...newMetrics[index], value: e.target.value };
                          setPreviewData((prev) => ({
                            ...prev,
                            impact: {
                              ...prev.impact,
                              metrics: newMetrics,
                              title: prev.impact?.title || ''
                            }
                          }));
                        }}
                        placeholder="e.g., Up to 50%"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Description</label>
                      <Textarea
                        value={previewData.impact?.metrics?.[index]?.description || ''}
                        onChange={(e) => {
                          const newMetrics = [...(previewData.impact?.metrics || [])];
                          while (newMetrics.length <= index) {
                            newMetrics.push({ metric: '', value: '', description: '', icon: '' });
                          }
                          newMetrics[index] = { ...newMetrics[index], description: e.target.value };
                          setPreviewData((prev) => ({
                            ...prev,
                            impact: {
                              ...prev.impact,
                              metrics: newMetrics,
                              title: prev.impact?.title || ''
                            }
                          }));
                        }}
                        rows={2}
                        placeholder="e.g., reduction in average claim cycle time"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium mb-1">Icon</label>
                      <div className="flex flex-col space-y-2">
                        {previewData.impact?.metrics?.[index]?.icon && (
                          <div className="flex items-center mt-2">
                            <div className="w-16 h-16 bg-muted rounded-md overflow-hidden">
                              <img
                                src={previewData.impact.metrics[index].icon}
                                alt={`Impact ${index + 1} Icon`}
                                className="object-contain w-full h-full"
                              />
                            </div>
                          </div>
                        )}
                        <CloudinaryUploader
                          onSuccess={(url) => {
                            const newMetrics = [...(previewData.impact?.metrics || [])];
                            while (newMetrics.length <= index) {
                              newMetrics.push({ metric: '', value: '', description: '', icon: '' });
                            }
                            newMetrics[index] = { ...newMetrics[index], icon: url };
                            setPreviewData((prev) => ({
                              ...prev,
                              impact: {
                                ...prev.impact,
                                metrics: newMetrics,
                                title: prev.impact?.title || ''
                              }
                            }));
                            showToast('Success', `Impact metric ${index + 1} icon uploaded successfully`);
                          }}
                          onError={(error) => {
                            console.error(`Error uploading impact metric ${index + 1} icon:`, error);
                            showToast('Error', `Failed to upload impact metric ${index + 1} icon`, 'destructive');
                          }}
                          folder={`case-studies/impact/${index}`}
                          resourceType="image"
                          buttonText="Upload Icon"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Conclusion Section */}
              <div className="space-y-4 border-t pt-6 mt-6">
                <h2 className="text-lg font-semibold">Conclusion</h2>
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Conclusion Title</label>
                  <Input
                    value={previewData.conclusion?.title || ''}
                    onChange={(e) =>
                      setPreviewData((prev) => ({
                        ...prev,
                        conclusion: {
                          ...prev.conclusion,
                          title: e.target.value,
                          text: prev.conclusion?.text || ''
                        }
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Conclusion Text</label>
                  <Textarea
                    value={previewData.conclusion?.text || ''}
                    onChange={(e) =>
                      setPreviewData((prev) => ({
                        ...prev,
                        conclusion: {
                          ...prev.conclusion,
                          text: e.target.value,
                          title: prev.conclusion?.title || ''
                        }
                      }))
                    }
                    rows={4}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/dashboard/case-studies')}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isCreating}>
                  {isCreating ? 'Creating...' : 'Create Case Study'}
                </Button>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="preview" className="mt-6">
            <CaseStudyCard
              {...{
                id: 'preview',
                title: previewData.useCaseTitle,
                headerImage: previewData.headerImage || '',
                industry: previewData.industry || '',
                role: previewData.role || '',
                vector: previewData.vector || '',
                potentiallyImpactedKpis: previewData.potentiallyImpactedKpis || '',
                kpis: previewData.kpis || {
                  claimCycleTime: '',
                  straightThroughRate: '',
                  customerComplaintVolume: ''
                },
                introduction: previewData.introduction || {
                  title: '',
                  text: '',
                  problems: [],
                  questions: []
                },
                process: {
                  title: previewData.process?.title || '',
                  steps: previewData.process?.steps || []
                },
                solution: {
                  title: previewData.solution?.title || '',
                  description: previewData.solution?.description || '',
                  items: previewData.solution?.items || []
                },
                impact: {
                  title: previewData.impact?.title || '',
                  metrics: previewData.impact?.metrics || []
                },
                conclusion: {
                  title: previewData.conclusion?.title || '',
                  text: previewData.conclusion?.text || ''
                }
              }}
            />
          </TabsContent>
        </Tabs>
    </div>
  );
}
