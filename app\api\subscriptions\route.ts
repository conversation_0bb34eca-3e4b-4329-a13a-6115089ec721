import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { userSubscriptions, plans, subscriptionHistory, teams } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { stripe } from '@/lib/payments/stripe';
import { syncTeamSubscription } from '@/lib/services/subscription-sync';

// GET /api/subscriptions - Get current user's subscription
export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's team
    const team = await getTeamForUser(user.id);
    if (!team) {
      return NextResponse.json({ error: 'Team not found' }, { status: 404 });
    }

    // Get the user's subscription
    const subscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.teamId, team.id),
      with: {
        plan: true,
      },
      orderBy: (userSubscriptions, { desc }) => [desc(userSubscriptions.createdAt)],
    });

    if (!subscription) {
      return NextResponse.json({
        subscription: null,
        message: 'No active subscription found'
      });
    }

    // Sync team subscription with Stripe to ensure data is up-to-date
    console.log(`[Subscriptions API] Syncing team ${team.id} subscription with Stripe`);
    const syncResult = await syncTeamSubscription(team.id);

    if (syncResult.success && syncResult.updated) {
      console.log(`[Subscriptions API] Team subscription updated: ${syncResult.planName} (${syncResult.subscriptionStatus})`);
      // Refresh team data after sync
      const updatedTeam = await getTeamForUser(user.id);
      if (updatedTeam) {
        Object.assign(team, updatedTeam);
      }
    } else if (!syncResult.success) {
      console.warn(`[Subscriptions API] Sync failed but continuing: ${syncResult.error}`);
    }

    // Get the latest Stripe subscription data
    let stripeSubscription = null;
    if (team.stripeSubscriptionId) {
      try {
        stripeSubscription = await stripe.subscriptions.retrieve(
          team.stripeSubscriptionId,
          {
            expand: ['items.data.price.product'],
          }
        );
        console.log(`[Subscriptions API] Stripe subscription retrieved: ${stripeSubscription.id}`);
      } catch (error) {
        console.error('[Subscriptions API] Error retrieving Stripe subscription:', error);
      }
    }

    // If we couldn't get the subscription from the team, try using the subscription from the database
    if (!stripeSubscription && subscription?.stripeSubscriptionId) {
      try {
        stripeSubscription = await stripe.subscriptions.retrieve(
          subscription.stripeSubscriptionId,
          {
            expand: ['items.data.price.product'],
          }
        );
        console.log('Stripe subscription retrieved from database:', stripeSubscription.id);
      } catch (error) {
        console.error('Error retrieving Stripe subscription from database:', error);
        // Continue without Stripe data
      }
    }

    return NextResponse.json({
      subscription,
      stripeSubscription,
      team: {
        id: team.id,
        name: team.name,
        planName: team.planName,
        subscriptionStatus: team.subscriptionStatus,
        stripeSubscriptionId: team.stripeSubscriptionId,
        stripeCustomerId: team.stripeCustomerId,
      },
    });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json({ error: 'Failed to fetch subscription' }, { status: 500 });
  }
}

// POST /api/subscriptions - Create or update a subscription
export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the request data
    const data = await request.json();

    // Validate required fields
    if (!data.planId) {
      return NextResponse.json({ error: 'Plan ID is required' }, { status: 400 });
    }

    // Get the user's team
    const team = await getTeamForUser(user.id);
    if (!team) {
      return NextResponse.json({ error: 'Team not found' }, { status: 404 });
    }

    // Get the plan
    const plan = await db.query.plans.findFirst({
      where: eq(plans.id, data.planId),
    });

    if (!plan) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 });
    }

    // Check if the user already has a subscription
    const existingSubscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.teamId, team.id),
      orderBy: (userSubscriptions, { desc }) => [desc(userSubscriptions.createdAt)],
    });

    // Create a checkout URL for the new subscription
    const checkoutUrl = `/api/payments/checkout?planName=${plan.name}`;

    // If there's an existing subscription, we'll handle the upgrade/downgrade in the checkout API
    return NextResponse.json({
      message: existingSubscription
        ? 'Subscription change initiated'
        : 'New subscription initiated',
      checkoutUrl,
      plan,
    });
  } catch (error) {
    console.error('Error creating/updating subscription:', error);
    return NextResponse.json({ error: 'Failed to process subscription' }, { status: 500 });
  }
}
