/**
 * Client-side API utilities for fetching data
 * This file contains functions that can be safely imported in client components
 */

// User API functions
export async function fetchCurrentUser() {
  try {
    const response = await fetch('/api/auth/user');
    if (!response.ok) {
      return null;
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching user:', error);
    return null;
  }
}

export async function fetchTeamForUser() {
  try {
    const response = await fetch('/api/team');
    if (!response.ok) {
      return null;
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching team:', error);
    return null;
  }
}

export async function fetchActivityLogs() {
  try {
    const response = await fetch('/api/activity-logs');
    if (!response.ok) {
      throw new Error('Failed to fetch activity logs');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    throw error;
  }
}

export async function fetchMembersWithActivePlans(teamId: number) {
  try {
    const response = await fetch(`/api/team/${teamId}/members/active-plans`);
    if (!response.ok) {
      return [];
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching members with active plans:', error);
    return [];
  }
}
