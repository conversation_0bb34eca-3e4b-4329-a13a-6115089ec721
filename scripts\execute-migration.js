const { db } = require('../lib/db/drizzle');
const fs = require('fs');
const path = require('path');

async function executeMigration() {
  try {
    console.log('Executing migration to add solution4 and solution5 fields...');

    // Read the SQL file
    const sqlPath = path.join(__dirname, '../lib/db/migrations/0005_add_solution4_solution5_fields.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // Execute the SQL
    await db.execute(sql);

    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error executing migration:', error);
    process.exit(1);
  }
}

executeMigration();
