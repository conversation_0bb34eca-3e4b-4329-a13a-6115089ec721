-- Drop existing tables if they exist
DROP TABLE IF EXISTS "case_study_icons";
DROP TABLE IF EXISTS "case_studies";

-- Create tables
CREATE TABLE "case_studies" (
  "id" serial PRIMARY KEY,
  "use_case_title" varchar(255) NOT NULL,
  "industry" varchar(100),
  "role" varchar(100),
  "vector" varchar(100),
  "potentially_impacted_kpis" text,
  "introduction_title" varchar(255),
  "introduction_text" text,
  "transition_to_challange" text,
  "challange_1" text,
  "challange_2" text,
  "challange_3" text,
  "transition_to_questions" text,
  "question_1" text,
  "question_2" text,
  "question_3" text,
  "process_section_title" text,
  "process_step_1" text,
  "process_step_2" text,
  "process_step_3" text,
  "solution_section_title" text,
  "solution_1" text,
  "solution_2" text,
  "solution_3" text,
  "solution_4" text,
  "solution_5" text,
  "potential_impact_section_title" text,
  "potential_impact_1_qualitative" text,
  "potential_impact_1_quantitative" text,
  "potential_impact_2_qualitative" text,
  "potential_impact_2_quantitative" text,
  "potential_impact_3_qualitative" text,
  "potential_impact_3_quantitative" text,
  "potential_impact_4_qualitative" text,
  "potential_impact_4_quantitative" text,
  "conclusion_title" varchar(255),
  "conclusion_text" text,
  "conclusion_result" text,
  "feature_image_url" varchar(255),
  "preview_image_url" varchar(255),
  "created_at" timestamp NOT NULL DEFAULT now(),
  "updated_at" timestamp NOT NULL DEFAULT now(),
  "deleted_at" timestamp
);

CREATE TABLE "case_study_icons" (
  "id" serial PRIMARY KEY,
  "case_study_id" integer NOT NULL REFERENCES "case_studies"("id"),
  "icon_type" varchar(50) NOT NULL,
  "icon_url" varchar(255) NOT NULL,
  "order" integer NOT NULL,
  "created_at" timestamp NOT NULL DEFAULT now()
); 