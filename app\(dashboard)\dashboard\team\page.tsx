'use client';

import { useState } from 'react';
import {
  useGetTeamQuery,
  useInviteMemberMutation,
  useRemoveMemberMutation,
  useDeleteUserMutation,
  useCancelInvitationMutation,
  useUpdateTeamMutation,
} from '@/lib/redux/api/teamApi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Loader2,
  PlusCircle,
  Edit,
  Trash2,
  X,
  Check,
  UserPlus,
  Users,
  Mail,
  AlertTriangle,
  Copy,
  Link,
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import SafeReduxWrapper from '@/components/SafeReduxWrapper';
import { useUser } from '@/lib/auth';
import { use } from 'react';
import { generateInvitationLink } from '@/lib/utils/invitation-links';
import { useSubscription } from '@/lib/hooks/use-subscription';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function TeamPage() {
  return (
    <SafeReduxWrapper fallback={<TeamPageLoading />}>
      <TeamPageContent />
    </SafeReduxWrapper>
  );
}

function TeamPageLoading() {
  return (
    <div className='container mx-auto py-8 !px-0 md:px-4'>
      <h1 className='text-3xl font-bold mb-8'>Team Management</h1>
      <div className='grid gap-8'>
        <Card>
          <CardHeader>
            <div className='h-7 w-48 bg-gray-200 rounded animate-pulse'></div>
            <div className='h-5 w-64 bg-gray-100 rounded animate-pulse'></div>
          </CardHeader>
          <CardContent>
            <div className='space-y-6'>
              <div className='h-10 w-full bg-gray-200 rounded animate-pulse'></div>
              <div className='h-10 w-full bg-gray-200 rounded animate-pulse'></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className='h-7 w-48 bg-gray-200 rounded animate-pulse'></div>
            <div className='h-5 w-64 bg-gray-100 rounded animate-pulse'></div>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {[1, 2, 3].map((i) => (
                <div key={i} className='flex items-center justify-between'>
                  <div className='flex items-center space-x-4'>
                    <div className='h-10 w-10 bg-gray-200 rounded-full animate-pulse'></div>
                    <div>
                      <div className='h-5 w-32 bg-gray-200 rounded animate-pulse'></div>
                      <div className='h-4 w-24 bg-gray-100 rounded animate-pulse mt-1'></div>
                    </div>
                  </div>
                  <div className='h-9 w-20 bg-gray-200 rounded animate-pulse'></div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function TeamPageContent() {
  const { userPromise } = useUser();
  const user = use(userPromise);
  const isOwner = user?.role === 'owner';
  const { hasActiveSubscription, planFeatures, planName } = useSubscription();

  const { data: team, isLoading, error, refetch } = useGetTeamQuery();
  const [inviteMember, { isLoading: isInviting }] = useInviteMemberMutation();
  const [removeMember, { isLoading: isRemoving }] = useRemoveMemberMutation();
  const [deleteUser, { isLoading: isDeleting }] = useDeleteUserMutation();
  const [cancelInvitation, { isLoading: isCanceling }] =
    useCancelInvitationMutation();
  const [updateTeam, { isLoading: isUpdating }] = useUpdateTeamMutation();

  const [isEditingTeamName, setIsEditingTeamName] = useState(false);
  const [teamName, setTeamName] = useState('');
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'member' | 'owner'>('member');
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [memberToRemove, setMemberToRemove] = useState<number | null>(null);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<number | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [invitationToCancel, setInvitationToCancel] = useState<number | null>(
    null
  );
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);

  // Initialize team name when data is loaded
  if (team && teamName === '' && !isEditingTeamName) {
    setTeamName(team.name);
  }

  const handleInviteMember = async () => {
    if (!inviteEmail) {
      toast({
        title: 'Error',
        description: 'Email is required',
        variant: 'destructive',
      });
      return;
    }

    // Check user limit for current plan
    const currentMemberCount = team?.members?.length || 0;
    const maxUsers = planFeatures.maxUsers;

    if (maxUsers !== -1 && currentMemberCount >= maxUsers) {
      toast({
        title: 'User Limit Reached',
        description: `Your ${planName} plan allows up to ${maxUsers} user${maxUsers === 1 ? '' : 's'}. Upgrade to add more team members.`,
        variant: 'destructive',
      });
      return;
    }

    try {
      const result = await inviteMember({
        email: inviteEmail,
        role: inviteRole,
      }).unwrap();

      if (result.userAdded) {
        // Existing user was added directly to the team
        if (result.emailSent) {
          toast({
            title: 'Success',
            description:
              'User already exists and has been added to the team. Notification email sent.',
          });
        } else {
          toast({
            title: 'Partial Success',
            description:
              'User already exists and has been added to the team, but notification email could not be sent.',
            variant: 'default',
          });
        }
      } else if (result.roleUpdated) {
        // Existing user's role was updated
        toast({
          title: 'Success',
          description:
            result.success || "User's role has been updated successfully.",
        });
      } else if (result.emailSent) {
        // New user invitation sent successfully
        toast({
          title: 'Success',
          description: 'Invitation sent successfully',
        });
      } else {
        // New user invitation created but email failed
        toast({
          title: 'Partial Success',
          description:
            'Invitation created but email could not be sent. The user will need the invitation link to join.',
          variant: 'default',
        });
      }

      setInviteEmail('');
      setInviteRole('member');
      setIsInviteDialogOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.data?.error || 'Failed to send invitation',
        variant: 'destructive',
      });
    }
  };

  const handleRemoveMember = async () => {
    if (!memberToRemove) return;

    try {
      await removeMember({ memberId: memberToRemove }).unwrap();
      toast({
        title: 'Success',
        description: 'Team member removed successfully',
      });
      setMemberToRemove(null);
      setIsRemoveDialogOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.data?.error || 'Failed to remove team member',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await deleteUser({ userId: userToDelete }).unwrap();
      toast({
        title: 'Success',
        description: 'User deleted successfully',
      });
      setUserToDelete(null);
      setIsDeleteDialogOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.data?.error || 'Failed to delete user',
        variant: 'destructive',
      });
    }
  };

  const handleCancelInvitation = async () => {
    if (!invitationToCancel) return;

    try {
      await cancelInvitation({ invitationId: invitationToCancel }).unwrap();
      toast({
        title: 'Success',
        description: 'Invitation canceled successfully',
      });
      setInvitationToCancel(null);
      setIsCancelDialogOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.data?.error || 'Failed to cancel invitation',
        variant: 'destructive',
      });
    }
  };

  const handleUpdateTeamName = async () => {
    if (!teamName.trim()) {
      toast({
        title: 'Error',
        description: 'Team name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    try {
      await updateTeam({ name: teamName.trim() }).unwrap();
      toast({
        title: 'Success',
        description: 'Team name updated successfully',
      });
      setIsEditingTeamName(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.data?.error || 'Failed to update team name',
        variant: 'destructive',
      });
    }
  };

  const getUserInitials = (name?: string, email?: string) => {
    if (name) {
      return name
        .split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase();
    }
    if (email) {
      return email[0].toUpperCase();
    }
    return '?';
  };

  const copyInvitationLink = (invitationId: number) => {
    const link = generateInvitationLink(invitationId);
    navigator.clipboard
      .writeText(link)
      .then(() => {
        toast({
          title: 'Link Copied',
          description: 'Invitation link copied to clipboard',
        });
      })
      .catch((error) => {
        console.error('Failed to copy invitation link:', error);
        toast({
          title: 'Error',
          description: 'Failed to copy invitation link',
          variant: 'destructive',
        });
      });
  };

  if (isLoading) {
    return <TeamPageLoading />;
  }

  if (error) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <h1 className='text-3xl font-bold mb-8'>Team Management</h1>
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-red-500'>
              Failed to load team data. Please try again later.
            </p>
            <Button onClick={() => refetch()} className='mt-4'>
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!team) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <h1 className='text-3xl font-bold mb-8'>Team Management</h1>
        <Card>
          <CardHeader>
            <CardTitle>No Team Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p>You are not currently part of any team.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <h1 className='text-3xl font-bold mb-8'>Team Management</h1>

      <div className='grid gap-8'>
        {/* Team Details */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div>
                <CardTitle>Team Details</CardTitle>
                <CardDescription>Manage your team information</CardDescription>
              </div>
              {isOwner && !isEditingTeamName && (
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => setIsEditingTeamName(true)}
                >
                  <Edit className='h-4 w-4 mr-2' />
                  Edit
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div>
                <Label htmlFor='teamName'>Team Name</Label>
                {isEditingTeamName ? (
                  <div className='flex items-center gap-2 mt-1'>
                    <Input
                      id='teamName'
                      value={teamName}
                      onChange={(e) => setTeamName(e.target.value)}
                      disabled={isUpdating}
                    />
                    <Button
                      size='sm'
                      onClick={handleUpdateTeamName}
                      disabled={isUpdating}
                    >
                      {isUpdating ? (
                        <Loader2 className='h-4 w-4 animate-spin' />
                      ) : (
                        <Check className='h-4 w-4' />
                      )}
                    </Button>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => {
                        setIsEditingTeamName(false);
                        setTeamName(team.name);
                      }}
                      disabled={isUpdating}
                    >
                      <X className='h-4 w-4' />
                    </Button>
                  </div>
                ) : (
                  <div className='mt-1 p-2 border rounded-md bg-gray-50'>
                    {team.name}
                  </div>
                )}
              </div>

              {/* Only show subscription info if not an owner or if owner has an active subscription */}
              {(!isOwner ||
                (isOwner &&
                  (team.planName ||
                    team.subscriptionStatus === 'active' ||
                    team.subscriptionStatus === 'trialing'))) && (
                <div>
                  <Label>Subscription</Label>
                  <div className='mt-1 p-2 border rounded-md bg-gray-50 flex justify-between items-center'>
                    <div>
                      <span className='font-medium'>
                        {team.planName || 'Free Plan'}
                      </span>
                      <span className='text-sm text-gray-500 ml-2'>
                        ({team.subscriptionStatus || 'inactive'})
                      </span>
                    </div>
                    {isOwner && (
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => (window.location.href = '/pricing')}
                      >
                        Manage Subscription
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Team Members & Invitations */}
        <Tabs defaultValue='members'>
          <TabsList className='grid w-full max-w-md grid-cols-2 mb-6'>
            <TabsTrigger value='members' className='flex items-center gap-2'>
              <Users className='h-4 w-4' />
              Team Members
            </TabsTrigger>
            <TabsTrigger
              value='invitations'
              className='flex items-center gap-2'
            >
              <Mail className='h-4 w-4' />
              Invitations
              {team.invitations.length > 0 && (
                <Badge variant='secondary' className='ml-2'>
                  {team.invitations.length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          {/* Members Tab */}
          <TabsContent value='members'>
            <Card>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <div>
                    <CardTitle>Team Members</CardTitle>
                    <CardDescription>
                      Manage your team members ({team.teamMembers.length}
                      {planFeatures.maxUsers !== -1 && ` / ${planFeatures.maxUsers}`}{' '}
                      {team.teamMembers.length === 1 ? 'member' : 'members'})
                    </CardDescription>
                    {planFeatures.maxUsers !== -1 && team.teamMembers.length >= planFeatures.maxUsers && (
                      <Alert className="mt-2">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          You've reached your plan's user limit.
                          <Button
                            variant="link"
                            className="p-0 h-auto font-semibold text-blue-600"
                            onClick={() => window.location.href = '/dashboard/billing'}
                          >
                            Upgrade your plan
                          </Button> to add more team members.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                  {isOwner && (
                    <Button
                      onClick={() => setIsInviteDialogOpen(true)}
                      disabled={planFeatures.maxUsers !== -1 && team.teamMembers.length >= planFeatures.maxUsers}
                    >
                      <UserPlus className='h-4 w-4 mr-2' />
                      Invite Member
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  {team.teamMembers.map((member) => (
                    <div
                      key={member.id}
                      className='flex items-center justify-between'
                    >
                      <div className='flex items-center space-x-4'>
                        <Avatar>
                          <AvatarFallback>
                            {getUserInitials(
                              member.user.name,
                              member.user.email
                            )}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className='font-medium'>
                            {member.user.name || member.user.email}
                          </p>
                          <div className='flex items-center'>
                            <Badge
                              variant={
                                member.role === 'owner'
                                  ? 'default'
                                  : 'secondary'
                              }
                              className='mr-2'
                            >
                              {member.role}
                            </Badge>
                            {member.user.id === user?.id && (
                              <Badge variant='outline'>You</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      {isOwner && member.user.id !== user?.id && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant='outline'
                              size='sm'
                              disabled={isRemoving || isDeleting}
                            >
                              Actions
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem
                              onClick={() => {
                                setMemberToRemove(member.id);
                                setIsRemoveDialogOpen(true);
                              }}
                            >
                              <Trash2 className='h-4 w-4 mr-2' />
                              Remove from Team
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className='text-red-600'
                              onClick={() => {
                                setUserToDelete(member.user.id);
                                setIsDeleteDialogOpen(true);
                              }}
                            >
                              <Trash2 className='h-4 w-4 mr-2' />
                              Delete User
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Invitations Tab */}
          <TabsContent value='invitations'>
            <Card>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <div>
                    <CardTitle>Pending Invitations</CardTitle>
                    <CardDescription>
                      Manage your team invitations ({team.invitations.length}{' '}
                      {team.invitations.length === 1
                        ? 'invitation'
                        : 'invitations'}
                      )
                    </CardDescription>
                  </div>
                  {isOwner && (
                    <Button
                      onClick={() => setIsInviteDialogOpen(true)}
                      disabled={planFeatures.maxUsers !== -1 && team.teamMembers.length >= planFeatures.maxUsers}
                    >
                      <UserPlus className='h-4 w-4 mr-2' />
                      Invite Member
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {team.invitations.length === 0 ? (
                  <div className='text-center py-6 text-gray-500'>
                    No pending invitations
                  </div>
                ) : (
                  <div className='space-y-4'>
                    {team.invitations.map((invitation) => (
                      <div
                        key={invitation.id}
                        className='flex items-center justify-between'
                      >
                        <div className='flex items-center space-x-4'>
                          <Avatar>
                            <AvatarFallback>
                              {invitation.email[0].toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className='font-medium'>{invitation.email}</p>
                            <div className='flex items-center'>
                              <Badge
                                variant={
                                  invitation.role === 'owner'
                                    ? 'default'
                                    : 'secondary'
                                }
                                className='mr-2'
                              >
                                {invitation.role}
                              </Badge>
                              <span className='text-xs text-gray-500'>
                                Invited{' '}
                                {new Date(
                                  invitation.invitedAt
                                ).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>
                        {isOwner && (
                          <div className='flex gap-2'>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => copyInvitationLink(invitation.id)}
                              title='Copy invitation link'
                            >
                              <Link className='h-4 w-4' />
                            </Button>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => {
                                setInvitationToCancel(invitation.id);
                                setIsCancelDialogOpen(true);
                              }}
                              disabled={isCanceling}
                            >
                              <X className='h-4 w-4 mr-2' />
                              Cancel
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Invite Member Dialog */}
      <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Team Member</DialogTitle>
            <DialogDescription>
              Send an invitation to join your team
            </DialogDescription>
          </DialogHeader>
          <div className='space-y-4 py-4'>
            <div className='space-y-2'>
              <Label htmlFor='email'>Email</Label>
              <Input
                id='email'
                type='email'
                placeholder='<EMAIL>'
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
              />
            </div>
            <div className='space-y-2'>
              <Label>Role</Label>
              <RadioGroup
                value={inviteRole}
                onValueChange={(value) =>
                  setInviteRole(value as 'member' | 'owner')
                }
                className='flex space-x-4'
              >
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='member' id='member' />
                  <Label htmlFor='member'>Member</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='owner' id='owner' />
                  <Label htmlFor='owner'>Admin</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='viewer' id='viewer' />
                  <Label htmlFor='viewer'>Viewer</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsInviteDialogOpen(false)}
              disabled={isInviting}
            >
              Cancel
            </Button>
            <Button onClick={handleInviteMember} disabled={isInviting}>
              {isInviting ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Sending...
                </>
              ) : (
                <>
                  <PlusCircle className='mr-2 h-4 w-4' />
                  Send Invitation
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Member Dialog */}
      <Dialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Team Member</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this team member?
            </DialogDescription>
          </DialogHeader>
          <div className='py-4 flex items-center justify-center text-amber-500'>
            <AlertTriangle className='h-12 w-12' />
          </div>
          <p className='text-center'>
            This action cannot be undone. The user will lose access to your
            team's resources.
          </p>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => {
                setIsRemoveDialogOpen(false);
                setMemberToRemove(null);
              }}
              disabled={isRemoving}
            >
              Cancel
            </Button>
            <Button
              variant='destructive'
              onClick={handleRemoveMember}
              disabled={isRemoving}
            >
              {isRemoving ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Removing...
                </>
              ) : (
                <>
                  <Trash2 className='mr-2 h-4 w-4' />
                  Remove Member
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cancel Invitation Dialog */}
      <Dialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Invitation</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel this invitation?
            </DialogDescription>
          </DialogHeader>
          <div className='py-4 flex items-center justify-center text-amber-500'>
            <AlertTriangle className='h-12 w-12' />
          </div>
          <p className='text-center'>
            The invitation will be deleted and the recipient will not be able to
            join your team.
          </p>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => {
                setIsCancelDialogOpen(false);
                setInvitationToCancel(null);
              }}
              disabled={isCanceling}
            >
              Keep Invitation
            </Button>
            <Button
              variant='destructive'
              onClick={handleCancelInvitation}
              disabled={isCanceling}
            >
              {isCanceling ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Canceling...
                </>
              ) : (
                <>
                  <X className='mr-2 h-4 w-4' />
                  Cancel Invitation
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to permanently delete this user?
            </DialogDescription>
          </DialogHeader>
          <div className='py-4 flex items-center justify-center text-red-500'>
            <AlertTriangle className='h-12 w-12' />
          </div>
          <p className='text-center font-semibold text-red-500'>
            WARNING: This action is irreversible!
          </p>
          <p className='text-center mt-2'>
            This will permanently delete the user account and all associated
            data. The user will no longer be able to access the system.
          </p>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setUserToDelete(null);
              }}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant='destructive'
              onClick={handleDeleteUser}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className='mr-2 h-4 w-4' />
                  Delete User Permanently
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
