'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import dynamic from 'next/dynamic';

import {
  FileTextIcon,
  QuestionMarkCircledIcon,
  GearIcon,
  LightningBoltIcon,
  TargetIcon,
  CheckCircledIcon,
  CrossCircledIcon,
  ImageIcon,
  UploadIcon
} from "@radix-ui/react-icons";

import { toast } from '@/components/ui/use-toast';
import { useCreateCaseStudyMutation } from '@/lib/redux/api/caseStudiesApi';

// Dynamically import Image component to avoid hydration errors
const Image = dynamic(() => import('next/image'), { ssr: false });

export default function AddCaseForm({ onSuccess }: { onSuccess?: () => void }) {
  const router = useRouter();
  const [createCaseStudy, { isLoading: isCreating }] = useCreateCaseStudyMutation();
  const [headerImage, setHeaderImage] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  // Use useEffect to handle client-side only code
  useEffect(() => {
    setMounted(true);
  }, []);

  // Form state
  const [formData, setFormData] = useState({
    useCaseTitle: '',
    industry: '',
    role: '',
    potentiallyImpactedKpis: '',
    introductionTitle: '',
    introductionText: '',
    transitionToProblems: '',
    problem1: '',
    problem2: '',
    problem3: '',
    transitionToQuestions: '',
    question1: '',
    question2: '',
    question3: '',
    processTitle: '',
    processStep1Title: '',
    processStep1Description: '',
    processStep2Title: '',
    processStep2Description: '',
    processStep3Title: '',
    processStep3Description: '',
    solutionTitle: '',
    solutionDescription: '',
    solution1Title: '',
    solution1Description: '',
    solution2Title: '',
    solution2Description: '',
    solution3Title: '',
    solution3Description: '',
    impactTitle: '',
    potentialImpact1: '',
    potentialImpact2: '',
    potentialImpact3: '',
    potentialImpact4: '',
    conclusionTitle: '',
    conclusionText: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleSelectChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'header');

      try {
        const response = await fetch('/api/case-studies/upload-media', {
          method: 'POST',
          body: formData,
        });
        const data = await response.json();
        if (data.url) {
          setHeaderImage(data.url);
        }
      } catch (error) {
        console.error('Error uploading image:', error);
        toast({
          title: 'Error',
          description: 'Failed to upload image',
          variant: 'destructive',
        });
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const caseStudyData = {
        ...formData,
        headerImage,
      };

      await createCaseStudy(caseStudyData).unwrap();

      toast({
        title: 'Success',
        description: 'Case study created successfully',
      });

      // Reset form
      setFormData({
        useCaseTitle: '',
        industry: '',
        role: '',
        potentiallyImpactedKpis: '',
        introductionTitle: '',
        introductionText: '',
        transitionToProblems: '',
        problem1: '',
        problem2: '',
        problem3: '',
        transitionToQuestions: '',
        question1: '',
        question2: '',
        question3: '',
        processTitle: '',
        processStep1Title: '',
        processStep1Description: '',
        processStep2Title: '',
        processStep2Description: '',
        processStep3Title: '',
        processStep3Description: '',
        solutionTitle: '',
        solutionDescription: '',
        solution1Title: '',
        solution1Description: '',
        solution2Title: '',
        solution2Description: '',
        solution3Title: '',
        solution3Description: '',
        impactTitle: '',
        potentialImpact1: '',
        potentialImpact2: '',
        potentialImpact3: '',
        potentialImpact4: '',
        conclusionTitle: '',
        conclusionText: '',
      });
      setHeaderImage(null);

      if (onSuccess) {
        onSuccess();
      } else {
        router.push('/dashboard/case-studies');
      }
    } catch (error) {
      console.error('Failed to create case study:', error);
      toast({
        title: 'Error',
        description: 'Failed to create case study',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Header Section */}
          <div className="space-y-4">
            <div className="relative h-48 bg-muted rounded-lg overflow-hidden">
              {mounted && headerImage ? (
                <Image src={headerImage} alt="Header" layout="fill" objectFit="cover" />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <label className="cursor-pointer flex flex-col items-center">
                    <ImageIcon className="w-8 h-8 text-muted-foreground" />
                    <span className="mt-2 text-sm text-muted-foreground">Upload Header Image</span>
                    <input type="file" className="hidden" onChange={handleImageUpload} accept="image/*" />
                  </label>
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="useCaseTitle">Use Case Title</Label>
                <Input
                  id="useCaseTitle"
                  placeholder="Enter the use case title"
                  value={formData.useCaseTitle}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <Label htmlFor="industry">Industry</Label>
                <Select onValueChange={(value) => handleSelectChange('industry', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="retail-ecommerce">Retail & E-commerce</SelectItem>
                    <SelectItem value="mining-natural-resources">Mining & Natural Resources</SelectItem>
                    <SelectItem value="healthcare-pharmaceuticals">Healthcare-Pharmaceuticals</SelectItem>
                    <SelectItem value="fmcg-cpg-consumer-goods">FMCG-CPG & Consumer Goods</SelectItem>
                    <SelectItem value="energy-utilities">Energy & Utilities</SelectItem>
                    <SelectItem value="construction-real-estate">Construction & Real-Estate</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                    <SelectItem value="media-entertainment-gaming">Media-Entertainment and Gaming</SelectItem>
                    <SelectItem value="banking-financial-services">Banking & Financial Services</SelectItem>
                    <SelectItem value="agriculture-dairy-farming">Agriculture & Dairy Farming</SelectItem>
                    <SelectItem value="legal-professional-services">Legal & Professional Services</SelectItem>
                    <SelectItem value="travel-transportation-logistics">Travel, Transportation logistics</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="role">Role</Label>
                <Select onValueChange={(value) => handleSelectChange('role', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ceo">CEO</SelectItem>
                    <SelectItem value="coo">COO</SelectItem>
                    <SelectItem value="cfo">CFO</SelectItem>
                    <SelectItem value="cmo">CMO</SelectItem>
                    <SelectItem value="cto">CTO</SelectItem>
                    <SelectItem value="chro">CHRO</SelectItem>
                    <SelectItem value="cro">CRO</SelectItem>
                    <SelectItem value="cxo">CXO</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="founder">Founder</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="potentiallyImpactedKpis">Potentially Impacted KPIs</Label>
                <Input
                  id="potentiallyImpactedKpis"
                  placeholder="Enter KPIs"
                  value={formData.potentiallyImpactedKpis}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>

          {/* Introduction Section */}
          <div className="space-y-4 border rounded-lg p-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <FileTextIcon />
              Introduction
            </h3>
            <div>
              <Label htmlFor="introductionTitle">Title</Label>
              <Input
                id="introductionTitle"
                placeholder="Introduction title"
                value={formData.introductionTitle}
                onChange={handleChange}
              />
            </div>
            <div>
              <Label htmlFor="introductionText">Content</Label>
              <Textarea
                id="introductionText"
                placeholder="Write the introduction..."
                className="h-32"
                value={formData.introductionText}
                onChange={handleChange}
              />
            </div>
          </div>

          {/* Problems Section */}
          <div className="space-y-4 border rounded-lg p-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <CrossCircledIcon />
              Problems
            </h3>
            <div>
              <Label htmlFor="transitionToProblems">Transition to Problems</Label>
              <Textarea
                id="transitionToProblems"
                placeholder="Write the transition..."
                value={formData.transitionToProblems}
                onChange={handleChange}
              />
            </div>
            {[1, 2, 3].map((num) => (
              <div key={`problem${num}`}>
                <Label htmlFor={`problem${num}`}>Problem {num}</Label>
                <Textarea
                  id={`problem${num}`}
                  placeholder={`Describe problem ${num}...`}
                  value={formData[`problem${num}` as keyof typeof formData]}
                  onChange={handleChange}
                />
              </div>
            ))}
          </div>

          {/* Questions Section */}
          <div className="space-y-4 border rounded-lg p-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <QuestionMarkCircledIcon />
              Questions
            </h3>
            <div>
              <Label htmlFor="transitionToQuestions">Transition to Questions</Label>
              <Textarea
                id="transitionToQuestions"
                placeholder="Write the transition..."
                value={formData.transitionToQuestions}
                onChange={handleChange}
              />
            </div>
            {[1, 2, 3].map((num) => (
              <div key={`question${num}`}>
                <Label htmlFor={`question${num}`}>Question {num}</Label>
                <Textarea
                  id={`question${num}`}
                  placeholder={`Write question ${num}...`}
                  value={formData[`question${num}` as keyof typeof formData]}
                  onChange={handleChange}
                />
              </div>
            ))}
          </div>

          {/* Process Section */}
          <div className="space-y-4 border rounded-lg p-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <GearIcon />
              Process
            </h3>
            <div>
              <Label htmlFor="processTitle">Section Title</Label>
              <Input
                id="processTitle"
                placeholder="Process section title"
                value={formData.processTitle}
                onChange={handleChange}
              />
            </div>
            {[1, 2, 3].map((num) => (
              <div key={`process${num}`} className="space-y-2">
                <Label htmlFor={`processStep${num}Title`}>Step {num} Title</Label>
                <Input
                  id={`processStep${num}Title`}
                  placeholder={`Step ${num} title`}
                  value={formData[`processStep${num}Title` as keyof typeof formData]}
                  onChange={handleChange}
                />
                <Label htmlFor={`processStep${num}Description`}>Step {num} Description</Label>
                <div className="flex gap-4">
                  <div className="flex-grow">
                    <Textarea
                      id={`processStep${num}Description`}
                      placeholder={`Describe step ${num}...`}
                      value={formData[`processStep${num}Description` as keyof typeof formData]}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="flex-shrink-0">
                    <label className="cursor-pointer flex flex-col items-center justify-center w-20 h-20 border-2 border-dashed rounded-lg">
                      <UploadIcon className="w-6 h-6 text-muted-foreground" />
                      <span className="mt-1 text-xs text-muted-foreground">Upload Icon</span>
                      <input type="file" className="hidden" accept="image/*" />
                    </label>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Solution Section */}
          <div className="space-y-4 border rounded-lg p-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <LightningBoltIcon />
              Solution
            </h3>
            <div>
              <Label htmlFor="solutionTitle">Section Title</Label>
              <Input
                id="solutionTitle"
                placeholder="Solution section title"
                value={formData.solutionTitle}
                onChange={handleChange}
              />
            </div>
            <div>
              <Label htmlFor="solutionDescription">Overview</Label>
              <Textarea
                id="solutionDescription"
                placeholder="Write solution overview..."
                value={formData.solutionDescription}
                onChange={handleChange}
              />
            </div>
            {[1, 2, 3, 4, 5].map((num) => (
              <div key={`solution${num}`} className="space-y-2">
                <Label htmlFor={`solution${num}Title`}>Solution {num} Title</Label>
                <Input
                  id={`solution${num}Title`}
                  placeholder={`Solution ${num} title`}
                  value={formData[`solution${num}Title` as keyof typeof formData]}
                  onChange={handleChange}
                />
                <Label htmlFor={`solution${num}Description`}>Solution {num} Description</Label>
                <div className="flex gap-4">
                  <div className="flex-grow">
                    <Textarea
                      id={`solution${num}Description`}
                      placeholder={`Describe solution ${num}...`}
                      value={formData[`solution${num}Description` as keyof typeof formData]}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="flex-shrink-0">
                    <label className="cursor-pointer flex flex-col items-center justify-center w-20 h-20 border-2 border-dashed rounded-lg">
                      <UploadIcon className="w-6 h-6 text-muted-foreground" />
                      <span className="mt-1 text-xs text-muted-foreground">Upload Icon</span>
                      <input type="file" className="hidden" accept="image/*" />
                    </label>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Impact Section */}
          <div className="space-y-4 border rounded-lg p-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <TargetIcon />
              Impact
            </h3>
            <div>
              <Label htmlFor="impactTitle">Section Title</Label>
              <Input
                id="impactTitle"
                placeholder="Impact section title"
                value={formData.impactTitle}
                onChange={handleChange}
              />
            </div>
            {[1, 2, 3, 4].map((num) => (
              <div key={`impact${num}`}>
                <Label htmlFor={`potentialImpact${num}`}>Potential Impact {num}</Label>
                <Input
                  id={`potentialImpact${num}`}
                  placeholder={`Describe impact ${num}...`}
                  value={formData[`potentialImpact${num}` as keyof typeof formData]}
                  onChange={handleChange}
                />
              </div>
            ))}
          </div>

          {/* Conclusion Section */}
          <div className="space-y-4 border rounded-lg p-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <CheckCircledIcon />
              Conclusion
            </h3>
            <div>
              <Label htmlFor="conclusionTitle">Title</Label>
              <Input
                id="conclusionTitle"
                placeholder="Conclusion title"
                value={formData.conclusionTitle}
                onChange={handleChange}
              />
            </div>
            <div>
              <Label htmlFor="conclusionText">Content</Label>
              <Textarea
                id="conclusionText"
                placeholder="Write the conclusion..."
                className="h-32"
                value={formData.conclusionText}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/dashboard/case-studies')}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? 'Creating...' : 'Create Case Study'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
