import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies, caseStudyIcons } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { eq, and, isNull } from 'drizzle-orm';

// Type for case study with icons
type CaseStudyWithIcons = typeof caseStudies.$inferSelect & {
  icons: (typeof caseStudyIcons.$inferSelect)[];
};

// Format URL for images
function formatUrl(url: string | null): string | null {
  if (!url) return null;

  // If URL is already a full URL, return it
  if (url.startsWith('http')) return url;

  // Otherwise, it's a relative path, prepend the base URL
  return `${process.env.NEXT_PUBLIC_BASE_URL || ''}${url}`;
}

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Ensure params is properly awaited
    const resolvedParams = await params;
    const id = String(resolvedParams.id);

    if (!id || isNaN(parseInt(id))) {
      return new NextResponse('Invalid ID', { status: 400 });
    }

    // Get single case study with its icons
    const caseStudy = (await db.query.caseStudies.findFirst({
      where: eq(caseStudies.id, parseInt(id)),
      with: {
        icons: {
          orderBy: (icons, { asc }) => [asc(icons.order)],
        },
      },
    })) as CaseStudyWithIcons | null;

    if (!caseStudy) {
      return new NextResponse('Case study not found', { status: 404 });
    }

    // Format all URLs and handle market data
    const formattedCaseStudy = {
      ...caseStudy,
      featureImageUrl: formatUrl(caseStudy.featureImageUrl as string | null),
      previewImageUrl: formatUrl(caseStudy.previewImageUrl as string | null),
      icons: caseStudy.icons.map((icon) => ({
        ...icon,
        iconUrl: formatUrl(icon.iconUrl as string | null),
      })),
      // Ensure market data is properly formatted
      marketIntelligenceData: caseStudy.marketIntelligenceData,
      marketMetricsData: caseStudy.marketMetricsData,
    };

    // Log market data for debugging
    console.log('\n==== API RESPONSE MARKET DATA ====');
    console.log('Case Study ID:', caseStudy.id);
    console.log(
      'marketIntelligenceData type:',
      typeof caseStudy.marketIntelligenceData
    );
    console.log('marketMetricsData type:', typeof caseStudy.marketMetricsData);
    console.log(
      'marketIntelligenceData:',
      caseStudy.marketIntelligenceData ? 'Present' : 'Not present'
    );
    console.log(
      'marketMetricsData:',
      caseStudy.marketMetricsData ? 'Present' : 'Not present'
    );
    console.log('================================\n');

    return NextResponse.json(formattedCaseStudy);
  } catch (error) {
    console.error('Error fetching case study:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}

export async function PUT(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('=== CASE STUDY UPDATE REQUEST STARTED ===');
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    console.log('User authenticated:', session.user.id);

    // Check if the user is an owner
    if (
      session.user.role !== 'owner' &&
      session.user.role !== 'teamMember' &&
      session.user.role !== 'viewer'
    ) {
      console.log(
        `Access denied: Non-owner (role: ${session.user.role}) attempting to update case study`
      );
      return new NextResponse(
        'Forbidden: Only owners can update case studies',
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!id || isNaN(parseInt(id))) {
      return new NextResponse('Invalid ID', { status: 400 });
    }

    const body = await req.json();
    console.log(
      'Received update data:',
      JSON.stringify(
        {
          id: body.id,
          title: body.useCaseTitle,
          headerImage: body.headerImage,
          thumbnailImage: body.thumbnailImage || body.previewImageUrl,
          vector: body.vector,
          hasProcess: !!body.process,
          hasSolution: !!body.solution,
          hasImpact: !!body.impact,
        },
        null,
        2
      )
    );

    // Extract image URLs from the body for later use with icons
    let headerImage = body.headerImage;
    let thumbnailImage = body.thumbnailImage || body.previewImageUrl;

    console.log('Initial image values:', { headerImage, thumbnailImage });

    // Create a copy of the body without the icon-related fields
    const {
      headerImage: _h,
      thumbnailImage: _t,
      previewImageUrl: _p,
      deletedAt: _d,
      ...dbData
    } = body;

    // Start a transaction to ensure both case study and icons are updated atomically
    await db.transaction(async (tx) => {
      // Update the case study - explicitly set deletedAt to null to ensure it's never accidentally set
      const updated = await tx
        .update(caseStudies)
        .set({
          ...dbData,
          // deletedAt: null, // Explicitly set to null to prevent accidental deletion
          updatedAt: new Date(),
        })
        .where(eq(caseStudies.id, parseInt(id)))
        .returning();

      if (updated.length === 0) {
        throw new Error('Case study not found');
      }

      // Process icons if they exist in the request
      console.log('Processing icons for case study:', id);

      // Use the icons array directly from the request body if it exists
      const icons = body.icons || [];

      // Log the icons for debugging
      console.log('Updating icons for case study:', id);
      console.log('Header image:', headerImage);
      console.log('Thumbnail image:', thumbnailImage);
      console.log('Icons from request:', icons.length);
      console.log('Icons by type:', {
        header: icons.filter((i: any) => i.iconType === 'header').length,
        thumbnail: icons.filter((i: any) => i.iconType === 'icon').length,
        process: icons.filter((i: any) => i.iconType === 'process').length,
        solution: icons.filter((i: any) => i.iconType === 'solution').length,
        impact: icons.filter((i: any) => i.iconType === 'impact').length,
      });

      // Delete existing icons first
      await tx
        .delete(caseStudyIcons)
        .where(eq(caseStudyIcons.caseStudyId, parseInt(id)));

      // Prepare new icon inserts
      const iconInserts = [];

      // Add icons from the request body
      if (icons && icons.length > 0) {
        icons.forEach((icon: any) => {
          if (icon.iconUrl && icon.iconType) {
            console.log(`Adding ${icon.iconType} icon:`, icon.iconUrl);
            iconInserts.push({
              caseStudyId: parseInt(id),
              iconType: icon.iconType,
              iconUrl: icon.iconUrl,
              order: icon.order || 0,
            });
          }
        });
      }

      // Add header and thumbnail icons if they're not already in the icons array
      // This is for backward compatibility
      if (headerImage && !icons.some((i: any) => i.iconType === 'header')) {
        console.log('Adding header image from headerImage field:', headerImage);
        iconInserts.push({
          caseStudyId: parseInt(id),
          iconType: 'header',
          iconUrl: headerImage,
          order: 0,
        });
      }

      if (thumbnailImage && !icons.some((i: any) => i.iconType === 'icon')) {
        console.log(
          'Adding thumbnail image from thumbnailImage field:',
          thumbnailImage
        );
        iconInserts.push({
          caseStudyId: parseInt(id),
          iconType: 'icon',
          iconUrl: thumbnailImage,
          order: 0,
        });
      }

      // Insert all new icons
      if (iconInserts.length > 0) {
        console.log(
          `Inserting ${iconInserts.length} icons for case study ${id}`
        );
        await tx.insert(caseStudyIcons).values(iconInserts);
      } else {
        console.log('No icons to insert for case study:', id);
      }
    });

    // Get the updated case study with icons using the query builder
    const updatedCaseStudy = (await db.query.caseStudies.findFirst({
      where: eq(caseStudies.id, parseInt(id)),
      with: {
        icons: {
          orderBy: (icons, { asc }) => [asc(icons.order)],
        },
      },
    })) as CaseStudyWithIcons | null;

    if (!updatedCaseStudy) {
      console.error('Case study not found after update');
      return new NextResponse('Case study not found after update', {
        status: 404,
      });
    }

    // Format all URLs in the response
    const formattedCaseStudy = {
      ...updatedCaseStudy,
      featureImageUrl: formatUrl(
        updatedCaseStudy.featureImageUrl as string | null
      ),
      previewImageUrl: formatUrl(
        updatedCaseStudy.previewImageUrl as string | null
      ),
      icons: updatedCaseStudy.icons.map((icon) => ({
        ...icon,
        iconUrl: formatUrl(icon.iconUrl as string | null),
      })),
    };

    console.log('Returning updated case study with icons:', {
      id: formattedCaseStudy.id,
      title: formattedCaseStudy.useCaseTitle,
      iconCount: formattedCaseStudy.icons.length,
      icons: formattedCaseStudy.icons.map((i) => ({
        type: i.iconType,
        url: i.iconUrl,
      })),
    });

    return NextResponse.json(formattedCaseStudy);
  } catch (error) {
    console.error('Error updating case study:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}

export async function DELETE(
  _req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Check if the user is an owner
    if (session.user.role !== 'owner') {
      console.log(
        `Access denied: Non-owner (role: ${session.user.role}) attempting to delete case study`
      );
      return new NextResponse(
        'Forbidden: Only owners can delete case studies',
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!id || isNaN(parseInt(id))) {
      return new NextResponse('Invalid ID', { status: 400 });
    }

    console.log(`Deleting case study with ID: ${id}`);

    let result;
    try {
      // Start a transaction to ensure all related data is deleted
      await db.transaction(async (tx) => {
        // Delete case study icons first
        await tx
          .delete(caseStudyIcons)
          .where(eq(caseStudyIcons.caseStudyId, parseInt(id)));

        // Delete bookmarks related to this case study (if any)
        try {
          const { bookmarks } = await import('@/lib/db/schema');
          await tx
            .delete(bookmarks)
            .where(eq(bookmarks.caseStudyId, parseInt(id)));
        } catch (err) {
          console.log('No bookmarks table or no bookmarks to delete');
        }

        // Delete the case study itself
        result = await tx
          .delete(caseStudies)
          .where(eq(caseStudies.id, parseInt(id)))
          .returning();

        if (result.length === 0) {
          throw new Error('Case study not found');
        }
      });

      // If we get here, the deletion was successful
      console.log(`Successfully deleted case study with ID: ${id}`);
    } catch (txError) {
      console.error('Transaction error:', txError);
      return new NextResponse('Case study not found or error during deletion', {
        status: 404,
      });
    }

    if (!result) {
      return new NextResponse('Case study not found', { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting case study:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}
