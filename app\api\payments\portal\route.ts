import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { createCustomerPortalSession } from '@/lib/payments/stripe';

export async function POST(request: NextRequest) {
  try {
    const user = await getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an owner - owners don't need to access billing
    if (user.role === 'owner') {
      return NextResponse.json({ error: 'Owners cannot access billing portal' }, { status: 403 });
    }

    // Get the request body for returnUrl
    let returnUrl: string | undefined;
    try {
      const body = await request.json();
      returnUrl = body.returnUrl;
    } catch (error) {
      // If there's no body or it can't be parsed, continue without a returnUrl
      console.log('No request body or invalid JSON, using default returnUrl');
    }

    // Get the team for the user
    const team = await getTeamForUser(user.id);

    if (!team) {
      return NextResponse.json({ error: 'No team found for user' }, { status: 404 });
    }

    // Check if the team has a Stripe customer ID and subscription ID
    if (!team.stripeCustomerId) {
      return NextResponse.json({ error: 'No customer found' }, { status: 404 });
    }

    // Check if the team has an active subscription
    if (!team.stripeSubscriptionId) {
      return NextResponse.json({
        error: 'No active subscription found',
        details: 'You need to have an active subscription to access the billing portal'
      }, { status: 400 });
    }

    // Create a Stripe customer portal session
    try {
      console.log('Creating customer portal session for team:', {
        teamId: team.id,
        stripeCustomerId: team.stripeCustomerId,
        stripeProductId: team.stripeProductId,
        planName: team.planName,
        returnUrl: returnUrl || 'default'
      });

      // Use our enhanced createCustomerPortalSession function with the returnUrl
      const session = await createCustomerPortalSession({
        ...team,
        returnUrl
      });

      console.log('Customer portal session created:', session.url);

      return NextResponse.json({ url: session.url });
    } catch (error) {
      console.error('Error creating customer portal session:', error);
      return NextResponse.json({
        error: 'Failed to create customer portal session',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Customer portal error:', error);
    return NextResponse.json({ error: 'Failed to create customer portal session' }, { status: 500 });
  }
}
