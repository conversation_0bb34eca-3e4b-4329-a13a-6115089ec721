'use client';

import React, { use, useState, useEffect } from 'react';
import { Check, X, Loader2, Plus, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import Navbar from '@/components/Navbar';
import Footer from '@/components/sections/Homepage/Footer';
import { useUser } from '@/lib/auth';
import { toast } from '@/components/ui/use-toast';
import { planFeaturesConfig } from '@/lib/config/plan-features';

// Plan features are now imported from shared configuration

const PricingPage = () => {
  const router = useRouter();
  const { userPromise } = useUser();
  // Safely handle the user promise to avoid errors for non-authenticated users
  let user;
  try {
    user = use(userPromise);
  } catch (error) {
    console.log('User not authenticated');
    // User is not authenticated, that's okay for the pricing page
    user = null;
  }
  const [couponCode, setCouponCode] = useState('');
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discountType: 'percentage' | 'fixed';
    discountAmount: number;
    description: string | null;
  } | null>(null);
  const [isValidatingCoupon, setIsValidatingCoupon] = useState(false);
  const [pricingPlans, setPricingPlans] = useState<any[]>([]);
  const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);
  const [isLoadingPlans, setIsLoadingPlans] = useState(true);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
  const [userSubscriptionData, setUserSubscriptionData] = useState<any>(null);

  // Check if user has an active subscription
  useEffect(() => {
    const checkSubscription = async () => {
      // Only check subscription if user is logged in
      if (!user) return;

      try {
        // Use the same endpoint as the billing page to check subscription status
        const response = await fetch('/api/user/team');

        if (response.ok) {
          const data = await response.json();
          console.log('Pricing page - User subscription data:', data);

          // Store subscription data
          setUserSubscriptionData(data);

          // Check if user has an active subscription
          const isActive =
            data.subscriptionStatus === 'active' ||
            data.subscriptionStatus === 'trialing';

          setHasActiveSubscription(isActive);

          if (isActive) {
            console.log('User has active subscription:', data.planName);
          }
        }
      } catch (error) {
        console.error('Error checking subscription status:', error);
      }
    };

    checkSubscription();
  }, [user]);

  // Fetch plans from Stripe
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setIsLoadingPlans(true);
        setFetchError(null);

        const response = await fetch('/api/plans/purchase');

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success || !Array.isArray(data.plans)) {
          throw new Error('No pricing plans available');
        }

        // Transform database plans to UI format
        const transformedPlans = data.plans.map((plan: any) => {
          const planConfig = planFeaturesConfig[plan.name as keyof typeof planFeaturesConfig];

          return {
            name: plan.displayName,
            price: plan.priceMonthly ? `$${plan.priceMonthly / 100}` : 'Custom',
            priceId: plan.stripePriceIdMonthly || '',
            productId: plan.stripeProductId || '',
            period: plan.priceMonthly ? '/ Month' : 'Pricing',
            description: plan.description,
            features: plan.features?.features || planConfig?.features || [],
            cta: planConfig?.cta || 'Get Started',
            popular: plan.name === 'pro',
            color: planConfig?.color || 'bg-white',
            note: planConfig?.note || null,
            planName: plan.name // Add internal plan name for API calls
          };
        });

        // Add Enterprise plan manually (contact only)
        transformedPlans.push({
          name: 'Enterprise',
          price: 'Custom',
          priceId: '',
          productId: '',
          period: 'Pricing',
          description: 'For large organizations - contact us for pricing',
          features: planFeaturesConfig.enterprise?.features || [],
          cta: 'Contact Us',
          popular: false,
          color: planFeaturesConfig.enterprise?.color || 'bg-white',
          note: planFeaturesConfig.enterprise?.note || null,
          planName: 'enterprise'
        });

        setPricingPlans(transformedPlans);
      } catch (error) {
        console.error('Error fetching plans:', error);
        setFetchError(error instanceof Error ? error.message : 'Failed to load pricing plans');
        setPricingPlans([]);
      } finally {
        setIsLoadingPlans(false);
      }
    };

    fetchPlans();
  }, []);

  // Function to handle coupon code validation
  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a coupon code',
        variant: 'destructive',
      });
      return;
    }

    setIsValidatingCoupon(true);

    try {
      console.log('Validating coupon code:', couponCode);

      const response = await fetch('/api/coupon-codes/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: couponCode }),
      });

      console.log('Validation response status:', response.status);

      const data = await response.json();
      console.log('Validation response data:', data);

      if (data.valid) {
        setAppliedCoupon(data.coupon);
        toast({
          title: 'Success',
          description: `Coupon code "${data.coupon.code}" applied successfully!`,
        });
      } else {
        setAppliedCoupon(null);
        toast({
          title: 'Invalid Coupon',
          description: data.message || 'The coupon code is invalid or expired',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error validating coupon:', error);
      setAppliedCoupon(null);
      toast({
        title: 'Error',
        description: 'Failed to validate coupon code. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsValidatingCoupon(false);
    }
  };

  // Function to calculate discounted price
  const getDiscountedPrice = (originalPrice: string) => {
    if (!appliedCoupon) return originalPrice;

    // Remove currency symbol and commas from the price string
    const numericPrice = parseFloat(originalPrice.replace(/[$,]/g, ''));

    // If parsing failed, return the original price
    if (isNaN(numericPrice)) {
      console.error('Failed to parse price:', originalPrice);
      return originalPrice;
    }

    if (appliedCoupon.discountType === 'percentage') {
      const discountedPrice =
        numericPrice * (1 - appliedCoupon.discountAmount / 100);
      return `$${discountedPrice.toFixed(2)}`;
    } else {
      // For fixed amount discount
      const discountedPrice = Math.max(
        0,
        numericPrice - appliedCoupon.discountAmount
      );
      return `$${discountedPrice.toFixed(2)}`;
    }
  };

  return (
    <>
      <Navbar />
      <main className='min-h-screen bg-gray-50 pt-32'>
        <div className='container mx-auto px-4 md:px-6 lg:px-8 max-w-6xl'>
          <div className='text-center mb-16'>
            <h1 className='text-4xl md:text-5xl font-bold mb-8 text-gray-900'>
              Plans Designed for Every Stage of Your AI Journey
            </h1>
            <p className='text-lg md:text-xl text-gray-700 max-w-3xl mx-auto'>
              Whether you're exploring possibilities or driving enterprise-wide
              AI transformation, we have a plan tailored for you.
            </p>
            {user?.role === 'owner' && (
              <div className='mt-4 p-4 bg-blue-50 rounded-lg max-w-3xl mx-auto'>
                <p className='text-blue-800'>
                  <strong>Note:</strong> As an owner, you don't need to purchase
                  a subscription plan. These plans are for team members only.
                </p>
              </div>
            )}
          </div>

          {isLoadingPlans ? (
            <div className='flex justify-center items-center h-40'>
              <Loader2 className='h-8 w-8 animate-spin text-primary' />
              <span className='ml-2 text-gray-600'>Loading plans from Stripe...</span>
            </div>
          ) : fetchError ? (
            <div className='flex flex-col justify-center items-center h-40 text-center'>
              <div className='text-red-600 mb-4'>
                <X className='h-12 w-12 mx-auto mb-2' />
                <h3 className='text-lg font-semibold'>Failed to Load Pricing</h3>
                <p className='text-sm text-gray-600 mt-1'>{fetchError}</p>
              </div>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className='mt-2'
              >
                Try Again
              </Button>
            </div>
          ) : pricingPlans.length === 0 ? (
            <div className='flex flex-col justify-center items-center h-40 text-center'>
              <div className='text-gray-600 mb-4'>
                <AlertTriangle className='h-12 w-12 mx-auto mb-2' />
                <h3 className='text-lg font-semibold'>No Plans Available</h3>
                <p className='text-sm text-gray-600 mt-1'>Please check back later or contact support.</p>
              </div>
            </div>
          ) : (
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              {pricingPlans.map((plan, index) => (
                <div
                  key={index}
                  className={`${
                    plan.color === 'white' ? 'bg-white' : plan.color
                  } ${
                    plan.popular ? 'relative z-10 shadow-xl' : ''
                  } border border-[#d0d7e0] rounded-lg overflow-hidden`}
                >
                  <div
                    className={`p-8 border-b ${
                      plan.name === 'Pro'
                        ? 'border-blue-400'
                        : 'border-[#eaeff4]'
                    }`}
                  >
                    <h3
                      className={`text-2xl font-bold mb-2 ${
                        plan.name === 'Pro' ? 'text-white' : 'text-gray-900'
                      }`}
                    >
                      {plan.name}
                    </h3>
                    <p
                      className={`mb-4 h-12 flex items-center ${
                        plan.name === 'Pro' ? 'text-white/80' : 'text-gray-600'
                      }`}
                    >
                      {plan.description}
                    </p>
                    <div className='mb-6'>
                      {appliedCoupon && (
                        <div className='mb-1'>
                          <span
                            className={`text-2xl font-bold line-through ${
                              plan.name === 'Pro'
                                ? 'text-white/60'
                                : 'text-gray-400'
                            }`}
                          >
                            {plan.price}
                          </span>
                          <span
                            className={`ml-1 ${
                              plan.name === 'Pro'
                                ? 'text-white/60'
                                : 'text-gray-400'
                            }`}
                          >
                            {plan.period}
                          </span>
                        </div>
                      )}
                      <div>
                        <span
                          className={`text-4xl font-bold ${
                            plan.name === 'Pro' ? 'text-white' : 'text-gray-900'
                          }`}
                        >
                          {appliedCoupon
                            ? getDiscountedPrice(plan.price)
                            : plan.price}
                        </span>
                        <span
                          className={`ml-1 ${
                            plan.name === 'Pro'
                              ? 'text-white/80'
                              : 'text-gray-500'
                          }`}
                        >
                          {plan.period}
                        </span>
                      </div>
                    </div>
                    <Button
                      className={`w-full py-6 rounded-md ${
                        plan.name === 'Pro'
                          ? 'bg-white hover:bg-gray-50 text-blue-600 border border-white'
                          : 'bg-white text-blue-600 border border-blue-600 hover:bg-blue-50'
                      }`}
                      variant='outline'
                      onClick={async () => {
                        // For Enterprise plan, redirect to contact page
                        if (plan.planName === 'enterprise') {
                          toast({
                            title: 'Enterprise Plan',
                            description: 'Please contact us for Enterprise pricing and setup.',
                          });
                          router.push('/contact');
                          return;
                        }

                        if (!user) {
                          router.push('/sign-up');
                          return;
                        }

                        if (user.role === 'owner') {
                          toast({
                            title: 'Owner Account',
                            description: "As an owner, you don't need to purchase a plan. This is for team members only.",
                            variant: 'destructive',
                          });
                          return;
                        }

                        if (hasActiveSubscription) {
                          // If user has the same plan, redirect to billing
                          if (userSubscriptionData?.planName === plan.planName) {
                            toast({
                              title: 'Already Subscribed',
                              description: `You already have an active ${plan.planName} subscription.`,
                            });
                            router.push('/dashboard/billing');
                            return;
                          }

                          // If user has a different plan, redirect to contact for plan changes
                          toast({
                            title: 'Plan Change Required',
                            description: 'To change your plan, please contact our support team.',
                          });
                          router.push('/contact');
                          return;
                        }

                        // Use new purchase API (industry best practice)
                        try {
                          const response = await fetch('/api/plans/purchase', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              planName: plan.planName,
                              billingCycle: 'monthly'
                            }),
                          });

                          const data = await response.json();

                          if (data.success && data.checkoutUrl) {
                            // Redirect to Stripe checkout
                            window.location.href = data.checkoutUrl;
                          } else {
                            // Handle different error types
                            if (data.error === 'Already subscribed') {
                              toast({
                                title: 'Already Subscribed',
                                description: data.message,
                              });
                            } else if (data.error === 'Plan change not supported') {
                              toast({
                                title: 'Plan Change Required',
                                description: data.message,
                              });
                            } else {
                              toast({
                                title: 'Purchase Failed',
                                description: data.message || 'Unable to start purchase process',
                                variant: 'destructive',
                              });
                            }

                            // Handle specific redirect cases
                            if (data.redirectTo) {
                              setTimeout(() => {
                                router.push(data.redirectTo);
                              }, 1500);
                            }
                          }
                        } catch (error) {
                          console.error('Purchase error:', error);
                          toast({
                            title: 'Error',
                            description: 'Unable to process purchase. Please try again.',
                            variant: 'destructive',
                          });
                        }
                      }}
                      disabled={user?.role === 'owner'}
                    >
                      {user?.role === 'owner'
                        ? 'Not Available for Owners'
                        : plan.cta}
                    </Button>
                  </div>
                  <div className='p-8'>
                    <ul className='space-y-4'>
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className='flex items-center'>
                          {feature.included ? (
                            <Check
                              className={`h-5 w-5 mr-3 flex-shrink-0 ${
                                plan.name === 'Pro'
                                  ? 'text-white'
                                  : 'text-green-500'
                              }`}
                            />
                          ) : (
                            <X
                              className={`h-5 w-5 mr-3 flex-shrink-0 ${
                                plan.name === 'Pro'
                                  ? 'text-white/40'
                                  : 'text-gray-300'
                              }`}
                            />
                          )}
                          <span
                            className={
                              plan.name === 'Pro'
                                ? feature.included
                                  ? 'text-white'
                                  : 'text-white/40'
                                : feature.included
                                ? 'text-gray-900'
                                : 'text-gray-400'
                            }
                          >
                            {feature.name}
                          </span>
                        </li>
                      ))}
                    </ul>

                    {plan.note && (
                      <div
                        className={`mt-6 pt-4 border-t ${
                          plan.name === 'Pro'
                            ? 'border-blue-400'
                            : 'border-[#eaeff4]'
                        }`}
                      >
                        <p
                          className={`text-sm ${
                            plan.name === 'Pro'
                              ? 'text-white/80'
                              : 'text-gray-900'
                          }`}
                        >
                          {plan.note}
                        </p>
                      </div>
                    )}

                    {/* Add coupon input field only under the Starter plan */}
                    {plan.name === 'Starter' && (
                      <div className='mt-6 pt-4 border-t border-[#eaeff4] dark:border-white/20'>
                        <div className='mb-2 text-sm text-gray-600 dark:text-white'>
                          Coupon Code:
                        </div>
                        <div className='flex gap-2 items-center'>
                          <div className='relative flex-1'>
                            <Input
                              placeholder='FREE50'
                              value={couponCode}
                              onChange={(e) =>
                                setCouponCode(e.target.value.toUpperCase())
                              }
                              className='flex-1 uppercase border-gray-300 dark:border-white/20 dark:bg-black dark:text-white'
                            />
                            {couponCode && (
                              <div
                                className='absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer text-gray-400 hover:text-gray-600 dark:text-white/60 dark:hover:text-white'
                                onClick={() => setCouponCode('')}
                              >
                                ✕
                              </div>
                            )}
                          </div>
                          <Button
                            onClick={validateCoupon}
                            disabled={isValidatingCoupon}
                            className='bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-800'
                          >
                            {isValidatingCoupon ? '...' : 'Apply'}
                          </Button>
                        </div>
                        {appliedCoupon && (
                          <div className='mt-3 p-2 bg-green-50 border border-green-200 rounded-md text-sm text-green-700 dark:bg-black dark:border-green-600 dark:text-green-400'>
                            Coupon <strong>{appliedCoupon.code}</strong>{' '}
                            applied:
                            {appliedCoupon.discountType === 'percentage'
                              ? ` ${appliedCoupon.discountAmount}% off`
                              : ` $${appliedCoupon.discountAmount.toFixed(
                                  2
                                )} off`}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className='mt-16 bg-[#eaeff4] rounded-lg p-8 text-center'>
            <h2 className='text-2xl font-bold mb-4 text-gray-900'>
              Need a custom solution?
            </h2>
            <p className='text-gray-700 mb-6 max-w-2xl mx-auto'>
              We offer tailored solutions for enterprises with specific
              requirements. Contact our sales team to discuss your needs.
            </p>
            <Button
              variant='outline'
              className='bg-white hover:bg-gray-50 text-gray-900'
              onClick={() => router.push('/contact')}
            >
              Contact Sales
            </Button>
          </div>

          <section className='relative py-20 w-full overflow-hidden mt-16'>
            {/* Animated Grid Background */}
            <div className='absolute inset-0'>
              <div className='w-full h-full bg-[#eaeff4] opacity-60'></div>
            </div>

            <div className='relative z-10 container mx-auto px-4 md:px-6 lg:px-8'>
              {/* FAQ Header */}
              <div className='text-center mb-16'>
                <h2 className='text-3xl md:text-5xl font-bold mb-6'>
                  Frequently Asked{' '}
                  <span className='text-blue-500'>Questions</span>
                </h2>
                <p className='text-base md:text-lg'>
                  Find quick answers to some of the most common
                  <br />
                  questions about our pricing plans.
                </p>
              </div>

              {/* FAQ Items */}
              <div className='max-w-3xl mx-auto space-y-4'>
                <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'>
                  <button
                    className='w-full px-8 py-6 flex items-center justify-between gap-4'
                    onClick={() =>
                      setOpenFaqIndex(openFaqIndex === 0 ? null : 0)
                    }
                  >
                    <span className='text-lg font-medium text-left text-gray-900'>
                      Is there a trial available before I subscribe?
                    </span>
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                        openFaqIndex === 0 ? 'rotate-45' : ''
                      }`}
                    >
                      <Plus className='h-5 w-5 text-white' />
                    </div>
                  </button>
                  <div
                    className='overflow-hidden transition-all duration-300 ease-in-out'
                    style={{
                      maxHeight: openFaqIndex === 0 ? '500px' : '0',
                    }}
                  >
                    <div className='px-8 pb-6 text-gray-600'>
                      Yes, we offer a 14-day free trial. After the trial period
                      ends, your selected plan will automatically begin, and you
                      will be charged accordingly.
                    </div>
                  </div>
                </div>

                <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'>
                  <button
                    className='w-full px-8 py-6 flex items-center justify-between gap-4'
                    onClick={() =>
                      setOpenFaqIndex(openFaqIndex === 1 ? null : 1)
                    }
                  >
                    <span className='text-lg font-medium text-left text-gray-900'>
                      Can I upgrade my plan later?
                    </span>
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                        openFaqIndex === 1 ? 'rotate-45' : ''
                      }`}
                    >
                      <Plus className='h-5 w-5 text-white' />
                    </div>
                  </button>
                  <div
                    className='overflow-hidden transition-all duration-300 ease-in-out'
                    style={{
                      maxHeight: openFaqIndex === 1 ? '500px' : '0',
                    }}
                  >
                    <div className='px-8 pb-6 text-gray-600'>
                      Absolutely. You can easily move from Starter to Pro or
                      Enterprise as your team and AI ambitions grow.
                    </div>
                  </div>
                </div>

                <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'>
                  <button
                    className='w-full px-8 py-6 flex items-center justify-between gap-4'
                    onClick={() =>
                      setOpenFaqIndex(openFaqIndex === 2 ? null : 2)
                    }
                  >
                    <span className='text-lg font-medium text-left text-gray-900'>
                      Can I cancel my subscription anytime?
                    </span>
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                        openFaqIndex === 2 ? 'rotate-45' : ''
                      }`}
                    >
                      <Plus className='h-5 w-5 text-white' />
                    </div>
                  </button>
                  <div
                    className='overflow-hidden transition-all duration-300 ease-in-out'
                    style={{
                      maxHeight: openFaqIndex === 2 ? '500px' : '0',
                    }}
                  >
                    <div className='px-8 pb-6 text-gray-600'>
                      Yes. Our plans are flexible. You can cancel or modify your
                      subscription with a simple request — no long-term
                      lock-ins.
                    </div>
                  </div>
                </div>

                <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'>
                  <button
                    className='w-full px-8 py-6 flex items-center justify-between gap-4'
                    onClick={() =>
                      setOpenFaqIndex(openFaqIndex === 3 ? null : 3)
                    }
                  >
                    <span className='text-lg font-medium text-left text-gray-900'>
                      Do you offer volume or multi-team discounts?
                    </span>
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                        openFaqIndex === 3 ? 'rotate-45' : ''
                      }`}
                    >
                      <Plus className='h-5 w-5 text-white' />
                    </div>
                  </button>
                  <div
                    className='overflow-hidden transition-all duration-300 ease-in-out'
                    style={{
                      maxHeight: openFaqIndex === 3 ? '500px' : '0',
                    }}
                  >
                    <div className='px-8 pb-6 text-gray-600'>
                      For Enterprise customers with large teams, we provide
                      custom pricing options and bundled user packages. Contact
                      us for details.
                    </div>
                  </div>
                </div>

                <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'>
                  <button
                    className='w-full px-8 py-6 flex items-center justify-between gap-4'
                    onClick={() =>
                      setOpenFaqIndex(openFaqIndex === 4 ? null : 4)
                    }
                  >
                    <span className='text-lg font-medium text-left text-gray-900'>
                      Will I get notified before my trial or subscription
                      renews?
                    </span>
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                        openFaqIndex === 4 ? 'rotate-45' : ''
                      }`}
                    >
                      <Plus className='h-5 w-5 text-white' />
                    </div>
                  </button>
                  <div
                    className='overflow-hidden transition-all duration-300 ease-in-out'
                    style={{
                      maxHeight: openFaqIndex === 4 ? '500px' : '0',
                    }}
                  >
                    <div className='px-8 pb-6 text-gray-600'>
                      Yes, you'll receive a reminder before your trial ends and
                      before any subscription renewals, so you stay fully
                      informed.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>
      <Footer />
    </>
  );
};

export default PricingPage;
