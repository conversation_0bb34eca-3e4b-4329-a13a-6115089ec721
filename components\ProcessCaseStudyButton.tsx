'use client';

import { Button } from '@/components/ui/button';
import { SpeakerQuietIcon as SparklesIcon } from '@radix-ui/react-icons';

interface ProcessCaseStudyButtonProps {
  caseStudyId: number;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function ProcessCaseStudyButton({
  caseStudyId,
  variant = "outline",
  size = "default",
  className = ""
}: ProcessCaseStudyButtonProps) {
  // Create a URL for the process API endpoint
  const processUrl = `/api/case-studies/${caseStudyId}/process`;

  return (
    <form action={processUrl} method="post">
      <Button
        type="submit"
        variant={variant}
        size={size}
        className={className}
      >
        <SparklesIcon className="mr-2 h-4 w-4" />
        Process with AI
      </Button>
    </form>
  );
}

// Add default export
export default ProcessCaseStudyButton;
