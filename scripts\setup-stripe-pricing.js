#!/usr/bin/env node

/**
 * Complete Stripe Pricing Setup Script
 * 
 * This script will:
 * 1. Create all required products and prices in Stripe
 * 2. Generate environment variables for your .env file
 * 3. Provide SQL commands to update your database
 * 
 * Usage:
 * 1. Set STRIPE_SECRET_KEY in your environment
 * 2. Run: node scripts/setup-stripe-pricing.js
 * 3. Copy the generated environment variables to your .env file
 * 4. Run the provided SQL commands to update your database
 */

const Stripe = require('stripe');
require('dotenv').config();

// Pricing configuration - UPDATE THESE VALUES AS NEEDED
const PRICING_CONFIG = {
  starter: {
    name: 'Starter',
    description: 'Perfect for individuals getting started with AI - 14 day free trial',
    monthlyPrice: 1200, // $12.00 in cents
    yearlyPrice: null,  // No yearly option
    trialDays: 14,
    features: [
      '14-day free trial',
      'Access to 10 curated AI use cases',
      'Preview of Market Intelligence Unit (MIU)',
      '1 registered user'
    ]
  },
  pro: {
    name: 'Pro',
    description: 'For teams ready to scale with AI - unlimited access',
    monthlyPrice: 6000,  // $60.00 in cents
    yearlyPrice: 60000,  // $600.00 in cents (10 months pricing)
    trialDays: 0,
    features: [
      'Unlimited case study access',
      'Full MIU access',
      'Roadmap tools & prioritization framework',
      'Team collaboration features',
      'Dedicated account support',
      'Up to 3 users',
      'Priority email support'
    ]
  },
  enterprise: {
    name: 'Enterprise',
    description: 'For large organizations - contact us for pricing',
    monthlyPrice: null,  // Contact for pricing
    yearlyPrice: null,   // Contact for pricing
    trialDays: 0,
    features: [
      'Everything in Pro',
      'Up to 7 CXO-level users',
      'Custom integrations',
      'Dedicated account manager',
      'SLA guarantees',
      'Custom branding',
      'SSO integration'
    ]
  }
};

async function setupStripePricing() {
  console.log('🚀 Setting up Stripe pricing for production...\n');

  // Validate Stripe key
  if (!process.env.STRIPE_SECRET_KEY) {
    console.error('❌ Error: STRIPE_SECRET_KEY environment variable is required');
    console.log('Please set your Stripe secret key in your .env file or environment variables');
    process.exit(1);
  }

  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
  const results = {};

  try {
    // Test Stripe connection
    console.log('🔍 Testing Stripe connection...');
    await stripe.products.list({ limit: 1 });
    console.log('✅ Stripe connection successful\n');

    // Create products and prices
    for (const [planKey, config] of Object.entries(PRICING_CONFIG)) {
      console.log(`📦 Creating ${config.name} plan...`);

      // Create product
      const product = await stripe.products.create({
        name: config.name,
        description: config.description,
        active: true,
        metadata: {
          plan_type: planKey,
          max_users: planKey === 'starter' ? '1' : planKey === 'pro' ? '3' : '7'
        }
      });

      console.log(`   ✅ Product created: ${product.id}`);
      results[planKey] = { product: product.id, prices: {} };

      // Create monthly price (if applicable)
      if (config.monthlyPrice) {
        const monthlyPriceData = {
          product: product.id,
          unit_amount: config.monthlyPrice,
          currency: 'usd',
          recurring: { interval: 'month' },
          metadata: { plan_type: planKey, billing_cycle: 'monthly' }
        };

        // Add trial for starter plan
        if (planKey === 'starter' && config.trialDays > 0) {
          monthlyPriceData.recurring.trial_period_days = config.trialDays;
        }

        const monthlyPrice = await stripe.prices.create(monthlyPriceData);
        console.log(`   ✅ Monthly price created: ${monthlyPrice.id} ($${config.monthlyPrice / 100}/month)`);
        results[planKey].prices.monthly = monthlyPrice.id;
      }

      // Create yearly price (if applicable)
      if (config.yearlyPrice) {
        const yearlyPrice = await stripe.prices.create({
          product: product.id,
          unit_amount: config.yearlyPrice,
          currency: 'usd',
          recurring: { interval: 'year' },
          metadata: { plan_type: planKey, billing_cycle: 'yearly' }
        });
        console.log(`   ✅ Yearly price created: ${yearlyPrice.id} ($${config.yearlyPrice / 100}/year)`);
        results[planKey].prices.yearly = yearlyPrice.id;
      }

      console.log('');
    }

    // Generate output
    generateEnvironmentVariables(results);
    generateDatabaseUpdate(results);
    generateSummary(results);

  } catch (error) {
    console.error('❌ Error setting up Stripe pricing:', error.message);
    
    if (error.type === 'StripeAuthenticationError') {
      console.log('\n💡 Make sure your STRIPE_SECRET_KEY is correct and has the required permissions');
    }
    
    process.exit(1);
  }
}

function generateEnvironmentVariables(results) {
  console.log('📝 ENVIRONMENT VARIABLES');
  console.log('=' .repeat(50));
  console.log('# Add these to your .env file:\n');
  
  // Product IDs
  console.log('# Stripe Product IDs');
  Object.entries(results).forEach(([planKey, data]) => {
    const envKey = `STRIPE_${planKey.toUpperCase()}_PRODUCT_ID`;
    console.log(`${envKey}=${data.product}`);
  });
  
  console.log('\n# Stripe Price IDs');
  Object.entries(results).forEach(([planKey, data]) => {
    if (data.prices.monthly) {
      const envKey = `STRIPE_${planKey.toUpperCase()}_PRICE_ID_MONTHLY`;
      console.log(`${envKey}=${data.prices.monthly}`);
    }
    if (data.prices.yearly) {
      const envKey = `STRIPE_${planKey.toUpperCase()}_PRICE_ID_YEARLY`;
      console.log(`${envKey}=${data.prices.yearly}`);
    }
  });
  
  console.log('\n');
}

function generateDatabaseUpdate(results) {
  console.log('🗄️  DATABASE UPDATE');
  console.log('=' .repeat(50));
  console.log('# Run this SQL to update your database:\n');
  
  Object.entries(results).forEach(([planKey, data]) => {
    const config = PRICING_CONFIG[planKey];
    const monthlyPrice = data.prices.monthly || 'NULL';
    const yearlyPrice = data.prices.yearly || 'NULL';
    
    console.log(`UPDATE plans SET`);
    console.log(`  stripe_product_id = '${data.product}',`);
    console.log(`  stripe_price_id_monthly = ${monthlyPrice ? `'${monthlyPrice}'` : 'NULL'},`);
    console.log(`  stripe_price_id_yearly = ${yearlyPrice ? `'${yearlyPrice}'` : 'NULL'},`);
    console.log(`  price_monthly = ${config.monthlyPrice || 'NULL'},`);
    console.log(`  price_yearly = ${config.yearlyPrice || 'NULL'}`);
    console.log(`WHERE name = '${planKey}';`);
    console.log('');
  });
}

function generateSummary(results) {
  console.log('📊 SETUP SUMMARY');
  console.log('=' .repeat(50));
  
  Object.entries(results).forEach(([planKey, data]) => {
    const config = PRICING_CONFIG[planKey];
    console.log(`\n${config.name} Plan:`);
    console.log(`  Product ID: ${data.product}`);
    if (data.prices.monthly) {
      console.log(`  Monthly Price: ${data.prices.monthly} ($${config.monthlyPrice / 100}/month)`);
    }
    if (data.prices.yearly) {
      console.log(`  Yearly Price: ${data.prices.yearly} ($${config.yearlyPrice / 100}/year)`);
    }
    if (!data.prices.monthly && !data.prices.yearly) {
      console.log(`  Pricing: Contact for pricing`);
    }
  });
  
  console.log('\n🎉 Stripe pricing setup completed successfully!');
  console.log('\n📋 Next Steps:');
  console.log('1. Copy the environment variables to your .env file');
  console.log('2. Run the SQL commands to update your database');
  console.log('3. Restart your application');
  console.log('4. Test the pricing flow on your website');
}

// Run the setup
if (require.main === module) {
  setupStripePricing();
}

module.exports = { setupStripePricing, PRICING_CONFIG };
