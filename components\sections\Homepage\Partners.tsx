'use client';

import { AnimatedGridPattern } from '@/components/magicui/animated-grid-pattern';
import { useEffect, useRef, useState } from 'react';

interface Partner {
  name: string;
  logo: string;
}

const partners: Partner[] = [
  {
    name: 'L&T',
    logo: '/images/logos/partners/1.png',
  },
  {
    name: '<PERSON>bull',
    logo: '/images/logos/partners/2.png',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    logo: '/images/logos/partners/3.png',
  },
  {
    name: 'Neterwala Group',
    logo: '/images/logos/partners/4.png',
  },
  {
    name: 'GRO Capital',
    logo: '/images/logos/partners/5.png',
  },
  {
    name: 'Cummins',
    logo: '/images/logos/partners/6.png',
  },
  {
    name: 'ELP',
    logo: '/images/logos/partners/7.png',
  },
  {
    name: '<PERSON> Gaea',
    logo: '/images/logos/partners/8.png',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    logo: '/images/logos/partners/9.png',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    logo: '/images/logos/partners/10.png',
  },
  {
    name: 'DBS',
    logo: '/images/logos/partners/11.png',
  },
  {
    name: 'HDFC Bank',
    logo: '/images/logos/partners/12.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/13.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/14.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/15.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/16.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/17.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/18.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/19.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/20.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/21.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/22.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/23.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/24.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/25.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/26.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/27.png',
  },
  {
    name: '',
    logo: '/images/logos/partners/28.png',
  },
];

export default function Partners() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  // Create a continuous loop by duplicating the partners array multiple times
  const duplicatedPartners = [
    ...partners,
    ...partners,
    ...partners,
    ...partners,
  ];

  // Create a wrapper div for the marquee animation with proper animation
  const MarqueeWrapper = ({ children }: { children: React.ReactNode }) => {
    return (
      <div
        className={`inline-flex whitespace-nowrap animate-marquee-infinite ${
          isHovered ? 'pause-animation' : ''
        }`}
        style={{
          width: 'max-content',
          animationDuration: '50s', // Fixed duration for faster animation
        }}
      >
        {children}
      </div>
    );
  };

  // Reset animation when window is resized
  useEffect(() => {
    const handleResize = () => {
      if (scrollRef.current) {
        scrollRef.current.scrollLeft = 0;
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <section className='relative py-16 md:py-20 w-full overflow-hidden bg-[#F8FAFC]'>
      {/* Animated Grid Background */}
      <div className='absolute inset-0'>
        <AnimatedGridPattern
          width={70}
          height={70}
          className='text-gray-200/80 md:hidden'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={8}
          x={0}
          y={0}
        />
        <AnimatedGridPattern
          width={150}
          height={150}
          className='text-gray-200/80 hidden md:block'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={20}
          x={0}
          y={0}
        />
      </div>

      {/* Radial Gradient */}
      <div className='z-10 absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0)_0%,rgba(255,255,255,0.2)_25%,rgba(255,255,255,0.9)_50%,rgba(255,255,255,1)_75%)] pointer-events-none'></div>

      <div className='relative z-10 container mx-auto px-4 md:px-6 lg:px-8'>
        <h2 className='text-center mb-12 md:mb-16'>
          <span className='text-3xl md:text-5xl font-bold text-gray-900'>
            Trusted{' '}
          </span>
          <span className='text-3xl md:text-5xl font-bold text-blue-500'>
            Partners
          </span>
        </h2>

        {/* Partner Logos with Side Blur Effect */}
        <div
          className='relative max-w-4xl mx-auto overflow-hidden'
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          style={{ width: '100%' }}
        >
          {/* Left Overlay */}
          <div className='absolute left-0 top-0 bottom-0 w-12 md:w-24 bg-gradient-to-r from-white to-transparent z-10'></div>

          {/* Partner Logos */}
          <div
            ref={scrollRef}
            className='flex gap-10 md:gap-14 items-center py-4 px-12 md:px-16 overflow-x-hidden hide-scrollbar'
            style={{
              WebkitOverflowScrolling: 'touch', // For smoother scrolling on iOS
            }}
          >
            <MarqueeWrapper>
              {duplicatedPartners.map((partner, index) => (
                <div
                  key={index}
                  className='flex-shrink-0 flex items-center justify-center mx-4'
                  style={{ minWidth: '100px' }} // Ensure consistent width for each logo
                >
                  <img
                    src={partner.logo}
                    alt={partner.name}
                    className='h-24 md:h-32 w-auto opacity-90 hover:opacity-100 transition-all duration-300 transform hover:scale-105'
                    loading='lazy' // Lazy load images for better performance
                  />
                </div>
              ))}
            </MarqueeWrapper>
          </div>

          {/* Right Overlay */}
          <div className='absolute right-0 top-0 bottom-0 w-12 md:w-24 bg-gradient-to-l from-white to-transparent z-10'></div>
        </div>
      </div>
    </section>
  );
}
