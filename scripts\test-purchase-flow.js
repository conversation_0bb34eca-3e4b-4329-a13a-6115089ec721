/**
 * Test script to verify purchase flow
 */

async function testPurchaseFlow() {
  const baseUrl = 'http://localhost:3000';

  console.log('🧪 Testing Purchase Flow...\n');

  try {
    // Test 1: Get available plans
    console.log('1️⃣ Testing GET /api/plans/purchase (without auth)...');
    const plansResponse = await fetch(`${baseUrl}/api/plans/purchase`);
    const plansData = await plansResponse.json();

    if (plansResponse.status === 401) {
      console.log('✅ Correctly requires authentication');
    } else {
      console.log('❌ Should require authentication');
      console.log('Response:', plansData);
    }

    // Test 2: Test purchase without auth
    console.log('\n2️⃣ Testing POST /api/plans/purchase (without auth)...');
    const purchaseResponse = await fetch(`${baseUrl}/api/plans/purchase`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        planName: 'starter',
        billingCycle: 'monthly'
      }),
    });
    const purchaseData = await purchaseResponse.json();

    if (purchaseResponse.status === 401) {
      console.log('✅ Correctly requires authentication');
      console.log('Message:', purchaseData.message);
    } else {
      console.log('❌ Should require authentication');
      console.log('Response:', purchaseData);
    }

    // Test 3: Test invalid plan name
    console.log('\n3️⃣ Testing invalid plan name...');
    const invalidResponse = await fetch(`${baseUrl}/api/plans/purchase`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        planName: 'invalid',
        billingCycle: 'monthly'
      }),
    });
    const invalidData = await invalidResponse.json();

    if (invalidResponse.status === 401) {
      console.log('✅ Correctly requires authentication (expected)');
    } else if (invalidResponse.status === 400) {
      console.log('✅ Correctly validates plan name');
      console.log('Message:', invalidData.message);
    } else {
      console.log('❌ Unexpected response');
      console.log('Response:', invalidData);
    }

    console.log('\n🎉 Purchase flow API tests completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Sign in to test authenticated purchase flow');
    console.log('2. Try purchasing a plan from the pricing page');
    console.log('3. Test with existing subscription');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testPurchaseFlow();
