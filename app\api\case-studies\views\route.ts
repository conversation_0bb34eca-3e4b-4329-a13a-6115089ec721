import { NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { db } from '@/lib/db/drizzle';
import { caseStudies } from '@/lib/db/schema';
import { eq, sql } from 'drizzle-orm';

export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    if (session.user.role !== 'member') {
      return new NextResponse(`Forbidden: Only member's view is counted`, {
        status: 403,
      });
    }

    const { caseStudyId } = await req.json();

    await db
      .update(caseStudies)
      .set({ views: sql`${caseStudies.views} + 1` })
      .where(eq(caseStudies.id, caseStudyId));

    return NextResponse.json(
      {
        success: true,
        message: 'Views incremented successfully',
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating views:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}
