import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies, caseStudyIcons } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { and, or, like, isNull, sql } from 'drizzle-orm';

// Type for case study with icons
type CaseStudyWithIcons = typeof caseStudies.$inferSelect & {
  icons: (typeof caseStudyIcons.$inferSelect)[];
};

// Format URL for images
function formatUrl(url: string | null): string | null {
  if (!url) return null;

  // If URL is already a full URL, return it
  if (url.startsWith('http')) return url;

  // Otherwise, it's a relative path, prepend the base URL
  return `${process.env.NEXT_PUBLIC_BASE_URL || ''}${url}`;
}

export async function GET(req: Request) {
  try {
    const session = await getSession();
    console.log('Search API - Session:', JSON.stringify(session, null, 2));

    if (!session?.user?.id) {
      console.log('Search API - Unauthorized: No user ID in session');
      return new NextResponse('Unauthorized', { status: 401 });
    }

    console.log(
      `Search API - User ID: ${session.user.id}, Role: ${
        session.user.role || 'unknown'
      }`
    );

    const url = new URL(req.url);
    const { searchParams } = url;

    // Get search query
    const query = searchParams.get('q') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');

    console.log(`Searching for: "${query}" (Page ${page}, Size ${pageSize})`);

    if (!query) {
      // If no query is provided, return empty results
      return NextResponse.json({
        caseStudies: [],
        pagination: {
          total: 0,
          page,
          pageSize,
          totalPages: 0,
        },
      });
    }

    // Create search conditions for multiple fields
    const searchConditions = or(
      // Exact match (case insensitive)
      sql`LOWER(${caseStudies.useCaseTitle}) LIKE LOWER(${'%' + query + '%'})`,
      sql`LOWER(${caseStudies.industry}) LIKE LOWER(${'%' + query + '%'})`,
      sql`LOWER(${caseStudies.role}) LIKE LOWER(${'%' + query + '%'})`,

      ...(query.includes(' ')
        ? query
            .toLowerCase()
            .split(/\s+/)
            .filter((word) => word.length > 0)
            .map((word) =>
              or(
                sql`LOWER(${caseStudies.useCaseTitle}) LIKE LOWER(${
                  '%' + word + '%'
                })`,
                sql`LOWER(${caseStudies.industry}) LIKE LOWER(${
                  '%' + word + '%'
                })`,
                sql`LOWER(${caseStudies.role}) LIKE LOWER(${'%' + word + '%'})`
              )
            )
        : [])
    );

    // Get total count for pagination
    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(caseStudies)
      .where(searchConditions)
      .then((result) => result[0].count);

    const searchResults = (await db.query.caseStudies.findMany({
      columns: {
        id: true,
        useCaseTitle: true,
        industry: true,
        role: true,
        featureImageUrl: true,
        previewImageUrl: true,
        introductionText: true,
        marketIntelligenceData: true,
        marketMetricsData: true,
      },
      where: searchConditions,
      with: {
        icons: {
          columns: {
            iconUrl: true,
            order: true,
          },
          orderBy: (icons, { asc }) => [asc(icons.order)],
        },
      },
      limit: pageSize,
      offset: (page - 1) * pageSize,
      orderBy: (caseStudies, { desc }) => [desc(caseStudies.createdAt)],
    })) as CaseStudyWithIcons[];

    // Format URLs for all case studies
    const formattedCaseStudies = searchResults.map((caseStudy) => ({
      ...caseStudy,
      featureImageUrl: formatUrl(caseStudy.featureImageUrl as string | null),
      previewImageUrl: formatUrl(caseStudy.previewImageUrl as string | null),
      icons: caseStudy.icons.map((icon) => ({
        ...icon,
        iconUrl: formatUrl(icon.iconUrl as string | null),
      })),
    }));

    return NextResponse.json({
      caseStudies: formattedCaseStudies,
      pagination: {
        total: totalCount,
        page,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize),
      },
      query,
    });
  } catch (error: any) {
    console.error('Error searching case studies:', error);
    console.error(
      'Error details:',
      JSON.stringify(error, Object.getOwnPropertyNames(error), 2)
    );

    // Return a more detailed error message in development
    const errorMessage =
      process.env.NODE_ENV === 'development'
        ? `Internal Error: ${error.message}`
        : 'Internal Error';

    return new NextResponse(errorMessage, { status: 500 });
  }
}
