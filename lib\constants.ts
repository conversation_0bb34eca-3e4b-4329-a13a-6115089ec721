export const faqs = [
    {
      question: "What is this platform designed to do?",
      answer: "Our platform is built to help enterprises bridge the gap between AI ambition and execution. We provide curated, high-impact AI use cases, real-time market signals, and strategic insights to help you explore, prioritize, and scale AI initiatives confidently."
    },
    {
      question: "Who is this platform for?",
      answer: "It’s designed for CXOs, innovation leaders, and transformation teams who want to drive business outcomes using AI — without getting lost in the noise. Whether you're new to AI or already piloting solutions, this platform meets you where you are in your journey."
    },
    {
      question: "How does “Trending Now” work?",
      answer: "We analyze real-time user behavior across the platform to surface the most visited and explored use cases. This gives you insight into what your peers and competitors are prioritizing right now."
    },
    {
      question: "What does the “Market Signals” section offer?",
      answer: "We map external market intelligence (like industry trends, adoption stats, and funding flows) to specific use cases — helping you validate timing, demand, and competitive benchmarks before you act"
    },
    {
      question: "How often is the content or data updated?",
      answer: "Trending insights and market signals are updated in near real-time. Our use case library is continuously refreshed based on evolving tech maturity and enterprise adoption."
    }
  ];

export const testimonials = [
    {
      name: "One of India’s largest natural resources & mining conglomerates.",
      role: "CIO",
      testimonial: "“Network Science has been a transformative partner, leveraging AR, VR, and computer vision to enhance our efficiency and innovation. Their tailored solutions addressed critical challenges and delivered tangible value.”",
      avatar: "/user.png"
    },
    {
      name: "India’s largest travel & lifestyle rewards program.",
      role: "CEO",
      testimonial: "“Network Science went beyond the SOW, deeply understanding our systems and future needs without cost escalations. Their process-driven, collaborative approach earned my trust for future deep tech projects.”",
      avatar: "/user.png"
    },
    {
      name: "Mumbai’s prominent interior designer",
      role: "Founder",
      testimonial: "“Working with Network Science was a pleasure—their dedication, expertise, and professionalism exceeded expectations. They went far beyond scope, proving themselves invaluable partners.”",
      avatar: "/user.png"
    },
    // {
    //   name: "Emma R.",
    //   role: "Product Owner",
    //   testimonial: "It helped us prioritize initiatives that aligned with both market momentum and internal goals.",
    //   avatar: "/user.png"
    // },
    // {
    //   name: "Alex M.",
    //   role: "Tech Lead",
    //   testimonial: "Integration was seamless and the results were immediate. Our team's efficiency improved by 40%.",
    //   avatar: "/user.png"
    // },
    // {
    //   name: "Lisa W.",
    //   role: "Operations Manager",
    //   testimonial: "The automated workflows have transformed how we handle our daily operations. Exceptional tool!",
    //   avatar: "/user.png"
    // },
    // {
    //   name: "James H.",
    //   role: "CTO",
    //   testimonial: "As a CTO, I've seen many tools, but this one stands out. The ROI was evident within weeks.",
    //   avatar: "/user.png"
    // },
    // {
    //   name: "Rachel B.",
    //   role: "UX Designer",
    //   testimonial: "The interface is intuitive and the AI suggestions for design improvements are spot-on.",
    //   avatar: "/user.png"
    // },
    // {
    //   name: "Tom K.",
    //   role: "Business Analyst",
    //   testimonial: "The predictive analytics have helped us stay ahead of market trends consistently.",
    //   avatar: "/user.png"
    // }
  ];

  
