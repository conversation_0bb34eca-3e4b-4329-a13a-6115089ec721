import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { syncTeamSubscription, validateSubscriptionIntegrity } from '@/lib/services/subscription-sync';

/**
 * POST /api/subscriptions/sync - Force sync current user's subscription
 * Industry standard: Idempotent operation with proper error handling
 */
export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Unauthorized',
          message: 'Authentication required'
        }, 
        { status: 401 }
      );
    }

    // Get user's team
    const team = await getTeamForUser(user.id);
    if (!team) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Team not found',
          message: 'No team associated with user'
        }, 
        { status: 404 }
      );
    }

    console.log(`[Sync API] User ${user.email} requesting sync for team ${team.id}`);
    
    // Perform the sync operation
    const result = await syncTeamSubscription(team.id);
    
    if (result.success) {
      // Also validate integrity after sync
      const validation = await validateSubscriptionIntegrity(team.id);
      
      return NextResponse.json({
        success: true,
        updated: result.updated,
        planName: result.planName,
        subscriptionStatus: result.subscriptionStatus,
        timestamp: result.timestamp,
        message: result.updated 
          ? 'Subscription data updated successfully' 
          : 'Subscription data is already up to date',
        validation: {
          isValid: validation.isValid,
          issues: validation.issues,
          recommendations: validation.recommendations,
        }
      });
    } else {
      console.error(`[Sync API] Sync failed for team ${team.id}:`, result.error);
      
      return NextResponse.json({
        success: false,
        error: result.error,
        timestamp: result.timestamp,
        message: 'Failed to sync subscription data'
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('[Sync API] Unexpected error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Internal server error during sync',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * GET /api/subscriptions/sync - Get sync status and validation
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }

    const team = await getTeamForUser(user.id);
    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' }, 
        { status: 404 }
      );
    }

    // Get validation status without syncing
    const validation = await validateSubscriptionIntegrity(team.id);
    
    return NextResponse.json({
      teamId: team.id,
      currentPlan: team.planName,
      subscriptionStatus: team.subscriptionStatus,
      stripeCustomerId: team.stripeCustomerId,
      stripeSubscriptionId: team.stripeSubscriptionId,
      lastUpdated: team.updatedAt,
      validation,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('[Sync API] Error getting sync status:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to get sync status'
    }, { status: 500 });
  }
}
