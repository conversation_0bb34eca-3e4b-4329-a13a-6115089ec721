import { AnimatedGridPattern } from '@/components/magicui/animated-grid-pattern';
import { Badge } from '@/components/ui/badge';

interface Feature {
  title: string;
  description: string;
  icon: string;
  alt: string;
}

const features: Feature[] = [
  {
    title: 'Use Case Library',
    description:
      'Explore 400+ actionable AI use cases designed to solve business challenges and drive innovation across industries',
    icon: '/use-case-library.png',
    alt: 'Use Case Library',
  },
  {
    title: 'Competitive Insights',
    description:
      'Gain a deeper understanding of how your competitors are leveraging AI to stay ahead in your industry',
    icon: '/competitive-insight.png',
    alt: 'Competitive Insights',
  },
  {
    title: 'Industry Spotlights',
    description:
      'Discover sector-specific AI trends and emerging opportunities shaping your market',
    icon: '/industry-spotlight.png',
    alt: 'Industry Spotlights',
  },
  {
    title: 'Trends',
    description:
      'Track the latest AI adoption trends, market growth and innovation drivers across regions and industries',
    icon: '/trends.png',
    alt: 'Trends',
  },
  {
    title: 'AI Roadmap',
    description:
      'Plan your AI journey with a step-by-step guide to implementing and scaling AI solutions effectively',
    icon: '/ai-roadmap.png',
    alt: 'AI Roadmap',
  },
  {
    title: 'Demystify AI',
    description:
      'Break down complex AI concepts into simple, actionable insights to help you make informed decisions',
    icon: '/demystify-ai.png',
    alt: 'Demystify AI',
  },
];

interface FeatureCardProps {
  feature: Feature;
}

function FeatureCard({ feature }: FeatureCardProps) {
  return (
    <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#f4f4f4] rounded-[20px] p-2  hover:border-[#3384FF]/20 transition-all duration-300 group'>
      <div className='aspect-[4/2.5] bg-[#EAEFF4] rounded-t-2xl flex items-center justify-center mb-6'>
        <div className='flex items-center bg-transparent justify-center '>
          <img src={feature.icon} alt={feature.alt} className='w-30 h-30' />
        </div>
      </div>

      <div className='px-4 pb-4'>
        <h3 className='text-xl font-semibold mb-3 text-gray-900'>
          {feature.title}
        </h3>
        <p className='text-gray-600 leading-relaxed text-sm'>
          {feature.description}
        </p>
      </div>
    </div>
  );
}

export default function Features() {
  return (
    <section className='relative w-full pt-12 pb-0 md:pb-32 overflow-hidden bg-[#F8FAFC]'>
      {/* Animated Grid Background */}
      <div className='absolute inset-0'>
        <AnimatedGridPattern
          width={60}
          height={60}
          className='text-gray-200/80 md:hidden'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={10}
          x={0}
          y={0}
        />
        <AnimatedGridPattern
          width={150}
          height={150}
          className='text-gray-200/80 hidden md:block'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={20}
          x={0}
          y={0}
        />
      </div>
      {/* Updated Radial Gradient */}
      <div className='absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0)_0%,rgba(255,255,255,0.2)_25%,rgba(255,255,255,0.9)_50%,rgba(255,255,255,1)_75%)] pointer-events-none'></div>

      <div className='relative z-10 container mx-auto px-4 md:px-6 lg:px-8'>
        {/* Section Header */}
        <div className='text-center max-w-3xl mx-auto mb-10 md:mb-24'>
          <div className='inline-block mb-3'>
            <Badge
              variant='secondary'
              className='px-3 md:px-4 py-1.5 text-sm bg-white/95 backdrop-blur-sm border-none shadow-md text-gray-600'
            >
              Why Choose Turin OS?
            </Badge>
          </div>
          <h2 className='text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-6 px-4 md:px-0 text-black'>
            <span className='text-blue-500'>Explore,</span> Strategize,Transform
          </h2>
          <p className='text-lg md:text-xl text-gray-600 px-4 md:px-0'>
            Your AI journey here - features built to guide you
            <br className='hidden md:block' />
            from discovery to execution
          </p>
        </div>

        {/* Feature Cards Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8'>
          {features.map((feature, index) => (
            <FeatureCard key={index} feature={feature} />
          ))}
        </div>
      </div>
    </section>
  );
}
