'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';
import { isCloudinaryUrl } from '@/lib/utils/url-validator';

interface CloudinaryImageProps {
  src: string | null | undefined;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackSrc?: string;
}

/**
 * Optimizes a Cloudinary URL for the specified width
 */
function getResponsiveImageUrl(url: string, width: number): string {
  if (!url) return '';

  // Ensure URL is using HTTPS
  if (url.startsWith('http://')) {
    url = url.replace('http://', 'https://');
  }

  if (!isCloudinaryUrl(url)) return url;

  // Extract parts of the Cloudinary URL
  // Format: https://res.cloudinary.com/cloud-name/image/upload/v1234567890/folder/public-id.ext
  const parts = url.split('/upload/');
  if (parts.length !== 2) return url;

  // Insert transformation parameters
  return `${parts[0]}/upload/c_fill,w_${width},q_auto,f_auto/${parts[1]}`;
}

export function CloudinaryImage({
  src,
  alt,
  width = 800,
  height = 600,
  className,
  fallbackSrc = '/placeholder-image.png',
}: CloudinaryImageProps) {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);

  // Get optimized Cloudinary URL if applicable
  const imageUrl = src
    ? (isCloudinaryUrl(src) ? getResponsiveImageUrl(src, width) : src)
    : fallbackSrc;

  // Log the image URL for debugging
  React.useEffect(() => {
    if (src) {
      console.log(`CloudinaryImage: Loading image from ${src}`);
      console.log(`CloudinaryImage: Optimized URL: ${imageUrl}`);
    }
  }, [src, imageUrl]);

  const handleLoad = () => {
    console.log(`CloudinaryImage: Successfully loaded image: ${imageUrl}`);
    setIsLoading(false);
  };

  const handleError = () => {
    console.error(`CloudinaryImage: Failed to load image: ${imageUrl}`);
    console.error(`CloudinaryImage: Using fallback: ${fallbackSrc}`);
    setIsLoading(false);
    setError(true);
  };

  // If we have an error or no source, show the fallback immediately
  if (error || !src) {
    return (
      <div className="relative">
        <Image
          src={fallbackSrc}
          alt={alt}
          width={width}
          height={height}
          className={className}
        />
      </div>
    );
  }

  return (
    <div className="relative">
      {isLoading && (
        <Skeleton className={className || `w-${width || 'full'} h-${height || 40}`} />
      )}
      <Image
        src={imageUrl}
        alt={alt}
        width={width}
        height={height}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'}`}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  );
}

// For backward compatibility
export const WasabiImage = CloudinaryImage;
