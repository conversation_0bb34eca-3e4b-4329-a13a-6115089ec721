import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { getPlanByName } from '@/lib/db/seeders/pricing-plans';
import { stripe } from '@/lib/payments/stripe';
import { z } from 'zod';

/**
 * Simple plan purchase API
 * Industry best practice: Single responsibility - only handle plan purchases
 */

const purchaseSchema = z.object({
  planName: z.enum(['starter', 'pro']), // Enterprise requires contact
  billingCycle: z.enum(['monthly', 'yearly']).default('monthly')
});

export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized',
          message: 'Please sign in to purchase a plan'
        },
        { status: 401 }
      );
    }

    // Get user's team
    const team = await getTeamForUser(user.id);
    if (!team) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'No team found',
          message: 'Unable to find your team. Please contact support.'
        },
        { status: 400 }
      );
    }

    // Check if user already has an active subscription
    if (team.subscriptionStatus === 'active' || team.subscriptionStatus === 'trialing') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Active subscription exists',
          message: 'You already have an active subscription. Please visit the billing page to manage it.',
          redirectTo: '/dashboard/billing'
        },
        { status: 400 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = purchaseSchema.parse(body);
    const { planName, billingCycle } = validatedData;

    // Get plan from database
    const plan = await getPlanByName(planName);
    if (!plan) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Plan not found',
          message: `The ${planName} plan is not available`
        },
        { status: 404 }
      );
    }

    // Get the appropriate price ID
    const priceId = billingCycle === 'yearly' 
      ? plan.stripePriceIdYearly 
      : plan.stripePriceIdMonthly;

    if (!priceId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Price not available',
          message: `${billingCycle} billing is not available for the ${planName} plan`
        },
        { status: 400 }
      );
    }

    // Create Stripe customer if needed
    let customerId = team.stripeCustomerId;
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name || user.email,
        metadata: {
          teamId: team.id.toString(),
          userId: user.id.toString()
        }
      });
      customerId = customer.id;
    }

    // Create checkout session
    const sessionParams: any = {
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'subscription',
      success_url: `${request.nextUrl.origin}/dashboard/billing?purchase_success=true`,
      cancel_url: `${request.nextUrl.origin}/pricing?purchase_cancelled=true`,
      customer: customerId,
      client_reference_id: team.id.toString(),
      allow_promotion_codes: true,
      currency: 'usd',
      metadata: {
        planName,
        billingCycle,
        teamId: team.id.toString(),
        userId: user.id.toString()
      }
    };

    // Add trial period for starter plan
    if (planName === 'starter') {
      sessionParams.subscription_data = {
        trial_period_days: 14
      };
    }

    const session = await stripe.checkout.sessions.create(sessionParams);

    if (!session.url) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Checkout session creation failed',
          message: 'Unable to create payment session. Please try again.'
        },
        { status: 500 }
      );
    }

    console.log(`[Purchase API] Created checkout session for user ${user.email}, plan: ${planName}, billing: ${billingCycle}`);

    return NextResponse.json({
      success: true,
      checkoutUrl: session.url,
      sessionId: session.id,
      planName,
      billingCycle,
      message: 'Redirecting to checkout...'
    });

  } catch (error: any) {
    console.error('[Purchase API] Error:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request',
          message: 'Please check your request and try again.',
          details: error.errors
        },
        { status: 400 }
      );
    }

    // Handle Stripe errors
    if (error.type && error.type.startsWith('Stripe')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Payment processing error',
          message: 'Unable to process payment. Please try again or contact support.'
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'An unexpected error occurred. Please try again.'
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to retrieve available plans for purchase
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get plans from database
    const { getStaticPricingPlans } = await import('@/lib/db/seeders/pricing-plans');
    const plans = await getStaticPricingPlans();

    // Filter out enterprise plan for direct purchase
    const purchasablePlans = plans.filter(plan => plan.name !== 'enterprise');

    return NextResponse.json({
      success: true,
      plans: purchasablePlans.map(plan => ({
        name: plan.name,
        displayName: plan.displayName,
        description: plan.description,
        priceMonthly: plan.priceMonthly,
        priceYearly: plan.priceYearly,
        features: plan.features,
        maxUsers: plan.maxUsers
      }))
    });

  } catch (error: any) {
    console.error('[Purchase API] Error fetching plans:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch plans',
        message: 'Unable to load pricing plans. Please refresh the page.'
      },
      { status: 500 }
    );
  }
}
