'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, RefreshCw, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

interface SyncTestData {
  teamData?: any;
  subscriptionData?: any;
  stripeData?: any;
  syncResult?: any;
  lastSync?: string;
}

/**
 * Test page for subscription synchronization
 * This page helps debug sync issues between Stripe and local database
 */
export default function TestSyncPage() {
  const [testData, setTestData] = useState<SyncTestData>({});
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Fetch current data
  const fetchCurrentData = async () => {
    setIsLoading(true);
    try {
      // Get team data
      const teamResponse = await fetch('/api/user/team');
      const teamData = teamResponse.ok ? await teamResponse.json() : null;

      // Get subscription data
      const subscriptionResponse = await fetch('/api/subscriptions');
      const subscriptionData = subscriptionResponse.ok ? await subscriptionResponse.json() : null;

      setTestData({
        teamData,
        subscriptionData,
        lastSync: new Date().toISOString(),
      });

    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch current data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Test sync functionality
  const testSync = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/subscriptions/sync', {
        method: 'POST',
      });

      const syncResult = await response.json();

      setTestData(prev => ({
        ...prev,
        syncResult,
        lastSync: new Date().toISOString(),
      }));

      if (syncResult.success) {
        toast({
          title: 'Sync Successful',
          description: syncResult.message,
          variant: 'default',
        });

        // Refresh data after sync
        setTimeout(() => fetchCurrentData(), 1000);
      } else {
        toast({
          title: 'Sync Failed',
          description: syncResult.error,
          variant: 'destructive',
        });
      }

    } catch (error) {
      console.error('Error testing sync:', error);
      toast({
        title: 'Error',
        description: 'Failed to test sync functionality',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchCurrentData();
  }, []);

  const renderDataCard = (title: string, data: any, description?: string) => (
    <Card className="dark:bg-black dark:border-white/20">
      <CardHeader>
        <CardTitle className="dark:text-white">{title}</CardTitle>
        {description && (
          <CardDescription className="dark:text-white/70">
            {description}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent>
        <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-auto max-h-64">
          {JSON.stringify(data, null, 2)}
        </pre>
      </CardContent>
    </Card>
  );

  const renderSyncStatus = () => {
    if (!testData.syncResult) return null;

    const { success, updated, planName, subscriptionStatus, error } = testData.syncResult;

    return (
      <Card className="dark:bg-black dark:border-white/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 dark:text-white">
            {success ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <XCircle className="h-5 w-5 text-red-500" />
            )}
            Sync Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-muted-foreground dark:text-white/70">Success:</span>
              <Badge variant={success ? 'default' : 'destructive'} className="ml-2">
                {success ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground dark:text-white/70">Updated:</span>
              <Badge variant={updated ? 'default' : 'secondary'} className="ml-2">
                {updated ? 'Yes' : 'No'}
              </Badge>
            </div>
          </div>

          {planName && (
            <div>
              <span className="text-sm font-medium text-muted-foreground dark:text-white/70">Plan Name:</span>
              <span className="ml-2 dark:text-white">{planName}</span>
            </div>
          )}

          {subscriptionStatus && (
            <div>
              <span className="text-sm font-medium text-muted-foreground dark:text-white/70">Status:</span>
              <span className="ml-2 dark:text-white">{subscriptionStatus}</span>
            </div>
          )}

          {error && (
            <div className="text-red-600 dark:text-red-400">
              <span className="text-sm font-medium">Error:</span>
              <span className="ml-2">{error}</span>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderComparisonTable = () => {
    const teamPlan = testData.teamData?.planName;
    const teamStatus = testData.teamData?.subscriptionStatus;
    const stripePlan = testData.subscriptionData?.stripeSubscription?.items?.data?.[0]?.price?.product?.name;
    const stripeStatus = testData.subscriptionData?.stripeSubscription?.status;

    return (
      <Card className="dark:bg-black dark:border-white/20">
        <CardHeader>
          <CardTitle className="dark:text-white">Data Comparison</CardTitle>
          <CardDescription className="dark:text-white/70">
            Compare local database vs Stripe data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b dark:border-white/20">
                  <th className="text-left p-2 dark:text-white">Field</th>
                  <th className="text-left p-2 dark:text-white">Local Database</th>
                  <th className="text-left p-2 dark:text-white">Stripe</th>
                  <th className="text-left p-2 dark:text-white">Match</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b dark:border-white/20">
                  <td className="p-2 font-medium dark:text-white">Plan Name</td>
                  <td className="p-2 dark:text-white">{teamPlan || 'N/A'}</td>
                  <td className="p-2 dark:text-white">{stripePlan || 'N/A'}</td>
                  <td className="p-2">
                    {teamPlan && stripePlan ? (
                      teamPlan.toLowerCase().includes(stripePlan.toLowerCase()) ||
                      stripePlan.toLowerCase().includes(teamPlan.toLowerCase()) ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="p-2 font-medium dark:text-white">Status</td>
                  <td className="p-2 dark:text-white">{teamStatus || 'N/A'}</td>
                  <td className="p-2 dark:text-white">{stripeStatus || 'N/A'}</td>
                  <td className="p-2">
                    {teamStatus && stripeStatus ? (
                      teamStatus === stripeStatus ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold dark:text-white">Subscription Sync Test</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchCurrentData}
            disabled={isLoading}
            className="dark:bg-black dark:text-white dark:hover:bg-white/10 dark:border-white/20"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh Data
          </Button>
          <Button
            onClick={testSync}
            disabled={isLoading}
            className="dark:bg-white dark:text-black dark:hover:bg-white/90"
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Test Sync
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Sync Status */}
        {renderSyncStatus()}

        {/* Data Comparison */}
        {renderComparisonTable()}

        {/* Raw Data */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {renderDataCard(
            'Team Data',
            testData.teamData,
            'Local database team information'
          )}
          {renderDataCard(
            'Subscription Data',
            testData.subscriptionData,
            'Combined subscription and Stripe data'
          )}
        </div>

        {/* Last Sync Time */}
        {testData.lastSync && (
          <div className="text-center text-sm text-muted-foreground dark:text-white/70">
            Last updated: {new Date(testData.lastSync).toLocaleString()}
          </div>
        )}
      </div>
    </div>
  );
}
