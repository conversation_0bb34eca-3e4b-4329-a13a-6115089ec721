import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { teams, teamMembers, invitations, users } from '@/lib/db/schema';
import { getUser } from '@/lib/db/server-queries';
import { eq, and } from 'drizzle-orm';

// GET /api/team - Get team data with members and invitations
export async function GET() {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's team
    const userTeamMember = await db.query.teamMembers.findFirst({
      where: eq(teamMembers.userId, user.id),
      with: {
        team: {
          with: {
            teamMembers: {
              with: {
                user: {
                  columns: {
                    id: true,
                    name: true,
                    email: true,
                    role: true,
                  },
                },
              },
            },
            invitations: {
              where: eq(invitations.status, 'pending'),
            },
          },
        },
      },
    });

    if (!userTeamMember?.team) {
      return NextResponse.json({ error: 'Team not found' }, { status: 404 });
    }

    // Get inviter details for each invitation
    const invitationsWithInviters = await Promise.all(
      userTeamMember.team.invitations.map(async (invitation) => {
        const inviter = await db.query.users.findFirst({
          where: eq(users.id, invitation.invitedBy),
          columns: {
            id: true,
            name: true,
            email: true,
          },
        });

        return {
          ...invitation,
          inviter,
        };
      })
    );

    return NextResponse.json({
      ...userTeamMember.team,
      invitations: invitationsWithInviters,
    });
  } catch (error) {
    console.error('Error fetching team data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/team - Update team details
export async function PUT(req: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a team owner
    const userTeamMember = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, user.id),
        eq(teamMembers.role, 'owner')
      ),
      with: {
        team: true,
      },
    });

    if (!userTeamMember?.team) {
      return NextResponse.json({ error: 'Team not found or you are not an owner' }, { status: 403 });
    }

    const body = await req.json();
    const { name } = body;

    if (!name) {
      return NextResponse.json({ error: 'Team name is required' }, { status: 400 });
    }

    // Update team
    const updatedTeam = await db
      .update(teams)
      .set({ name, updatedAt: new Date() })
      .where(eq(teams.id, userTeamMember.team.id))
      .returning();

    return NextResponse.json(updatedTeam[0]);
  } catch (error) {
    console.error('Error updating team:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
