import { db } from '@/lib/db/drizzle';
import { plans } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

/**
 * Database utilities for pricing plans
 * Industry best practice: Centralized plan data access
 */

/**
 * Get all active pricing plans from database
 */
export async function getActivePricingPlans() {
  try {
    const pricingPlans = await db.query.plans.findMany({
      where: eq(plans.isActive, true),
      orderBy: (plans, { asc }) => [asc(plans.priceMonthly)]
    });
    
    return pricingPlans;
  } catch (error) {
    console.error('Error fetching pricing plans:', error);
    throw error;
  }
}

/**
 * Get plan by name
 */
export async function getPlanByName(planName: string) {
  try {
    const plan = await db.query.plans.findFirst({
      where: eq(plans.name, planName.toLowerCase())
    });
    
    return plan;
  } catch (error) {
    console.error(`Error fetching plan ${planName}:`, error);
    throw error;
  }
}

/**
 * Get plan by ID
 */
export async function getPlanById(planId: number) {
  try {
    const plan = await db.query.plans.findFirst({
      where: eq(plans.id, planId)
    });
    
    return plan;
  } catch (error) {
    console.error(`Error fetching plan with ID ${planId}:`, error);
    throw error;
  }
}
