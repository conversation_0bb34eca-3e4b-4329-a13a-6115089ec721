import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { checkPlanLimit } from '@/lib/auth/subscription-actions';
import { z } from 'zod';

// Schema for limit check request
const limitCheckSchema = z.object({
  limitType: z.enum(['maxUsers', 'maxCaseStudies', 'maxStorage']),
  currentCount: z.number().min(0),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = limitCheckSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { limitType, currentCount } = validationResult.data;

    // Check plan limit
    const result = await checkPlanLimit(limitType, currentCount);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error checking plan limit:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
