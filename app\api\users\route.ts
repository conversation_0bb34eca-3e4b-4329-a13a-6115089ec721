import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { users, userSubscriptions } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { sql } from 'drizzle-orm';

export async function GET(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    if (
      session.user.role !== 'owner' &&
      session.user.role !== 'teamMember' &&
      session.user.role !== 'viewer'
    ) {
      return new NextResponse(
        'Forbidden: Only owners, team members, and viewers can read users data',
        { status: 403 }
      );
    }

    const url = new URL(req.url);
    const { searchParams } = url;
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '20');

    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(users)
      .then((result) => result[0].count);

    const usersData = await db.query.users.findMany({
      columns: {
        id: true,
        name: true,
        email: true,
        industry: true,
        role: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
      },
      with: {
        subscriptions: true,
      },
      limit: pageSize,
      offset: (page - 1) * pageSize,
      orderBy: (users, { desc }) => [desc(users.createdAt)],
    });

    return NextResponse.json({
      succeed: true,
      message: 'Users data fetched successfully',
      users: [...usersData],
      pagination: {
        page,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize),
        totalCount,
      },
    });
  } catch (error) {
    console.error('Error fetching users data:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
