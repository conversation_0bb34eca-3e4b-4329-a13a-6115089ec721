/**
 * URL validation utilities
 */

/**
 * Validates if a string is a valid URL
 * @param url The URL to validate
 * @returns True if the URL is valid, false otherwise
 */
export function isValidUrl(url: string): boolean {
  if (!url) return false;

  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * Checks if a URL is a Cloudinary URL
 * @param url The URL to check
 * @returns True if the URL is a Cloudinary URL, false otherwise
 */
export function isCloudinaryUrl(url: string): boolean {
  if (!url) return false;

  return url.includes('res.cloudinary.com/') ||
         url.includes('cloudinary.com/') ||
         url.includes('cloudinary.com%2F'); // URL encoded version
}
