'use client';

import { useState, useEffect } from 'react';
import { isCloudinaryUrl, getResponsiveImageUrl } from '@/lib/cloudinary';

/**
 * Gets an optimized image URL for display
 * @param url The original image URL
 * @param width The desired width
 * @returns The optimized image URL
 */
export function getOptimizedImageUrl(url: string | null | undefined, width: number = 800): string {
  if (!url) return '/placeholder-image.png';

  // If it's a Cloudinary URL, optimize it
  if (isCloudinaryUrl(url)) {
    return getResponsiveImageUrl(url, width);
  }

  // Otherwise, return the original URL
  return url;
}

/**
 * React hook to get an optimized image URL
 * @param url The original image URL
 * @param width The desired width
 * @returns An object with the optimized image URL
 */
export function useOptimizedImageUrl(url: string | null | undefined, width: number = 800) {
  const [imageUrl, setImageUrl] = useState<string>(url || '/placeholder-image.png');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!url) {
      setImageUrl('/placeholder-image.png');
      return;
    }

    // Brief loading state for consistency with previous implementation
    setIsLoading(true);
    try {
      setImageUrl(getOptimizedImageUrl(url, width));
    } catch (err) {
      console.error('Error optimizing image URL:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setImageUrl(url); // Fall back to the original URL
    } finally {
      setIsLoading(false);
    }
  }, [url, width]);

  return { imageUrl, isLoading, error };
}

// For backward compatibility
export const getImageUrl = getOptimizedImageUrl;
export const useImageUrl = useOptimizedImageUrl;
