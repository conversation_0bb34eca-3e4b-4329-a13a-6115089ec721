import { db } from '@/lib/db/drizzle';
import { plans } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

/**
 * Static pricing plans configuration
 * Industry best practice: Store pricing in database for consistency
 */
export const STATIC_PRICING_PLANS = [
  {
    name: 'starter',
    displayName: 'Starter',
    description: 'Perfect for individuals getting started with AI - 14 day free trial',
    priceMonthly: 1200, // $12.00 in cents
    priceYearly: null,
    stripePriceIdMonthly: process.env.STRIPE_STARTER_PRICE_ID || 'price_starter_monthly',
    stripePriceIdYearly: null,
    stripeProductId: process.env.STRIPE_STARTER_PRODUCT_ID || 'prod_starter',
    features: {
      trial: {
        enabled: true,
        days: 14,
        description: '14-day free trial'
      },
      caseStudies: {
        limit: 10,
        description: 'Access to 10 curated AI use cases'
      },
      users: {
        limit: 1,
        description: '1 registered user'
      },
      features: [
        { name: '14-day free trial', included: true },
        { name: 'Access to 10 curated AI use cases', included: true },
        { name: 'Preview of Market Intelligence Unit (MIU)', included: true },
        { name: '1 registered user', included: true },
        { name: 'Unlimited use case access', included: false },
        { name: 'Full MIU access', included: false },
        { name: 'Roadmap tools & prioritization framework', included: false },
        { name: 'Team collaboration features', included: false },
        { name: 'Dedicated account support', included: false }
      ]
    },
    maxUsers: 1,
    isActive: true,
    isPublic: true
  },
  {
    name: 'pro',
    displayName: 'Pro',
    description: 'For teams ready to scale with AI - unlimited access',
    priceMonthly: 2900, // $29.00 in cents
    priceYearly: 29000, // $290.00 in cents (10 months pricing)
    stripePriceIdMonthly: process.env.STRIPE_PRO_PRICE_ID_MONTHLY || 'price_pro_monthly',
    stripePriceIdYearly: process.env.STRIPE_PRO_PRICE_ID_YEARLY || 'price_pro_yearly',
    stripeProductId: process.env.STRIPE_PRO_PRODUCT_ID || 'prod_pro',
    features: {
      trial: {
        enabled: false,
        days: 0,
        description: 'No trial period'
      },
      caseStudies: {
        limit: -1, // unlimited
        description: 'Unlimited case study access'
      },
      users: {
        limit: 3,
        description: 'Up to 3 users'
      },
      features: [
        { name: 'Unlimited case study access', included: true },
        { name: 'Full MIU access', included: true },
        { name: 'Roadmap tools & prioritization framework', included: true },
        { name: 'Team collaboration features', included: true },
        { name: 'Dedicated account support', included: true },
        { name: 'Up to 3 users', included: true },
        { name: 'Priority email support', included: true }
      ]
    },
    maxUsers: 3,
    isActive: true,
    isPublic: true
  },
  {
    name: 'enterprise',
    displayName: 'Enterprise',
    description: 'For large organizations - contact us for pricing',
    priceMonthly: null, // Contact for pricing
    priceYearly: null,
    stripePriceIdMonthly: null,
    stripePriceIdYearly: null,
    stripeProductId: process.env.STRIPE_ENTERPRISE_PRODUCT_ID || 'prod_enterprise',
    features: {
      trial: {
        enabled: false,
        days: 0,
        description: 'Custom trial available'
      },
      caseStudies: {
        limit: -1, // unlimited
        description: 'Unlimited case study access'
      },
      users: {
        limit: 7,
        description: 'Up to 7 CXO-level users'
      },
      features: [
        { name: 'Everything in Pro', included: true },
        { name: 'Up to 7 CXO-level users', included: true },
        { name: 'Custom integrations', included: true },
        { name: 'Dedicated account manager', included: true },
        { name: 'SLA guarantees', included: true },
        { name: 'Custom branding', included: true },
        { name: 'SSO integration', included: true }
      ]
    },
    maxUsers: 7,
    isActive: true,
    isPublic: true
  }
] as const;

/**
 * Seed pricing plans into database
 * Industry best practice: Idempotent seeding with upsert logic
 */
export async function seedPricingPlans() {
  console.log('🌱 Seeding pricing plans...');
  
  try {
    for (const planData of STATIC_PRICING_PLANS) {
      // Check if plan already exists
      const existingPlan = await db.query.plans.findFirst({
        where: eq(plans.name, planData.name)
      });

      if (existingPlan) {
        // Update existing plan
        await db.update(plans)
          .set({
            displayName: planData.displayName,
            description: planData.description,
            priceMonthly: planData.priceMonthly,
            priceYearly: planData.priceYearly,
            stripePriceIdMonthly: planData.stripePriceIdMonthly,
            stripePriceIdYearly: planData.stripePriceIdYearly,
            stripeProductId: planData.stripeProductId,
            features: planData.features,
            maxUsers: planData.maxUsers,
            isActive: planData.isActive,
            isPublic: planData.isPublic,
            updatedAt: new Date()
          })
          .where(eq(plans.id, existingPlan.id));
        
        console.log(`✅ Updated plan: ${planData.name}`);
      } else {
        // Insert new plan
        await db.insert(plans).values({
          name: planData.name,
          displayName: planData.displayName,
          description: planData.description,
          priceMonthly: planData.priceMonthly,
          priceYearly: planData.priceYearly,
          stripePriceIdMonthly: planData.stripePriceIdMonthly,
          stripePriceIdYearly: planData.stripePriceIdYearly,
          stripeProductId: planData.stripeProductId,
          features: planData.features,
          maxUsers: planData.maxUsers,
          isActive: planData.isActive,
          isPublic: planData.isPublic
        });
        
        console.log(`✅ Created plan: ${planData.name}`);
      }
    }
    
    console.log('🎉 Pricing plans seeded successfully!');
    return { success: true };
  } catch (error) {
    console.error('❌ Error seeding pricing plans:', error);
    throw error;
  }
}

/**
 * Get static pricing plans from database
 * Industry best practice: Single source of truth from database
 */
export async function getStaticPricingPlans() {
  try {
    const pricingPlans = await db.query.plans.findMany({
      where: eq(plans.isActive, true),
      orderBy: (plans, { asc }) => [asc(plans.priceMonthly)]
    });
    
    return pricingPlans;
  } catch (error) {
    console.error('Error fetching pricing plans:', error);
    throw error;
  }
}

/**
 * Get plan by name
 */
export async function getPlanByName(planName: string) {
  try {
    const plan = await db.query.plans.findFirst({
      where: eq(plans.name, planName.toLowerCase())
    });
    
    return plan;
  } catch (error) {
    console.error(`Error fetching plan ${planName}:`, error);
    throw error;
  }
}
