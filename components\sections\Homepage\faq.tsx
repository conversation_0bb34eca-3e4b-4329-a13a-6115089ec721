import { AnimatedGridPattern } from '@/components/magicui/animated-grid-pattern';
import { Badge } from '@/components/ui/badge';
import { faqs } from '@/lib/constants';
import { Plus } from 'lucide-react';
import { useState } from 'react';

export default function Faq() {
  const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);
  return (
    <section className='relative pb-16 md:py-32 w-full overflow-hidden'>
      {/* Animated Grid Background */}
      <div className='absolute inset-0'>
        <AnimatedGridPattern
          width={60}
          height={60}
          className='text-gray-200/80 md:hidden'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={10}
          x={0}
          y={0}
        />
        <AnimatedGridPattern
          width={150}
          height={150}
          className='text-gray-200/80 hidden md:block'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={20}
          x={0}
          y={0}
        />
      </div>
      {/* Updated Radial Gradient */}
      <div className='absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0)_0%,rgba(255,255,255,0.2)_25%,rgba(255,255,255,0.9)_50%,rgba(255,255,255,1)_75%)] pointer-events-none'></div>

      <div className='relative z-10 container mx-auto px-4 md:px-6 lg:px-8'>
        {/* FAQ Header */}
        <div className='text-center mb-16'>
          <div className='inline-block mb-3'>
            <Badge
              variant='secondary'
              className='px-3 md:px-4 py-1.5 text-sm bg-white/95 backdrop-blur-sm border-none shadow-md text-gray-600'
            >
              Questions? We Have Answers
            </Badge>
          </div>
          <h2 className='text-3xl md:text-5xl font-bold mb-6'>
            Frequently Asked <span className='text-blue-500'>Questions</span>
          </h2>
          <p className='text-base md:text-lg'>
            Find quick answers to some of the most common
            <br />
            questions about Increasy.
          </p>
        </div>

        {/* FAQ Items */}
        <div className='max-w-3xl mx-auto space-y-4'>
          {faqs.map((faq, index) => (
            <div
              key={index}
              className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'
            >
              <button
                className='w-full px-8 py-6 flex items-center justify-between gap-4'
                onClick={() =>
                  setOpenFaqIndex(openFaqIndex === index ? null : index)
                }
              >
                <span className='text-lg font-medium text-left text-gray-900'>
                  {faq.question}
                </span>
                <div
                  className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                    openFaqIndex === index ? 'rotate-45' : ''
                  }`}
                >
                  <Plus className='h-5 w-5 text-white' />
                </div>
              </button>
              <div
                className='overflow-hidden transition-all duration-300 ease-in-out'
                style={{
                  maxHeight: openFaqIndex === index ? '500px' : '0',
                }}
              >
                <div className='px-8 pb-6 text-gray-600'>{faq.answer}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
