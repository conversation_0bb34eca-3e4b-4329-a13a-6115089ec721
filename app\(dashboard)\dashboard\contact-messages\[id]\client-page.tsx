'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft,
  Mail,
  Phone,
  Building,
  Calendar,
  CheckCircle,
  Clock,
  Eye,
  Send,
  RefreshCw,
} from 'lucide-react';
import { format } from 'date-fns';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { useUser } from '@/lib/auth';

type ContactMessage = {
  id: number;
  name: string;
  email: string;
  phone: string | null;
  company: string | null;
  inquiryType: string;
  subject: string;
  message: string;
  status: string;
  createdAt: string;
  readAt: string | null;
  repliedAt: string | null;
  repliedBy: number | null;
};

// Client component that receives the ID as a prop
export default function ContactMessageDetailClient({
  messageId,
}: {
  messageId: string;
}) {
  const { userPromise } = useUser();
  const user = use(userPromise);

  const router = useRouter();
  const [message, setMessage] = useState<ContactMessage | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [replyText, setReplyText] = useState('');
  const [isSending, setIsSending] = useState(false);

  // Fetch contact message
  useEffect(() => {
    const fetchMessage = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/contact-messages/${messageId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch contact message');
        }
        const data = await response.json();
        setMessage(data.message);

        // Mark as read if it's unread
        if (data.message.status === 'unread') {
          markAsRead(parseInt(messageId));
        }
      } catch (error) {
        console.error('Error fetching contact message:', error);
        // Use sample data for now
        setMessage(generateSampleMessage(parseInt(messageId)));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMessage();
  }, [messageId]);

  // Mark message as read
  const markAsRead = async (id: number) => {
    try {
      console.log('Client - Marking message as read:', id);
      const response = await fetch(`/api/contact-messages/${id}/read`, {
        method: 'PUT',
      });

      const data = await response.json();
      console.log('Client - Mark as read response:', data);

      if (!response.ok) {
        console.error(
          'Client - Failed to mark message as read:',
          data.error,
          data.details
        );
        // Don't throw an error, just log it and continue
        // This way the UI will still update even if the API call fails
      }

      // Update local state regardless of API success
      // This ensures a good user experience even if the backend fails
      if (message) {
        console.log('Client - Updating message state to read');
        const now = new Date();
        setMessage({
          ...message,
          status: 'read',
          readAt: now.toISOString(), // Store as ISO string in client state
        });
      }
    } catch (error) {
      console.error('Client - Error marking message as read:', error);
      // Still update the UI for better user experience
      if (message) {
        const now = new Date();
        setMessage({
          ...message,
          status: 'read',
          readAt: now.toISOString(), // Store as ISO string in client state
        });
      }
    }
  };

  // Send reply
  const handleSendReply = async () => {
    if (!replyText.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a reply message',
        variant: 'destructive',
      });
      return;
    }

    setIsSending(true);
    try {
      const response = await fetch(`/api/contact-messages/${messageId}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ replyText }),
      });

      if (!response.ok) {
        throw new Error('Failed to send reply');
      }

      toast({
        title: 'Success',
        description:
          'Reply sent successfully and email has been sent to the contacter',
      });

      // Update local state
      if (message) {
        const now = new Date();
        setMessage({
          ...message,
          status: 'replied',
          repliedAt: now.toISOString(), // Store as ISO string in client state
        });
      }

      // Clear reply text
      setReplyText('');
    } catch (error) {
      console.error('Error sending reply:', error);
      toast({
        title: 'Error',
        description: 'Failed to send reply. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSending(false);
    }
  };

  // Generate sample message for development
  const generateSampleMessage = (id: number): ContactMessage => {
    const createdAt = new Date(
      Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000
    );
    const readAt =
      Math.random() > 0.3
        ? new Date(createdAt.getTime() + 2 * 60 * 60 * 1000).toISOString()
        : null;
    const repliedAt =
      readAt && Math.random() > 0.5
        ? new Date(
            new Date(readAt).getTime() + 3 * 60 * 60 * 1000
          ).toISOString()
        : null;

    let status = 'unread';
    if (readAt) status = 'read';
    if (repliedAt) status = 'replied';

    return {
      id,
      name: `John Doe`,
      email: `john.doe${id}@example.com`,
      phone: Math.random() > 0.5 ? '+****************' : null,
      company: Math.random() > 0.5 ? 'Acme Corporation' : null,
      inquiryType: ['general', 'sales', 'support', 'partnership'][
        Math.floor(Math.random() * 4)
      ],
      subject: 'Question about your services',
      message: `Hello,\n\nI'm interested in learning more about your services. Could you please provide more information about your pricing plans and features?\n\nThank you,\nJohn Doe`,
      status,
      createdAt: createdAt.toISOString(),
      readAt,
      repliedAt,
      repliedBy: repliedAt ? 1 : null,
    };
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'unread':
        return (
          <Badge
            variant='outline'
            className='bg-blue-50 text-blue-600 border-blue-200'
          >
            <Clock className='mr-1 h-3 w-3' />
            Unread
          </Badge>
        );
      case 'read':
        return (
          <Badge
            variant='outline'
            className='bg-yellow-50 text-yellow-600 border-yellow-200'
          >
            <Eye className='mr-1 h-3 w-3' />
            Read
          </Badge>
        );
      case 'replied':
        return (
          <Badge
            variant='outline'
            className='bg-green-50 text-green-600 border-green-200'
          >
            <CheckCircle className='mr-1 h-3 w-3' />
            Replied
          </Badge>
        );
      default:
        return (
          <Badge
            variant='outline'
            className='bg-gray-50 text-gray-600 border-gray-200'
          >
            {status}
          </Badge>
        );
    }
  };

  // Get inquiry type badge
  const getInquiryTypeBadge = (type: string) => {
    switch (type) {
      case 'general':
        return (
          <Badge
            variant='outline'
            className='bg-gray-50 text-gray-600 border-gray-200'
          >
            General
          </Badge>
        );
      case 'sales':
        return (
          <Badge
            variant='outline'
            className='bg-purple-50 text-purple-600 border-purple-200'
          >
            Sales
          </Badge>
        );
      case 'support':
        return (
          <Badge
            variant='outline'
            className='bg-orange-50 text-orange-600 border-orange-200'
          >
            Support
          </Badge>
        );
      case 'partnership':
        return (
          <Badge
            variant='outline'
            className='bg-indigo-50 text-indigo-600 border-indigo-200'
          >
            Partnership
          </Badge>
        );
      default:
        return (
          <Badge
            variant='outline'
            className='bg-gray-50 text-gray-600 border-gray-200'
          >
            {type}
          </Badge>
        );
    }
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center h-[50vh]'>
        <RefreshCw className='h-8 w-8 animate-spin text-gray-400' />
      </div>
    );
  }

  if (!message) {
    return (
      <div className='space-y-6'>
        <Button variant='ghost' onClick={() => router.back()} className='mb-6'>
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Messages
        </Button>
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-12'>
            <Mail className='h-12 w-12 text-gray-300 mb-4' />
            <h2 className='text-xl font-semibold text-gray-700'>
              Message Not Found
            </h2>
            <p className='text-gray-500 mt-2'>
              The message you're looking for doesn't exist or has been deleted.
            </p>
            <Button
              onClick={() => router.push('/dashboard/contact-messages')}
              className='mt-6'
            >
              View All Messages
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <Button variant='ghost' onClick={() => router.back()} className='mb-6'>
        <ArrowLeft className='mr-2 h-4 w-4' />
        Back to Messages
      </Button>

      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        {/* Message Details */}
        <div className='md:col-span-2'>
          <Card>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <CardTitle>Message Details</CardTitle>
                {getStatusBadge(message.status)}
              </div>
              <CardDescription>
                Received on{' '}
                {format(new Date(message.createdAt), 'MMMM d, yyyy h:mm a')}
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div>
                <h3 className='text-lg font-semibold mb-2'>
                  {message.subject}
                </h3>
                <div className='bg-gray-50 p-4 rounded-md whitespace-pre-wrap'>
                  {message.message}
                </div>
              </div>

              {message.status === 'replied' && message.repliedAt && (
                <div className='mt-8'>
                  <div className='flex items-center justify-between mb-2'>
                    <h3 className='text-lg font-semibold'>Your Reply</h3>
                    <span className='text-sm text-gray-500'>
                      {format(
                        new Date(message.repliedAt),
                        'MMMM d, yyyy h:mm a'
                      )}
                    </span>
                  </div>
                  <div className='bg-blue-50 p-4 rounded-md'>
                    <p>Reply content would be displayed here.</p>
                  </div>
                </div>
              )}

              {message.status !== 'replied' &&
                (user as any)?.teamRole !== 'viewer' && (
                  <div className='mt-8'>
                    <h3 className='text-lg font-semibold mb-2'>Send a Reply</h3>
                    <Textarea
                      placeholder='Type your reply here...'
                      className='min-h-[150px]'
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                    />
                    <div className='flex justify-end mt-4'>
                      <Button
                        onClick={handleSendReply}
                        disabled={
                          isSending ||
                          !replyText.trim() ||
                          (user as any)?.teamRole === 'viewer'
                        }
                        className='flex items-center'
                      >
                        {isSending ? (
                          <>
                            <RefreshCw className='mr-2 h-4 w-4 animate-spin' />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className='mr-2 h-4 w-4' />
                            Send Reply
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                )}
            </CardContent>
          </Card>
        </div>

        {/* Contact Information */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <h3 className='text-sm font-medium text-gray-500'>Name</h3>
                <p className='text-gray-900'>{message.name}</p>
              </div>
              <div>
                <h3 className='text-sm font-medium text-gray-500'>Email</h3>
                <p className='text-gray-900'>
                  <a
                    href={`mailto:${message.email}`}
                    className='text-blue-600 hover:underline'
                  >
                    {message.email}
                  </a>
                </p>
              </div>
              {message.phone && (
                <div>
                  <h3 className='text-sm font-medium text-gray-500'>Phone</h3>
                  <p className='text-gray-900'>
                    <a
                      href={`tel:${message.phone}`}
                      className='text-blue-600 hover:underline'
                    >
                      {message.phone}
                    </a>
                  </p>
                </div>
              )}
              {message.company && (
                <div>
                  <h3 className='text-sm font-medium text-gray-500'>Company</h3>
                  <p className='text-gray-900'>{message.company}</p>
                </div>
              )}
              <div>
                <h3 className='text-sm font-medium text-gray-500'>
                  Inquiry Type
                </h3>
                <div className='mt-1'>
                  {getInquiryTypeBadge(message.inquiryType)}
                </div>
              </div>

              <Separator className='my-4' />

              <div>
                <h3 className='text-sm font-medium text-gray-500'>Received</h3>
                <p className='text-gray-900 flex items-center gap-2'>
                  <Calendar className='h-4 w-4 text-gray-400' />
                  {format(new Date(message.createdAt), 'MMMM d, yyyy h:mm a')}
                </p>
              </div>
              {message.readAt && (
                <div>
                  <h3 className='text-sm font-medium text-gray-500'>Read</h3>
                  <p className='text-gray-900 flex items-center gap-2'>
                    <Eye className='h-4 w-4 text-gray-400' />
                    {format(new Date(message.readAt), 'MMMM d, yyyy h:mm a')}
                  </p>
                </div>
              )}
              {message.repliedAt && (
                <div>
                  <h3 className='text-sm font-medium text-gray-500'>Replied</h3>
                  <p className='text-gray-900 flex items-center gap-2'>
                    <CheckCircle className='h-4 w-4 text-gray-400' />
                    {format(new Date(message.repliedAt), 'MMMM d, yyyy h:mm a')}
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <div className='w-full'>
                <Button
                  variant='outline'
                  className='w-full'
                  onClick={() =>
                    window.open(
                      `mailto:${message.email}?subject=Re: ${message.subject}`
                    )
                  }
                >
                  <Mail className='mr-2 h-4 w-4' />
                  Email Directly
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
