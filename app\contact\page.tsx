'use client';

import React, { useState, useEffect } from 'react';
import { Mail, Phone, MapPin, Send, CheckCircle, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import Navbar from '@/components/Navbar';
import Footer from '@/components/sections/Homepage/Footer';

const ContactPage = () => {
  const [formState, setFormState] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    subject: '',
    message: '',
    inquiryType: 'general',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');
  const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);

  // Handle URL parameters to pre-fill form
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const subject = urlParams.get('subject');

    if (subject) {
      setFormState(prev => ({
        ...prev,
        subject: decodeURIComponent(subject),
        inquiryType: 'sales', // Set to sales for enterprise inquiries
      }));
    }
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));
  };

  const handleRadioChange = (value: string) => {
    setFormState((prev) => ({ ...prev, inquiryType: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      // Send the form data to our API endpoint
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formState),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit contact message');
      }

      setIsSubmitted(true);
      setFormState({
        name: '',
        email: '',
        phone: '',
        company: '',
        subject: '',
        message: '',
        inquiryType: 'general',
      });
    } catch (err) {
      console.error('Error submitting contact form:', err);
      setError('There was an error submitting your message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Navbar />
      <main className='min-h-screen bg-gray-50 pt-24 pb-16'>
        <div className='container mx-auto px-4 md:px-6 lg:px-8 max-w-6xl'>
          <div className='text-center mb-12'>
            <h1 className='text-4xl md:text-5xl font-bold mb-4'>
              Get in Touch
            </h1>
            <p className='text-xl text-gray-600 max-w-3xl mx-auto'>
              Have questions or need assistance? We're here to help. Reach out
              to our team using any of the methods below.
            </p>
          </div>

          <div className='grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16'>
            <div className='bg-white rounded-2xl shadow-md p-8 flex flex-col items-center text-center'>
              <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4'>
                <Mail className='h-6 w-6 text-blue-600' />
              </div>
              <h3 className='text-xl font-semibold mb-2'>Email Us</h3>
              <p className='text-gray-600 mb-4'>
                Our team typically responds within 24 hours
              </p>
              <a
                href='mailto:<EMAIL>'
                className='text-blue-600 font-medium hover:underline'
              >
                <EMAIL>
              </a>
            </div>

            <div className='bg-white rounded-2xl shadow-md p-8 flex flex-col items-center text-center'>
              <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4'>
                <Phone className='h-6 w-6 text-blue-600' />
              </div>
              <h3 className='text-xl font-semibold mb-2'>Call Us</h3>
              <div className='w-full mb-4'>
                <span className='text-gray-600 block mb-1'>Available</span>
                <div className='flex justify-center w-full xs:whitespace-nowrap sm:whitespace-nowrap gap-4 lg:gap-2.5 text-gray-600'>
                  <span>Monday - Friday:</span>
                  <span>9:00 AM - 6:00 PM IST</span>
                </div>
              </div>
              <a
                href='tel:******-123-4567'
                className='text-blue-600 font-medium hover:underline'
              >
                +44 7771086459
              </a>
            </div>

            <div className='bg-white rounded-2xl shadow-md p-8 flex flex-col items-center text-center'>
              <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4'>
                <MapPin className='h-6 w-6 text-blue-600' />
              </div>
              <h3 className='text-xl font-semibold mb-2'>Visit Us</h3>
              <p className='text-gray-600 mb-4'>Our headquarters location</p>
              <address className='not-italic text-blue-600 font-medium'>
                United Kingdom 5th Floor, 30 Churchill Place, Canary Wharf
                <br />
                London E14 5RE
              </address>
            </div>
          </div>

          <div className='bg-white rounded-2xl shadow-lg overflow-hidden'>
            <div className='grid grid-cols-1 lg:grid-cols-2'>
              <div className='p-8 lg:p-12'>
                <h2 className='text-2xl font-bold mb-6'>Send Us a Message</h2>

                {isSubmitted ? (
                  <div className='flex flex-col items-center justify-center py-12 text-center'>
                    <div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4'>
                      <CheckCircle className='h-8 w-8 text-green-600' />
                    </div>
                    <h3 className='text-xl font-semibold mb-2'>
                      Message Sent!
                    </h3>
                    <p className='text-gray-600 mb-6'>
                      Thank you for reaching out. We'll get back to you as soon
                      as possible.
                    </p>
                    <Button
                      onClick={() => setIsSubmitted(false)}
                      variant='outline'
                    >
                      Send Another Message
                    </Button>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className='space-y-6'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                      <div className='space-y-2'>
                        <Label htmlFor='name'>Full Name</Label>
                        <Input
                          id='name'
                          name='name'
                          placeholder='John Doe'
                          value={formState.name}
                          onChange={handleChange}
                          required
                        />
                      </div>
                      <div className='space-y-2'>
                        <Label htmlFor='email'>Email Address</Label>
                        <Input
                          id='email'
                          name='email'
                          type='email'
                          placeholder='<EMAIL>'
                          value={formState.email}
                          onChange={handleChange}
                          required
                        />
                      </div>
                    </div>

                    <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                      <div className='space-y-2'>
                        <Label htmlFor='phone'>Phone Number (Optional)</Label>
                        <Input
                          id='phone'
                          name='phone'
                          placeholder='+****************'
                          value={formState.phone}
                          onChange={handleChange}
                        />
                      </div>
                      <div className='space-y-2'>
                        <Label htmlFor='company'>Company (Optional)</Label>
                        <Input
                          id='company'
                          name='company'
                          placeholder='Your Company'
                          value={formState.company}
                          onChange={handleChange}
                        />
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <Label>Inquiry Type</Label>
                      <RadioGroup
                        value={formState.inquiryType}
                        onValueChange={handleRadioChange}
                        className='flex flex-wrap gap-4'
                      >
                        <div className='flex items-center space-x-2'>
                          <RadioGroupItem value='general' id='general' />
                          <Label htmlFor='general' className='cursor-pointer'>
                            General Inquiry
                          </Label>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <RadioGroupItem value='sales' id='sales' />
                          <Label htmlFor='sales' className='cursor-pointer'>
                            Sales
                          </Label>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <RadioGroupItem value='support' id='support' />
                          <Label htmlFor='support' className='cursor-pointer'>
                            Technical Support
                          </Label>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <RadioGroupItem
                            value='partnership'
                            id='partnership'
                          />
                          <Label
                            htmlFor='partnership'
                            className='cursor-pointer'
                          >
                            Partnership
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='subject'>Subject</Label>
                      <Input
                        id='subject'
                        name='subject'
                        placeholder='How can we help you?'
                        value={formState.subject}
                        onChange={handleChange}
                        required
                      />
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='message'>Message</Label>
                      <Textarea
                        id='message'
                        name='message'
                        placeholder='Please provide details about your inquiry...'
                        value={formState.message}
                        onChange={handleChange}
                        rows={5}
                        required
                      />
                    </div>

                    {error && (
                      <div className='bg-red-50 text-red-600 p-3 rounded-md text-sm'>
                        {error}
                      </div>
                    )}

                    <Button
                      type='submit'
                      className='w-full'
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <span className='animate-spin mr-2'>
                            <svg
                              className='h-5 w-5'
                              xmlns='http://www.w3.org/2000/svg'
                              fill='none'
                              viewBox='0 0 24 24'
                            >
                              <circle
                                className='opacity-25'
                                cx='12'
                                cy='12'
                                r='10'
                                stroke='currentColor'
                                strokeWidth='4'
                              ></circle>
                              <path
                                className='opacity-75'
                                fill='currentColor'
                                d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                              ></path>
                            </svg>
                          </span>
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className='mr-2 h-4 w-4' />
                          Send Message
                        </>
                      )}
                    </Button>
                  </form>
                )}
              </div>
              <div className='bg-blue-600 p-8 lg:p-12 text-white flex flex-col justify-center'>
                <h2 className='text-2xl font-bold mb-6'>We're Here to Help</h2>
                <div className='space-y-6'>
                  <p>
                    Whether you have questions about our platform, need
                    technical support, or want to explore partnership
                    opportunities, our team is ready to assist you.
                  </p>
                  <p>
                    We value your feedback and are committed to providing
                    exceptional service to all our customers and partners.
                  </p>
                  <div className='pt-4'>
                    <h3 className='text-xl font-semibold mb-4'>
                      Business Hours
                    </h3>
                    <ul className='space-y-2'>
                      <li className='flex justify-between'>
                        <span>Monday - Friday:</span>
                        <span>9:00 AM - 6:00 PM IST</span>
                      </li>
                      <li className='flex justify-between'>
                        <span>Saturday:</span>
                        <span>10:00 AM - 2:00 PM IST</span>
                      </li>
                      <li className='flex justify-between'>
                        <span>Sunday:</span>
                        <span>Closed</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <section className='relative py-32 w-full overflow-hidden mt-16'>
            {/* Animated Grid Background */}
            <div className='absolute inset-0'>
              <div className='w-full h-full bg-[#eaeff4] opacity-60'></div>
            </div>

            <div className='relative z-10 container mx-auto px-4 md:px-6 lg:px-8'>
              {/* FAQ Header */}
              <div className='text-center mb-16'>
                <h2 className='text-3xl md:text-5xl font-bold mb-6'>
                  Frequently Asked{' '}
                  <span className='text-blue-500'>Questions</span>
                </h2>
                <p className='text-base md:text-lg'>
                  Find quick answers to some of the most common
                  <br />
                  questions about contacting us.
                </p>
              </div>

              {/* FAQ Items */}
              <div className='max-w-3xl mx-auto space-y-4'>
                <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'>
                  <button
                    className='w-full px-8 py-6 flex items-center justify-between gap-4'
                    onClick={() =>
                      setOpenFaqIndex(openFaqIndex === 0 ? null : 0)
                    }
                  >
                    <span className='text-lg font-medium text-left text-gray-900'>
                      What is the typical response time?
                    </span>
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                        openFaqIndex === 0 ? 'rotate-45' : ''
                      }`}
                    >
                      <Plus className='h-5 w-5 text-white' />
                    </div>
                  </button>
                  <div
                    className='overflow-hidden transition-all duration-300 ease-in-out'
                    style={{
                      maxHeight: openFaqIndex === 0 ? '500px' : '0',
                    }}
                  >
                    <div className='px-8 pb-6 text-gray-600'>
                      We aim to respond to all inquiries within 24 hours during
                      business days. For urgent matters, please call our support
                      line.
                    </div>
                  </div>
                </div>

                <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'>
                  <button
                    className='w-full px-8 py-6 flex items-center justify-between gap-4'
                    onClick={() =>
                      setOpenFaqIndex(openFaqIndex === 1 ? null : 1)
                    }
                  >
                    <span className='text-lg font-medium text-left text-gray-900'>
                      Do you offer demos?
                    </span>
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                        openFaqIndex === 1 ? 'rotate-45' : ''
                      }`}
                    >
                      <Plus className='h-5 w-5 text-white' />
                    </div>
                  </button>
                  <div
                    className='overflow-hidden transition-all duration-300 ease-in-out'
                    style={{
                      maxHeight: openFaqIndex === 1 ? '500px' : '0',
                    }}
                  >
                    <div className='px-8 pb-6 text-gray-600'>
                      Yes, we offer personalized demos for businesses interested
                      in our platform. Please select "Sales" as your inquiry
                      type when contacting us.
                    </div>
                  </div>
                </div>

                <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'>
                  <button
                    className='w-full px-8 py-6 flex items-center justify-between gap-4'
                    onClick={() =>
                      setOpenFaqIndex(openFaqIndex === 2 ? null : 2)
                    }
                  >
                    <span className='text-lg font-medium text-left text-gray-900'>
                      How can I report a technical issue?
                    </span>
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                        openFaqIndex === 2 ? 'rotate-45' : ''
                      }`}
                    >
                      <Plus className='h-5 w-5 text-white' />
                    </div>
                  </button>
                  <div
                    className='overflow-hidden transition-all duration-300 ease-in-out'
                    style={{
                      maxHeight: openFaqIndex === 2 ? '500px' : '0',
                    }}
                  >
                    <div className='px-8 pb-6 text-gray-600'>
                      For technical issues, please select "Technical Support" as
                      your inquiry type and provide as much detail as possible
                      about the problem you're experiencing.
                    </div>
                  </div>
                </div>

                <div className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border-4 border-[#EAEFF4] rounded-3xl shadow-sm hover:shadow-md transition-all duration-300'>
                  <button
                    className='w-full px-8 py-6 flex items-center justify-between gap-4'
                    onClick={() =>
                      setOpenFaqIndex(openFaqIndex === 3 ? null : 3)
                    }
                  >
                    <span className='text-lg font-medium text-left text-gray-900'>
                      Do you have offices outside the US?
                    </span>
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center transition-transform duration-300 ${
                        openFaqIndex === 3 ? 'rotate-45' : ''
                      }`}
                    >
                      <Plus className='h-5 w-5 text-white' />
                    </div>
                  </button>
                  <div
                    className='overflow-hidden transition-all duration-300 ease-in-out'
                    style={{
                      maxHeight: openFaqIndex === 3 ? '500px' : '0',
                    }}
                  >
                    <div className='px-8 pb-6 text-gray-600'>
                      While our headquarters is in San Francisco, we have remote
                      team members around the world and can provide support in
                      multiple languages and time zones.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>
      <Footer />
    </>
  );
};

export default ContactPage;
