import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies, plans } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { eq, gt } from 'drizzle-orm';

export async function GET(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    if (session.user.role !== 'owner') {
      return new NextResponse(
        'Forbidden: Only owners can access this trending features',
        { status: 403 }
      );
    }

    // Get all case studies with views > 0, sorted by views descending
    const caseStudiesData = await db.query.caseStudies.findMany({
      where: gt(caseStudies.views, 0),
      orderBy: (cs, { desc }) => desc(cs.views),
      columns: {
        id: true,
        useCaseTitle: true,
        createdAt: true,
        trendingStatus: true,
        views: true,
        industry: true,
        role: true,
        vector: true,
      },
    });

    return NextResponse.json(caseStudiesData);
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

export async function PUT(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    if (session.user.role !== 'owner') {
      return new NextResponse(
        'Forbidden: Only owners can access this trending features',
        { status: 403 }
      );
    }

    const body = await req.json();
    const {
      mode = 'on',
      caseStudyId,
    }: { mode: 'off' | 'on'; caseStudyId: number } = body;
    if (!mode) {
      return new NextResponse('Mode is required', { status: 400 });
    }

    if (!caseStudyId) {
      return new NextResponse('Case study ID is required', { status: 400 });
    }

    // Check if the case study exists
    const caseStudy = await db.query.caseStudies.findFirst({
      where: eq(caseStudies.id, caseStudyId),
    });

    if (!caseStudy) {
      return new NextResponse('Case study not found', { status: 404 });
    }

    await db
      .update(caseStudies)
      .set({ trendingStatus: mode === 'on' })
      .where(eq(caseStudies.id, caseStudyId))
      .execute();

    return NextResponse.json({
      success: true,
      message: 'Trending status updated',
      mode: mode === 'on',
    });
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
