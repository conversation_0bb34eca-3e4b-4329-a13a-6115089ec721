/**
 * Centralized plan configuration for MorphX
 * This file contains all plan-related constants and utilities
 */

// Plan names (normalized)
export const PLAN_NAMES = {
  STARTER: 'starter',
  PRO: 'pro',
  ENTERPRISE: 'enterprise',
} as const;

export type PlanName = typeof PLAN_NAMES[keyof typeof PLAN_NAMES];

// Plan features interface
export interface PlanFeatures {
  // User limits
  maxUsers: number;
  maxCaseStudies: number;
  maxStorage: number; // in MB

  // Feature access
  accessToCuratedCases: boolean;
  miuPreview: boolean;
  fullMiuAccess: boolean;
  unlimitedUseCaseAccess: boolean;
  roadmapTools: boolean;
  teamCollaboration: boolean;
  dedicatedSupport: boolean;
  quarterlyReports: boolean;

  // Technical features
  advancedAnalytics: boolean;
  apiAccess: boolean;
  customBranding: boolean;
  prioritySupport: boolean;
  ssoIntegration: boolean;
  customIntegrations: boolean;
}

// Plan configuration based on YOUR ACTUAL pricing structure
export const PLAN_CONFIG: Record<PlanName, PlanFeatures> = {
  [PLAN_NAMES.STARTER]: {
    // User limits - 14 day trial
    maxUsers: 1,
    maxCaseStudies: 10, // 10 curated case studies access
    maxStorage: 50, // 50MB during trial

    // Feature access (trial limitations)
    accessToCuratedCases: true,
    miuPreview: true,
    fullMiuAccess: false,
    unlimitedUseCaseAccess: false,
    roadmapTools: false,
    teamCollaboration: false,
    dedicatedSupport: false,
    quarterlyReports: false,

    // Technical features
    advancedAnalytics: false, // Feature removed from all plans
    apiAccess: false, // Feature removed from all plans
    customBranding: false,
    prioritySupport: false,
    ssoIntegration: false,
    customIntegrations: false,
  },

  [PLAN_NAMES.PRO]: {
    // User limits - Up to 3 users
    maxUsers: 3, // Up to 3 users
    maxCaseStudies: -1, // unlimited
    maxStorage: -1, // unlimited

    // Feature access - Full features
    accessToCuratedCases: true,
    miuPreview: true,
    fullMiuAccess: true,
    unlimitedUseCaseAccess: true,
    roadmapTools: true,
    teamCollaboration: true,
    dedicatedSupport: false, // Enterprise only
    quarterlyReports: false, // Enterprise only

    // Technical features
    advancedAnalytics: false, // Feature removed from all plans
    apiAccess: false, // Feature removed from all plans
    customBranding: false, // Enterprise only
    prioritySupport: false, // Enterprise only
    ssoIntegration: false, // Enterprise only
    customIntegrations: false, // Enterprise only
  },

  [PLAN_NAMES.ENTERPRISE]: {
    // User limits - Up to 7 CXO-level users
    maxUsers: 7, // Up to 7 CXO-level users
    maxCaseStudies: -1, // unlimited
    maxStorage: -1, // unlimited

    // Feature access - All features
    accessToCuratedCases: true,
    miuPreview: true,
    fullMiuAccess: true,
    unlimitedUseCaseAccess: true,
    roadmapTools: true,
    teamCollaboration: true,
    dedicatedSupport: true,
    quarterlyReports: true,

    // Technical features - All features
    advancedAnalytics: false, // Feature removed from all plans
    apiAccess: false, // Feature removed from all plans
    customBranding: true,
    prioritySupport: true,
    ssoIntegration: true,
    customIntegrations: true,
  },
};

/**
 * Normalize plan name from various sources (Stripe, database, etc.)
 */
export function normalizePlanName(planName: string | null | undefined): PlanName {
  if (!planName) return PLAN_NAMES.STARTER;

  const normalized = planName.toLowerCase().trim();

  // Direct matches
  if (normalized === PLAN_NAMES.STARTER) return PLAN_NAMES.STARTER;
  if (normalized === PLAN_NAMES.PRO) return PLAN_NAMES.PRO;
  if (normalized === PLAN_NAMES.ENTERPRISE) return PLAN_NAMES.ENTERPRISE;

  // Partial matches for Stripe product names
  if (normalized.includes('starter')) return PLAN_NAMES.STARTER;
  if (normalized.includes('pro')) return PLAN_NAMES.PRO;
  if (normalized.includes('enterprise')) return PLAN_NAMES.ENTERPRISE;

  // Default fallback
  console.warn(`Unknown plan name: ${planName}, defaulting to starter`);
  return PLAN_NAMES.STARTER;
}

/**
 * Get plan features for a given plan name
 */
export function getPlanFeatures(planName: string | null | undefined): PlanFeatures {
  const normalized = normalizePlanName(planName);
  return PLAN_CONFIG[normalized];
}

/**
 * Check if a plan has a specific feature
 */
export function hasPlanFeature(planName: string | null | undefined, feature: keyof PlanFeatures): boolean {
  const features = getPlanFeatures(planName);
  return Boolean(features[feature]);
}

/**
 * Get plan limit for a specific resource
 */
export function getPlanLimit(planName: string | null | undefined, limitType: 'maxUsers' | 'maxCaseStudies' | 'maxStorage'): number {
  const features = getPlanFeatures(planName);
  return features[limitType];
}

/**
 * Check if current usage is within plan limits
 */
export function isWithinPlanLimit(
  planName: string | null | undefined,
  limitType: 'maxUsers' | 'maxCaseStudies' | 'maxStorage',
  currentUsage: number
): { allowed: boolean; limit: number; message?: string } {
  const limit = getPlanLimit(planName, limitType);

  // -1 means unlimited
  if (limit === -1) {
    return { allowed: true, limit: -1 };
  }

  const allowed = currentUsage < limit;
  return {
    allowed,
    limit,
    message: allowed ? undefined : `You've reached your plan limit of ${limit} ${limitType.replace('max', '').toLowerCase()}`
  };
}

/**
 * Get display name for plan
 */
export function getPlanDisplayName(planName: string | null | undefined): string {
  const normalized = normalizePlanName(planName);
  return normalized.charAt(0).toUpperCase() + normalized.slice(1);
}

/**
 * Check if subscription status is active
 */
export function isActiveSubscription(status: string | null | undefined): boolean {
  if (!status) return false;
  return ['active', 'trialing'].includes(status.toLowerCase());
}

/**
 * Check if subscription is in grace period
 */
export function isGracePeriodSubscription(status: string | null | undefined): boolean {
  if (!status) return false;
  return ['past_due'].includes(status.toLowerCase());
}

/**
 * Get plan comparison for upgrades
 */
export function comparePlans(currentPlan: string | null | undefined, targetPlan: string | null | undefined): {
  isUpgrade: boolean;
  isDowngrade: boolean;
  isSame: boolean;
} {
  const current = normalizePlanName(currentPlan);
  const target = normalizePlanName(targetPlan);

  const planOrder = [PLAN_NAMES.STARTER, PLAN_NAMES.PRO, PLAN_NAMES.ENTERPRISE];
  const currentIndex = planOrder.indexOf(current);
  const targetIndex = planOrder.indexOf(target);

  return {
    isUpgrade: targetIndex > currentIndex,
    isDowngrade: targetIndex < currentIndex,
    isSame: targetIndex === currentIndex,
  };
}
