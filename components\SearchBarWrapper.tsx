'use client';

import { SearchBar } from '@/components/SearchBar';

interface SearchBarWrapperProps {
  userRole: string;
  dropdownPosition?: 'top' | 'bottom';
}

export function SearchBarWrapper({ userRole, dropdownPosition }: SearchBarWrapperProps) {
  return (
    <div className="w-full" style={{ minHeight: '60px' }}>
      <SearchBar userRole={userRole} className="w-full" dropdownPosition={dropdownPosition} />
    </div>
  );
}
