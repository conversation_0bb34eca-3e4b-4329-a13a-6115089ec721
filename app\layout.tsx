import './globals.css';
import type { Metadata, Viewport } from 'next';
import { Manrope } from 'next/font/google';
import { UserProvider } from '@/lib/auth';
import { getUser } from '@/lib/db/server-queries';
import { ReduxProvider } from '@/lib/redux/provider';
import { Toaster } from '@/components/ui/toaster';
import { CloudinaryProvider } from '@/components/providers/cloudinary-provider';
import { ThemeProvider } from '@/components/providers/theme-provider';

// Initialize server-side utilities
import '@/lib/server-init';

export const metadata: Metadata = {
  title: 'Turinos AI',
  description: 'Gateway to AI-fy your business with insights that inspire action',
};

export const viewport: Viewport = {
  maximumScale: 1,
};

const manrope = Manrope({ subsets: ['latin'] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  let userPromise = getUser();

  return (
    <html
      lang="en"
      className={`bg-white dark:bg-gray-950 text-black dark:text-white ${manrope.className}`}
      suppressHydrationWarning
    >
      <body className="min-h-[100dvh] bg-background" suppressHydrationWarning>
        <ThemeProvider defaultTheme="light" storageKey="turinos-theme">
          <UserProvider userPromise={userPromise}>
            <ReduxProvider>
              <CloudinaryProvider>
                {children}
                <Toaster />
              </CloudinaryProvider>
            </ReduxProvider>
          </UserProvider>
        </ThemeProvider>
        {/* Add this script to help debug hydration issues */}
        <script dangerouslySetInnerHTML={{ __html: `
          window.addEventListener('error', function(e) {
            if (e.message && e.message.includes('Hydration')) {
              console.log('Hydration error details:', e);
            }
          });
        `}} />
      </body>
    </html>
  );
}
