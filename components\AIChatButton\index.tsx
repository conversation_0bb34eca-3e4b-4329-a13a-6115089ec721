'use client';

import React, { useState } from 'react';
import { MessageSquareText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import AIChatBox from '@/components/AIChatBox';

interface AIChatButtonProps {
  caseStudyTitle?: string;
  caseStudyId?: string | number;
}

export const AIChatButton: React.FC<AIChatButtonProps> = ({
  caseStudyTitle,
  caseStudyId
}) => {
  const [isChatOpen, setIsChatOpen] = useState(false);

  return (
    <>
      <Button
        onClick={() => setIsChatOpen(true)}
        className="fixed bottom-4 right-4 h-14 w-14 rounded-full shadow-lg bg-blue-500 hover:bg-blue-600 z-40"
        size="icon"
      >
        <MessageSquareText className="h-6 w-6" />
        <span className="sr-only">Open AI Chat</span>
      </Button>

      <AIChatBox
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
        caseStudyTitle={caseStudyTitle}
        caseStudyId={caseStudyId}
      />
    </>
  );
};

export default AIChatButton;
