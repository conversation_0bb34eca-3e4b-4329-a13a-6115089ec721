# Cron Jobs Documentation

This document outlines the cron jobs used in the application for automated tasks.

## Available Cron Jobs

### 1. Process Case Studies

**Endpoint:** `/api/cron/process-cases`  
**Recommended Schedule:** Daily at 12 PM  
**Purpose:** Processes case studies by sending them to the MIUAgent API for market intelligence data.

**Example crontab entry:**
```
0 12 * * * curl -X GET https://your-domain.com/api/cron/process-cases -H "Authorization: Bearer YOUR_CRON_SECRET"
```

### 2. Cleanup Temporary Files

**Endpoint:** `/api/cron/cleanup-temp-files`  
**Recommended Schedule:** Weekly (e.g., Sunday at 1 AM)  
**Purpose:** Cleans up temporary files in the development environment that are older than 7 days.

**Example crontab entry:**
```
0 1 * * 0 curl -X GET https://your-domain.com/api/cron/cleanup-temp-files -H "Authorization: Bearer YOUR_CRON_SECRET"
```

## Configuration

The following environment variables need to be set for the cron jobs to work properly:

```
# Base URL of your application
BASE_URL=https://your-domain.com

# Secret key for authorizing cron job requests
CRON_SECRET=your_cron_secret_key

# API key for batch processing
BATCH_PROCESSING_API_KEY=your_batch_processing_api_key

# MIUAgent API configuration
MIUAGENT_API_URL=https://miuagent-api.leafcraftstudios.com
MIUAGENT_API_KEY=your_miuagent_api_key
```

## Setting Up Cron Jobs

### On Linux/Unix Systems

1. Edit your crontab:
   ```
   crontab -e
   ```

2. Add the cron job entries as shown in the examples above.

### On Windows Systems

1. Use Windows Task Scheduler to create scheduled tasks.
2. Set the action to run a PowerShell script that makes the HTTP request.

### Using a Cron Job Service

Alternatively, you can use services like:
- [Cron-job.org](https://cron-job.org/)
- [EasyCron](https://www.easycron.com/)
- [SetCronJob](https://www.setcronjob.com/)

## Monitoring

All cron jobs log their activity using the application's structured logging system. You can monitor their execution by checking:

1. Application logs
2. The health endpoint at `/api/health`

## Troubleshooting

If a cron job is failing:

1. Check that the environment variables are correctly set
2. Verify that the authorization header is being sent correctly
3. Check the application logs for detailed error messages
4. Try running the job manually to see if it works
