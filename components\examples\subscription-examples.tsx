'use client';

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { SubscriptionGuard, PlanUsage, SubscriptionStatus } from '@/components/subscription/subscription-guard';
import { SubscriptionConditional, useSubscriptionAction } from '@/lib/auth/with-subscription';
import { useSubscription, usePlanLimit } from '@/lib/hooks/use-subscription';
import { BarChart3, Users, FileText, Database, Zap } from 'lucide-react';

/**
 * Example component showing how to use subscription guards
 */
export function SubscriptionExamples() {
  const { planFeatures, hasActiveSubscription } = useSubscription();
  const { checkAndExecute } = useSubscriptionAction();

  // Example of checking plan limits
  const userLimit = usePlanLimit('maxUsers', 2); // Current user count: 2
  const caseStudyLimit = usePlanLimit('maxCaseStudies', 15); // Current case studies: 15

  const handleAdvancedAnalytics = () => {
    // Advanced analytics feature has been removed from all plans
    console.log('Advanced analytics feature has been removed from all subscription plans');
    alert('Advanced analytics is no longer available. This feature has been removed from all plans.');
  };

  const handleAddUser = () => {
    checkAndExecute(
      async () => {
        // Check if we can add more users
        const limitCheck = await userLimit.recheck();
        if (!limitCheck?.allowed) {
          console.log('Cannot add more users - plan limit reached');
          return;
        }

        console.log('Adding new user...');
        // Proceed with adding user
      },
      {
        requireActiveSubscription: true,
        onRestricted: () => {
          console.log('Active subscription required to add users');
        }
      }
    );
  };

  return (
    <div className="space-y-8 p-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Subscription System Examples</h2>
        <p className="text-muted-foreground mb-6">
          This page demonstrates various subscription protection patterns and components.
        </p>
      </div>

      {/* Subscription Status Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Badge variant="outline">Status</Badge>
            Current Subscription
          </CardTitle>
          <CardDescription>
            Your current subscription status and plan information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SubscriptionStatus />
          <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Advanced Analytics:</span>
              <Badge variant={planFeatures.advancedAnalytics ? "default" : "secondary"} className="ml-2">
                {planFeatures.advancedAnalytics ? "Available" : "Not Available"}
              </Badge>
            </div>
            <div>
              <span className="font-medium">API Access:</span>
              <Badge variant={planFeatures.apiAccess ? "default" : "secondary"} className="ml-2">
                {planFeatures.apiAccess ? "Available" : "Not Available"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Plan Usage Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Plan Usage
          </CardTitle>
          <CardDescription>
            Current usage against your plan limits
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <PlanUsage
            limitType="maxUsers"
            currentCount={2}
            label="Team Members"
          />
          <PlanUsage
            limitType="maxCaseStudies"
            currentCount={15}
            label="Case Studies"
          />
          <PlanUsage
            limitType="maxStorage"
            currentCount={250}
            label="Storage Used"
            unit="MB"
          />
        </CardContent>
      </Card>

      {/* Feature-Protected Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Advanced Analytics
          </CardTitle>
          <CardDescription>
            This section requires the advanced analytics feature
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SubscriptionGuard feature="advancedAnalytics">
            <div className="space-y-4">
              <p className="text-green-600">✅ You have access to advanced analytics!</p>
              <Button onClick={handleAdvancedAnalytics}>
                Open Advanced Analytics
              </Button>
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium">Conversion Rate</h4>
                  <p className="text-2xl font-bold text-green-600">12.5%</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium">Revenue Growth</h4>
                  <p className="text-2xl font-bold text-blue-600">+23%</p>
                </div>
              </div>
            </div>
          </SubscriptionGuard>
        </CardContent>
      </Card>

      {/* API Access Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            API Access
          </CardTitle>
          <CardDescription>
            API keys and documentation for developers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SubscriptionGuard feature="apiAccess">
            <div className="space-y-4">
              <p className="text-green-600">✅ API access is available in your plan!</p>
              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <code className="text-sm">
                  API Key: sk_live_abc123...
                </code>
              </div>
              <Button variant="outline">
                View API Documentation
              </Button>
            </div>
          </SubscriptionGuard>
        </CardContent>
      </Card>

      {/* Team Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team Management
          </CardTitle>
          <CardDescription>
            Manage your team members and invitations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span>Current team size: 2 members</span>
              <Button
                onClick={handleAddUser}
                disabled={userLimit.percentageUsed >= 100}
              >
                Add Team Member
              </Button>
            </div>

            {userLimit.percentageUsed >= 80 && (
              <div className="p-3 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  {userLimit.percentageUsed >= 100
                    ? "You've reached your team member limit. Upgrade to add more members."
                    : "You're approaching your team member limit."
                  }
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Conditional Content Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Conditional Content</CardTitle>
          <CardDescription>
            Content that shows/hides based on subscription status
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <SubscriptionConditional requireActiveSubscription>
            <div className="p-4 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg">
              <p className="text-green-800 dark:text-green-200">
                ✅ This content is only visible to subscribers
              </p>
            </div>
          </SubscriptionConditional>

          <SubscriptionConditional requiredFeature="customBranding">
            <div className="p-4 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg">
              <p className="text-blue-800 dark:text-blue-200">
                ✅ Custom branding options available
              </p>
            </div>
          </SubscriptionConditional>

          <SubscriptionConditional
            requiredFeature="customBranding"
            inverse
            fallback={null}
          >
            <div className="p-4 bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg">
              <p className="text-gray-600 dark:text-gray-400">
                💡 Upgrade to Pro for custom branding options
              </p>
            </div>
          </SubscriptionConditional>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common subscription-related actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" asChild>
              <a href="/pricing">View Plans</a>
            </Button>
            <Button variant="outline" asChild>
              <a href="/dashboard/billing">Manage Subscription</a>
            </Button>
            <SubscriptionConditional requireActiveSubscription>
              <Button variant="outline">
                <Zap className="h-4 w-4 mr-2" />
                Upgrade Plan
              </Button>
            </SubscriptionConditional>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
