import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return new NextResponse('Invalid ID', { status: 400 });
    }

    // Get the case study with focus on market intelligence data
    const caseStudy = await db.query.caseStudies.findFirst({
      where: eq(caseStudies.id, id),
      columns: {
        id: true,
        useCaseTitle: true,
        marketIntelligenceData: true,
        marketMetricsData: true,
        marketSize: true,
        marketCAGR: true,
        marketROI: true,
        aiProcessed: true,
        aiProcessingStatus: true,
      }
    });

    if (!caseStudy) {
      return new NextResponse('Case study not found', { status: 404 });
    }

    // Return the raw data
    return NextResponse.json({
      success: true,
      caseStudy,
      // Check if the data is already an object or needs to be parsed
      parsedMarketIntelligence: caseStudy.marketIntelligenceData ?
        (typeof caseStudy.marketIntelligenceData === 'string' ?
          JSON.parse(caseStudy.marketIntelligenceData) :
          caseStudy.marketIntelligenceData) :
        null,
      parsedMarketMetrics: caseStudy.marketMetricsData ?
        (typeof caseStudy.marketMetricsData === 'string' ?
          JSON.parse(caseStudy.marketMetricsData) :
          caseStudy.marketMetricsData) :
        null,
    });
  } catch (error) {
    console.error('Error checking market data:', error);
    return new NextResponse(
      JSON.stringify({
        success: false,
        error: 'Internal Error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
