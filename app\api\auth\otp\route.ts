import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { otpVerifications, users } from '@/lib/db/schema';
import { eq, and, gt } from 'drizzle-orm';
import { z } from 'zod';
import { sendEmail } from '@/lib/email/mailer';

// Generate a random 6-digit OTP
function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Send OTP email
async function sendOtpEmail(
  email: string,
  otp: string,
  purpose: string
): Promise<boolean> {
  const subject =
    purpose === 'signup'
      ? 'Verify Your Email - Turinos'
      : 'Password Reset - Turinos';

  const text =
    purpose === 'signup'
      ? `Your verification code is: ${otp}. This code will expire in 10 minutes.`
      : `Your password reset code is: ${otp}. This code will expire in 10 minutes.`;

  const html =
    purpose === 'signup'
      ? `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3384FF;">Verify Your Email</h2>
        <p>Thank you for signing up with Turinos. Please use the following code to verify your email address:</p>
        <div style="background-color: #f4f7fb; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
          <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px;">${otp}</span>
        </div>
        <p>This code will expire in 10 minutes.</p>
        <p>If you didn't request this verification, please ignore this email.</p>
      </div>
    `
      : `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3384FF;">Password Reset</h2>
        <p>You requested a password reset for your Turinos account. Please use the following code to reset your password:</p>
        <div style="background-color: #f4f7fb; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
          <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px;">${otp}</span>
        </div>
        <p>This code will expire in 10 minutes.</p>
        <p>If you didn't request this password reset, please ignore this email.</p>
      </div>
    `;

  return await sendEmail({
    to: email,
    subject,
    text,
    html,
  });
}

// Schema for generating OTP
const generateOtpSchema = z.object({
  email: z.string().email(),
  purpose: z.enum(['signup', 'password-reset']),
});

// Schema for verifying OTP
const verifyOtpSchema = z.object({
  email: z.string().email(),
  otp: z.string().length(6),
  purpose: z.enum(['signup', 'password-reset']),
});

// POST - Generate and send OTP
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validationResult = generateOtpSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid request data',
          errors: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { email, purpose } = validationResult.data;

    // For signup, check if email already exists and is verified
    if (purpose === 'signup') {
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      if (existingUser.length > 0 && existingUser[0].isVerified) {
        return NextResponse.json(
          {
            success: false,
            message: 'Email already registered and verified',
          },
          { status: 400 }
        );
      }
    }

    // For password reset, check if email exists
    if (purpose === 'password-reset') {
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      if (existingUser.length === 0) {
        return NextResponse.json(
          {
            success: false,
            message: 'Email not found',
          },
          { status: 404 }
        );
      }
    }

    // Generate OTP
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Delete any existing OTPs for this email and purpose
    await db
      .delete(otpVerifications)
      .where(
        and(
          eq(otpVerifications.email, email),
          eq(otpVerifications.purpose, purpose)
        )
      );

    // Insert new OTP
    await db.insert(otpVerifications).values({
      email,
      otp,
      purpose,
      expiresAt,
      verified: false,
      attempts: 0,
    });

    // Send OTP email
    const emailSent = await sendOtpEmail(email, otp, purpose);

    if (!emailSent) {
      return NextResponse.json(
        {
          success: false,
          message: 'Failed to send OTP email',
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'OTP sent successfully',
    });
  } catch (error) {
    console.error('Error generating OTP:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
      },
      { status: 500 }
    );
  }
}

// PUT - Verify OTP
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validationResult = verifyOtpSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid request data',
          errors: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { email, otp, purpose } = validationResult.data;
    console.log('Verifying OTP for:', { email, purpose, otp });

    // Find OTP record
    const otpRecord = await db
      .select()
      .from(otpVerifications)
      .where(
        and(
          eq(otpVerifications.email, email),
          eq(otpVerifications.purpose, purpose),
          eq(otpVerifications.verified, false),
          gt(otpVerifications.expiresAt, new Date())
        )
      )
      .limit(1);

    if (otpRecord.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: 'OTP expired or not found',
        },
        { status: 400 }
      );
    }

    const record = otpRecord[0];

    // Check attempts
    if (record.attempts >= 5) {
      return NextResponse.json(
        {
          success: false,
          message: 'Too many failed attempts. Please request a new OTP.',
        },
        { status: 400 }
      );
    }

    // Increment attempts
    await db
      .update(otpVerifications)
      .set({ attempts: record.attempts + 1 })
      .where(eq(otpVerifications.id, record.id));

    // Verify OTP
    if (record.otp !== otp) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid OTP',
        },
        { status: 400 }
      );
    }

    // Mark OTP as verified
    await db
      .update(otpVerifications)
      .set({ verified: true })
      .where(eq(otpVerifications.id, record.id));

    // If signup purpose, mark user as verified if they exist
    if (purpose === 'signup') {
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      if (existingUser.length > 0) {
        await db
          .update(users)
          .set({ isVerified: true })
          .where(eq(users.id, existingUser[0].id));
      }
    }

    return NextResponse.json({
      success: true,
      message: 'OTP verified successfully',
    });
  } catch (error) {
    console.error('Error verifying OTP:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
      },
      { status: 500 }
    );
  }
}
