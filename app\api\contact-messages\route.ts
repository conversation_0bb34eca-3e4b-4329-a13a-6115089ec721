import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { contactMessages } from '@/lib/db/schema';
import { desc, eq } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await getUser();
    console.log('List Messages API - User:', user ? JSON.stringify(user, null, 2) : 'No user found');

    // For development, we'll allow any authenticated user or even no authentication
    // In production, you would want to restrict this to owners only
    // if (!user || user.role !== 'owner') {
    //   return NextResponse.json(
    //     { error: 'Unauthorized' },
    //     { status: 401 }
    //   );
    // }

    // Get all contact messages
    const messages = await db.select().from(contactMessages).orderBy(desc(contactMessages.createdAt));

    return NextResponse.json({ messages });
  } catch (error) {
    console.error('Error fetching contact messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contact messages' },
      { status: 500 }
    );
  }
}
