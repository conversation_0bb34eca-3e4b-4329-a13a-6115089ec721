"use client";

import {
  ComponentPropsWithoutRef,
  useEffect,
  useId,
  useRef,
  useState,
  useMemo,
} from "react";
import { cn } from "@/lib/utils";

export interface AnimatedGridPatternProps
  extends ComponentPropsWithoutRef<"svg"> {
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  strokeWidth?: number;
  strokeDasharray?: string;
  numSquares?: number;
  maxOpacity?: number;
  duration?: number;
  repeatDelay?: number;
}

export function AnimatedGridPattern({
  width = 200,
  height = 200,
  x = 0,
  y = 0,
  strokeWidth = 1,
  strokeDasharray = "0",
  numSquares = 10,
  className,
  maxOpacity = 0.5,
  duration = 4,
  repeatDelay = 0,
  ...props
}: AnimatedGridPatternProps) {
  const id = useId();
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [mounted, setMounted] = useState(false);

  // Memoize the animation config to prevent unnecessary re-renders
  const animationConfig = useMemo(() => ({
    duration,
    maxOpacity,
    repeatDelay,
  }), [duration, maxOpacity, repeatDelay]);

  // Use a seeded random function to ensure server/client consistency
  const getPos = (dims: typeof dimensions, seed: number) => {
    // Simple deterministic pseudo-random function
    const pseudoRandom = (seed: number) => {
      const x = Math.sin(seed) * 10000;
      return x - Math.floor(x);
    };

    return [
      Math.floor((pseudoRandom(seed) * dims.width) / width),
      Math.floor((pseudoRandom(seed + 1) * dims.height) / height),
    ];
  };

  const generateSquares = (count: number, dims: typeof dimensions) => {
    if (dims.width === 0 || dims.height === 0) return [];
    return Array.from({ length: count }, (_, i) => ({
      id: i,
      pos: mounted ? getPos(dims, i) : [0, 0], // Only generate positions when mounted
      opacity: mounted ? (i / count) * animationConfig.maxOpacity : 0, // Deterministic opacity
    }));
  };

  const [squares, setSquares] = useState<Array<{id: number, pos: number[], opacity: number}>>([]);

  // Initialize squares after mount
  useEffect(() => {
    setMounted(true);
    if (dimensions.width && dimensions.height) {
      setSquares(generateSquares(numSquares, dimensions));
    }
  }, [dimensions.width, dimensions.height]);

  // Update squares periodically, but only on client side
  useEffect(() => {
    if (!mounted || !dimensions.width || !dimensions.height) return;

    // Store reference to the current dimensions and config to avoid closure issues
    const currentDimensions = dimensions;
    const currentNumSquares = numSquares;
    const currentConfig = { ...animationConfig };

    // Create a stable getPos function that doesn't depend on changing references
    const updatePositions = (index: number) => {
      return getPos(currentDimensions, index + Date.now());
    };

    const interval = setInterval(() => {
      setSquares(currentSquares =>
        currentSquares.map((square, index) => ({
          ...square,
          pos: updatePositions(index),
          opacity: ((index / currentNumSquares) + 0.2) * currentConfig.maxOpacity,
        }))
      );
    }, (currentConfig.duration + currentConfig.repeatDelay) * 1000);

    // Clear interval on cleanup
    return () => {
      clearInterval(interval);
    };
  }, [dimensions, numSquares, animationConfig, mounted]);

  // Resize observer
  useEffect(() => {
    if (!mounted) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        setDimensions({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        });
      }
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, [mounted]);

  return (
    <div ref={containerRef} className="w-full h-full">
      <svg
        aria-hidden="true"
        className={cn(
          "pointer-events-none absolute inset-0 h-full w-full transition-all duration-300",
          className
        )}
        {...props}
      >
        <defs>
          <pattern
            id={id}
            width={width}
            height={height}
            patternUnits="userSpaceOnUse"
            x={x}
            y={y}
          >
            <path
              d={`M.5 ${height}V.5H${width}`}
              fill="none"
              stroke="currentColor"
              strokeWidth={strokeWidth}
              strokeDasharray={strokeDasharray}
              className="text-gray-200"
            />
          </pattern>
        </defs>
        <rect
          width="100%"
          height="100%"
          strokeWidth="0"
          fill={`url(#${id})`}
          className="text-gray-200"
        />
        <g>
          {squares.map(({ pos: [x, y], id, opacity }) => (
            <rect
              key={id}
              width={width - strokeWidth}
              height={height - strokeWidth}
              x={x * width + strokeWidth / 2}
              y={y * height + strokeWidth / 2}
              className="fill-current transition-opacity duration-1000"
              style={{ opacity }}
              strokeWidth="0"
            />
          ))}
        </g>
      </svg>
    </div>
  );
}
