/**
 * Debug script to compare the responses from both subscription APIs
 */

async function debugSubscriptionAPIs() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🔍 Debugging Subscription API Responses...\n');

  try {
    // Test 1: Check /api/user/team endpoint (used by member dashboard)
    console.log('1️⃣ Testing GET /api/user/team (Member Dashboard)...');
    const teamResponse = await fetch(`${baseUrl}/api/user/team`);
    
    if (teamResponse.status === 401) {
      console.log('❌ Not authenticated - please sign in first');
      return;
    }
    
    if (teamResponse.ok) {
      const teamData = await teamResponse.json();
      console.log('✅ Team API Response:');
      console.log('   Team ID:', teamData.id);
      console.log('   Team Name:', teamData.name);
      console.log('   Plan Name:', teamData.planName || 'NULL');
      console.log('   Subscription Status:', teamData.subscriptionStatus || 'NULL');
      console.log('   Stripe Customer ID:', teamData.stripeCustomerId || 'NULL');
      console.log('   Stripe Subscription ID:', teamData.stripeSubscriptionId || 'NULL');
    } else {
      console.log('❌ Team API failed:', teamResponse.status);
    }

    // Test 2: Check /api/user/subscription endpoint (used by billing page)
    console.log('\n2️⃣ Testing GET /api/user/subscription (Billing Page)...');
    const subscriptionResponse = await fetch(`${baseUrl}/api/user/subscription`);
    
    if (subscriptionResponse.ok) {
      const subscriptionData = await subscriptionResponse.json();
      console.log('✅ Subscription API Response:');
      console.log('   Success:', subscriptionData.success);
      console.log('   Has Subscription:', subscriptionData.hasSubscription);
      
      if (subscriptionData.hasSubscription && subscriptionData.subscription) {
        console.log('   Plan Name:', subscriptionData.subscription.planName);
        console.log('   Plan Display Name:', subscriptionData.subscription.planDisplayName);
        console.log('   Status:', subscriptionData.subscription.status);
        console.log('   Is Active:', subscriptionData.subscription.isActive);
        console.log('   Data Inconsistency:', subscriptionData.subscription.dataInconsistency);
        console.log('   Message:', subscriptionData.subscription.message);
      } else {
        console.log('   Redirect To:', subscriptionData.redirectTo);
        console.log('   Message:', subscriptionData.message);
      }
      
      if (subscriptionData.team) {
        console.log('   Team ID:', subscriptionData.team.id);
        console.log('   Team Name:', subscriptionData.team.name);
      }
    } else {
      console.log('❌ Subscription API failed:', subscriptionResponse.status);
    }

    console.log('\n🔍 Analysis:');
    console.log('If the member dashboard shows a plan but billing page shows "No Plan",');
    console.log('there might be a caching issue or the member dashboard is using');
    console.log('different logic to determine the plan name.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

debugSubscriptionAPIs();
