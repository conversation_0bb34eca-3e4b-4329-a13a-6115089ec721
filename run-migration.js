require('dotenv').config();
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  // Create a new client
  const client = new Client({
    connectionString: process.env.POSTGRES_URL
  });

  try {
    // Connect to the database
    console.log('Connecting to database...');
    await client.connect();
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'lib', 'db', 'migrations', 'add_missing_csv_fields.sql');
    console.log(`Reading SQL file from: ${sqlFilePath}`);
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    console.log('Executing SQL...');
    await client.query(sqlContent);
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the connection
    await client.end();
  }
}

runMigration().catch(console.error);
