'use client';

import { useState, useRef, use } from 'react';
import { CaseStudyResponse } from '@/hooks/useCaseStudies';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  DownloadIcon,
  UploadIcon,
  EyeOpenIcon,
} from '@radix-ui/react-icons';
import { SearchBarWrapper } from '@/components/SearchBarWrapper';
import { DeleteButton } from '@/components/DeleteButton';
import {
  useGetCaseStudiesQuery,
  useDeleteCaseStudyMutation,
  useImportCaseStudiesMutation,
  useLazyExportCaseStudiesQuery,
} from '@/lib/redux/api/caseStudiesApi';
import { createImportFormData } from '@/lib/utils/form-data-helper';
import { toast } from '@/components/ui/use-toast';
import SafeReduxWrapper from '@/components/SafeReduxWrapper';
import { Loading } from '@/components/ui/loading';
import { useUser } from '@/lib/auth';

export default function CaseStudiesPage() {
  return (
    <SafeReduxWrapper
      fallback={<Loading text='Loading case studies...' size='lg' />}
    >
      <CaseStudiesContent />
    </SafeReduxWrapper>
  );
}

export interface SearchCaseStudy {
  id: number;
  useCaseTitle: string;
  industry: string | null;
  role: string | null;
  headerImage?: string | null;
  featureImageUrl?: string | null;
  previewImageUrl?: string | null;
  icons: Array<{
    iconUrl: string;
    iconType: string;
  }>;
}

function CaseStudiesContent() {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { data, isLoading, error } = useGetCaseStudiesQuery({
    page,
    pageSize: 10,
  });
  const { userPromise } = useUser();
  const user = use(userPromise);
  const [deleteCaseStudy] = useDeleteCaseStudyMutation();
  const [importCaseStudies, { isLoading: isImporting }] =
    useImportCaseStudiesMutation();
  const [exportCaseStudies, { isLoading: isExporting }] =
    useLazyExportCaseStudiesQuery();

  const handleImport = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length) return;

    const file = e.target.files[0];
    // Use the helper function to create FormData
    const formData = createImportFormData(file);

    try {
      const result = await importCaseStudies(formData).unwrap();
      toast({
        title: 'Import Successful',
        description: `Imported ${result.imported} case studies`,
      });
    } catch (error) {
      console.error('Failed to import case studies:', error);
      toast({
        title: 'Import Failed',
        description: 'Failed to import case studies',
        variant: 'destructive',
      });
    }

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleExport = async () => {
    try {
      const blob = await exportCaseStudies().unwrap();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'case-studies.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Export Successful',
        description: 'Case studies exported successfully',
      });
    } catch (error) {
      console.error('Failed to export case studies:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export case studies',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) return <Loading text='Loading case studies...' size='lg' />;
  if (error)
    return <div className='p-6 text-red-500'>Error loading case studies</div>;

  const isViewer = (user as any)?.teamRole === 'viewer';
  const isTeamMember = (user as any)?.teamRole === 'teamMember';

  return (
    <div className='container mx-auto p-6'>
      <div className='flex justify-between items-center mb-6'>
        <h1 className='text-2xl font-bold'>Case Studies</h1>

        <div className='flex-1 mx-4 max-w-md'>
          <SearchBarWrapper userRole='owner' />
        </div>

        {!isViewer && (
          <div className='flex gap-2'>
            <Button
              variant='outline'
              onClick={() => router.push('/dashboard/import-cases')}
            >
              <UploadIcon className='mr-2 h-4 w-4' />
              Import/Export
            </Button>
            <Button onClick={() => router.push('/dashboard/add-case-study')}>
              <PlusIcon className='mr-2 h-4 w-4' />
              Add Case Study
            </Button>
          </div>
        )}
        <input
          type='file'
          ref={fileInputRef}
          onChange={handleFileChange}
          accept='.csv'
          className='hidden'
        />
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {data?.caseStudies.map((caseStudy: any) => {
          const imageURL =
            caseStudy.featureImageUrl ||
            caseStudy.previewImageUrl ||
            caseStudy.icons[0]?.iconUrl;

          return (
            <Card key={caseStudy.id} className='overflow-hidden'>
              <CardHeader className='p-0'>
                <div className='relative h-40 w-full'>
                  {/* First try to find a thumbnail in the icons array */}
                  {imageURL ? (
                    <img
                      src={imageURL}
                      alt={caseStudy.useCaseTitle}
                      className='w-full h-full object-cover'
                    />
                  ) : (
                    <div className='w-full h-full bg-muted flex items-center justify-center'>
                      <span className='text-muted-foreground'>No image</span>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className='p-4'>
                <CardTitle className='text-lg mb-2 line-clamp-2'>
                  {caseStudy.useCaseTitle}
                </CardTitle>
                <div className='flex gap-2 mb-3'>
                  {caseStudy.industry && (
                    <Badge variant='outline'>{caseStudy.industry}</Badge>
                  )}
                  {caseStudy.role && (
                    <Badge variant='outline'>{caseStudy.role}</Badge>
                  )}
                </div>
                <div className='text-sm text-muted-foreground mb-2'>
                  Uploaded by:{' '}
                  {caseStudy.user?.name ? (
                    caseStudy.user.name
                  ) : caseStudy.userId ? (
                    <span className='text-amber-600'>
                      User ID: {caseStudy.userId}
                    </span>
                  ) : (
                    'Unknown'
                  )}
                </div>
                <div className='flex justify-between mt-4'>
                  <div className='flex gap-1'>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() =>
                        router.push(`/dashboard/case-studies/${caseStudy.id}`)
                      }
                    >
                      <EyeOpenIcon className='h-4 w-4 mr-1' />
                      View
                    </Button>
                    {!isViewer && (
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() =>
                          router.push(
                            `/dashboard/import-cases?editId=${caseStudy.id}`
                          )
                        }
                      >
                        Edit
                      </Button>
                    )}
                  </div>
                  {!isViewer && !isTeamMember && (
                    <DeleteButton
                      caseStudyId={caseStudy.id}
                      size='icon'
                      variant='outline'
                      className='text-red-500'
                    />
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {data?.pagination && data.pagination.totalPages > 1 && (
        <div className='flex justify-center mt-8 gap-2'>
          <Button
            variant='outline'
            onClick={() => setPage((p) => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className='py-2 px-4'>
            Page {page} of {data.pagination.totalPages}
          </span>
          <Button
            variant='outline'
            onClick={() =>
              setPage((p) => Math.min(data.pagination.totalPages, p + 1))
            }
            disabled={page === data.pagination.totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
