'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@/lib/auth';
import { use, useActionState, startTransition } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  updateAccount,
  updatePasswordSimple,
  deleteAccount,
} from '@/app/(login)/actions';
import {
  Loader2,
  Save,
  AlertTriangle,
  Edit,
  Check,
  X,
  CreditCard,
} from 'lucide-react';
import { customerPortalAction } from '@/lib/payments/actions';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { industries } from '@/constants';

type ActionState = {
  error?: string;
  success?: string;
};

export default function SettingsPage() {
  const { userPromise } = useUser();
  const user = use(userPromise);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isPasswordEditMode, setIsPasswordEditMode] = useState(false);
  const [teamData, setTeamData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [userIndustry, setUserIndustry] = useState(user?.industry || '');

  useEffect(() => {
    if (user?.industry) {
      setUserIndustry(user.industry);
    }
  }, [user]);

  // Fetch team data for subscription info (for members and owners)
  useEffect(() => {
    const fetchTeamData = async () => {
      if (user) {
        setIsLoading(true);
        try {
          const response = await fetch('/api/user/team');
          if (response.ok) {
            const data = await response.json();
            setTeamData(data);
          }
        } catch (error) {
          console.error('Error fetching team data:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchTeamData();
  }, [user]);

  // Account information form state
  const [accountState, accountFormAction, isAccountPending] = useActionState<
    ActionState,
    FormData
  >(updateAccount, { error: '', success: '' });

  // Password update form state (using simplified version without current password)
  const [passwordState, passwordFormAction, isPasswordPending] = useActionState<
    ActionState,
    FormData
  >(updatePasswordSimple, { error: '', success: '' });

  // Account deletion form state
  const [deleteState, deleteFormAction, isDeletePending] = useActionState<
    ActionState,
    FormData
  >(deleteAccount, { error: '', success: '' });

  // Close edit mode when account update is successful
  useEffect(() => {
    if (accountState.success) {
      setIsEditMode(false);
    }
  }, [accountState.success]);

  // Close password edit mode when password update is successful
  useEffect(() => {
    if (passwordState.success) {
      setIsPasswordEditMode(false);
    }
  }, [passwordState.success]);

  // Close delete dialog when there's an error (success will redirect)
  useEffect(() => {
    if (deleteState.error) {
      // Keep dialog open to show the error
    } else if (isDeletePending === false && deleteState.success) {
      setIsDeleteDialogOpen(false);
    }
  }, [deleteState, isDeletePending]);

  const handleAccountSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const formData = new FormData(e.currentTarget);

    startTransition(() => {
      accountFormAction(formData);
    });

    // We'll let the success state control this instead of immediately closing
    // This ensures the form stays in edit mode until the action completes
  };

  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
  };

  const cancelEdit = () => {
    setIsEditMode(false);
  };

  const handlePasswordSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    startTransition(() => {
      passwordFormAction(formData);
    });

    // We'll let the success state control this instead of immediately closing
    // This ensures the form stays in edit mode until the action completes
  };

  const togglePasswordEditMode = () => {
    setIsPasswordEditMode(!isPasswordEditMode);
  };

  const cancelPasswordEdit = () => {
    setIsPasswordEditMode(false);
  };

  const handleDeleteSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    startTransition(() => {
      deleteFormAction(formData);
    });

    // We'll keep the dialog open until the action completes
    // The redirect in the action will handle navigation if successful
  };

  return (
    <div className='container mx-auto py-8'>
      <h1 className='text-3xl font-bold mb-8 dark:text-white'>
        Account Settings
      </h1>

      <Tabs defaultValue='account' className='space-y-6'>
        <TabsList className='grid w-full grid-cols-2 md:w-auto md:inline-flex dark:bg-gray-800'>
          <TabsTrigger
            value='account'
            className='dark:data-[state=active]:bg-gray-950 dark:data-[state=active]:text-white dark:text-gray-300'
          >
            Account
          </TabsTrigger>
          <TabsTrigger
            value='security'
            className='dark:data-[state=active]:bg-gray-950 dark:data-[state=active]:text-white dark:text-gray-300'
          >
            Security
          </TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value='account'>
          <Card className='dark:bg-black dark:border-white/20'>
            <CardHeader className='flex flex-row items-center justify-between'>
              <div>
                <CardTitle className='dark:text-white'>
                  Profile Information
                </CardTitle>
                <CardDescription className='dark:text-white/70'>
                  {isEditMode
                    ? 'Edit your account details below.'
                    : 'Your account details and profile information.'}
                </CardDescription>
              </div>
              {!isEditMode ? (
                <Button
                  onClick={toggleEditMode}
                  variant='outline'
                  size='sm'
                  className='dark:bg-black dark:text-white dark:hover:bg-white/10 dark:border-white/20'
                >
                  <Edit className='h-4 w-4 mr-2' />
                  Edit Profile
                </Button>
              ) : null}
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Profile Picture */}
              <div className='flex flex-col items-center sm:flex-row sm:items-start gap-6'>
                <div className='flex flex-col items-center gap-2'>
                  <Avatar className='h-24 w-24'>
                    <AvatarFallback className='bg-blue-500 text-white text-2xl'>
                      {user?.name?.charAt(0) || user?.email?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <p className='text-sm text-gray-500 dark:text-white/70'>
                    Profile Picture
                  </p>
                </div>

                <div className='flex-1'>
                  <form className='space-y-4' onSubmit={handleAccountSubmit}>
                    <div>
                      <Label htmlFor='name' className='dark:text-white'>
                        Full Name
                      </Label>
                      <Input
                        id='name'
                        name='name'
                        placeholder='Enter your full name'
                        defaultValue={user?.name || ''}
                        required
                        disabled={!isEditMode}
                        className={
                          !isEditMode
                            ? 'bg-gray-50 dark:bg-gray-900 dark:text-white dark:border-white/20'
                            : 'dark:bg-black dark:text-white dark:border-white/20'
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor='email' className='dark:text-white'>
                        Email Address
                      </Label>
                      <Input
                        id='email'
                        name='email'
                        type='email'
                        placeholder='Enter your email address'
                        defaultValue={user?.email || ''}
                        required
                        disabled={!isEditMode}
                        className={
                          !isEditMode
                            ? 'bg-gray-50 dark:bg-gray-900 dark:text-white dark:border-white/20'
                            : 'dark:bg-black dark:text-white dark:border-white/20'
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor='role' className='dark:text-white'>
                        Role
                      </Label>
                      <Input
                        id='role'
                        name='role'
                        value={user?.role || ''}
                        disabled
                        className='bg-gray-50 dark:bg-gray-900 dark:text-white dark:border-white/20'
                      />
                      <p className='text-xs text-gray-500 dark:text-white/70 mt-1'>
                        Your account role cannot be changed.
                      </p>
                    </div>

                    {/* Only show industry and company name for members and managers, not owners */}
                    {(user?.role === 'member' || user?.role === 'manager') && (
                      <>
                        <div>
                          <Label htmlFor='industry' className='dark:text-white'>
                            Industry
                          </Label>
                          {/* <Input
                            id='industry'
                            name='industry'
                            placeholder='Enter your industry'
                            defaultValue={user?.industry || ''}
                            disabled={!isEditMode}
                            className={
                              !isEditMode
                                ? 'bg-gray-50 dark:bg-gray-900 dark:text-white dark:border-white/20'
                                : 'dark:bg-black dark:text-white dark:border-white/20'
                            }
                          /> */}

                          <Select
                            name='industry'
                            value={userIndustry || ''}
                            disabled={!isEditMode}
                            onValueChange={(value) => setUserIndustry(value)}
                          >
                            <SelectTrigger className='w-full dark:bg-black dark:text-white dark:border-white/20'>
                              <SelectValue placeholder='Select your industry' />
                            </SelectTrigger>
                            <SelectContent className='dark:bg-black dark:text-white dark:border-white/20'>
                              {industries.map((industry, index) => (
                                <SelectItem key={index} value={industry.value}>
                                  {industry.title}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label
                            htmlFor='companyName'
                            className='dark:text-white'
                          >
                            Company Name
                          </Label>
                          <Input
                            id='companyName'
                            name='companyName'
                            placeholder='Enter your company name'
                            defaultValue={user?.companyName || ''}
                            disabled={!isEditMode}
                            className={
                              !isEditMode
                                ? 'bg-gray-50 dark:bg-gray-900 dark:text-white dark:border-white/20'
                                : 'dark:bg-black dark:text-white dark:border-white/20'
                            }
                          />
                        </div>
                      </>
                    )}

                    {accountState.error && (
                      <p className='text-red-500 text-sm'>
                        {accountState.error}
                      </p>
                    )}
                    {accountState.success && (
                      <p className='text-green-500 text-sm'>
                        {accountState.success}
                      </p>
                    )}

                    {isEditMode && (
                      <div className='flex gap-2'>
                        <Button
                          type='submit'
                          disabled={isAccountPending}
                          className='w-full sm:w-auto relative dark:bg-blue-600 dark:hover:bg-blue-700 dark:text-white'
                        >
                          {isAccountPending ? (
                            <>
                              <span className='opacity-0'>Save Changes</span>
                              <span className='absolute inset-0 flex items-center justify-center'>
                                <Loader2 className='h-4 w-4 animate-spin' />
                                <span className='ml-2'>Saving...</span>
                              </span>
                            </>
                          ) : (
                            <>
                              <Check className='mr-2 h-4 w-4' />
                              Save Changes
                            </>
                          )}
                        </Button>
                        <Button
                          type='button'
                          variant='outline'
                          onClick={cancelEdit}
                          className='w-full sm:w-auto dark:bg-black dark:text-white dark:hover:bg-white/10 dark:border-white/20'
                          disabled={isAccountPending}
                        >
                          <X className='mr-2 h-4 w-4' />
                          Cancel
                        </Button>
                      </div>
                    )}
                  </form>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value='security' className='space-y-6'>
          <div className='grid gap-6'>
            {/* Password Change Card */}
            <Card className='dark:bg-black dark:border-white/20'>
              <CardHeader className='flex flex-row items-center justify-between'>
                <div>
                  <CardTitle className='dark:text-white'>
                    Change Password
                  </CardTitle>
                  <CardDescription className='dark:text-white/70'>
                    {isPasswordEditMode
                      ? 'Enter your new password below.'
                      : 'Update your password to keep your account secure.'}
                  </CardDescription>
                </div>
                {!isPasswordEditMode ? (
                  <Button
                    onClick={togglePasswordEditMode}
                    variant='outline'
                    size='sm'
                    className='dark:bg-black dark:text-white dark:hover:bg-white/10 dark:border-white/20'
                  >
                    <Edit className='h-4 w-4 mr-2' />
                    Change Password
                  </Button>
                ) : null}
              </CardHeader>
              <CardContent>
                {isPasswordEditMode ? (
                  <form className='space-y-4' onSubmit={handlePasswordSubmit}>
                    {/* Current password field removed as user is already authenticated */}

                    <div>
                      <Label htmlFor='newPassword' className='dark:text-white'>
                        New Password
                      </Label>
                      <Input
                        id='newPassword'
                        name='newPassword'
                        type='password'
                        autoComplete='new-password'
                        required
                        minLength={8}
                        maxLength={100}
                        className='dark:bg-black dark:text-white dark:border-white/20'
                      />
                      <p className='text-xs text-gray-500 dark:text-white/70 mt-1'>
                        Password must be at least 8 characters long.
                      </p>
                    </div>

                    <div>
                      <Label
                        htmlFor='confirmPassword'
                        className='dark:text-white'
                      >
                        Confirm New Password
                      </Label>
                      <Input
                        id='confirmPassword'
                        name='confirmPassword'
                        type='password'
                        autoComplete='new-password'
                        required
                        minLength={8}
                        maxLength={100}
                        className='dark:bg-black dark:text-white dark:border-white/20'
                      />
                    </div>

                    {passwordState.error && (
                      <p className='text-red-500 text-sm'>
                        {passwordState.error}
                      </p>
                    )}
                    {passwordState.success && (
                      <p className='text-green-500 text-sm'>
                        {passwordState.success}
                      </p>
                    )}

                    <div className='flex gap-2'>
                      <Button
                        type='submit'
                        disabled={isPasswordPending}
                        className='relative dark:bg-blue-600 dark:hover:bg-blue-700 dark:text-white'
                      >
                        {isPasswordPending ? (
                          <>
                            <span className='opacity-0'>Update Password</span>
                            <span className='absolute inset-0 flex items-center justify-center'>
                              <Loader2 className='h-4 w-4 animate-spin' />
                              <span className='ml-2'>Updating...</span>
                            </span>
                          </>
                        ) : (
                          <>
                            <Check className='mr-2 h-4 w-4' />
                            Update Password
                          </>
                        )}
                      </Button>
                      <Button
                        type='button'
                        variant='outline'
                        onClick={cancelPasswordEdit}
                        disabled={isPasswordPending}
                        className='dark:bg-black dark:text-white dark:hover:bg-white/10 dark:border-white/20'
                      >
                        <X className='mr-2 h-4 w-4' />
                        Cancel
                      </Button>
                    </div>
                  </form>
                ) : (
                  <div className='py-4'>
                    <p className='text-gray-500 dark:text-white/70'>
                      You can update your password at any time to keep your
                      account secure. No need to enter your current password.
                    </p>
                    {passwordState.success && (
                      <p className='text-green-500 text-sm mt-2'>
                        {passwordState.success}
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Account Deletion Card */}
            <Card className='border-red-100 dark:bg-black dark:border-red-800'>
              <CardHeader>
                <CardTitle className='text-red-600 dark:text-red-500'>
                  Delete Account
                </CardTitle>
                <CardDescription className='dark:text-white/70'>
                  Permanently delete your account and all associated data. This
                  action cannot be undone.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Dialog
                  open={isDeleteDialogOpen}
                  onOpenChange={setIsDeleteDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      variant='destructive'
                      className='dark:bg-red-700 dark:hover:bg-red-800 dark:text-white'
                    >
                      Delete Account
                    </Button>
                  </DialogTrigger>
                  <DialogContent className='dark:bg-gray-900 dark:border-gray-700'>
                    <DialogHeader>
                      <DialogTitle className='dark:text-white'>
                        Are you absolutely sure?
                      </DialogTitle>
                      <DialogDescription className='dark:text-white/70'>
                        This action cannot be undone. This will permanently
                        delete your account and remove all your data from our
                        servers.
                      </DialogDescription>
                    </DialogHeader>

                    <form onSubmit={handleDeleteSubmit} className='space-y-4'>
                      <div>
                        <Label
                          htmlFor='password'
                          className='text-red-500 dark:text-red-400'
                        >
                          Enter your password to confirm
                        </Label>
                        <Input
                          id='password'
                          name='password'
                          type='password'
                          autoComplete='current-password'
                          required
                          className='border-red-200 dark:bg-gray-800 dark:text-white dark:border-red-800'
                        />
                      </div>

                      {deleteState.error && (
                        <p className='text-red-500 dark:text-red-400 text-sm flex items-center'>
                          <AlertTriangle className='h-4 w-4 mr-1' />
                          {deleteState.error}
                        </p>
                      )}

                      <DialogFooter>
                        <Button
                          type='button'
                          variant='outline'
                          onClick={() => setIsDeleteDialogOpen(false)}
                          className='dark:bg-gray-800 dark:text-white dark:hover:bg-gray-700 dark:border-gray-600'
                        >
                          Cancel
                        </Button>
                        <Button
                          type='submit'
                          variant='destructive'
                          disabled={isDeletePending}
                          className='relative dark:bg-red-700 dark:hover:bg-red-800 dark:text-white'
                        >
                          {isDeletePending ? (
                            <>
                              <span className='opacity-0'>Delete Account</span>
                              <span className='absolute inset-0 flex items-center justify-center'>
                                <Loader2 className='h-4 w-4 animate-spin' />
                                <span className='ml-2'>Deleting...</span>
                              </span>
                            </>
                          ) : (
                            <>
                              <AlertTriangle className='mr-2 h-4 w-4' />
                              Delete Account
                            </>
                          )}
                        </Button>
                      </DialogFooter>
                    </form>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
