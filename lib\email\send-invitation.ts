import { sendEmail } from './mailer';
import { getTeamInvitationEmail, getExistingUserInvitationEmail } from './templates';
import { db } from '@/lib/db/drizzle';
import { users, teams, invitations } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

/**
 * Send a team invitation email to new users
 */
export async function sendTeamInvitation(invitationId: number): Promise<boolean> {
  try {
    // Get the invitation with team and inviter details
    const invitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, invitationId),
      with: {
        team: true,
      },
    });

    if (!invitation) {
      console.error(`Invitation with ID ${invitationId} not found`);
      return false;
    }

    // Get the inviter details
    const inviter = await db.query.users.findFirst({
      where: eq(users.id, invitation.invitedBy),
    });

    if (!inviter) {
      console.error(`Inviter with ID ${invitation.invitedBy} not found`);
      return false;
    }

    // Generate a unique invitation link
    const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
    const inviteLink = `${baseUrl}/sign-up?inviteId=${invitation.id}`;

    // Generate the email content
    const emailContent = getTeamInvitationEmail({
      teamName: invitation.team.name,
      inviterName: inviter.name || '',
      inviterEmail: inviter.email,
      role: invitation.role,
      inviteLink,
    });

    // Send the email
    const emailSent = await sendEmail({
      to: invitation.email,
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
    });

    return emailSent;
  } catch (error) {
    console.error('Error sending team invitation:', error);
    return false;
  }
}

/**
 * Send a team invitation email to existing users
 */
export async function sendExistingUserInvitation(invitationId: number, existingUser: any): Promise<boolean> {
  try {
    // Get the invitation with team and inviter details
    const invitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, invitationId),
      with: {
        team: true,
      },
    });

    if (!invitation) {
      console.error(`Invitation with ID ${invitationId} not found`);
      return false;
    }

    // Get the inviter details
    const inviter = await db.query.users.findFirst({
      where: eq(users.id, invitation.invitedBy),
    });

    if (!inviter) {
      console.error(`Inviter with ID ${invitation.invitedBy} not found`);
      return false;
    }

    // Generate a unique invitation link
    const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
    const inviteLink = `${baseUrl}/dashboard/invitations?inviteId=${invitation.id}`;

    // Generate the email content
    const emailContent = getExistingUserInvitationEmail({
      teamName: invitation.team.name,
      inviterName: inviter.name || '',
      inviterEmail: inviter.email,
      role: invitation.role,
      inviteLink,
      userName: existingUser.name || existingUser.email,
    });

    // Send the email
    const emailSent = await sendEmail({
      to: invitation.email,
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
    });

    return emailSent;
  } catch (error) {
    console.error('Error sending existing user invitation:', error);
    return false;
  }
}
