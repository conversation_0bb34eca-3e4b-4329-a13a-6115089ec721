import { NextRequest, NextResponse } from 'next/server';
import { PlanFeatures, PLAN_CONFIG, normalizePlanName, isActiveSubscription, isGracePeriodSubscription } from '@/lib/config/plans';

// Re-export types for backward compatibility
export type { PlanFeatures } from '@/lib/config/plans';
export type SubscriptionStatus = 'active' | 'trialing' | 'past_due' | 'canceled' | 'unpaid' | 'incomplete' | 'incomplete_expired';

// Re-export plan config for backward compatibility
export const PLAN_LIMITS = PLAN_CONFIG;

// Routes that require active subscription (members only)
const SUBSCRIPTION_REQUIRED_ROUTES = [
  '/dashboard/member',
  '/dashboard/trends',
  '/dashboard/reports',
  '/dashboard/user',
  '/dashboard/user/case-studies',
  '/dashboard/user/search',
  '/dashboard/test-subscription',
];

// Routes that require specific plan features
// Note: Advanced analytics and API access have been removed from all plans
const FEATURE_RESTRICTED_ROUTES = {
  // '/dashboard/analytics': 'advancedAnalytics', // Feature removed from all plans
  // '/dashboard/api': 'apiAccess', // Feature removed from all plans
  '/dashboard/branding': 'customBranding',
} as const;

// Grace period routes (accessible during grace period)
const GRACE_PERIOD_ROUTES = [
  '/dashboard/billing',
  '/dashboard/settings',
  '/dashboard/help',
];

// Routes that should bypass subscription validation entirely
const BYPASS_VALIDATION_ROUTES = [
  '/dashboard/billing',
  '/dashboard/settings',
  '/dashboard/help',
  '/api/stripe/checkout',
  '/api/payments/checkout',
  '/api/payments/portal',
  '/api/subscriptions',
  '/api/subscription-validation',
];

/**
 * Check if a route should bypass subscription validation entirely
 */
function shouldBypassValidation(pathname: string): boolean {
  return BYPASS_VALIDATION_ROUTES.some(route =>
    pathname === route || pathname.startsWith(`${route}/`)
  );
}

/**
 * Check if a route requires subscription validation
 */
function requiresSubscription(pathname: string): boolean {
  // First check if we should bypass validation entirely
  if (shouldBypassValidation(pathname)) {
    return false;
  }

  return SUBSCRIPTION_REQUIRED_ROUTES.some(route =>
    pathname === route || pathname.startsWith(`${route}/`)
  );
}

/**
 * Check if a route requires specific plan features
 */
function getRequiredFeature(pathname: string): keyof PlanFeatures | null {
  for (const [route, feature] of Object.entries(FEATURE_RESTRICTED_ROUTES)) {
    if (pathname === route || pathname.startsWith(`${route}/`)) {
      return feature as keyof PlanFeatures;
    }
  }
  return null;
}

/**
 * Check if a route is accessible during grace period
 */
function isGracePeriodRoute(pathname: string): boolean {
  return GRACE_PERIOD_ROUTES.some(route =>
    pathname === route || pathname.startsWith(`${route}/`)
  );
}

// Use centralized functions from plans config
export { normalizePlanName as getPlanName, isActiveSubscription as isValidSubscriptionStatus, isGracePeriodSubscription as isGracePeriodStatus } from '@/lib/config/plans';

/**
 * Simplified subscription middleware function for Edge Runtime
 * This version is very conservative to avoid redirect loops
 */
export async function validateSubscription(request: NextRequest): Promise<NextResponse | null> {
  const { pathname } = request.nextUrl;
  const url = new URL(request.url);

  console.log(`[Subscription Middleware] Checking route: ${pathname}`);

  // Always allow access for now - validation will be handled by individual pages
  // This prevents redirect loops while still providing the infrastructure
  console.log(`[Subscription Middleware] Allowing access to ${pathname} (validation handled by pages)`);

  return null; // Continue with request
}

// Export server actions from separate file to avoid 'use server' conflicts
export { hasFeatureAccess, getCurrentPlanLimits, checkPlanLimit, getSubscriptionStatus } from './subscription-actions';
