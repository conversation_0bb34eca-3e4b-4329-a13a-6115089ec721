import { sql } from 'drizzle-orm';
import dotenv from 'dotenv';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';

// Load environment variables from .env file
dotenv.config();

/**
 * Update database with new Stripe IDs from setup-stripe-pricing.js output
 * 
 * Usage:
 * 1. Run setup-stripe-pricing.js first
 * 2. Update the STRIPE_IDS object below with the generated IDs
 * 3. Run: npx tsx scripts/update-database-with-new-stripe-ids.ts
 */

// UPDATE THESE WITH YOUR NEW STRIPE IDS FROM setup-stripe-pricing.js
const STRIPE_IDS = {
  starter: {
    productId: 'prod_SPEO67NrGsOE2A',
    monthlyPriceId: 'price_1RUPvjIiRJwYTHShn0MvI3Di',
    yearlyPriceId: null
  },
  pro: {
    productId: 'prod_SPEOF0qBkDy8q5',
    monthlyPriceId: 'price_1RUPvkIiRJwYTHShfvUeOs4s',
    yearlyPriceId: 'price_1RUPvkIiRJwYTHShPeagKyVo'
  },
  enterprise: {
    productId: 'prod_SPEOMdhOH8OqVd',
    monthlyPriceId: null,
    yearlyPriceId: null
  }
};

async function updateDatabaseWithStripeIds() {
  console.log('🗄️  Updating database with new Stripe IDs...');

  // Check if POSTGRES_URL is set
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not set');
  }

  console.log('Connecting to database...');

  // Create a new connection to the database
  const migrationClient = postgres(process.env.POSTGRES_URL, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Update each plan with new Stripe IDs
    for (const [planName, stripeData] of Object.entries(STRIPE_IDS)) {
      console.log(`Updating ${planName} plan...`);
      
      await db.execute(sql`
        UPDATE plans SET
          stripe_product_id = ${stripeData.productId},
          stripe_price_id_monthly = ${stripeData.monthlyPriceId},
          stripe_price_id_yearly = ${stripeData.yearlyPriceId},
          updated_at = NOW()
        WHERE name = ${planName}
      `);
      
      console.log(`✅ Updated ${planName} plan with new Stripe IDs`);
    }

    console.log('\n🎉 Database updated successfully with new Stripe IDs!');
    
    // Verify the updates
    console.log('\n🔍 Verifying updates...');
    const plans = await db.execute(sql`
      SELECT name, stripe_product_id, stripe_price_id_monthly, stripe_price_id_yearly 
      FROM plans 
      WHERE name IN ('starter', 'pro', 'enterprise')
      ORDER BY name
    `);
    
    console.log('\nCurrent database state:');
    plans.forEach((plan: any) => {
      console.log(`${plan.name}:`);
      console.log(`  Product ID: ${plan.stripe_product_id || 'NULL'}`);
      console.log(`  Monthly Price ID: ${plan.stripe_price_id_monthly || 'NULL'}`);
      console.log(`  Yearly Price ID: ${plan.stripe_price_id_yearly || 'NULL'}`);
    });

  } catch (error) {
    console.error('❌ Error updating database:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    console.log('\nClosing database connection...');
    await migrationClient.end();
  }
}

// Run the update
updateDatabaseWithStripeIds().catch(console.error);
