#!/usr/bin/env tsx

/**
 * Seed pricing plans script
 * Industry best practice: Separate seeding script for database initialization
 */

import { seedPricingPlans } from '../lib/db/seeders/pricing-plans';

async function main() {
  console.log('🚀 Starting pricing plans seeding...');
  
  try {
    await seedPricingPlans();
    console.log('✅ Pricing plans seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding pricing plans:', error);
    process.exit(1);
  }
}

// Run the script
main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
