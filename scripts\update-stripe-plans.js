require('dotenv').config();
const Stripe = require('stripe');
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function main() {
  try {
    console.log('Starting Stripe plans update...');

    // 1. Archive all existing products
    console.log('Fetching existing products...');
    const existingProducts = await stripe.products.list({ limit: 100, active: true });
    
    console.log(`Found ${existingProducts.data.length} active products. Archiving them...`);
    for (const product of existingProducts.data) {
      console.log(`Archiving product: ${product.id} (${product.name})`);
      await stripe.products.update(product.id, { active: false });
    }
    console.log('All existing products archived successfully.');

    // 2. Create new products and prices for Starter and Pro plans
    console.log('Creating new products and prices...');

    // Create Starter plan
    const starterProduct = await stripe.products.create({
      name: 'Starter Plan',
      description: 'Ideal for early explorers and small teams',
      metadata: {
        plan_type: 'starter',
        max_users: '1',
      },
    });
    console.log(`Created Starter product: ${starterProduct.id}`);

    // Create price for Starter plan
    const starterPrice = await stripe.prices.create({
      product: starterProduct.id,
      unit_amount: 1200, // $12.00
      currency: 'usd',
      recurring: {
        interval: 'month',
      },
      metadata: {
        plan_type: 'starter',
      },
    });
    console.log(`Created Starter price: ${starterPrice.id} ($12/month)`);

    // Create Pro plan
    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'Ideal for teams ready to build strategic AI capabilities',
      metadata: {
        plan_type: 'pro',
        max_users: '3',
      },
    });
    console.log(`Created Pro product: ${proProduct.id}`);

    // Create price for Pro plan
    const proPrice = await stripe.prices.create({
      product: proProduct.id,
      unit_amount: 6000, // $60.00
      currency: 'usd',
      recurring: {
        interval: 'month',
      },
      metadata: {
        plan_type: 'pro',
      },
    });
    console.log(`Created Pro price: ${proPrice.id} ($60/month)`);

    // 3. Update the database with the new product and price IDs
    console.log('\nPlease update your application with the following IDs:');
    console.log('Starter Plan:');
    console.log(`- Product ID: ${starterProduct.id}`);
    console.log(`- Price ID: ${starterPrice.id}`);
    console.log('\nPro Plan:');
    console.log(`- Product ID: ${proProduct.id}`);
    console.log(`- Price ID: ${proPrice.id}`);

    console.log('\nStripe plans update completed successfully!');
  } catch (error) {
    console.error('Error updating Stripe plans:', error);
  }
}

main();
