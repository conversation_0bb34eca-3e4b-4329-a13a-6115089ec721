import { NextResponse } from 'next/server';
import { cleanupTempFiles } from '@/lib/utils/cleanup-temp-files';
import { getSession } from '@/lib/auth/session';

export async function POST(req: Request) {
  try {
    // Only allow authenticated users with owner role
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    
    // Check if user has owner role
    if (session.user.role !== 'owner') {
      return new NextResponse('Forbidden - Requires owner role', { status: 403 });
    }

    // Get max age from request body
    const body = await req.json();
    const maxAgeInDays = body.maxAgeInDays || 7;
    
    // Run cleanup
    const result = await cleanupTempFiles(maxAgeInDays);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in cleanup endpoint:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
