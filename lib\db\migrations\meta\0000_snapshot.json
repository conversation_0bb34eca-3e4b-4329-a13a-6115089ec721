{"id": "9dd32d85-ee63-439d-8708-1d15d08b76c9", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.activity_logs": {"name": "activity_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"activity_logs_team_id_teams_id_fk": {"name": "activity_logs_team_id_teams_id_fk", "tableFrom": "activity_logs", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "activity_logs_user_id_users_id_fk": {"name": "activity_logs_user_id_users_id_fk", "tableFrom": "activity_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.case_studies": {"name": "case_studies", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "use_case_title": {"name": "use_case_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "industry": {"name": "industry", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "vector": {"name": "vector", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "potentially_impacted_kpis": {"name": "potentially_impacted_kpis", "type": "text", "primaryKey": false, "notNull": false}, "introduction_title": {"name": "introduction_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "introduction_text": {"name": "introduction_text", "type": "text", "primaryKey": false, "notNull": false}, "transition_to_challange": {"name": "transition_to_challange", "type": "text", "primaryKey": false, "notNull": false}, "challange_1": {"name": "challange_1", "type": "text", "primaryKey": false, "notNull": false}, "challange_2": {"name": "challange_2", "type": "text", "primaryKey": false, "notNull": false}, "challange_3": {"name": "challange_3", "type": "text", "primaryKey": false, "notNull": false}, "transition_to_questions": {"name": "transition_to_questions", "type": "text", "primaryKey": false, "notNull": false}, "question_1": {"name": "question_1", "type": "text", "primaryKey": false, "notNull": false}, "question_2": {"name": "question_2", "type": "text", "primaryKey": false, "notNull": false}, "question_3": {"name": "question_3", "type": "text", "primaryKey": false, "notNull": false}, "process_section_title": {"name": "process_section_title", "type": "text", "primaryKey": false, "notNull": false}, "process_step_1": {"name": "process_step_1", "type": "text", "primaryKey": false, "notNull": false}, "process_step_2": {"name": "process_step_2", "type": "text", "primaryKey": false, "notNull": false}, "process_step_3": {"name": "process_step_3", "type": "text", "primaryKey": false, "notNull": false}, "solution_section_title": {"name": "solution_section_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_1": {"name": "solution_1", "type": "text", "primaryKey": false, "notNull": false}, "solution_2": {"name": "solution_2", "type": "text", "primaryKey": false, "notNull": false}, "solution_3": {"name": "solution_3", "type": "text", "primaryKey": false, "notNull": false}, "solution_4": {"name": "solution_4", "type": "text", "primaryKey": false, "notNull": false}, "solution_5": {"name": "solution_5", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_section_title": {"name": "potential_impact_section_title", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_1_qualitative": {"name": "potential_impact_1_qualitative", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_1_quantitative": {"name": "potential_impact_1_quantitative", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_2_qualitative": {"name": "potential_impact_2_qualitative", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_2_quantitative": {"name": "potential_impact_2_quantitative", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_3_qualitative": {"name": "potential_impact_3_qualitative", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_3_quantitative": {"name": "potential_impact_3_quantitative", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_4_qualitative": {"name": "potential_impact_4_qualitative", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_4_quantitative": {"name": "potential_impact_4_quantitative", "type": "text", "primaryKey": false, "notNull": false}, "conclusion_title": {"name": "conclusion_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "conclusion_text": {"name": "conclusion_text", "type": "text", "primaryKey": false, "notNull": false}, "conclusion_result": {"name": "conclusion_result", "type": "text", "primaryKey": false, "notNull": false}, "feature_image_url": {"name": "feature_image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "preview_image_url": {"name": "preview_image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.case_study_icons": {"name": "case_study_icons", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "case_study_id": {"name": "case_study_id", "type": "integer", "primaryKey": false, "notNull": true}, "icon_type": {"name": "icon_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "icon_url": {"name": "icon_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"case_study_icons_case_study_id_case_studies_id_fk": {"name": "case_study_icons_case_study_id_case_studies_id_fk", "tableFrom": "case_study_icons", "tableTo": "case_studies", "columnsFrom": ["case_study_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "integer", "primaryKey": false, "notNull": true}, "invited_at": {"name": "invited_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}}, "indexes": {}, "foreignKeys": {"invitations_team_id_teams_id_fk": {"name": "invitations_team_id_teams_id_fk", "tableFrom": "invitations", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitations_invited_by_users_id_fk": {"name": "invitations_invited_by_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_members": {"name": "team_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"team_members_user_id_users_id_fk": {"name": "team_members_user_id_users_id_fk", "tableFrom": "team_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "team_members_team_id_teams_id_fk": {"name": "team_members_team_id_teams_id_fk", "tableFrom": "team_members", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_product_id": {"name": "stripe_product_id", "type": "text", "primaryKey": false, "notNull": false}, "plan_name": {"name": "plan_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subscription_status": {"name": "subscription_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"teams_stripe_customer_id_unique": {"name": "teams_stripe_customer_id_unique", "nullsNotDistinct": false, "columns": ["stripe_customer_id"]}, "teams_stripe_subscription_id_unique": {"name": "teams_stripe_subscription_id_unique", "nullsNotDistinct": false, "columns": ["stripe_subscription_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'member'"}, "industry": {"name": "industry", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "agreed_to_terms": {"name": "agreed_to_terms", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}