/**
 * Correct Stripe Setup Script
 * Creates plans based on YOUR actual pricing structure
 */

import Stripe from 'stripe';
import * as dotenv from 'dotenv';
import * as readline from 'readline';

// Load environment variables
dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

function askConfirmation(question: string): Promise<boolean> {
  return new Promise((resolve) => {
    rl.question(`${question} (y/N): `, (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function main(): Promise<void> {
  console.log('🎯 Correct Stripe Plan Setup');
  console.log('=============================\n');
  
  console.log('This script will create the CORRECT plan structure:');
  console.log('📋 Starter Plan: 14-day free trial');
  console.log('📋 Pro Plan: Paid subscription');
  console.log('📋 Enterprise: Contact Us (no Stripe product)\n');

  // Get Pro plan pricing
  console.log('💰 Let\'s set up your Pro plan pricing:\n');
  
  const proMonthlyPrice = await askQuestion('What is your Pro plan monthly price in USD? (e.g., 29.00): $');
  const proYearlyPrice = await askQuestion('What is your Pro plan yearly price in USD? (e.g., 290.00): $');
  
  const monthlyAmount = Math.round(parseFloat(proMonthlyPrice) * 100); // Convert to cents
  const yearlyAmount = Math.round(parseFloat(proYearlyPrice) * 100);   // Convert to cents
  
  console.log(`\n✅ Pro Plan Pricing:`);
  console.log(`   Monthly: $${(monthlyAmount / 100).toFixed(2)}`);
  console.log(`   Yearly: $${(yearlyAmount / 100).toFixed(2)}`);
  
  const confirmPricing = await askConfirmation('\nIs this pricing correct?');
  if (!confirmPricing) {
    console.log('❌ Setup cancelled. Please run the script again with correct pricing.');
    rl.close();
    return;
  }

  // Show what will be created
  console.log('\n🆕 Plans to be created:');
  console.log('=======================');
  console.log('1. Starter Plan');
  console.log('   - 14-day free trial');
  console.log('   - $0.00/month after trial (or convert to Pro)');
  console.log('');
  console.log('2. Pro Plan');
  console.log(`   - $${(monthlyAmount / 100).toFixed(2)}/month`);
  console.log(`   - $${(yearlyAmount / 100).toFixed(2)}/year`);
  console.log('');
  console.log('3. Enterprise Plan');
  console.log('   - Contact Us (no Stripe product needed)');
  console.log('');

  // Check for existing subscriptions
  console.log('🔍 Checking for active subscriptions...');
  const subscriptions = await stripe.subscriptions.list({ status: 'active', limit: 100 });
  
  if (subscriptions.data.length > 0) {
    console.log(`⚠️  WARNING: Found ${subscriptions.data.length} active subscriptions!`);
    console.log('   These will need to be migrated to the new plans.');
    subscriptions.data.forEach((sub, index) => {
      console.log(`   ${index + 1}. ${sub.id} (Customer: ${sub.customer})`);
    });
    console.log('');
    
    const continueWithActive = await askConfirmation('Do you want to continue anyway?');
    if (!continueWithActive) {
      console.log('❌ Setup cancelled. Please handle active subscriptions first.');
      rl.close();
      return;
    }
  }

  // Final confirmation
  const finalConfirm = await askConfirmation('🚨 This will ARCHIVE all existing Stripe products and create new ones. Continue?');
  if (!finalConfirm) {
    console.log('❌ Setup cancelled.');
    rl.close();
    return;
  }

  console.log('\n🔄 Starting setup...\n');

  try {
    // Step 1: Archive existing products and prices
    console.log('1️⃣ Archiving existing products and prices...');
    
    const existingPrices = await stripe.prices.list({ limit: 100 });
    for (const price of existingPrices.data) {
      if (price.active) {
        await stripe.prices.update(price.id, { active: false });
        console.log(`   ✅ Archived price: ${price.id}`);
      }
    }

    const existingProducts = await stripe.products.list({ limit: 100 });
    for (const product of existingProducts.data) {
      if (product.active) {
        await stripe.products.update(product.id, { active: false });
        console.log(`   ✅ Archived product: ${product.name} (${product.id})`);
      }
    }

    // Step 2: Create Starter Plan
    console.log('\n2️⃣ Creating Starter Plan...');
    
    const starterProduct = await stripe.products.create({
      name: 'Starter Plan',
      description: 'Perfect for individuals getting started - 14 day free trial',
      metadata: {
        plan_type: 'starter',
        trial_days: '14',
        created_by: 'morphx-correct-setup'
      }
    });
    console.log(`   ✅ Created Starter product: ${starterProduct.id}`);

    const starterPrice = await stripe.prices.create({
      product: starterProduct.id,
      currency: 'usd',
      unit_amount: 0,
      recurring: {
        interval: 'month',
        trial_period_days: 14
      },
      metadata: {
        plan_type: 'starter',
        billing_interval: 'trial'
      }
    });
    console.log(`   ✅ Created Starter price: Free with 14-day trial (${starterPrice.id})`);

    // Step 3: Create Pro Plan
    console.log('\n3️⃣ Creating Pro Plan...');
    
    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'For teams and businesses ready to scale with AI',
      metadata: {
        plan_type: 'pro',
        created_by: 'morphx-correct-setup'
      }
    });
    console.log(`   ✅ Created Pro product: ${proProduct.id}`);

    const proMonthlyPriceObj = await stripe.prices.create({
      product: proProduct.id,
      currency: 'usd',
      unit_amount: monthlyAmount,
      recurring: { interval: 'month' },
      metadata: {
        plan_type: 'pro',
        billing_interval: 'monthly'
      }
    });
    console.log(`   ✅ Created Pro monthly price: $${(monthlyAmount / 100).toFixed(2)} (${proMonthlyPriceObj.id})`);

    const proYearlyPriceObj = await stripe.prices.create({
      product: proProduct.id,
      currency: 'usd',
      unit_amount: yearlyAmount,
      recurring: { interval: 'year' },
      metadata: {
        plan_type: 'pro',
        billing_interval: 'yearly'
      }
    });
    console.log(`   ✅ Created Pro yearly price: $${(yearlyAmount / 100).toFixed(2)} (${proYearlyPriceObj.id})`);

    // Step 4: Generate environment variables
    console.log('\n4️⃣ Environment Variables:');
    console.log('=========================');
    console.log('Add these to your .env file:\n');
    
    console.log('# Stripe Product IDs');
    console.log(`STRIPE_STARTER_PRODUCT_ID=${starterProduct.id}`);
    console.log(`STRIPE_PRO_PRODUCT_ID=${proProduct.id}`);
    console.log('');
    console.log('# Stripe Price IDs');
    console.log(`STRIPE_STARTER_TRIAL_PRICE_ID=${starterPrice.id}`);
    console.log(`STRIPE_PRO_MONTHLY_PRICE_ID=${proMonthlyPriceObj.id}`);
    console.log(`STRIPE_PRO_YEARLY_PRICE_ID=${proYearlyPriceObj.id}`);

    console.log('\n✅ Setup completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Copy the environment variables above to your .env file');
    console.log('2. Remove any old Stripe environment variables');
    console.log('3. Restart your application');
    console.log('4. Test the new plans in your billing page');
    console.log('5. Update your pricing page to show only Starter and Pro plans');
    console.log('6. Set Enterprise to "Contact Us" button');

    if (subscriptions.data.length > 0) {
      console.log('\n⚠️  Don\'t forget to migrate your active subscriptions!');
    }

  } catch (error: any) {
    console.error('\n❌ Error during setup:', error.message);
  } finally {
    rl.close();
  }
}

// Run the script
if (require.main === module) {
  main();
}

export { main as setupCorrectPlans };
