// Force dynamic rendering since we use cookies
export const dynamic = 'force-dynamic';

import { db } from '@/lib/db/drizzle';
import { bookmarks, caseStudies } from '@/lib/db/schema';
import { eq, isNull } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';
import { redirect } from 'next/navigation';
import BookmarksClientPage from './client-page';

export default async function BookmarksPage() {
  // Get the current user
  const user = await getUser();

  // Route protection is now handled by the layout

  // Fetch bookmarked case studies if the table exists and user is logged in
  let userBookmarks = [];
  let validBookmarks:any = [];

  if (user && user.id) {
    try {
      userBookmarks = await db.query.bookmarks.findMany({
        where: eq(bookmarks.userId, user.id),
        with: {
          caseStudy: {
            // where: isNull(caseStudies.deletedAt),
            with: {
              icons: true,
            },
          },
        },
      });

      // Filter out any bookmarks where the case study is null (deleted)
      validBookmarks = userBookmarks.filter(bookmark => bookmark.caseStudy);

      // Log the bookmarks for debugging
      console.log('Bookmarks found:', validBookmarks.length);
      validBookmarks.forEach((bookmark, index) => {
        console.log(`Bookmark ${index + 1}:`, {
          id: bookmark.id,
          caseStudyId: bookmark.caseStudyId,
          title: bookmark.caseStudy.useCaseTitle,
          featureImageUrl: bookmark.caseStudy.featureImageUrl,
          previewImageUrl: bookmark.caseStudy.previewImageUrl,
          icons: bookmark.caseStudy.icons?.map(icon => ({
            type: icon.iconType,
            url: icon.iconUrl
          }))
        });
      });
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
      // If the bookmarks table doesn't exist yet, we'll just use an empty array
    }
  } else {
    console.log('No user ID available, skipping bookmark fetch');
  }

  return <BookmarksClientPage initialBookmarks={validBookmarks} />;
}
