'use client';

import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/sections/Homepage/Footer';

const FAQPage = () => {
  const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);

  const faqs = [
    {
      question: "What is Turinos?",
      answer: "Turinos is an AI-powered platform that helps businesses transform their operations through data-driven insights and strategic recommendations. We provide comprehensive tools for business analysis, AI implementation, and strategic decision-making."
    },
    {
      question: "How can Turinos benefit my business?",
      answer: "Turinos offers tailored solutions that help you identify opportunities, optimize operations, and make data-driven decisions. Our platform analyzes your business context and provides actionable insights, case studies, and implementation strategies specific to your industry and goals."
    },
    {
      question: "What industries does Turinos support?",
      answer: "Turinos supports a wide range of industries including technology, finance, healthcare, retail, manufacturing, and more. Our AI models are trained on diverse datasets and can adapt to specific industry requirements and regulations."
    },
    {
      question: "How do I get started with Turinos?",
      answer: "Getting started is simple. Sign up for an account, complete your business profile, and our platform will immediately begin analyzing your needs. Our onboarding process guides you through setting up your dashboard and accessing relevant insights."
    },
    {
      question: "What kind of support do you offer?",
      answer: "We offer 24/7 technical support, dedicated account managers for enterprise clients, comprehensive documentation, video tutorials, and regular webinars. Our team is always available to help you maximize the value from our platform."
    },
    {
      question: "Is my data secure with Turinos?",
      answer: "Yes, we take data security very seriously. We employ industry-standard encryption, regular security audits, and comply with major data protection regulations. Your data is stored securely and never shared without your explicit permission."
    },
    {
      question: "What are the pricing options?",
      answer: "We offer flexible pricing tiers including Starter, Professional, and Enterprise plans. Each plan is designed to scale with your needs. Visit our pricing page for detailed information about features and costs."
    }
  ];

  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-6 lg:px-8 max-w-4xl">
        <h1 className="text-4xl font-bold text-center mb-4">Frequently Asked Questions</h1>
        <p className="text-gray-600 text-center mb-16 text-lg">Find answers to common questions about Turinos and how it can help your business</p>

        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <div key={index} className="overflow-hidden">
              <div
                className="bg-white text-gray-900 p-8 rounded-t-2xl hover:bg-gray-50 transition-colors duration-300 cursor-pointer shadow-sm"
                style={{ borderRadius: openFaqIndex === index ? '1rem 1rem 0 0' : '1rem' }}
                onClick={() => setOpenFaqIndex(openFaqIndex === index ? null : index)}
              >
                <div className="flex justify-between items-center w-full text-lg">
                  <span className="font-medium">{faq.question}</span>
                  <span className="text-2xl ml-4">
                    {openFaqIndex === index ?
                      <Minus className="h-5 w-5 flex-shrink-0 text-gray-400" /> :
                      <Plus className="h-5 w-5 flex-shrink-0 text-gray-400" />
                    }
                  </span>
                </div>
              </div>
              <div
                className="bg-white text-gray-600 overflow-hidden transition-all duration-300 ease-in-out shadow-sm"
                style={{
                  maxHeight: openFaqIndex === index ? '500px' : '0',
                  opacity: openFaqIndex === index ? 1 : 0,
                  padding: openFaqIndex === index ? '1.5rem 2rem' : '0 2rem',
                  borderRadius: '0 0 1rem 1rem'
                }}
              >
                <p className="text-lg leading-relaxed">{faq.answer}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
      </main>
      <Footer />
    </>
  );
};

export default FAQPage;
