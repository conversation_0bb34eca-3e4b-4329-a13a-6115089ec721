'use client';

import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload, FileText, Image as LucideImage, Download } from 'lucide-react';
import NextImage from 'next/image';

export function CaseStudyUpload() {
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [featureImage, setFeatureImage] = useState<File | null>(null);
  const [previewImage, setPreviewImage] = useState<File | null>(null);
  const [processIcons, setProcessIcons] = useState<File[]>([]);
  const [solutionIcons, setSolutionIcons] = useState<File[]>([]);
  const [impactIcons, setImpactIcons] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, setter: (file: File | null) => void) => {
    const file = e.target.files?.[0] || null;
    setter(file);
  };

  const handleMultipleFilesChange = (e: React.ChangeEvent<HTMLInputElement>, setter: (files: File[]) => void) => {
    const files = Array.from(e.target.files || []);
    setter(files);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/case-studies/upload-csv', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      toast({
        title: 'Success',
        description: 'Case studies imported successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to import case studies',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownloadSample = async () => {
    try {
      const response = await fetch('/api/case-studies/sample-csv');
      if (!response.ok) throw new Error('Download failed');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'sample_case_study.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to download sample CSV',
        variant: 'destructive',
      });
    }
  };

  const handleUpload = async () => {
    if (!csvFile) {
      toast({
        title: 'Error',
        description: 'Please select a CSV file',
        variant: 'destructive',
      });
      return;
    }

    setIsUploading(true);
    const formData = new FormData();
    formData.append('csv', csvFile);
    if (featureImage) formData.append('featureImage', featureImage);
    if (previewImage) formData.append('previewImage', previewImage);
    processIcons.forEach((file, index) => formData.append('processIcons', file));
    solutionIcons.forEach((file, index) => formData.append('solutionIcons', file));
    impactIcons.forEach((file, index) => formData.append('impactIcons', file));

    try {
      const response = await fetch('/api/case-studies/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Upload failed');
      }

      toast({
        title: 'Success',
        description: `Successfully imported ${data.imported} case studies`,
      });

      // Reset form
      setCsvFile(null);
      setFeatureImage(null);
      setPreviewImage(null);
      setProcessIcons([]);
      setSolutionIcons([]);
      setImpactIcons([]);
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Upload failed',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Logo and CSV Buttons Section */}
      <div className="flex flex-col items-center space-y-6 mb-8">
        <div className="w-32 h-12 relative">
          <NextImage
            src="/path4ai-logo.png"
            alt="Path4AI Logo"
            fill
            style={{ objectFit: 'contain' }}
            priority
          />
        </div>
        <div className="flex flex-col items-center gap-4 w-full max-w-2xl">
          <input
            type="file"
            accept=".csv"
            onChange={handleFileUpload}
            className="hidden"
            id="csv-upload"
            disabled={isUploading}
          />
          <Button
            variant="default"
            onClick={() => document.getElementById('csv-upload')?.click()}
            disabled={isUploading}
            className="w-full max-w-md"
          >
            <Upload className="mr-2 h-4 w-4" />
            {isUploading ? 'Uploading...' : 'Import Case Studies'}
          </Button>
          
          <Button
            variant="outline"
            onClick={handleDownloadSample}
            className="w-full max-w-md"
          >
            <Download className="mr-2 h-4 w-4" />
            Download Sample CSV
          </Button>
        </div>
      </div>

      {/* Main Upload Form */}
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Upload Case Study</CardTitle>
          <CardDescription>
            Upload a CSV file containing case study data and associated images
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="featureImage">Feature Image</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="featureImage"
                type="file"
                accept="image/*"
                onChange={(e) => handleFileChange(e, setFeatureImage)}
                className="flex-1"
              />
              {featureImage && (
                <LucideImage className="h-5 w-5 text-green-500" />
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="previewImage">Preview Image</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="previewImage"
                type="file"
                accept="image/*"
                onChange={(e) => handleFileChange(e, setPreviewImage)}
                className="flex-1"
              />
              {previewImage && (
                <LucideImage className="h-5 w-5 text-green-500" />
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Process Icons (max 3)</Label>
            <div className="flex items-center space-x-2">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleMultipleFilesChange(e, setProcessIcons)}
                className="flex-1"
              />
              {processIcons.length > 0 && (
                <LucideImage className="h-5 w-5 text-green-500" />
              )}
            </div>
            {processIcons.length > 0 && (
              <div className="text-sm text-gray-500">
                {processIcons.length} file(s) selected
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label>Solution Icons (max 5)</Label>
            <div className="flex items-center space-x-2">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleMultipleFilesChange(e, setSolutionIcons)}
                className="flex-1"
              />
              {solutionIcons.length > 0 && (
                <LucideImage className="h-5 w-5 text-green-500" />
              )}
            </div>
            {solutionIcons.length > 0 && (
              <div className="text-sm text-gray-500">
                {solutionIcons.length} file(s) selected
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label>Impact Icons (max 4)</Label>
            <div className="flex items-center space-x-2">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleMultipleFilesChange(e, setImpactIcons)}
                className="flex-1"
              />
              {impactIcons.length > 0 && (
                <LucideImage className="h-5 w-5 text-green-500" />
              )}
            </div>
            {impactIcons.length > 0 && (
              <div className="text-sm text-gray-500">
                {impactIcons.length} file(s) selected
              </div>
            )}
          </div>

          <Button
            onClick={handleUpload}
            disabled={isUploading || !csvFile}
            className="w-full"
          >
            {isUploading ? (
              <>
                <Upload className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload Case Study
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
} 