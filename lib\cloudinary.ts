// This file is for server-side Cloudinary operations only
// For client-side operations, use cloudinary-browser.ts

// Import Cloudinary only on the server side
let cloudinary: any;

// Only import and configure Cloudinary on the server side
if (typeof window === 'undefined') {
  // We're on the server
  try {
    // Dynamic import to avoid client-side loading
    cloudinary = require('cloudinary').v2;

    // Configure Cloudinary
    cloudinary.config({
      cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'daf8hefrg',
      api_key: process.env.CLOUDINARY_API_KEY || '614559198916115',
      api_secret: process.env.CLOUDINARY_API_SECRET || 'OV5jQ9RurrjEubiLdOTpUB0VMFo',
    });

    // Log Cloudinary configuration for debugging
    console.log('\n========== CLOUDINARY CONFIG ==========');
    console.log(`Cloud name: ${cloudinary.config().cloud_name}`);
    console.log(`API key: ${cloudinary.config().api_key}`);
    console.log(`API secret: ${cloudinary.config().api_secret ? '***' + cloudinary.config().api_secret.toString().substring(cloudinary.config().api_secret.toString().length - 4) : 'Not set'}`);
    console.log('========== CLOUDINARY CONFIG END ==========\n');
  } catch (error) {
    console.error('Failed to initialize Cloudinary:', error);
  }
} else {
  // We're on the client side - don't use this file
  console.warn('cloudinary.ts should not be imported on the client side');
}

/**
 * Uploads a file to Cloudinary using an unsigned upload preset
 * @param file The file buffer to upload
 * @param options Upload options
 * @returns The Cloudinary URL of the uploaded file
 */
export async function uploadToCloudinary(
  file: Buffer,
  options: {
    folder?: string;
    filename?: string;
    resourceType?: 'image' | 'video' | 'raw' | 'auto';
  } = {}
): Promise<string> {
  console.log('\n========== CLOUDINARY UPLOAD ==========');

  // Determine the resource type
  const resourceType = options.resourceType || 'image';

  console.log('Uploading to Cloudinary with unsigned upload preset...');

  // Prepare the public_id if filename is provided
  let publicId: string | undefined;
  if (options.filename) {
    // Remove file extension and replace spaces with underscores
    publicId = options.filename
      .replace(/\.[^/.]+$/, '') // Remove extension
      .replace(/\s+/g, '_'); // Replace spaces with underscores
  }

  // Use Cloudinary's Node.js SDK for server-side uploads
  return new Promise<string>((resolve, reject) => {
    // Create upload stream with options
    const uploadOptions: any = {
      folder: options.folder || 'case-studies',
      resource_type: resourceType,
      upload_preset: 'Path4ai', // Use your upload preset name
    };

    // Add public_id if available
    if (publicId) {
      uploadOptions.public_id = publicId;
    }

    console.log('Upload options:', uploadOptions);

    // Create upload stream
    const uploadStream = cloudinary.uploader.upload_stream(
      uploadOptions,
      (error: any, result: any) => {
        if (error) {
          console.error('Error in upload stream:', error);
          reject(error);
          return;
        }

        if (!result) {
          reject(new Error('No result from Cloudinary upload'));
          return;
        }

        console.log('Upload result:', {
          url: result.secure_url,
          public_id: result.public_id,
          format: result.format,
          bytes: result.bytes
        });

        console.log('✅ File uploaded to Cloudinary successfully');
        console.log(`URL: ${result.secure_url}`);
        console.log(`Public ID: ${result.public_id}`);
        console.log(`Resource type: ${result.resource_type}`);
        console.log(`Format: ${result.format}`);
        console.log(`Size: ${result.bytes} bytes`);
        console.log('========== CLOUDINARY UPLOAD COMPLETE ==========\n');

        resolve(result.secure_url);
      }
    );

    // Write buffer to stream
    uploadStream.write(file);
    uploadStream.end();
  });
}

/**
 * Checks if a URL is a Cloudinary URL
 */
export function isCloudinaryUrl(url: string): boolean {
  if (!url) return false;

  const cloudName = process.env.CLOUDINARY_CLOUD_NAME || 'daf8hefrg';
  return url.includes(`res.cloudinary.com/${cloudName}/`);
}

/**
 * Gets the public ID from a Cloudinary URL
 */
export function getPublicIdFromUrl(url: string): string | null {
  if (!isCloudinaryUrl(url)) return null;

  // Extract the public ID from the URL
  // Format: https://res.cloudinary.com/cloud-name/image/upload/v1234567890/folder/public-id.ext
  const regex = /\/v\d+\/(.+)$/;
  const match = url.match(regex);

  if (match && match[1]) {
    // Remove file extension
    return match[1].replace(/\.[^/.]+$/, '');
  }

  return null;
}

/**
 * Deletes a file from Cloudinary
 * @param url The Cloudinary URL of the file to delete
 */
export async function deleteFromCloudinary(url: string): Promise<boolean> {
  try {
    const publicId = getPublicIdFromUrl(url);
    if (!publicId) {
      console.error('❌ Invalid Cloudinary URL:', url);
      return false;
    }

    console.log('\n========== CLOUDINARY DELETE ==========');
    console.log(`Deleting file with public ID: ${publicId}`);

    const result = await cloudinary.uploader.destroy(publicId);

    console.log('Delete result:', result);
    console.log('========== CLOUDINARY DELETE COMPLETE ==========\n');

    return result.result === 'ok';
  } catch (error) {
    console.error('❌ Error deleting from Cloudinary:', error);
    return false;
  }
}

/**
 * Generates a Cloudinary URL with transformations
 * @param url The original Cloudinary URL
 * @param transformations The transformations to apply
 */
export function generateTransformedUrl(url: string, transformations: string[]): string {
  if (!isCloudinaryUrl(url)) return url;

  // Find the upload part in the URL
  const uploadIndex = url.indexOf('/upload/');
  if (uploadIndex === -1) return url;

  // Insert transformations after /upload/
  const transformationString = transformations.join(',');
  const transformedUrl = url.slice(0, uploadIndex + 8) +
                         transformationString + '/' +
                         url.slice(uploadIndex + 8);

  return transformedUrl;
}

/**
 * Generates a responsive image URL for different screen sizes
 * @param url The original Cloudinary URL
 * @param width The desired width
 */
export function getResponsiveImageUrl(url: string, width: number): string {
  return generateTransformedUrl(url, [`w_${width}`]);
}
