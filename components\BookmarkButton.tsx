'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { BookmarkIcon } from '@radix-ui/react-icons';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

interface BookmarkButtonProps {
  caseStudyId: number;
  initialIsBookmarked: boolean;
  compact?: boolean;
}

export function BookmarkButton({
  caseStudyId,
  initialIsBookmarked,
  compact = false,
}: BookmarkButtonProps) {
  const [isBookmarked, setIsBookmarked] = useState(initialIsBookmarked);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const toggleBookmark = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsLoading(true);
    try {
      const action = isBookmarked ? 'DELETE' : 'POST';
      const response = await fetch('/api/bookmarks', {
        method: action,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ caseStudyId }),
      });

      if (response.ok) {
        setIsBookmarked(!isBookmarked);
        toast({
          title: isBookmarked ? 'Bookmark removed' : 'Bookmark added',
          description: isBookmarked
            ? 'Case study removed from bookmarks'
            : 'Case study added to bookmarks',
        });
        router.refresh();
      } else if (response.status === 401) {
        console.error('User not authenticated');
        toast({
          title: 'Authentication Required',
          description: 'Please sign in to bookmark case studies',
          variant: 'destructive',
        });
        // Optionally redirect to login
        // router.push('/sign-in');
      } else {
        console.error('Failed to toggle bookmark', await response.text());
        toast({
          title: 'Error',
          description: 'Failed to update bookmark',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while updating bookmark',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (compact) {
    return (
      <Button
        onClick={toggleBookmark}
        disabled={isLoading}
        variant={isBookmarked ? 'default' : 'secondary'}
        size='sm'
        className={`rounded-full w-8 h-8 p-0 ${
          isBookmarked ? 'bg-blue-500 text-white' : 'bg-white text-blue-500'
        }`}
      >
        {isLoading ? (
          <Loader2 className='h-4 w-4 animate-spin' />
        ) : (
          <BookmarkIcon className='h-4 w-4' />
        )}
      </Button>
    );
  }

  return (
    <Button
      onClick={toggleBookmark}
      disabled={isLoading}
      variant={isBookmarked ? 'default' : 'outline'}
      size='sm'
      className={`flex items-center gap-2 relative ${
        isBookmarked ? 'bg-blue-600 text-white' : ''
      }`}
    >
      {isLoading ? (
        <>
          <span className='opacity-0'>
            <BookmarkIcon className='h-4 w-4 mr-2' />
            {isBookmarked ? 'Bookmarked' : 'Bookmark'}
          </span>
          <span className='absolute inset-0 flex items-center justify-center'>
            <Loader2 className='h-4 w-4 animate-spin mr-2' />
            Updating...
          </span>
        </>
      ) : (
        <>
          <BookmarkIcon className='h-4 w-4 mr-2' />
          {isBookmarked ? 'Bookmarked' : 'Bookmark'}
        </>
      )}
    </Button>
  );
}
