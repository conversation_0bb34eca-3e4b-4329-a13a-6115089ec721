import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { teamMembers } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { eq, and } from 'drizzle-orm';

// POST /api/team/members/remove - Remove a team member
export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a team owner
    const userTeamMember = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, session.user.id),
        eq(teamMembers.role, 'owner')
      ),
      with: {
        team: true,
      },
    });

    if (!userTeamMember?.team) {
      return NextResponse.json({ error: 'Team not found or you are not an owner' }, { status: 403 });
    }

    const body = await req.json();
    const { memberId } = body;

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID is required' }, { status: 400 });
    }

    // Check if the member exists and belongs to the user's team
    const memberToRemove = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.id, memberId),
        eq(teamMembers.teamId, userTeamMember.team.id)
      ),
    });

    if (!memberToRemove) {
      return NextResponse.json({ error: 'Team member not found' }, { status: 404 });
    }

    // Prevent removing yourself
    if (memberToRemove.userId === session.user.id) {
      return NextResponse.json({ error: 'You cannot remove yourself from the team' }, { status: 400 });
    }

    // Remove the team member
    await db
      .delete(teamMembers)
      .where(eq(teamMembers.id, memberId));

    return NextResponse.json({ success: 'Team member removed successfully' });
  } catch (error) {
    console.error('Error removing team member:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
