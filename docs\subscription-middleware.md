# Subscription Middleware Documentation

This document explains how to use the comprehensive subscription middleware system implemented in the MorphX project.

## Overview

The subscription middleware provides:
- **Route-level protection** based on subscription status
- **Feature-level access control** based on plan limits
- **Plan usage tracking** and limit enforcement
- **Graceful error handling** with user-friendly redirects
- **Client-side hooks** for subscription management

## Architecture

### Core Components

1. **`lib/auth/subscription-middleware.ts`** - Server-side middleware functions
2. **`lib/hooks/use-subscription.ts`** - Client-side React hooks
3. **`lib/auth/with-subscription.tsx`** - Higher-order components and utilities
4. **`components/subscription/subscription-guard.tsx`** - UI components for protection
5. **`middleware.ts`** - Integration with Next.js middleware

## Plan Configuration

Plans are defined in `lib/auth/subscription-middleware.ts`:

```typescript
export const PLAN_LIMITS: PlanLimits = {
  starter: {
    maxUsers: 1,
    maxCaseStudies: 10,
    maxStorage: 100, // MB
    advancedAnalytics: false,
    apiAccess: false,
    customBranding: false,
    prioritySupport: false,
  },
  pro: {
    maxUsers: 3,
    maxCaseStudies: 100,
    maxStorage: 1000, // MB
    advancedAnalytics: true,
    apiAccess: true,
    customBranding: false,
    prioritySupport: true,
  },
  enterprise: {
    maxUsers: -1, // unlimited
    maxCaseStudies: -1, // unlimited
    maxStorage: -1, // unlimited
    advancedAnalytics: true,
    apiAccess: true,
    customBranding: true,
    prioritySupport: true,
  },
};
```

## Route Protection

### Automatic Protection

Routes are automatically protected based on patterns defined in the middleware:

```typescript
// Routes that require active subscription (members only)
const SUBSCRIPTION_REQUIRED_ROUTES = [
  '/dashboard/member',
  '/dashboard/trends',
  '/dashboard/reports',
  '/dashboard/user',
  '/dashboard/user/case-studies',
  '/dashboard/user/search',
];

// Routes that require specific plan features
// Note: Advanced analytics and API access have been removed from all plans
const FEATURE_RESTRICTED_ROUTES = {
  // '/dashboard/analytics': 'advancedAnalytics', // Feature removed from all plans
  // '/dashboard/api': 'apiAccess', // Feature removed from all plans
  '/dashboard/branding': 'customBranding',
} as const;
```

### Manual Protection

For custom route protection, use the `withSubscription` HOC:

```typescript
import { withSubscription } from '@/lib/auth/with-subscription';

const ProtectedPage = withSubscription(MyComponent, {
  requiredFeature: 'customBranding', // Note: advancedAnalytics removed from all plans
  requireActiveSubscription: true,
  redirectTo: '/dashboard/billing',
});
```

## Client-Side Usage

### Basic Subscription Hook

```typescript
import { useSubscription } from '@/lib/hooks/use-subscription';

function MyComponent() {
  const {
    hasActiveSubscription,
    planFeatures,
    planName,
    status,
    isLoading
  } = useSubscription();

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      <p>Plan: {planName}</p>
      <p>Status: {status}</p>
      <p>Has Analytics: {planFeatures.advancedAnalytics ? 'Yes' : 'No'}</p>
    </div>
  );
}
```

### Feature Access Check

```typescript
import { useFeatureAccess } from '@/lib/hooks/use-subscription';

function AnalyticsButton() {
  const { hasAccess, isLoading } = useFeatureAccess('advancedAnalytics');

  if (isLoading) return null;

  return hasAccess ? (
    <Button onClick={() => openAnalytics()}>
      View Analytics
    </Button>
  ) : (
    <Button disabled>
      Upgrade for Analytics
    </Button>
  );
}
```

### Plan Limit Checking

```typescript
import { usePlanLimit } from '@/lib/hooks/use-subscription';

function TeamManagement() {
  const userLimit = usePlanLimit('maxUsers', currentUserCount);

  return (
    <div>
      <p>Users: {userLimit.current} / {userLimit.limit}</p>
      <Progress value={userLimit.percentageUsed} />

      <Button
        disabled={!userLimit.allowed}
        onClick={addUser}
      >
        Add User
      </Button>

      {userLimit.message && (
        <Alert>{userLimit.message}</Alert>
      )}
    </div>
  );
}
```

### Protected Actions

```typescript
import { useSubscriptionAction } from '@/lib/auth/with-subscription';

function MyComponent() {
  const { checkAndExecute } = useSubscriptionAction();

  const handlePremiumAction = () => {
    checkAndExecute(
      () => {
        // This action requires advanced analytics
        performPremiumAction();
      },
      {
        requiredFeature: 'advancedAnalytics',
        onRestricted: () => {
          showUpgradeModal();
        }
      }
    );
  };

  return <Button onClick={handlePremiumAction}>Premium Action</Button>;
}
```

## UI Components

### Subscription Guard

Protects content based on subscription status:

```typescript
import { SubscriptionGuard } from '@/components/subscription/subscription-guard';

function PremiumContent() {
  return (
    <SubscriptionGuard feature="advancedAnalytics">
      <div>This content requires advanced analytics</div>
    </SubscriptionGuard>
  );
}
```

### Plan Usage Display

Shows current usage against plan limits:

```typescript
import { PlanUsage } from '@/components/subscription/subscription-guard';

function UsageDisplay() {
  return (
    <div>
      <PlanUsage
        limitType="maxUsers"
        currentCount={5}
        label="Team Members"
      />
      <PlanUsage
        limitType="maxStorage"
        currentCount={250}
        label="Storage Used"
        unit="MB"
      />
    </div>
  );
}
```

### Conditional Content

Show/hide content based on subscription:

```typescript
import { SubscriptionConditional } from '@/lib/auth/with-subscription';

function ConditionalFeatures() {
  return (
    <div>
      <SubscriptionConditional requiredFeature="apiAccess">
        <APIKeySection />
      </SubscriptionConditional>

      <SubscriptionConditional
        requiredFeature="customBranding"
        inverse
      >
        <UpgradePrompt />
      </SubscriptionConditional>
    </div>
  );
}
```

## Server-Side Usage

### Check Feature Access

```typescript
import { hasFeatureAccess } from '@/lib/auth/subscription-middleware';

export async function POST(request: NextRequest) {
  // Note: API access has been removed from all plans
  return NextResponse.json(
    { error: 'API access has been removed from all subscription plans' },
    { status: 410 } // Gone - feature no longer available
  );

  // Legacy code - keeping for reference
  /*
  const canUseAPI = await hasFeatureAccess('apiAccess');

  if (!canUseAPI) {
    return NextResponse.json(
      { error: 'API access not available in your plan' },
      { status: 403 }
    );
  }

  // Proceed with API logic
  */
}
```

### Check Plan Limits

```typescript
import { checkPlanLimit } from '@/lib/auth/subscription-middleware';

export async function POST(request: NextRequest) {
  const limitCheck = await checkPlanLimit('maxUsers', currentUserCount);

  if (!limitCheck.allowed) {
    return NextResponse.json(
      { error: limitCheck.message },
      { status: 403 }
    );
  }

  // Proceed with adding user
}
```

## Error Handling

The middleware automatically redirects users to the billing page with appropriate error parameters:

- `?error=subscription-required` - No active subscription
- `?error=feature-restricted&feature=advancedAnalytics` - Feature not in plan
- `?error=past-due` - Payment past due
- `?error=inactive` - Subscription inactive

The billing page handles these errors and displays appropriate messages.

## Best Practices

1. **Use guards for UI protection** - Wrap sensitive content in `SubscriptionGuard`
2. **Check limits before actions** - Use `usePlanLimit` for resource creation
3. **Provide upgrade paths** - Always show upgrade options when features are restricted
4. **Handle loading states** - Account for async subscription data loading
5. **Test all scenarios** - Test with different subscription states and plan types

## Testing

To test the subscription middleware:

1. **Mock subscription states** in your test environment
2. **Test route protection** by accessing protected routes without subscriptions
3. **Verify feature restrictions** work correctly for different plans
4. **Check error handling** and redirect behavior
5. **Test plan limit enforcement** with various usage scenarios

## Troubleshooting

Common issues and solutions:

1. **Middleware not triggering** - Check route patterns in `SUBSCRIPTION_REQUIRED_ROUTES`
2. **Features not restricted** - Verify feature names match `PLAN_LIMITS` keys
3. **Infinite redirects** - Ensure billing page is not in protected routes
4. **Stale subscription data** - Use the refresh functionality in hooks
5. **Database sync issues** - Check webhook handling for Stripe events
