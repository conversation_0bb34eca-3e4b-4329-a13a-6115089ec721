import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies, caseStudyIcons, bookmarks } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { and, or, isNull, sql, eq } from 'drizzle-orm';

// Type for case study with icons
type CaseStudyWithIcons = typeof caseStudies.$inferSelect & {
  icons: (typeof caseStudyIcons.$inferSelect)[];
};

// Format URL for images
function formatUrl(url: string | null): string | null {
  if (!url) return null;

  // If URL is already a full URL, return it
  if (url.startsWith('http')) return url;

  // Otherwise, it's a relative path, prepend the base URL
  return `${process.env.NEXT_PUBLIC_BASE_URL || ''}${url}`;
}

export async function GET(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(req.url);
    const { searchParams } = url;

    // Get search query
    const query = searchParams.get('q') || '';
    const limit = parseInt(searchParams.get('limit') || '8');

    console.log(
      `Inline Search API - Searching for: "${query}" (Limit: ${limit})`
    );

    if (!query) {
      // If no query is provided, return empty results
      return NextResponse.json({
        caseStudies: [],
        total: 0,
      });
    }

    // Log the search query for debugging
    console.log(`Search query: "${query}"`);

    // Create search conditions for multiple fields
    // We'll use a more flexible approach that works for both single and multi-word queries
    const searchConditions = or(
      // Exact match (case insensitive)
      sql`LOWER(${caseStudies.useCaseTitle}) LIKE LOWER(${'%' + query + '%'})`,
      sql`LOWER(${caseStudies.industry}) LIKE LOWER(${'%' + query + '%'})`,
      sql`LOWER(${caseStudies.role}) LIKE LOWER(${'%' + query + '%'})`,
      // sql`LOWER(${caseStudies.introductionText}) LIKE LOWER(${
      //   '%' + query + '%'
      // })`,
      // sql`LOWER(${caseStudies.introductionTitle}) LIKE LOWER(${
      //   '%' + query + '%'
      // })`,
      // sql`LOWER(${caseStudies.challange1}) LIKE LOWER(${'%' + query + '%'})`,
      // sql`LOWER(${caseStudies.challange2}) LIKE LOWER(${'%' + query + '%'})`,
      // sql`LOWER(${caseStudies.challange3}) LIKE LOWER(${'%' + query + '%'})`,
      // sql`LOWER(${caseStudies.processTitle}) LIKE LOWER(${'%' + query + '%'})`,
      // sql`LOWER(${caseStudies.solutionTitle}) LIKE LOWER(${'%' + query + '%'})`,
      // sql`LOWER(${caseStudies.solutionDescription}) LIKE LOWER(${
      //   '%' + query + '%'
      // })`,
      // sql`LOWER(${caseStudies.impactTitle}) LIKE LOWER(${'%' + query + '%'})`,
      // sql`LOWER(${caseStudies.conclusionTitle}) LIKE LOWER(${
      //   '%' + query + '%'
      // })`,
      // sql`LOWER(${caseStudies.conclusionText}) LIKE LOWER(${
      //   '%' + query + '%'
      // })`,

      // Also try word-by-word search for multi-word queries
      ...(query.includes(' ')
        ? query
            .toLowerCase()
            .split(/\s+/)
            .filter((word) => word.length > 0)
            .map((word) =>
              or(
                sql`LOWER(${caseStudies.useCaseTitle}) LIKE LOWER(${
                  '%' + word + '%'
                })`,
                sql`LOWER(${caseStudies.industry}) LIKE LOWER(${
                  '%' + word + '%'
                })`,
                sql`LOWER(${caseStudies.role}) LIKE LOWER(${'%' + word + '%'})`
              )
            )
        : [])
    );

    // Get total count
    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(caseStudies)
      .where(searchConditions)
      .then((result) => result[0].count);

    // Get search results with their icons
    const searchResults = (await db.query.caseStudies.findMany({
      columns: {
        id: true,
        useCaseTitle: true,
        industry: true,
        role: true,
        featureImageUrl: true,
        previewImageUrl: true,
        introductionText: true,
        marketIntelligenceData: true,
      },
      where: searchConditions,
      with: {
        icons: {
          columns: {
            iconUrl: true,
            order: true,
          },
          orderBy: (icons, { asc }) => [asc(icons.order)],
        },
      },
      limit: limit,
      orderBy: (caseStudies, { desc }) => [desc(caseStudies.createdAt)],
    })) as CaseStudyWithIcons[];

    // Get user's bookmarks
    const userBookmarks = await db.query.bookmarks.findMany({
      where: eq(bookmarks.userId, session.user.id),
    });

    // Create a set of bookmarked case study IDs for quick lookup
    const bookmarkedIds = new Set(
      userBookmarks.map((bookmark) => bookmark.caseStudyId)
    );

    // Format URLs for all case studies and add isBookmarked property
    const formattedCaseStudies = searchResults.map((caseStudy) => ({
      ...caseStudy,
      featureImageUrl: formatUrl(caseStudy.featureImageUrl as string | null),
      previewImageUrl: formatUrl(caseStudy.previewImageUrl as string | null),
      isBookmarked: bookmarkedIds.has(caseStudy.id),
      icons: caseStudy.icons.map((icon) => ({
        ...icon,
        iconUrl: formatUrl(icon.iconUrl as string | null),
      })),
    }));

    return NextResponse.json({
      caseStudies: formattedCaseStudies,
      total: totalCount,
      query,
    });
  } catch (error) {
    console.error('Error searching case studies:', error);
    console.error(
      'Error details:',
      JSON.stringify(error, Object.getOwnPropertyNames(error), 2)
    );

    // Return a more detailed error message in development
    const errorMessage =
      process.env.NODE_ENV === 'development'
        ? `Internal Error: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        : 'Internal Error';

    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
