import { setupGracefulShutdown } from './utils/graceful-shutdown';

// Track if initialization has been done
let initialized = false;

// Only run in server environment and only once
if (typeof window === 'undefined' && !initialized) {
  // Initialize graceful shutdown handlers
  setupGracefulShutdown();

  // Set the flag to prevent multiple initializations
  initialized = true;

  console.log('Server initialization completed');
}

export default function initServer() {
  // This function is intentionally empty
  // The initialization happens when the module is imported
}
