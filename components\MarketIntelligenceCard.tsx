'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Eye, Download, ExternalLink, BarChart, Globe, Lightbulb, TrendingUp } from 'lucide-react';
import { SafeImage } from './SafeImage';
import Link from 'next/link';

interface MarketMetric {
  name: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
}

interface MarketInsight {
  title: string;
  summary: string;
  source?: string;
  source_url?: string;
  type: string;
  date?: string;
}

interface MarketIntelligenceSection {
  title: string;
  description?: string;
  insights: MarketInsight[];
}

interface MarketIntelligenceCardProps {
  id: number | string;
  title: string;
  industry?: string;
  imageUrl?: string;
  marketIntelligence?: MarketIntelligenceSection[];
  marketMetrics?: Record<string, any>;
  onView?: (id: number | string) => void;
  onExport?: (id: number | string) => void;
}

export function MarketIntelligenceCard({
  id,
  title,
  industry,
  imageUrl,
  marketIntelligence = [],
  marketMetrics = {},
  onView,
  onExport
}: MarketIntelligenceCardProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // Format market metrics for display
  const formattedMetrics: MarketMetric[] = [
    {
      name: 'Market Size',
      value: marketMetrics['Market Size ($ Value)']
        ? `$${(Number(marketMetrics['Market Size ($ Value)']) / 1000000000).toFixed(1)}B`
        : 'N/A'
    },
    {
      name: 'CAGR',
      value: marketMetrics['CAGR'] ? `${marketMetrics['CAGR']}%` : 'N/A',
      trend: 'up'
    },
    {
      name: 'ROI Range',
      value: marketMetrics['ROI Range'] ? `${marketMetrics['ROI Range']}%` : 'N/A'
    },
    {
      name: 'Adoption Rate',
      value: marketMetrics['Adoption Rate'] ? `${marketMetrics['Adoption Rate']}%` : 'N/A'
    }
  ];

  // Get a sample of insights for the overview
  const sampleInsights = marketIntelligence.flatMap(section =>
    section.insights.slice(0, 1)
  ).slice(0, 2);

  const handleView = () => {
    if (onView) onView(id);
    else window.open(`/dashboard/case-studies/${id}/view`, '_blank');
  };

  const handleExport = () => {
    if (onExport) {
      onExport(id);
    } else {
      // Default export functionality - download as JSON
      window.open(`/api/case-studies/export?id=${id}`, '_blank');
    }
  };

  return (
    <Card className="overflow-hidden h-full flex flex-col">
      {/* Header with image */}
      <div className="relative h-48 w-full">
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-10" />
        <div className="absolute top-4 left-4 z-20">
          <Badge variant="secondary" className="bg-white/90 text-gray-800">
            {industry || 'General'}
          </Badge>
        </div>
        <div className="relative h-full w-full">
          <SafeImage
            src={imageUrl || '/placeholder-image.jpg'}
            alt={title}
            fill
            className="object-cover"
            fallbackSrc="/placeholder-image.jpg"
          />
        </div>
        <div className="absolute bottom-4 left-4 z-20">
          <h3 className="text-lg font-semibold text-white line-clamp-2">{title}</h3>
        </div>
      </div>

      {/* Content */}
      <div className="flex-grow flex flex-col">
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="flex-grow flex flex-col">
          <TabsList className="grid grid-cols-3 mx-4 mt-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="flex-grow flex flex-col p-4 pt-2">
            <div className="space-y-3 flex-grow">
              {marketMetrics && Object.keys(marketMetrics).length > 0 && (
                <div className="grid grid-cols-2 gap-2">
                  {formattedMetrics.slice(0, 2).map((metric, index) => (
                    <div key={index} className="bg-gray-50 p-2 rounded-md">
                      <div className="text-xs text-gray-500">{metric.name}</div>
                      <div className="font-semibold">{metric.value}</div>
                    </div>
                  ))}
                </div>
              )}

              {sampleInsights.length > 0 && (
                <div className="mt-3">
                  <h4 className="text-sm font-medium mb-2 flex items-center">
                    <Lightbulb className="h-4 w-4 mr-1 text-amber-500" />
                    Key Insights
                  </h4>
                  {sampleInsights.map((insight, index) => (
                    <div key={index} className="text-sm text-gray-600 mb-2">
                      <p className="line-clamp-2">{insight.summary}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          {/* Metrics Tab */}
          <TabsContent value="metrics" className="flex-grow flex flex-col p-4 pt-2">
            <div className="space-y-3 flex-grow">
              <h4 className="text-sm font-medium mb-2 flex items-center">
                <BarChart className="h-4 w-4 mr-1 text-blue-500" />
                Market Metrics
              </h4>

              <div className="grid grid-cols-2 gap-2">
                {formattedMetrics.slice(0, 3).map((metric, index) => (
                  <div key={index} className="bg-gray-50 p-2 rounded-md">
                    <div className="text-xs text-gray-500">{metric.name}</div>
                    <div className="font-semibold flex items-center break-words">
                      {metric.value}
                      {metric.trend === 'up' && <TrendingUp className="h-3 w-3 ml-1 text-green-500" />}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Insights Tab */}
          <TabsContent value="insights" className="flex-grow flex flex-col p-4 pt-2">
            <div className="space-y-3 flex-grow overflow-auto max-h-48">
              <h4 className="text-sm font-medium mb-2 flex items-center">
                <Globe className="h-4 w-4 mr-1 text-purple-500" />
                Market Intelligence
              </h4>

              {marketIntelligence.length > 0 ? (
                marketIntelligence.slice(0, 1).flatMap(section =>
                  section.insights.slice(0, 3).map((insight, index) => (
                    <div key={index} className="bg-gray-50 p-2 rounded-md mb-2">
                      <div className="flex justify-between items-start mb-1">
                        <Badge variant="outline" className="text-xs">
                          {insight.type}
                        </Badge>
                        {insight.source_url && (
                          <Link href={insight.source_url} target="_blank" className="text-xs text-blue-500 flex items-center">
                            <ExternalLink className="h-3 w-3 mr-1" />
                            Source
                          </Link>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 break-words">{insight.summary}</p>
                    </div>
                  ))
                )
              ) : (
                <p className="text-sm text-gray-500">No market intelligence data available.</p>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {/* Action buttons */}
        <div className="p-4 pt-2 mt-auto border-t flex justify-between">
          <Button variant="outline" size="sm" onClick={handleView}>
            <Eye className="h-4 w-4 mr-2" />
            View
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>
    </Card>
  );
}
