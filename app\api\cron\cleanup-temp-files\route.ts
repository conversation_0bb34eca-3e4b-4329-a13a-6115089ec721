import { NextResponse } from 'next/server';
import { cleanupTempFiles } from '@/lib/utils/cleanup-temp-files';
import { logger } from '@/lib/utils/logger';

export async function GET(req: Request) {
  const startTime = Date.now();
  
  try {
    // Verify the request is from a cron job
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const apiKey = authHeader.split(' ')[1];
    if (apiKey !== process.env.CRON_SECRET) {
      return new NextResponse('Invalid API key', { status: 401 });
    }

    logger.info('Starting scheduled cleanup of temporary files', { 
      module: 'cron',
      context: { source: 'cleanup-temp-files' }
    });

    // Default to 7 days for max age
    const maxAgeInDays = 7;
    
    // Run cleanup
    const result = await cleanupTempFiles(maxAgeInDays);
    const duration = Date.now() - startTime;
    
    logger.info(`Temporary files cleanup completed in ${duration}ms`, { 
      module: 'cron',
      context: { 
        duration,
        filesRemoved: result.filesRemoved,
        success: result.success
      }
    });
    
    return NextResponse.json({
      success: true,
      message: 'Cleanup job executed successfully',
      result,
      duration: `${duration}ms`,
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    logger.error(`Error executing cleanup job: ${errorMessage}`, { 
      module: 'cron',
      context: { 
        error: errorMessage,
        duration,
        stack: error instanceof Error ? error.stack : undefined
      }
    });
    
    return new NextResponse(
      JSON.stringify({
        success: false,
        error: 'Internal Error',
        message: errorMessage,
        duration: `${duration}ms`,
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
