import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Next.js automatically loads environment variables from .env files
// No need to use dotenv.config()

if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL environment variable is not set');
}

// Configure connection pool
const connectionOptions = {
  max: 10, // Maximum number of connections in the pool
  idle_timeout: 20, // Close connections after 20 seconds of inactivity
  connect_timeout: 10, // Timeout after 10 seconds when connecting
  prepare: false, // Disable prepared statements for better connection handling
};

// For development/production environments
let globalClient;

if (process.env.NODE_ENV === 'production') {
  // In production, use a single connection pool
  globalClient = postgres(process.env.POSTGRES_URL, connectionOptions);
} else {
  // In development, use a new connection pool if it doesn't exist
  if (!global.postgresClient) {
    global.postgresClient = postgres(process.env.POSTGRES_URL, connectionOptions);
  }
  globalClient = global.postgresClient;
}

export const client = globalClient;
export const db = drizzle(client, { schema });

// Note: Graceful shutdown is now handled centrally in lib/utils/graceful-shutdown.ts
// We don't need to register event listeners here to avoid duplicates
