'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { OtpVerification } from '@/components/OtpVerification/OtpVerification';
import { useRouter } from 'next/navigation';
import { resetPassword } from '@/app/(login)/actions';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';

interface ForgotPasswordDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ForgotPasswordDialog({
  isOpen,
  onOpenChange,
}: ForgotPasswordDialogProps) {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'email' | 'verification' | 'reset'>('email');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const { toast } = useToast();
  const router = useRouter();
  const [seePassword, setSeePassword] = useState(false);

  // Reset the state when the dialog is closed
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Reset state after a short delay to avoid visual glitches
      setTimeout(() => {
        setEmail('');
        setNewPassword('');
        setConfirmPassword('');
        setPasswordError('');
        setStep('email');
      }, 300);
    }
    onOpenChange(open);
  };

  // Handle email submission
  const handleSubmitEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // First check if the user exists
      const checkUserResponse = await fetch('/api/auth/check-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const checkUserData = await checkUserResponse.json();

      if (!checkUserData.exists) {
        toast({
          title: 'User not found',
          description: 'No account found with this email address.',
          variant: 'destructive',
        });
        setIsLoading(false);
        return;
      }

      // If user exists, send OTP
      const response = await fetch('/api/auth/otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          purpose: 'password-reset',
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Verification code sent',
          description: 'Please check your email for the verification code.',
        });
        setStep('verification');
      } else {
        toast({
          title: 'Error',
          description:
            data.message ||
            'Failed to send verification code. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error sending OTP:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP verification
  const handleVerifyOtp = async (otp: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/otp', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          otp,
          purpose: 'password-reset',
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Verification successful',
          description: 'Please set your new password.',
        });
        setStep('reset');
        return true;
      } else {
        toast({
          title: 'Verification failed',
          description:
            data.message || 'Failed to verify code. Please try again.',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Handle OTP resend
  const handleResendOtp = async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          purpose: 'password-reset',
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Code resent',
          description: 'A new verification code has been sent to your email.',
        });
        return true;
      } else {
        toast({
          title: 'Failed to resend code',
          description:
            data.message ||
            'Failed to send new verification code. Please try again.',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Error resending OTP:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Handle password reset
  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError('');

    // Validate passwords
    if (newPassword.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError('Passwords do not match');
      return;
    }

    setIsLoading(true);

    try {
      // Create a FormData object to match the expected format in the action
      const formData = new FormData();
      formData.append('email', email);
      formData.append('newPassword', newPassword);

      // Call the server action with the FormData
      const result = await resetPassword(null, formData);

      if (result.success) {
        toast({
          title: 'Password reset successful',
          description:
            'Your password has been reset. You can now sign in with your new password.',
        });
        handleOpenChange(false);
      } else {
        toast({
          title: 'Error',
          description:
            result.error || 'Failed to reset password. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className='sm:max-w-[425px] bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-800'>
        <DialogHeader>
          <DialogTitle className='text-black dark:text-white'>
            {step === 'email' && 'Reset Your Password'}
            {step === 'verification' && 'Verify Your Email'}
            {step === 'reset' && 'Set New Password'}
          </DialogTitle>
          <DialogDescription className='text-gray-600 dark:text-gray-300'>
            {step === 'email' &&
              'Enter your email address to receive a verification code'}
            {step === 'verification' &&
              'Enter the verification code sent to your email'}
            {step === 'reset' && 'Create a new password for your account'}
          </DialogDescription>
        </DialogHeader>

        {step === 'email' && (
          <form onSubmit={handleSubmitEmail} className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='email' className='text-black dark:text-white'>
                Email address
              </Label>
              <Input
                id='email'
                type='email'
                placeholder='<EMAIL>'
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className='bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-700 text-black dark:text-white'
              />
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button
                  type='button'
                  variant='outline'
                  className='border-gray-300 dark:border-gray-700 text-black dark:text-white'
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type='submit'
                disabled={isLoading}
                className='bg-blue-600 hover:bg-blue-700 text-white'
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Sending...
                  </>
                ) : (
                  'Send Verification Code'
                )}
              </Button>
            </DialogFooter>
          </form>
        )}

        {step === 'verification' && (
          <div className='py-4'>
            <OtpVerification
              email={email}
              purpose='password-reset'
              onVerificationSuccess={() => setStep('reset')}
              onResendOtp={handleResendOtp}
              onVerifyOtp={handleVerifyOtp}
              isInDialog={true}
            />
          </div>
        )}

        {step === 'reset' && (
          <form onSubmit={handleResetPassword} className='space-y-4'>
            <div className='space-y-2'>
              <Label
                htmlFor='new-password'
                className='text-black dark:text-white'
              >
                New Password
              </Label>
              <div className='relative'>
                <Input
                  id='new-password'
                  type={seePassword ? 'text' : 'password'}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  minLength={8}
                  className='bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-700 text-black dark:text-white'
                />
                <button
                  type='button'
                  onClick={() => setSeePassword(!seePassword)}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center text-sm text-gray-500 dark:text-white'
                >
                  {seePassword ? <Eye size={16} /> : <EyeOff size={16} />}
                </button>
              </div>
            </div>
            <div className='space-y-2'>
              <Label
                htmlFor='confirm-password'
                className='text-black dark:text-white'
              >
                Confirm Password
              </Label>
              <div className='relative'>
                <Input
                  id='confirm-password'
                  type={seePassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  minLength={8}
                  className='bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-700 text-black dark:text-white'
                />
                <button
                  type='button'
                  onClick={() => setSeePassword(!seePassword)}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center text-sm text-gray-500 dark:text-white'
                >
                  {seePassword ? <Eye size={16} /> : <EyeOff size={16} />}
                </button>
              </div>
            </div>
            {passwordError && (
              <div className='text-red-500 dark:text-red-400 text-sm'>
                {passwordError}
              </div>
            )}
            <DialogFooter>
              <Button
                type='submit'
                disabled={isLoading}
                className='bg-blue-600 hover:bg-blue-700 text-white'
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Resetting...
                  </>
                ) : (
                  'Reset Password'
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
