import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Types for our case study data
export interface CaseStudyData {
  id?: number;
  useCaseTitle: string;
  headerImage?: string;
  industry?: string;
  role?: string;
  kpis?: {
    claimCycleTime: string;
    straightThroughRate: string;
    customerComplaintVolume: string;
  };
  introduction?: {
    title: string;
    text: string;
    problems: string[];
    questions: string[];
  };
  process?: {
    title: string;
    steps: Array<{
      title: string;
      description: string;
      icon?: string;
    }>;
  };
  solution?: {
    title: string;
    description: string;
    items: Array<{
      title: string;
      description: string;
      icon?: string;
    }>;
  };
  impact?: {
    title: string;
    metrics: Array<{
      metric: string;
      value: string;
      description: string;
      icon?: string;
    }>;
  };
  conclusion?: {
    title: string;
    text: string;
  };
}

// Response types
export interface CaseStudyResponse {
  id: number;
  useCaseTitle: string;
  industry: string | null;
  role: string | null;
  headerImage?: string | null;
  // Add other fields as needed
}

export interface CaseStudiesResponse {
  caseStudies: CaseStudyResponse[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

// Simple API functions
const fetchCaseStudies = async (page = 1, pageSize = 10): Promise<CaseStudiesResponse> => {
  const response = await fetch(`/api/case-studies?page=${page}&pageSize=${pageSize}`);
  if (!response.ok) throw new Error('Failed to fetch case studies');
  return response.json();
};

const fetchCaseStudyById = async (id: number): Promise<CaseStudyResponse> => {
  const response = await fetch(`/api/case-studies/${id}`);
  if (!response.ok) throw new Error('Failed to fetch case study');
  return response.json();
};

const createCaseStudy = async (data: CaseStudyData): Promise<CaseStudyResponse> => {
  const response = await fetch('/api/case-studies', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Failed to create case study');
  return response.json();
};

const updateCaseStudy = async (id: number, data: CaseStudyData): Promise<CaseStudyResponse> => {
  const response = await fetch(`/api/case-studies/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Failed to update case study');
  return response.json();
};

const deleteCaseStudy = async (id: number): Promise<void> => {
  const response = await fetch(`/api/case-studies/${id}`, {
    method: 'DELETE',
  });
  if (!response.ok) throw new Error('Failed to delete case study');
};

export const uploadMedia = async (file: File, type: string): Promise<string> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);
  
  const response = await fetch('/api/case-studies/upload-media', {
    method: 'POST',
    body: formData,
  });
  
  if (!response.ok) throw new Error('Failed to upload media');
  const data = await response.json();
  return data.url;
};

// Simple React Query hooks
export const useCaseStudies = (page = 1, pageSize = 10) => {
  return useQuery({
    queryKey: ['caseStudies', page, pageSize],
    queryFn: () => fetchCaseStudies(page, pageSize),
  });
};

export const useCaseStudy = (id: number | undefined) => {
  return useQuery({
    queryKey: ['caseStudy', id],
    queryFn: () => fetchCaseStudyById(id as number),
    enabled: !!id,
  });
};

export const useCreateCaseStudy = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: createCaseStudy,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['caseStudies'] });
    },
  });
};

export const useUpdateCaseStudy = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (variables: { id: number; data: CaseStudyData }) => 
      updateCaseStudy(variables.id, variables.data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['caseStudies'] });
      queryClient.invalidateQueries({ queryKey: ['caseStudy', variables.id] });
    },
  });
};

export const useDeleteCaseStudy = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: deleteCaseStudy,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['caseStudies'] });
    },
  });
};

// Media upload functions
export async function uploadCSV(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('/api/case-studies/upload-csv', {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error('Failed to upload CSV');
  }

  return response.json();
}