import { CaseStudyCard } from '@/components/CaseStudyCard/Card';

export default function TestMarketIntelligencePage() {
  // Sample market intelligence data
  const marketIntelligence = [
    {
      title: "NS Insights Hub",
      description: "Expert analysis and research findings",
      insights: [
        {
          type: "trend",
          summary: "[<PERSON><PERSON><PERSON>](https://appian.com) predicts a 40% growth in the adoption of process intelligence technologies by 2027, with a 35% improvement in claims processing efficiency for insurance companies.",
          source_url: "https://appian.com/blog/acp/insurance/process-intelligence-insurance"
        },
        {
          type: "trend",
          summary: "[Skan.ai](https://skan.ai) forecasts a 30% increase in claims automation by 2026, resulting in cost reductions of over 20% and a significant enhancement in customer satisfaction for insurers.",
          source_url: "https://www.skan.ai/blogs/data-driven-insurance-top-process-intelligence-use-cases"
        }
      ]
    },
    {
      title: "Business Impact",
      description: "How AI affects business operations",
      insights: [
        {
          type: "Case Study",
          summary: "[Tractable](https://tractable.ai/) achieved a 90% reduction in claims processing time by implementing AI-driven automation for damage assessments, resulting in $1 billion savings annually.",
          source_url: "https://research.aimultiple.com/insurance-claims-ai/"
        }
      ]
    }
  ];

  // Sample market metrics data
  const marketMetrics = {
    "Market Size ($ Value)": 198130000000,
    "CAGR": 13.3,
    "ROI Range": 40
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Test Market Intelligence Display</h1>
      
      <CaseStudyCard
        id="test"
        title="Test Case Study"
        headerImage=""
        industry="Insurance"
        role="CIO"
        kpis={{
          claimCycleTime: "Claims Cycle Time",
          straightThroughRate: "Straight Through Rate",
          customerComplaintVolume: "Customer Complaint Volume"
        }}
        introduction={{
          title: "Introduction",
          text: "This is a test case study to verify that market intelligence data is displayed correctly.",
          problems: ["Problem 1", "Problem 2"],
          questions: ["Question 1", "Question 2"]
        }}
        process={{
          title: "Process",
          steps: [
            {
              title: "Step 1",
              description: "Step 1 description"
            },
            {
              title: "Step 2",
              description: "Step 2 description"
            }
          ]
        }}
        solution={{
          title: "Solution",
          description: "Solution description",
          items: [
            {
              title: "Solution 1",
              description: "Solution 1 description"
            },
            {
              title: "Solution 2",
              description: "Solution 2 description"
            }
          ]
        }}
        impact={{
          title: "Impact",
          metrics: [
            {
              metric: "Impact 1",
              value: "Value 1",
              description: "Impact 1 description"
            },
            {
              metric: "Impact 2",
              value: "Value 2",
              description: "Impact 2 description"
            }
          ]
        }}
        conclusion={{
          title: "Conclusion",
          text: "Conclusion text"
        }}
        marketIntelligence={marketIntelligence}
        marketMetrics={marketMetrics}
      />
    </div>
  );
}
