import {
  pgTable,
  serial,
  varchar,
  text,
  timestamp,
  integer,
  vector,
  jsonb,
  numeric,
  bigint,
  boolean,
  foreignKey,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  role: varchar('role', { length: 20 }).notNull().default('member'), // 'member', 'manager', 'owner'
  isVerified: boolean('is_verified').notNull().default(false),
  industry: varchar('industry', { length: 50 }),
  companyName: varchar('company_name', { length: 50 }),
  agreedToTerms: timestamp('agreed_to_terms'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  // deletedAt: timestamp('deleted_at'),
});

export const teams = pgTable('teams', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  stripeCustomerId: text('stripe_customer_id').unique(),
  stripeSubscriptionId: text('stripe_subscription_id').unique(),
  stripeProductId: text('stripe_product_id'),
  planName: varchar('plan_name', { length: 50 }),
  subscriptionStatus: varchar('subscription_status', { length: 20 }),
});

export const teamMembers = pgTable('team_members', {
  id: serial('id').primaryKey(),
  userId: integer('user_id')
    .notNull()
    .references(() => users.id),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  role: varchar('role', { length: 50 }).notNull(),
  joinedAt: timestamp('joined_at').notNull().defaultNow(),
});

export const activityLogs = pgTable('activity_logs', {
  id: serial('id').primaryKey(),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  userId: integer('user_id').references(() => users.id),
  action: text('action').notNull(),
  timestamp: timestamp('timestamp').notNull().defaultNow(),
  ipAddress: varchar('ip_address', { length: 45 }),
});

export const invitations = pgTable('invitations', {
  id: serial('id').primaryKey(),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  email: varchar('email', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull(),
  invitedBy: integer('invited_by')
    .notNull()
    .references(() => users.id),
  invitedAt: timestamp('invited_at').notNull().defaultNow(),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
});

export const teamsRelations = relations(teams, ({ many }) => ({
  teamMembers: many(teamMembers),
  activityLogs: many(activityLogs),
  invitations: many(invitations),
}));

export const invitationsRelations = relations(invitations, ({ one }) => ({
  team: one(teams, {
    fields: [invitations.teamId],
    references: [teams.id],
  }),
  invitedBy: one(users, {
    fields: [invitations.invitedBy],
    references: [users.id],
  }),
}));

export const teamMembersRelations = relations(teamMembers, ({ one }) => ({
  user: one(users, {
    fields: [teamMembers.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [teamMembers.teamId],
    references: [teams.id],
  }),
}));

export const activityLogsRelations = relations(activityLogs, ({ one }) => ({
  team: one(teams, {
    fields: [activityLogs.teamId],
    references: [teams.id],
  }),
  user: one(users, {
    fields: [activityLogs.userId],
    references: [users.id],
  }),
}));

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Team = typeof teams.$inferSelect;
export type NewTeam = typeof teams.$inferInsert;
export type TeamMember = typeof teamMembers.$inferSelect;
export type NewTeamMember = typeof teamMembers.$inferInsert;
export type ActivityLog = typeof activityLogs.$inferSelect;
export type NewActivityLog = typeof activityLogs.$inferInsert;
export type Invitation = typeof invitations.$inferSelect;
export type NewInvitation = typeof invitations.$inferInsert;
export type TeamDataWithMembers = Team & {
  teamMembers: (TeamMember & {
    user: Pick<User, 'id' | 'name' | 'email'>;
  })[];
};

export enum ActivityType {
  SIGN_UP = 'SIGN_UP',
  SIGN_IN = 'SIGN_IN',
  SIGN_OUT = 'SIGN_OUT',
  UPDATE_PASSWORD = 'UPDATE_PASSWORD',
  DELETE_ACCOUNT = 'DELETE_ACCOUNT',
  UPDATE_ACCOUNT = 'UPDATE_ACCOUNT',
  CREATE_TEAM = 'CREATE_TEAM',
  REMOVE_TEAM_MEMBER = 'REMOVE_TEAM_MEMBER',
  INVITE_TEAM_MEMBER = 'INVITE_TEAM_MEMBER',
  ACCEPT_INVITATION = 'ACCEPT_INVITATION',
  AI_PROCESS_CASE = 'AI_PROCESS_CASE',
}

export const caseStudies = pgTable('case_studies', {
  id: serial('id').primaryKey(),
  useCaseTitle: varchar('use_case_title', { length: 255 }).notNull(),
  industry: varchar('industry', { length: 100 }),
  role: varchar('role', { length: 100 }),
  vector: varchar('vector', { length: 100 }),
  potentiallyImpactedKpis: text('potentially_impacted_kpis'),
  userId: integer('user_id').references(() => users.id),
  introductionTitle: varchar('introduction_title', { length: 255 }),
  introductionText: text('introduction_text'),
  transitionToChallange: text('transition_to_challange'),
  challange1: text('challange_1'),
  challange2: text('challange_2'),
  challange3: text('challange_3'),
  transitionToQuestions: text('transition_to_questions'),
  question1: text('question_1'),
  question2: text('question_2'),
  question3: text('question_3'),
  processTitle: text('process_title'),
  processStep1Title: text('process_step_1_title'),
  processStep1Description: text('process_step_1_description'),
  processStep2Title: text('process_step_2_title'),
  processStep2Description: text('process_step_2_description'),
  processStep3Title: text('process_step_3_title'),
  processStep3Description: text('process_step_3_description'),
  solutionTitle: text('solution_title'),
  solutionDescription: text('solution_description'),
  solution1Title: text('solution_1_title'),
  solution1Description: text('solution_1_description'),
  solution2Title: text('solution_2_title'),
  solution2Description: text('solution_2_description'),
  solution3Title: text('solution_3_title'),
  solution3Description: text('solution_3_description'),
  solution4Title: text('solution_4_title'),
  solution4Description: text('solution_4_description'),
  solution5Title: text('solution_5_title'),
  solution5Description: text('solution_5_description'),
  solution1: text('solution_1'),
  solution2: text('solution_2'),
  solution3: text('solution_3'),
  solution4: text('solution_4'),
  solution5: text('solution_5'),
  impactTitle: text('impact_title'),
  potentialImpact1: text('potential_impact_1'),
  potentialImpact2: text('potential_impact_2'),
  potentialImpact3: text('potential_impact_3'),
  potentialImpact4: text('potential_impact_4'),
  // Impact metric and value fields
  impactMetric1: text('impact_metric_1'),
  impactValue1: text('impact_value_1'),
  impactMetric2: text('impact_metric_2'),
  impactValue2: text('impact_value_2'),
  impactMetric3: text('impact_metric_3'),
  impactValue3: text('impact_value_3'),
  impactMetric4: text('impact_metric_4'),
  impactValue4: text('impact_value_4'),
  conclusionTitle: varchar('conclusion_title', { length: 255 }),
  conclusionText: text('conclusion_text'),
  conclusionResult: text('conclusion_result'),
  conclusionResultTitle: text('conclusion_result_title'),
  featureImageUrl: varchar('feature_image_url', { length: 255 }),
  previewImageUrl: varchar('preview_image_url', { length: 255 }),
  // AI processing fields
  aiProcessed: timestamp('ai_processed'),
  aiProcessingStatus: varchar('ai_processing_status', { length: 50 }),
  aiProcessingError: text('ai_processing_error'),
  aiRequestId: varchar('ai_request_id', { length: 100 }),

  // Market intelligence data
  marketIntelligenceData: jsonb('market_intelligence_data'),
  marketMetricsData: jsonb('market_metrics_data'),
  marketSize: bigint('market_size', { mode: 'number' }),
  marketCAGR: numeric('market_cagr'),
  marketROI: numeric('market_roi'),
  views: integer('views').default(0),
  trendingStatus: boolean('trending_status').default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  // deletedAt: timestamp('deleted_at'),
});

export const caseStudyIcons = pgTable('case_study_icons', {
  id: serial('id').primaryKey(),
  caseStudyId: integer('case_study_id')
    .references(() => caseStudies.id)
    .notNull(),
  iconType: varchar('icon_type', { length: 50 }).notNull(), // 'process', 'solution', 'impact'
  iconUrl: varchar('icon_url', { length: 255 }).notNull(),
  order: integer('order').notNull(), // For ordering icons
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

export const bookmarks = pgTable('bookmarks', {
  id: serial('id').primaryKey(),
  userId: integer('user_id')
    .references(() => users.id)
    .notNull(),
  caseStudyId: integer('case_study_id')
    .references(() => caseStudies.id)
    .notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

export const caseStudiesRelations = relations(caseStudies, ({ many, one }) => ({
  icons: many(caseStudyIcons),
  bookmarks: many(bookmarks),
  user: one(users, {
    fields: [caseStudies.userId],
    references: [users.id],
  }),
}));

export const usersRelations = relations(users, ({ many }) => ({
  teamMembers: many(teamMembers),
  invitationsSent: many(invitations),
  bookmarks: many(bookmarks),
  caseStudies: many(caseStudies),
  // Temporarily removing the problematic relation
}));

export const caseStudyIconsRelations = relations(caseStudyIcons, ({ one }) => ({
  caseStudy: one(caseStudies, {
    fields: [caseStudyIcons.caseStudyId],
    references: [caseStudies.id],
  }),
}));

export const bookmarksRelations = relations(bookmarks, ({ one }) => ({
  user: one(users, {
    fields: [bookmarks.userId],
    references: [users.id],
  }),
  caseStudy: one(caseStudies, {
    fields: [bookmarks.caseStudyId],
    references: [caseStudies.id],
  }),
}));

export type CaseStudy = typeof caseStudies.$inferSelect;
export type NewCaseStudy = typeof caseStudies.$inferInsert;
export type CaseStudyIcon = typeof caseStudyIcons.$inferSelect;
export type NewCaseStudyIcon = typeof caseStudyIcons.$inferInsert;
export type Bookmark = typeof bookmarks.$inferSelect;
export type NewBookmark = typeof bookmarks.$inferInsert;

export const couponCodes = pgTable('coupon_codes', {
  id: serial('id').primaryKey(),
  code: varchar('code', { length: 50 }).notNull().unique(),
  discountType: varchar('discount_type', { length: 20 }).notNull(), // 'percentage' or 'fixed'
  discountAmount: numeric('discount_amount').notNull(), // percentage or fixed amount
  description: text('description'),
  maxUses: integer('max_uses'), // NULL means unlimited
  currentUses: integer('current_uses').notNull().default(0),
  validFrom: timestamp('valid_from').notNull().defaultNow(),
  validUntil: timestamp('valid_until'), // NULL means no expiration
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  createdBy: integer('created_by').references(() => users.id),
  isActive: boolean('is_active').notNull().default(true),
});

export const couponCodesRelations = relations(couponCodes, ({ one }) => ({
  creator: one(users, {
    fields: [couponCodes.createdBy],
    references: [users.id],
  }),
}));

export type CouponCode = typeof couponCodes.$inferSelect;
export type NewCouponCode = typeof couponCodes.$inferInsert;

// Contact messages schema
export const contactMessages = pgTable('contact_messages', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 50 }),
  company: varchar('company', { length: 100 }),
  inquiryType: varchar('inquiry_type', { length: 50 }).notNull(),
  subject: varchar('subject', { length: 255 }).notNull(),
  message: text('message').notNull(),
  status: varchar('status', { length: 20 }).notNull().default('unread'), // unread, read, replied
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  readAt: timestamp('read_at'),
  repliedAt: timestamp('replied_at'),
  repliedBy: integer('replied_by').references(() => users.id),
});

export const contactMessagesRelations = relations(
  contactMessages,
  ({ one }) => ({
    replier: one(users, {
      fields: [contactMessages.repliedBy],
      references: [users.id],
    }),
  })
);

export type ContactMessage = typeof contactMessages.$inferSelect;
export type NewContactMessage = typeof contactMessages.$inferInsert;

// OTP verification schema
export const otpVerifications = pgTable('otp_verifications', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull(),
  otp: varchar('otp', { length: 6 }).notNull(),
  purpose: varchar('purpose', { length: 20 }).notNull(), // 'signup', 'password-reset', etc.
  expiresAt: timestamp('expires_at').notNull(),
  verified: boolean('verified').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  attempts: integer('attempts').notNull().default(0),
});

export type OtpVerification = typeof otpVerifications.$inferSelect;
export type NewOtpVerification = typeof otpVerifications.$inferInsert;

// Plans schema
export const plans = pgTable('plans', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 50 }).notNull(),
  displayName: varchar('display_name', { length: 100 }).notNull(),
  description: text('description'),
  priceMonthly: integer('price_monthly').notNull(),
  priceYearly: integer('price_yearly'),
  stripePriceIdMonthly: varchar('stripe_price_id_monthly', {
    length: 100,
  }).notNull(),
  stripePriceIdYearly: varchar('stripe_price_id_yearly', { length: 100 }),
  stripeProductId: varchar('stripe_product_id', { length: 100 }).notNull(),
  features: jsonb('features').notNull(),
  maxUsers: integer('max_users').notNull().default(1),
  isActive: boolean('is_active').notNull().default(true),
  isPublic: boolean('is_public').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const userSubscriptions = pgTable('user_subscriptions', {
  id: serial('id').primaryKey(),
  userId: integer('user_id')
    .notNull()
    .references(() => users.id),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  planId: integer('plan_id')
    .notNull()
    .references(() => plans.id),
  stripeSubscriptionId: varchar('stripe_subscription_id', { length: 100 }),
  stripeCustomerId: varchar('stripe_customer_id', { length: 100 }),
  status: varchar('status', { length: 50 }).notNull(),
  currentPeriodStart: timestamp('current_period_start').notNull(),
  currentPeriodEnd: timestamp('current_period_end').notNull(),
  cancelAtPeriodEnd: boolean('cancel_at_period_end').notNull().default(false),
  canceledAt: timestamp('canceled_at'),
  trialStart: timestamp('trial_start'),
  trialEnd: timestamp('trial_end'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const subscriptionItems = pgTable('subscription_items', {
  id: serial('id').primaryKey(),
  subscriptionId: integer('subscription_id')
    .notNull()
    .references(() => userSubscriptions.id),
  stripeItemId: varchar('stripe_item_id', { length: 100 }).notNull(),
  stripePriceId: varchar('stripe_price_id', { length: 100 }).notNull(),
  quantity: integer('quantity').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const subscriptionHistory = pgTable('subscription_history', {
  id: serial('id').primaryKey(),
  userId: integer('user_id')
    .notNull()
    .references(() => users.id),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  planId: integer('plan_id')
    .notNull()
    .references(() => plans.id),
  stripeSubscriptionId: varchar('stripe_subscription_id', { length: 100 }),
  action: varchar('action', { length: 50 }).notNull(),
  previousStatus: varchar('previous_status', { length: 50 }),
  newStatus: varchar('new_status', { length: 50 }),
  previousPlanId: integer('previous_plan_id').references(() => plans.id),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Relations for plans
export const plansRelations = relations(plans, ({ many }) => ({
  userSubscriptions: many(userSubscriptions),
}));

// Relations for user subscriptions
export const userSubscriptionsRelations = relations(
  userSubscriptions,
  ({ one, many }) => ({
    user: one(users, {
      fields: [userSubscriptions.userId],
      references: [users.id],
    }),
    team: one(teams, {
      fields: [userSubscriptions.teamId],
      references: [teams.id],
    }),
    plan: one(plans, {
      fields: [userSubscriptions.planId],
      references: [plans.id],
    }),
    subscriptionItems: many(subscriptionItems),
  })
);

// Relations for subscription items
export const subscriptionItemsRelations = relations(
  subscriptionItems,
  ({ one }) => ({
    subscription: one(userSubscriptions, {
      fields: [subscriptionItems.subscriptionId],
      references: [userSubscriptions.id],
    }),
  })
);

// Relations for subscription history
export const subscriptionHistoryRelations = relations(
  subscriptionHistory,
  ({ one }) => ({
    user: one(users, {
      fields: [subscriptionHistory.userId],
      references: [users.id],
    }),
    team: one(teams, {
      fields: [subscriptionHistory.teamId],
      references: [teams.id],
    }),
    plan: one(plans, {
      fields: [subscriptionHistory.planId],
      references: [plans.id],
    }),
    previousPlan: one(plans, {
      fields: [subscriptionHistory.previousPlanId],
      references: [plans.id],
    }),
  })
);

// Add subscription relation to users
export const usersSubscriptionsRelations = relations(users, ({ many }) => ({
  subscriptions: many(userSubscriptions),
  subscriptionHistory: many(subscriptionHistory),
}));

// Add subscription relation to teams
export const teamsSubscriptionsRelations = relations(teams, ({ many }) => ({
  subscriptions: many(userSubscriptions),
  subscriptionHistory: many(subscriptionHistory),
}));

// Types for plans
export type Plan = typeof plans.$inferSelect;
export type NewPlan = typeof plans.$inferInsert;
export type UserSubscription = typeof userSubscriptions.$inferSelect;
export type NewUserSubscription = typeof userSubscriptions.$inferInsert;
export type SubscriptionItem = typeof subscriptionItems.$inferSelect;
export type NewSubscriptionItem = typeof subscriptionItems.$inferInsert;
export type SubscriptionHistory = typeof subscriptionHistory.$inferSelect;
export type NewSubscriptionHistory = typeof subscriptionHistory.$inferInsert;
