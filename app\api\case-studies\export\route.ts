import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies, caseStudyIcons } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { eq } from 'drizzle-orm';

export async function GET(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const url = new URL(req.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return new NextResponse('Case study ID is required', { status: 400 });
    }

    // Get the case study with its icons
    const caseStudy = await db.query.caseStudies.findFirst({
      where: eq(caseStudies.id, parseInt(id)),
      with: {
        icons: true
      }
    });

    if (!caseStudy) {
      return new NextResponse('Case study not found', { status: 404 });
    }

    // Format the case study data for export
    const exportData = {
      id: caseStudy.id,
      title: caseStudy.useCaseTitle,
      industry: caseStudy.industry,
      role: caseStudy.role,
      introduction: {
        title: caseStudy.introductionTitle,
        text: caseStudy.introductionText,
        challenges: [
          caseStudy.challange1,
          caseStudy.challange2,
          caseStudy.challange3
        ].filter(Boolean),
        questions: [
          caseStudy.question1,
          caseStudy.question2,
          caseStudy.question3
        ].filter(Boolean)
      },
      process: {
        title: caseStudy.processTitle,
        steps: [
          caseStudy.processStep1Title ? {
            title: caseStudy.processStep1Title,
            description: caseStudy.processStep1Description
          } : null,
          caseStudy.processStep2Title ? {
            title: caseStudy.processStep2Title,
            description: caseStudy.processStep2Description
          } : null,
          caseStudy.processStep3Title ? {
            title: caseStudy.processStep3Title,
            description: caseStudy.processStep3Description
          } : null
        ].filter(Boolean)
      },
      solution: {
        title: caseStudy.solutionTitle,
        description: caseStudy.solutionDescription,
        items: [
          caseStudy.solution1Title ? {
            title: caseStudy.solution1Title,
            description: caseStudy.solution1Description
          } : null,
          caseStudy.solution2Title ? {
            title: caseStudy.solution2Title,
            description: caseStudy.solution2Description
          } : null,
          caseStudy.solution3Title ? {
            title: caseStudy.solution3Title,
            description: caseStudy.solution3Description
          } : null
        ].filter(Boolean)
      },
      impact: {
        title: caseStudy.impactTitle,
        metrics: [
          caseStudy.potentialImpact1 ? { description: caseStudy.potentialImpact1 } : null,
          caseStudy.potentialImpact2 ? { description: caseStudy.potentialImpact2 } : null,
          caseStudy.potentialImpact3 ? { description: caseStudy.potentialImpact3 } : null,
          caseStudy.potentialImpact4 ? { description: caseStudy.potentialImpact4 } : null
        ].filter(Boolean)
      },
      conclusion: {
        title: caseStudy.conclusionTitle,
        text: caseStudy.conclusionText
      },
      marketIntelligence: caseStudy.marketIntelligenceData ? 
        JSON.parse(caseStudy.marketIntelligenceData as string) : null,
      marketMetrics: caseStudy.marketMetricsData ? 
        JSON.parse(caseStudy.marketMetricsData as string) : null,
      images: caseStudy.icons.map(icon => ({
        type: icon.iconType,
        url: icon.iconUrl,
        order: icon.order
      })),
      featureImage: caseStudy.featureImageUrl,
      exportedAt: new Date().toISOString(),
      exportedBy: session.user.id
    };

    // Set headers for file download
    const headers = new Headers();
    headers.set('Content-Type', 'application/json');
    headers.set('Content-Disposition', `attachment; filename="case-study-${id}.json"`);

    return new NextResponse(JSON.stringify(exportData, null, 2), {
      headers
    });
  } catch (error) {
    console.error('Error exporting case study:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}
