import { NextResponse } from 'next/server';

/**
 * This endpoint is deprecated.
 * Cloudinary uploads should be handled directly from the client using the Cloudinary Upload Widget.
 */
export async function POST(req: Request) {
  return new NextResponse(
    JSON.stringify({
      error: 'Deprecated API',
      message: 'This API endpoint is deprecated. Please use Cloudinary Upload Widget for direct uploads.',
    }),
    { status: 400, headers: { 'Content-Type': 'application/json' } }
  );
}