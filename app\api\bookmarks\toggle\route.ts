import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { bookmarks } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { and, eq } from 'drizzle-orm';

export async function GET(req: Request) {
  try {
    console.log('Bookmark toggle endpoint called');
    const session = await getSession();
    console.log('Session:', JSON.stringify(session, null, 2));

    if (!session?.user?.id) {
      console.log('Unauthorized: No user ID in session');
      // Redirect to login page instead of showing an error
      return NextResponse.redirect(new URL('/sign-in', req.url));
    }

    const url = new URL(req.url);

    // Check if the user is a member (not an owner)
    if (session.user.role !== 'member') {
      console.log(`Access denied: Non-member (role: ${session.user.role}) attempting to toggle bookmark`);
      // Redirect back to the case study page
      const caseStudyId = url.searchParams.get('caseStudyId');
      if (caseStudyId) {
        return NextResponse.redirect(new URL(`/dashboard/case-studies/${caseStudyId}`, req.url));
      } else {
        return NextResponse.redirect(new URL('/dashboard/case-studies', req.url));
      }
    }
    const caseStudyId = url.searchParams.get('caseStudyId');
    console.log(`Case study ID: ${caseStudyId}`);

    if (!caseStudyId) {
      console.log('Case study ID is required but not provided');
      return new NextResponse('Case study ID is required', { status: 400 });
    }

    try {
      // Check if bookmark already exists
      const existingBookmark = await db.query.bookmarks.findFirst({
        where: and(
          eq(bookmarks.userId, session.user.id),
          eq(bookmarks.caseStudyId, parseInt(caseStudyId))
        ),
      });

      console.log(`Existing bookmark: ${existingBookmark ? 'Yes' : 'No'}`);

      if (existingBookmark) {
        // Delete the bookmark
        console.log('Deleting bookmark');
        await db.delete(bookmarks).where(
          and(
            eq(bookmarks.userId, session.user.id),
            eq(bookmarks.caseStudyId, parseInt(caseStudyId))
          )
        );
        console.log('Bookmark deleted successfully');
      } else {
        // Create new bookmark
        console.log('Creating new bookmark');
        const newBookmark = await db.insert(bookmarks).values({
          userId: session.user.id,
          caseStudyId: parseInt(caseStudyId),
          createdAt: new Date(),
        }).returning();
        console.log('New bookmark created:', JSON.stringify(newBookmark, null, 2));
      }
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Continue with redirect even if there's a database error
    }

    // Redirect back to the case study page
    return NextResponse.redirect(new URL(`/dashboard/user/case-studies/${caseStudyId}/view`, req.url));
  } catch (error) {
    console.error('Error toggling bookmark:', error);
    console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
    // Redirect to the case study page even if there's an error
    const url = new URL(req.url);
    const caseStudyId = url.searchParams.get('caseStudyId');
    if (caseStudyId) {
      return NextResponse.redirect(new URL(`/dashboard/user/case-studies/${caseStudyId}/view`, req.url));
    } else {
      return NextResponse.redirect(new URL('/dashboard/member', req.url));
    }
  }
}
