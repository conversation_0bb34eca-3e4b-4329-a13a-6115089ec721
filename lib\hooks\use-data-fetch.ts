import { useCallback, useEffect, useRef, useState } from 'react';
import { fetchWithEnhancements } from '../api-utils';

interface UseFetchOptions<T> {
  // Initial data to use before fetch completes
  initialData?: T;
  // Whether to fetch automatically on component mount
  autoFetch?: boolean;
  // Fetch options to pass to fetchWithEnhancements
  fetchOptions?: RequestInit & {
    timeout?: number;
    retry?: number;
    retryDelay?: number;
    exponentialBackoff?: boolean;
    cacheKey?: string;
    cacheTtl?: number;
  };
  // Optional callback to transform the fetched data
  transform?: (data: any) => T;
  // Whether to revalidate data on window focus
  revalidateOnFocus?: boolean;
  // Whether to revalidate data on reconnect
  revalidateOnReconnect?: boolean;
  // Polling interval in milliseconds (0 means no polling)
  pollingInterval?: number;
  // Dependencies that trigger refetch when changed
  deps?: any[];
  // Whether to deduplicate requests (default: true)
  dedupingInterval?: number;
}

/**
 * Custom hook for efficient data fetching with:
 * - Caching
 * - Automatic revalidation
 * - Loading/error states
 * - Deduplication
 * - Polling
 * - Focus revalidation
 * - Network recovery
 * 
 * @param url URL to fetch from
 * @param options Fetch options and behavior configuration
 * @returns Fetch state and control functions
 */
export function useDataFetch<T = any>(url: string, options: UseFetchOptions<T> = {}) {
  const {
    initialData,
    autoFetch = true,
    fetchOptions,
    transform,
    revalidateOnFocus = true,
    revalidateOnReconnect = true,
    pollingInterval = 0,
    deps = [],
    dedupingInterval = 2000,
  } = options;
  
  // State for data, loading, and error
  const [data, setData] = useState<T | undefined>(initialData);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Refs for tracking fetch state and timing
  const activeRequestRef = useRef<AbortController | null>(null);
  const lastFetchTimeRef = useRef<number>(0);
  const lastUrlRef = useRef<string>(url);
  const mountedRef = useRef<boolean>(false);
  
  // Function to fetch data with all the enhancements
  const fetchData = useCallback(async (skipDeduping = false) => {
    // Skip if URL is empty
    if (!url) return;
    
    // Check deduping interval to avoid duplicate requests
    const now = Date.now();
    if (
      !skipDeduping && 
      lastFetchTimeRef.current > 0 && 
      now - lastFetchTimeRef.current < dedupingInterval && 
      url === lastUrlRef.current
    ) {
      return;
    }
    
    // Update refs
    lastFetchTimeRef.current = now;
    lastUrlRef.current = url;
    
    // Cancel any active request
    if (activeRequestRef.current) {
      activeRequestRef.current.abort();
    }
    
    // Create new abort controller
    const controller = new AbortController();
    activeRequestRef.current = controller;
    
    // Start loading
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch data with enhancements
      const result = await fetchWithEnhancements(url, {
        ...fetchOptions,
        signal: controller.signal,
      });
      
      // Apply transform if provided
      const transformedData = transform ? transform(result) : result;
      
      // Update state if component is still mounted
      if (mountedRef.current) {
        setData(transformedData);
        setIsLoading(false);
      }
      
      return transformedData;
    } catch (err) {
      // Only update error state if not aborted and component is mounted
      if ((err as any).name !== 'AbortError' && mountedRef.current) {
        setError(err as Error);
        setIsLoading(false);
      }
      throw err;
    } finally {
      if (controller === activeRequestRef.current) {
        activeRequestRef.current = null;
      }
    }
  }, [url, fetchOptions, transform, dedupingInterval]);
  
  // Manual refetch function exposed to consumers
  const refetch = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);
  
  // Handle automatic fetching, revalidation, and polling
  useEffect(() => {
    mountedRef.current = true;
    
    // Initial fetch if autoFetch is true
    if (autoFetch) {
      fetchData();
    }
    
    // Set up polling if interval is provided
    let pollingTimeout: NodeJS.Timeout | null = null;
    if (pollingInterval > 0) {
      const poll = () => {
        fetchData().finally(() => {
          if (mountedRef.current) {
            pollingTimeout = setTimeout(poll, pollingInterval);
          }
        });
      };
      
      pollingTimeout = setTimeout(poll, pollingInterval);
    }
    
    // Set up focus and reconnect event listeners
    let visibilityHandler: (() => void) | null = null;
    let reconnectHandler: (() => void) | null = null;
    
    if (revalidateOnFocus) {
      visibilityHandler = () => {
        if (document.visibilityState === 'visible') {
          fetchData();
        }
      };
      document.addEventListener('visibilitychange', visibilityHandler);
    }
    
    if (revalidateOnReconnect) {
      reconnectHandler = () => fetchData();
      window.addEventListener('online', reconnectHandler);
    }
    
    // Cleanup function
    return () => {
      mountedRef.current = false;
      
      // Cancel any active request
      if (activeRequestRef.current) {
        activeRequestRef.current.abort();
        activeRequestRef.current = null;
      }
      
      // Clear polling timeout
      if (pollingTimeout) {
        clearTimeout(pollingTimeout);
      }
      
      // Remove event listeners
      if (visibilityHandler) {
        document.removeEventListener('visibilitychange', visibilityHandler);
      }
      
      if (reconnectHandler) {
        window.removeEventListener('online', reconnectHandler);
      }
    };
  }, [url, autoFetch, fetchData, pollingInterval, revalidateOnFocus, revalidateOnReconnect, ...deps]);
  
  return {
    data,
    isLoading,
    error,
    refetch,
  };
}

export default useDataFetch;
