import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { signToken, verifyToken } from '@/lib/auth/session';
import { validateSubscription } from '@/lib/auth/subscription-middleware';

// Define route patterns for different roles
const OWNER_ROUTE_PATTERNS = [
  '/dashboard/owner',
  '/dashboard/add-case',
  '/dashboard/import-cases',
  '/dashboard/categories',
  '/dashboard/team',
  '/dashboard/case-studies',
  // '/dashboard/reports',
  '/dashboard/search',
  '/dashboard/coupon-codes',
  '/dashboard/users',
];

const MEMBER_ROUTE_PATTERNS = [
  '/dashboard/member',
  '/dashboard/trends',
  '/dashboard/reports',
  '/dashboard/user',
  '/dashboard/user/case-studies',
  '/dashboard/user/search',
];

// Team member can access all member routes plus some owner routes
const TEAM_MEMBER_ROUTE_PATTERNS = [
  '/dashboard/owner',
  '/dashboard/add-case',
  '/dashboard/import-cases',
  '/dashboard/categories',
  '/dashboard/team',
  '/dashboard/case-studies',
  // '/dashboard/reports',
  '/dashboard/search',
  '/dashboard/coupon-codes',
  '/dashboard/users',
  ...MEMBER_ROUTE_PATTERNS,
];

// Team viewer can access a subset of member/owner routes
const TEAM_VIEWER_ROUTE_PATTERNS = [
  '/dashboard/categories',
  '/dashboard/team',
  '/dashboard/case-studies',
  '/dashboard/search',
  // '/dashboard/reports',
  '/dashboard/users',
  ...MEMBER_ROUTE_PATTERNS,
];

// Define shared routes accessible to all roles
const SHARED_ROUTE_PATTERNS = [
  '/dashboard/settings',
  '/dashboard/help',
  '/dashboard/case-studies',
];

// Define member-only routes that owners cannot access
const MEMBER_ONLY_ROUTE_PATTERNS = ['/dashboard/billing'];

/**
 * Checks if a given path matches any of the patterns
 */
function matchesPattern(path: string, patterns: string[]): boolean {
  return patterns.some((pattern) => {
    // Exact match
    if (pattern === path) return true;
    // Path starts with pattern
    if (path.startsWith(`${pattern}/`)) return true;

    // Handle case study detail pages specifically
    if (
      pattern === '/dashboard/case-studies' &&
      path.match(/^\/dashboard\/case-studies\/\d+$/)
    ) {
      return true;
    }

    return false;
  });
}

const protectedRoutes = '/dashboard';
const authRoutes = ['/sign-in', '/sign-up', '/login', '/register'];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const sessionCookie = request.cookies.get('session');
  const isProtectedRoute = pathname.startsWith(protectedRoutes);
  const isAuthRoute = authRoutes.some((route) => pathname.startsWith(route));

  // Redirect unauthenticated users from protected routes to sign-in
  if (isProtectedRoute && !sessionCookie) {
    return NextResponse.redirect(new URL('/sign-in', request.url));
  }
  // if (!sessionCookie) {
  //   return NextResponse.redirect(new URL('/sign-in', request.url));
  // }
  if (sessionCookie) {
    const parsed = await verifyToken(sessionCookie.value);
    // Make sure the role is included in the session token
    const teamRole = parsed.user?.role || 'member';
    const role = parsed.user?.role || 'member'; // Default to 'member' if not set

    // Redirect authenticated users from auth routes to dashboard
    if (isAuthRoute && sessionCookie) {
      try {
        let dashboardPath = '/dashboard/member';
        if (role === 'owner') dashboardPath = '/dashboard/owner';
        // teamMember and viewer always go to /dashboard/member
        return NextResponse.redirect(new URL(dashboardPath, request.url));
      } catch (error) {
        // If token verification fails, continue to auth page
        console.error('Error verifying token:', error);
      }
    }

    // Role-based access control for protected routes
    if (isProtectedRoute && sessionCookie) {
      try {
        // Determine which patterns to use for this user
        const isOwnerRoute = matchesPattern(pathname, OWNER_ROUTE_PATTERNS);
        const isMemberRoute = matchesPattern(pathname, MEMBER_ROUTE_PATTERNS);
        const isSharedRoute = matchesPattern(pathname, SHARED_ROUTE_PATTERNS);
        const isMemberOnlyRoute = matchesPattern(
          pathname,
          MEMBER_ONLY_ROUTE_PATTERNS
        );
        const isTeamMemberRoute = matchesPattern(
          pathname,
          TEAM_MEMBER_ROUTE_PATTERNS
        );
        const isTeamViewerRoute = matchesPattern(
          pathname,
          TEAM_VIEWER_ROUTE_PATTERNS
        );

        // If it's a member-only route and user is an owner, redirect to owner dashboard
        if (isMemberOnlyRoute && role === 'owner') {
          return NextResponse.redirect(
            new URL('/dashboard/owner', request.url)
          );
        }
        // Shared route: allow all roles
        if (isSharedRoute) {
          // Continue with the request
        }
        // Owner route: only owner or teamMember
        else if (isOwnerRoute) {
          if (
            role === 'owner' ||
            teamRole === 'teamMember' ||
            role === 'viewer'
          ) {
          } else {
            // deny
            return NextResponse.redirect(
              new URL('/dashboard/member', request.url)
            );
          }
        }
        // Member route: member, teamMember, or viewer
        else if (isMemberRoute) {
          if (role === 'member') {
            // allow
          } else {
            return NextResponse.redirect(
              new URL('/dashboard/owner', request.url)
            );
          }
        }
        // Team member route: only teamMember
        else if (isTeamMemberRoute) {
          if (role === 'teamMember') {
            // allow
          } else {
            return NextResponse.redirect(
              new URL('/dashboard/member', request.url)
            );
          }
        }
        // Team viewer route: only viewer
        else if (isTeamViewerRoute) {
          if (role === 'viewer') {
            // allow
          } else {
            return NextResponse.redirect(
              new URL('/dashboard/member', request.url)
            );
          }
        }
  
      // Check subscription requirements for member routes
      if (role === 'member') {
        // Skip subscription validation if user just completed payment
        const url = new URL(request.url);
        const paymentSuccess = url.searchParams.get('payment_success') === 'true';

        if (paymentSuccess) {
          console.log('Payment success detected, skipping subscription validation');
          // Remove the payment_success parameter and continue
          url.searchParams.delete('payment_success');
          return NextResponse.redirect(url);
        }

        const subscriptionResponse = await validateSubscription(request);
        if (subscriptionResponse) {
          return subscriptionResponse; // Redirect to billing or error page
        }
      }
    } catch (error) {
        console.error('Error verifying token for role-based access:', error);
      }
    }

    let res = NextResponse.next();

    if (sessionCookie) {
      try {
        const expiresInOneDay = new Date(Date.now() + 24 * 60 * 60 * 1000);

        res.cookies.set({
          name: 'session',
          value: await signToken({
            ...parsed,
            user: {
              ...parsed.user,
              role: role,
              teamRole: teamRole,
            },
            expires: expiresInOneDay.toISOString(),
          }),
          httpOnly: true,
          secure: true,
          sameSite: 'lax',
          expires: expiresInOneDay,
        });
      } catch (error) {
        console.error('Error updating session:', error);
        res.cookies.delete('session');
        if (isProtectedRoute) {
          return NextResponse.redirect(new URL('/sign-in', request.url));
        }
      }
    }
    return res;
  }
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
