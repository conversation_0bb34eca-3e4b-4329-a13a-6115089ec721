'use client';

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';
import { SafeImage } from '@/components/SafeImage';
import { CaseStudyData } from './types';
import {
  ClockIcon,
  PersonIcon,
  LightningBoltIcon,
  MagnifyingGlassIcon,
  BarChartIcon,
  Link2Icon,
} from '@radix-ui/react-icons';

// Wave pattern component
export function CaseStudyCard(data: CaseStudyData & { isOwner: boolean }) {
  // Validate all icons before rendering
  const validateIcons = () => {
    // Helper function to safely validate URL
    const isValidUrl = (url: string): boolean => {
      try {
        // Check if it's a relative URL (starts with /)
        if (url.startsWith('/')) {
          return true;
        }

        // Check if it's an absolute URL
        new URL(url);
        return true;
      } catch (e) {
        return false;
      }
    };

    // Validate process step icons
    if (data.process?.steps) {
      data.process.steps.forEach((step, index) => {
        if (step.icon) {
          const isValid = isValidUrl(step.icon);
        }
      });
    }

    // Validate solution item icons
    if (data.solution?.items) {
      data.solution.items.forEach((item, index) => {
        if (item.icon) {
          const isValid = isValidUrl(item.icon);
        }
      });
    }

    // Validate impact metric icons
    if (data.impact?.metrics) {
      data.impact.metrics.forEach((metric, index) => {
        if (metric.icon) {
          const isValid = isValidUrl(metric.icon);
        }
      });
    }

    // Validate challenge icons
    if (data.introduction?.problemIcons) {
      data.introduction.problemIcons.forEach((icon, index) => {
        if (icon) {
          const isValid = isValidUrl(icon);
        }
      });
    }

    // Validate question icons
    if (data.introduction?.questionIcons) {
      data.introduction.questionIcons.forEach((icon, index) => {
        if (icon) {
          const isValid = isValidUrl(icon);
        }
      });
    }
  };

  // Validate icons on component mount
  React.useEffect(() => {
    validateIcons();
  }, []);
  return (
    <Card className='w-full max-w-8xl mx-auto overflow-hidden bg-white dark:bg-black border-gray-100 dark:border-gray-900'>
      {/* Header Section */}
      <CardHeader className='p-0 mb-4 md:mb-0'>
        <div className='flex flex-col'>
          {/* Title */}
          <div className='py-4 text-center px-2 sm:px-6 flex justify-center items-center'>
            <h1 className='text-2xl max-w-5xl font-bold text-center break-words text-black dark:text-white'>
              {data.title}
            </h1>
          </div>

          {/* Main Image */}
          <div className='relative h-60 sm:h-80 w-full sm:w-11/12 mx-auto overflow-hidden rounded-lg'>
            {data.headerImage || data.thumbnailImage ? (
              <>
                <SafeImage
                  src={data.headerImage || data.thumbnailImage || ''}
                  alt={data.title}
                  fill
                  className='object-cover object-center'
                  fallbackSrc='/placeholder-image.jpg'
                />
                {/* Wave Pattern on left side of image */}
                <div className='absolute top-0 left-0 h-full w-10 sm:w-16 overflow-hidden'>
                  <svg
                    className='h-full'
                    width='24'
                    viewBox='0 0 120 1200'
                    preserveAspectRatio='none'
                    xmlns='http://www.w3.org/2000/svg'
                    style={{
                      transform: 'rotate(90deg) translateY(-100%)',
                      transformOrigin: 'top left',
                    }}
                  >
                    <path
                      d='M0,0 C40,33 80,67 120,100 C80,133 40,167 0,200 C40,233 80,267 120,300 C80,333 40,367 0,400 C40,433 80,467 120,500 C80,533 40,567 0,600 C40,633 80,667 120,700 C80,733 40,767 0,800 C40,833 80,867 120,900 C80,933 40,967 0,1000 C40,1033 80,1067 120,1100 C80,1133 40,1167 0,1200'
                      fill='none'
                      stroke='rgba(255,255,255,0.5)'
                      strokeWidth='12'
                    />
                  </svg>
                </div>
              </>
            ) : (
              <div className='absolute inset-0 bg-gray-200' />
            )}
          </div>

          {/* Info Bar */}
          <div className='relative flex flex-col md:flex-row items-center justify-center gap-2 md:space-x-6 py-3 text-sm mt-8 sm:mt-10 px-2'>
            <div className='flex items-center'>
              <span className='text-gray-500 dark:text-gray-400'>
                Industry:
              </span>
              <span className='ml-2 font-medium text-black dark:text-white'>
                {data.industry}
              </span>
            </div>
            <div className='flex flex-row flex-wrap gap-2 md:space-x-6'>
              <div className='flex items-center'>
                <span className='text-gray-500 dark:text-gray-400'>Role:</span>
                <span className='ml-2 font-medium text-black dark:text-white'>
                  {data.role}
                </span>
              </div>
              <div className='flex items-center'>
                <span className='text-gray-500 dark:text-gray-400'>
                  Vector:
                </span>
                <span className='ml-2 font-medium text-black dark:text-white'>
                  {data.vector || ''}
                </span>
              </div>
            </div>

            {/* Wave Icon - Positioned Absolutely */}
            <div className='hidden md:block absolute left-6 top-1/2 transform -translate-y-1/2'>
              <svg
                width='142'
                height='40'
                viewBox='0 0 142 40'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  d='M142 1C134.111 1 134.111 9.56036 126.222 9.56036C118.333 9.56036 118.333 1 110.445 1C102.556 1 102.556 9.56036 94.6667 9.56036C86.7778 9.56036 86.7777 1 78.8889 1C71 1 71 9.56036 63.1113 9.56036C55.2225 9.56036 55.2225 1 47.3331 1C39.4444 1 39.4444 9.56036 31.5556 9.56036C23.6669 9.56036 23.6669 1 15.7782 1C7.88874 1 7.88874 9.56036 -2.14577e-06 9.56036'
                  stroke='url(#paint0_linear_6117_13275)'
                  strokeWidth='2.01896'
                  strokeMiterlimit='10'
                />
                <path
                  d='M142 15.4355C134.111 15.4355 134.111 23.9958 126.222 23.9958C118.333 23.9958 118.333 15.4355 110.445 15.4355C102.556 15.4355 102.556 23.9958 94.6667 23.9958C86.7778 23.9958 86.7777 15.4355 78.8889 15.4355C71 15.4355 71 23.9958 63.1113 23.9958C55.2225 23.9958 55.2225 15.4355 47.3331 15.4355C39.4444 15.4355 39.4444 23.9958 31.5556 23.9958C23.6669 23.9958 23.6669 15.4355 15.7782 15.4355C7.88874 15.4355 7.88874 23.9958 -2.14577e-06 23.9958'
                  stroke='url(#paint1_linear_6117_13275)'
                  strokeWidth='2.01896'
                  strokeMiterlimit='10'
                />
                <path
                  d='M142 29.7026C134.111 29.7026 134.111 38.2629 126.222 38.2629C118.333 38.2629 118.333 29.7026 110.445 29.7026C102.556 29.7026 102.556 38.2629 94.6667 38.2629C86.7778 38.2629 86.7777 29.7026 78.8889 29.7026C71 29.7026 71 38.2629 63.1113 38.2629C55.2225 38.2629 55.2225 29.7026 47.3331 29.7026C39.4444 29.7026 39.4444 38.2629 31.5556 38.2629C23.6669 38.2629 23.6669 29.7026 15.7782 29.7026C7.88874 29.7026 7.88874 38.2629 -2.14577e-06 38.2629'
                  stroke='url(#paint2_linear_6117_13275)'
                  strokeWidth='2.01896'
                  strokeMiterlimit='10'
                />
                <defs>
                  <linearGradient
                    id='paint0_linear_6117_13275'
                    x1='115.069'
                    y1='5.28018'
                    x2='106.908'
                    y2='25.8455'
                    gradientUnits='userSpaceOnUse'
                  >
                    <stop stopColor='#0179B4' />
                    <stop offset='0.876334' stopColor='#88D8FF' />
                  </linearGradient>
                  <linearGradient
                    id='paint1_linear_6117_13275'
                    x1='115.069'
                    y1='19.7157'
                    x2='106.908'
                    y2='40.2808'
                    gradientUnits='userSpaceOnUse'
                  >
                    <stop stopColor='#0179B4' />
                    <stop offset='0.876334' stopColor='#88D8FF' />
                  </linearGradient>
                  <linearGradient
                    id='paint2_linear_6117_13275'
                    x1='115.069'
                    y1='33.9828'
                    x2='106.908'
                    y2='54.5479'
                    gradientUnits='userSpaceOnUse'
                  >
                    <stop stopColor='#0179B4' />
                    <stop offset='0.876334' stopColor='#88D8FF' />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className='p-2 sm:p-6 space-y-8'>
        {/* KPI Badges */}
        <div className='text-center mb-4'>
          <h3 className='text-gray-500 dark:text-gray-400 mb-3'>
            Potentially Impacted KPIs
          </h3>
          <div className='flex flex-wrap gap-2 sm:gap-4 justify-center'>
            {/* Display up to 3 KPIs */}
            {data.impact.metrics && data.impact.metrics.length > 0 ? (
              data.impact.metrics.slice(0, 3).map((kpi, index) => (
                <Badge
                  key={`metric-${index}`}
                  variant='secondary'
                  className='py-2 px-4 rounded-full bg-gray-100 dark:bg-gray-900 text-black dark:text-white hover:bg-gray-200 dark:hover:bg-gray-800 whitespace-normal text-sm border-0'
                >
                  {kpi.metric}
                </Badge>
              ))
            ) : (
              // Default KPIs if nothing else is available
              <Badge
                variant='secondary'
                className='py-2 px-4 rounded-full bg-gray-100 dark:bg-gray-900 text-black dark:text-white hover:bg-gray-200 dark:hover:bg-gray-800 border-0'
              >
                <ClockIcon className='mr-2 h-4 w-4' />
                No KPIs specified
              </Badge>
            )}
          </div>
        </div>
        {/* Introduction Section */}
        <div className='space-y-6 mt-9 md:mt-7'>
          <div className='relative rounded-lg p-2 sm:p-6'>
            <h2 className='text-xl font-bold border-b border-dotted pb-2 mb-4 text-black dark:text-white'>
              Introduction:{' '}
              {data.introduction.title ||
                'Where the Experience Often Breaks First'}
            </h2>
            <p className='text-black dark:text-white mb-4'>
              {data.introduction.text ||
                "Claims are the frontline of customer experience in insurance. It's where promises are tested — and often, where they fall apart."}
            </p>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='col-span-1'>
                <p className='text-black dark:text-white mb-4 font-medium'>
                  {data.introduction.transitionToChallange ||
                    'Many insurers still operate with outdated systems where:'}
                </p>
                {data.introduction.problems.map((problem, index) => (
                  <div
                    key={`problem-${index}`}
                    className='flex items-start gap-3 mb-4'
                  >
                    <div className='flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 font-medium'>
                      {data.introduction.problemIcons &&
                      data.introduction.problemIcons[index] ? (
                        <div className='w-6 h-6 overflow-hidden rounded-full'>
                          <SafeImage
                            src={data.introduction.problemIcons[index]}
                            alt={`Challenge ${index + 1} icon`}
                            width={24}
                            height={24}
                            className='object-cover w-full h-full'
                            fallbackSrc={`/icons/challenge-${index + 1}.svg`}
                          />
                        </div>
                      ) : index === 0 ? (
                        <svg
                          width='12'
                          height='12'
                          viewBox='0 0 24 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <path
                            d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                          <path
                            d='M12 6V12L16 14'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                        </svg>
                      ) : index === 1 ? (
                        <svg
                          width='12'
                          height='12'
                          viewBox='0 0 24 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <path
                            d='M18 8H6C4.89543 8 4 8.89543 4 10V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V10C20 8.89543 19.1046 8 18 8Z'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                          <path
                            d='M16 8V6C16 4.89543 15.1046 4 14 4H10C8.89543 4 8 4.89543 8 6V8'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                          <path
                            d='M12 12V16'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                          <path
                            d='M12 12H14'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                        </svg>
                      ) : (
                        <svg
                          width='12'
                          height='12'
                          viewBox='0 0 24 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <path
                            d='M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                          <path
                            d='M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                        </svg>
                      )}
                    </div>
                    <p className='text-sm text-black dark:text-white'>
                      {problem || ''}
                    </p>
                  </div>
                ))}
              </div>

              <div className='col-span-1'>
                <p className='text-black dark:text-white mb-4 font-medium'>
                  {data.introduction.transitionToQuestions ||
                    'This forces leaders to ask:'}
                </p>
                {data.introduction.questions.map((question, index) => (
                  <div
                    key={`question-${index}`}
                    className='flex items-start gap-3 mb-4'
                  >
                    <div className='flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 font-medium'>
                      {data.introduction.questionIcons &&
                      data.introduction.questionIcons[index] ? (
                        <div className='w-6 h-6 overflow-hidden rounded-full'>
                          <SafeImage
                            src={data.introduction.questionIcons[index]}
                            alt={`Question ${index + 1} icon`}
                            width={24}
                            height={24}
                            className='object-cover w-full h-full'
                            fallbackSrc={`/icons/question-${index + 1}.svg`}
                          />
                        </div>
                      ) : index === 0 ? (
                        <svg
                          width='12'
                          height='12'
                          viewBox='0 0 24 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <path
                            d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                          <path
                            d='M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                          <path
                            d='M12 17H12.01'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                        </svg>
                      ) : index === 1 ? (
                        <svg
                          width='12'
                          height='12'
                          viewBox='0 0 24 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <path
                            d='M21 11.5C21.0034 12.8199 20.6951 14.1219 20.1 15.3C19.3944 16.7118 18.3098 17.8992 16.9674 18.7293C15.6251 19.5594 14.0782 19.9994 12.5 20C11.1801 20.0035 9.87812 19.6951 8.7 19.1L3 21L4.9 15.3C4.30493 14.1219 3.99656 12.8199 4 11.5C4.00061 9.92179 4.44061 8.37488 5.27072 7.03258C6.10083 5.69028 7.28825 4.6056 8.7 3.90003C9.87812 3.30496 11.1801 2.99659 12.5 3.00003H13C15.0843 3.11502 17.053 3.99479 18.5291 5.47089C20.0052 6.94699 20.885 8.91568 21 11V11.5Z'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                        </svg>
                      ) : (
                        <svg
                          width='12'
                          height='12'
                          viewBox='0 0 24 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <path
                            d='M12 16V12M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                            strokeLinejoin='round'
                          />
                        </svg>
                      )}
                    </div>
                    <p className='text-sm text-black dark:text-white'>
                      {question || ''}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Process Steps Section */}
        <div className='space-y-6'>
          <div className='relative rounded-lg p-2 sm:p-6'>
            <div className='flex flex-col md:flex-row md:justify-between md:items-center border-b border-dotted pb-2 mb-4 gap-2'>
              <h2 className='text-xl font-bold text-black dark:text-white'>
                {data.process?.title || 'Process'}
              </h2>
              {/* Wave Icon on right side of process section title */}
              <div className='flex items-center'>
                <svg
                  width='142'
                  height='40'
                  viewBox='0 0 142 40'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M142 1C134.111 1 134.111 9.56036 126.222 9.56036C118.333 9.56036 118.333 1 110.445 1C102.556 1 102.556 9.56036 94.6667 9.56036C86.7778 9.56036 86.7777 1 78.8889 1C71 1 71 9.56036 63.1113 9.56036C55.2225 9.56036 55.2225 1 47.3331 1C39.4444 1 39.4444 9.56036 31.5556 9.56036C23.6669 9.56036 23.6669 1 15.7782 1C7.88874 1 7.88874 9.56036 -2.14577e-06 9.56036'
                    stroke='url(#paint0_linear_6117_13275)'
                    strokeWidth='2.01896'
                    strokeMiterlimit='10'
                  />
                  <path
                    d='M142 15.4355C134.111 15.4355 134.111 23.9958 126.222 23.9958C118.333 23.9958 118.333 15.4355 110.445 15.4355C102.556 15.4355 102.556 23.9958 94.6667 23.9958C86.7778 23.9958 86.7777 15.4355 78.8889 15.4355C71 15.4355 71 23.9958 63.1113 23.9958C55.2225 23.9958 55.2225 15.4355 47.3331 15.4355C39.4444 15.4355 39.4444 23.9958 31.5556 23.9958C23.6669 23.9958 23.6669 15.4355 15.7782 15.4355C7.88874 15.4355 7.88874 23.9958 -2.14577e-06 23.9958'
                    stroke='url(#paint1_linear_6117_13275)'
                    strokeWidth='2.01896'
                    strokeMiterlimit='10'
                  />
                  <path
                    d='M142 29.7026C134.111 29.7026 134.111 38.2629 126.222 38.2629C118.333 38.2629 118.333 29.7026 110.445 29.7026C102.556 29.7026 102.556 38.2629 94.6667 38.2629C86.7778 38.2629 86.7777 29.7026 78.8889 29.7026C71 29.7026 71 38.2629 63.1113 38.2629C55.2225 38.2629 55.2225 29.7026 47.3331 29.7026C39.4444 29.7026 39.4444 38.2629 31.5556 38.2629C23.6669 38.2629 23.6669 29.7026 15.7782 29.7026C7.88874 29.7026 7.88874 38.2629 -2.14577e-06 38.2629'
                    stroke='url(#paint2_linear_6117_13275)'
                    strokeWidth='2.01896'
                    strokeMiterlimit='10'
                  />
                  <defs>
                    <linearGradient
                      id='paint0_linear_6117_13275'
                      x1='115.069'
                      y1='5.28018'
                      x2='106.908'
                      y2='25.8455'
                      gradientUnits='userSpaceOnUse'
                    >
                      <stop stopColor='#0179B4' />
                      <stop offset='0.876334' stopColor='#88D8FF' />
                    </linearGradient>
                    <linearGradient
                      id='paint1_linear_6117_13275'
                      x1='115.069'
                      y1='19.7157'
                      x2='106.908'
                      y2='40.2808'
                      gradientUnits='userSpaceOnUse'
                    >
                      <stop stopColor='#0179B4' />
                      <stop offset='0.876334' stopColor='#88D8FF' />
                    </linearGradient>
                    <linearGradient
                      id='paint2_linear_6117_13275'
                      x1='115.069'
                      y1='33.9828'
                      x2='106.908'
                      y2='54.5479'
                      gradientUnits='userSpaceOnUse'
                    >
                      <stop stopColor='#0179B4' />
                      <stop offset='0.876334' stopColor='#88D8FF' />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 mt-6'>
              {data.process?.steps &&
                data.process.steps.map((step, index) => (
                  <div key={`process-step-${index}`} className='relative'>
                    {/* Step */}
                    <div className='bg-blue-50 dark:bg-blue-950 p-4 rounded-lg h-full flex flex-col relative'>
                      <div className='mb-2'>
                        <h3 className='text-blue-700 dark:text-blue-300 font-medium break-words'>
                          Step {index + 1}: {step.title}
                        </h3>
                      </div>
                      <div className='flex-grow overflow-auto hide-scrollbar'>
                        <p className='text-sm text-black dark:text-white break-words'>
                          {step.description}
                        </p>
                      </div>
                    </div>

                    {/* Arrow (only between steps, not after the last one) */}
                    {index < data.process.steps.length - 1 && (
                      <div className='hidden md:flex absolute -right-6 top-1/2 transform -translate-y-1/2 z-10 '>
                        <svg
                          width='24'
                          height='24'
                          viewBox='0 0 40 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <path
                            d='M39.0607 13.0607C39.6464 12.4749 39.6464 11.5251 39.0607 10.9393L29.5147 1.3934C28.9289 0.807611 27.9792 0.807611 27.3934 1.3934C26.8076 1.97919 26.8076 2.92893 27.3934 3.51472L35.8787 12L27.3934 20.4853C26.8076 21.0711 26.8076 22.0208 27.3934 22.6066C27.9792 23.1924 28.9289 23.1924 29.5147 22.6066L39.0607 13.0607ZM0 13.5H38V10.5H0V13.5Z'
                            fill='currentColor'
                            className='text-blue-500 dark:text-blue-400'
                          />
                        </svg>
                      </div>
                    )}
                  </div>
                ))}
            </div>
          </div>
        </div>
        {/* Solution Blueprint Section */}
        <div className='space-y-6'>
          <div className='rounded-lg p-2 sm:p-6 bg-gray-100 dark:bg-gray-900'>
            <h2 className='text-2xl font-bold mb-6 text-left text-black dark:text-white'>
              {data.solution.title ||
                'Solution Blueprint: Reimagining Claims with AI'}
            </h2>
            {/* <p className='text-black dark:text-white mb-8 text-center'>
              {data.solution.description || ''}
            </p> */}
            {/* Responsive grid for all solution items */}
            <div className='flex flex-col md:flex-row flex-wrap gap-4 mb-4 justify-center w-full'>
              {data.solution.items.map((solution, index) => (
                <div
                  key={index}
                  className='bg-white dark:bg-black rounded-lg p-5 shadow-sm border-2 border-blue-200 dark:border-blue-300 w-full md:basis-[calc(50%-16px)] lg:basis-[calc(33.333%-11px)]'
                >
                  <h3 className='text-blue-500 dark:text-blue-400 font-semibold mb-2 flex items-start'>
                    <span className='text-blue-500 dark:text-blue-400 mr-2 font-bold'>
                      {(index + 1).toString().padStart(2, '0')}.
                    </span>
                    {solution.title.replace(':', '')}
                  </h3>
                  <div className='flex-grow pl-8'>
                    <p className='text-black dark:text-white'>
                      {solution.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* Impact Section */}
        <div className='space-y-6'>
          <div className='rounded-lg p-2 sm:p-6 bg-gray-100 dark:bg-gray-900'>
            <h2 className='text-2xl font-bold mb-6 text-black dark:text-white'>
              {data.impact.title || 'Potential Impact: From Lagging to Leading'}
            </h2>
            <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
              {/* Render impact metrics dynamically - limit to 3 */}
              {data.impact.metrics.slice(0, 4).map((metric, index) => {
                // Determine background color for icon based on index
                const iconBgColor =
                  index === 0
                    ? 'bg-purple-100 dark:bg-purple-900'
                    : index === 1
                    ? 'bg-yellow-100 dark:bg-yellow-900'
                    : index === 2
                    ? 'bg-red-100 dark:bg-red-900'
                    : 'bg-green-100 dark:bg-green-900';
                return (
                  <div
                    key={`impact-${index}`}
                    className='bg-white dark:bg-black rounded-lg p-6 shadow-sm border border-gray-100 dark:border-gray-900'
                  >
                    <div className='flex justify-between items-center'>
                      <div className='flex-1 pr-2'>
                        {/* Main metric value - large and bold */}
                        <div className='flex items-baseline'>
                          <h3
                            className={`text-3xl font-bold text-black dark:text-white ${
                              !metric.value && 'opacity-0'
                            }`}
                          >
                            {metric.value ||
                              (index === 0
                                ? 'Up to 50%'
                                : index === 1
                                ? 'Up to 70%'
                                : index === 2
                                ? 'Up to 40%'
                                : '20 to 30%')}
                          </h3>
                        </div>

                        {/* Always display the metric name */}
                        {/* <p className="text-blue-600 dark:text-blue-400 text-sm font-medium mt-1">
                          {metric.metric || `Impact ${index + 1}`}
                        </p> */}

                        {/* Description - prevent text truncation */}
                        <p className='text-black dark:text-white mt-2 break-words'>
                          {
                            metric.description || ''
                            // `Improvement in ${
                            //   metric.metric || 'processing efficiency'
                            // }`
                          }
                        </p>
                      </div>

                      {/* Icon */}
                      <div
                        className={`w-12 h-12 flex-shrink-0 ${iconBgColor} rounded-lg flex items-center justify-center`}
                      >
                        {metric.icon ? (
                          <SafeImage
                            src={metric.icon}
                            alt={metric.metric}
                            width={40}
                            height={40}
                            className='object-cover'
                            fallbackSrc={`/icons/impact-${index + 1}.svg`}
                          />
                        ) : (
                          <>
                            {index === 0 ? (
                              <ClockIcon className='h-6 w-6 text-purple-600 dark:text-purple-400' />
                            ) : index === 1 ? (
                              <BarChartIcon className='h-6 w-6 text-yellow-600 dark:text-yellow-400' />
                            ) : index === 2 ? (
                              <PersonIcon className='h-6 w-6 text-red-600 dark:text-red-400' />
                            ) : (
                              <ClockIcon className='h-6 w-6 text-green-600 dark:text-green-400' />
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        {/* Market Intelligence Section */}
        {/* {data.marketIntelligence && Array.isArray(data.marketIntelligence) && data.marketIntelligence.length > 0 && (
          <div className="space-y-6">
            <div className="relative">
              <h2 className="text-xl font-semibold flex items-center gap-2 text-black dark:text-white">
                <MagnifyingGlassIcon className="h-5 w-5" />
                Market Intelligence
              </h2>
            </div>

            {data.marketIntelligence.map((section, sectionIndex) => (
              <div key={sectionIndex} className="space-y-4 mb-6">
                <h3 className="text-lg font-medium text-black dark:text-white">{section.title}</h3>
                <p className="text-sm text-black dark:text-white">{section.description}</p>

                <div className="space-y-4">
                  {section.insights.map((insight, insightIndex) => (
                    <div key={insightIndex} className="bg-blue-50 dark:bg-blue-950 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <Badge variant="secondary" className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 border-0">
                          {insight.type}
                        </Badge>
                        <a
                          href={insight.source_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center gap-1 text-sm"
                        >
                          <Link2Icon className="h-3 w-3" />
                          Source
                        </a>
                      </div>
                      <p className="text-black dark:text-white" dangerouslySetInnerHTML={{ __html: insight.summary }} />
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )} */}

        {/* Conclusion Metrics */}
        {data.marketMetrics &&
          typeof data.marketMetrics === 'object' &&
          data.isOwner && (
            <div className='space-y-6'>
              <div className='relative'>
                {/* <h2 className="text-xl font-semibold flex items-center gap-2">
                <BarChartIcon className="h-5 w-5" />
                Conclusion
              </h2> */}
              </div>

              <div className='grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6'>
                {(data.marketMetrics?.marketSize ||
                  data.marketMetrics?.['Market Size ($ Value)']) && (
                  <div className='bg-green-50 dark:bg-green-950 rounded-lg p-6 text-center'>
                    <h3 className='text-sm font-medium text-green-800 dark:text-green-300 mb-2'>
                      Market Size
                    </h3>
                    <div className='text-2xl font-bold text-green-700 dark:text-green-400'>
                      {data.marketMetrics?.marketSize
                        ? `$${(
                            Number(data.marketMetrics.marketSize) / 1000000000
                          ).toFixed(1)}B`
                        : data.marketMetrics?.['Market Size ($ Value)']
                        ? `$${(
                            Number(
                              data.marketMetrics['Market Size ($ Value)']
                            ) / 1000000000
                          ).toFixed(1)}B`
                        : '$0B'}
                    </div>
                  </div>
                )}

                {(data.marketMetrics?.marketCAGR ||
                  data.marketMetrics?.['CAGR']) && (
                  <div className='bg-blue-50 dark:bg-blue-950 rounded-lg p-6 text-center'>
                    <h3 className='text-sm font-medium text-blue-800 dark:text-blue-300 mb-2'>
                      CAGR
                    </h3>
                    <div className='text-2xl font-bold text-blue-700 dark:text-blue-400'>
                      {data.marketMetrics?.marketCAGR
                        ? `${data.marketMetrics.marketCAGR}%`
                        : data.marketMetrics?.['CAGR']
                        ? `${data.marketMetrics['CAGR']}%`
                        : '0%'}
                    </div>
                  </div>
                )}

                {(data.marketMetrics?.marketROI ||
                  data.marketMetrics?.['ROI Range']) && (
                  <div className='bg-purple-50 dark:bg-purple-950 rounded-lg p-6 text-center'>
                    <h3 className='text-sm font-medium text-purple-800 dark:text-purple-300 mb-2'>
                      ROI Range
                    </h3>
                    <div className='text-2xl font-bold text-purple-700 dark:text-purple-400'>
                      {data.marketMetrics?.marketROI
                        ? `${data.marketMetrics.marketROI}%`
                        : data.marketMetrics?.['ROI Range']
                        ? `${data.marketMetrics['ROI Range']}%`
                        : '0%'}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

        {/* Conclusion Section */}
        <div className='space-y-4 bg-gray-100 dark:bg-gray-900 p-2 sm:p-6 rounded-lg'>
          <div className='relative'>
            <h2 className='text-xl font-semibold text-black dark:text-white'>
              {data.conclusion.title || 'Conclusion'}
            </h2>
          </div>
          <p className='text-black dark:text-white leading-relaxed '>
            {data.conclusion.text || ''}
          </p>
          <div className='mt-4'>
            <h3 className='font-bold mb-2 text-black dark:text-white'>
              {data.conclusion.resultTitle || 'The Result'}
            </h3>
            <p className='text-black dark:text-white '>
              {data.conclusion.result || ''}
            </p>
          </div>
        </div>

        {/* AI Processing Status */}
        {data.aiProcessingStatus && (
          <div className='p-2 sm:p-4 bg-gray-100 dark:bg-gray-900 rounded-lg'>
            <h3 className='text-lg font-medium mb-2 text-black dark:text-white'>
              AI Processing Status
            </h3>
            <p className='text-sm text-black dark:text-white'>
              {data.aiProcessingStatus}
            </p>
            {data.aiProcessingError && (
              <p className='text-sm text-red-600 dark:text-red-400 mt-2'>
                {data.aiProcessingError}
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
