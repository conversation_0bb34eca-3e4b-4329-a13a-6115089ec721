'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  ArrowRight,
  Users,
  Target,
  Lightbulb,
  Shield,
  Pen,
  BookOpen,
  Zap,
  Award,
} from 'lucide-react';
import Link from 'next/link';
import Navbar from '@/components/Navbar';
import Footer from '@/components/sections/Homepage/Footer';
import { OptimizedImage } from '@/components/ui/optimized-image';

const AboutPage = () => {
  return (
    <>
      <Navbar />
      <main className='min-h-screen bg-gray-50'>
        {/* Hero Section */}
        <section className='pt-32 pb-16 bg-white'>
          <div className='container mx-auto px-6 lg:px-8'>
            <div className='max-w-3xl mx-auto text-center'>
              <h1 className='text-4xl font-bold mb-4 text-black'>
                Move from AI Ambition to Execution
              </h1>
              <p className='text-lg text-gray-600 leading-relaxed mb-10 max-w-2xl mx-auto'>
                Turin OS gives business leaders the edge — with clear use cases,
                real-time data, and tools that drive execution.
              </p>
            </div>

            {/* Main Image */}
            <div className='max-w-4xl mx-auto mt-8 mb-16 shadow-lg'>
              <OptimizedImage
                src='https://res.cloudinary.com/dyiso4ohk/image/upload/v1746549370/3d-rendering-artificial-intelligence-ai-research-robot-cyborg-development-future-people-living-digital-data-mining-machine-learning-technology-design-computer-brain_hgidds.jpg'
                alt='Team collaborating on content'
                width={800}
                height={450}
                className='w-full h-auto rounded-lg'
                priority
                quality={90}
                timeout={8000}
                fallbackSrc='https://via.placeholder.com/800x450?text=AI+Innovation'
              />
            </div>
          </div>
        </section>

        {/* Vision Section */}
        <section className='py-12 bg-white border-t border-b border-gray-100'>
          <div className='container mx-auto px-6 lg:px-8'>
            <div className='text-center mb-12'>
              <h2 className='text-3xl font-bold mb-4 text-black'>
                Our <span className='text-blue-600'>Vision</span>
              </h2>
              <p className='text-gray-600 max-w-lg mx-auto'>
                To lead the charge in turning AI potential into real performance
                — fast, measurable, and market-moving. .
              </p>
            </div>

            {/* Four Feature Boxes */}
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12'>
              {[
                {
                  icon: '/images/about/icons/1.png',
                  title: 'Innovation That Drives Execution',
                  description: 'Content that drives engagement',
                },
                {
                  icon: '/images/about/icons/2.png',
                  title: 'Responsible AI Leadership',
                  description: 'Polished and professional',
                },
                {
                  icon: '/images/about/icons/3.png',
                  title: 'Sustainable, Scalable Growth',
                  description: 'Cutting-edge technology',
                },
                {
                  icon: '/images/about/icons/4.png',
                  title: 'Outcome-Driven Transformation',
                  description: 'Maximum reach and impact',
                },
              ].map((feature, index) => (
                <div
                  key={index}
                  className='bg-white p-6 rounded-lg border border-gray-200 text-center'
                >
                  <div className='mx-auto w-14 h-14 flex items-center justify-center bg-[#3384FF] rounded-2xl mb-4 shadow-[0_8px_32px_0_rgba(51,132,255,0.25)]'>
                    <OptimizedImage
                      src={feature.icon}
                      alt={feature.title}
                      width={32}
                      height={32}
                      className={`${index === 1 ? 'w-8' : 'w-7'} h-auto`}
                      quality={80}
                      timeout={5000}
                      fallbackSrc={`https://via.placeholder.com/32x32?text=${encodeURIComponent(
                        feature.title
                      )}`}
                    />
                  </div>
                  <h3 className='font-semibold text-sm mb-2 text-black'>
                    {feature.title}
                  </h3>
                  {/* <p className="text-gray-500 text-xs">{feature.description}</p> */}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section id='mission' className='py-16'>
          <div className='container mx-auto px-6 lg:px-8'>
            <div className='text-center mb-12'>
              <h2 className='text-3xl font-bold mb-4 text-black'>
                Our <span className='text-blue-600'>Mission</span>
              </h2>
              <p className='text-gray-600 max-w-2xl mx-auto'></p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-12 items-center mt-12'>
              <div className='shadow-md'>
                <OptimizedImage
                  src='https://res.cloudinary.com/dyiso4ohk/image/upload/v1746549412/ai-chip-artificial-intelligence-future-technology-innovation_vqwoqs.jpg'
                  alt='Content creation process'
                  width={600}
                  height={400}
                  className='w-full h-auto rounded-lg'
                  priority
                  quality={90}
                  timeout={8000}
                  fallbackSrc='https://via.placeholder.com/600x400?text=AI+Technology'
                />
              </div>
              <div>
                <p className='text-gray-700 mb-6'>
                  We aim to empower businesses to turn AI ambition into
                  real-world execution — by providing practical strategies,
                  curated insights, and the tools needed to deliver measurable
                  impact.
                </p>
                <p className='text-gray-700 mb-6'>
                  Through curated intelligence, strategic tools, and real-world
                  frameworks, we empower organizations to turn bold ideas into
                  tangible business results — fast, scalable, and sustainable.{' '}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className='py-16 bg-white border-t border-gray-100'>
          <div className='container mx-auto px-6 lg:px-8'>
            <div className='max-w-3xl mx-auto text-center mb-12'>
              <h2 className='text-3xl font-bold mb-4 text-black'>
                Meet the <span className='text-blue-600'>Team</span>
              </h2>
            </div>

            <div className='mb-12'>
              <h3 className='text-2xl font-bold mb-6 text-center'>
                Founding Team
              </h3>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto'>
                {[
                  {
                    name: 'Sandeep Hardikar',
                    role: 'Co-Founder & CEO',
                    image:
                      'https://res.cloudinary.com/dyiso4ohk/image/upload/v1746179693/Sandeep_nicqx2.png',
                  },
                  {
                    name: 'Asavari Hardikar',
                    role: 'Co-Founder, Chief Enablement Officer',
                    image:
                      'https://res.cloudinary.com/dyiso4ohk/image/upload/v1746179694/Asavari_yxzf1q.jpg',
                  },
                  {
                    name: 'Raju Chouthai',
                    role: 'Co-Founder, Head – India & APAC Business',
                    image:
                      'https://res.cloudinary.com/dyiso4ohk/image/upload/v1746179695/Raju_dobtu7.jpg',
                  },
                ].map((member, index) => (
                  <div key={index} className='shadow-md'>
                    <div className='relative'>
                      <OptimizedImage
                        src={member.image}
                        alt={member.name}
                        width={300}
                        height={300}
                        className='w-full h-[300px] object-cover rounded-lg'
                        quality={80}
                        timeout={5000}
                        fallbackSrc={`https://via.placeholder.com/300x300?text=${encodeURIComponent(
                          member.name
                        )}`}
                      />
                      <div className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 rounded-b-lg'>
                        <p className='text-white font-medium text-sm'>
                          {member.name}
                        </p>
                        <p className='text-gray-300 text-xs'>{member.role}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className='text-2xl font-bold mb-6 text-center'>Core Team</h3>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto'>
                {[
                  {
                    name: 'Nikhil Mahen',
                    role: 'Head of Innovation',
                    image:
                      'https://res.cloudinary.com/dyiso4ohk/image/upload/v1746568777/Untitled_design_1_gffgvr.png',
                  },
                  {
                    name: 'Prashant K Parihar',
                    role: 'Program Manager',
                    image:
                      'https://res.cloudinary.com/dyiso4ohk/image/upload/v1746179694/prashant_jl66ok.jpg',
                  },
                  {
                    name: 'Shereen Bajaj',
                    role: 'Product Lead',
                    image:
                      'https://res.cloudinary.com/dyiso4ohk/image/upload/v1746179695/Shereen_Bajaj_smytmg.png',
                  },
                ].map((member, index) => (
                  <div key={index} className='shadow-md'>
                    <div className='relative'>
                      <OptimizedImage
                        src={member.image}
                        alt={member.name}
                        width={300}
                        height={300}
                        className='w-full h-[300px] object-cover rounded-lg'
                        quality={80}
                        timeout={5000}
                        fallbackSrc={`https://via.placeholder.com/300x300?text=${encodeURIComponent(
                          member.name
                        )}`}
                      />
                      <div className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 rounded-b-lg'>
                        <p className='text-white font-medium text-sm'>
                          {member.name}
                        </p>
                        <p className='text-gray-300 text-xs'>{member.role}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        {/* <section className="py-16">
          <div className="container mx-auto px-6 lg:px-8 max-w-4xl text-center">
            <h2 className="text-3xl font-bold mb-6">Ready to transform your AI strategy?</h2>
            <p className="text-xl text-gray-600 mb-8">
              Join forward-thinking organizations that are turning AI ambition into real-world business impact.
            </p>
            <Button size="lg" asChild className="bg-blue-600 hover:bg-blue-700">
              <Link href="/sign-up">
                Get Started Today
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </section> */}
      </main>
      <Footer />
    </>
  );
};

export default AboutPage;
