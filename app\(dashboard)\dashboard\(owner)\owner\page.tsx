'use client';

import { use, useEffect, useState } from 'react';
import { useUser } from '@/lib/auth';
import { Card } from '@/components/ui/card';
import { LineChart, Line, ResponsiveContainer } from 'recharts';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '@/components/ui/accordion';
// Commented out welcome popup for owners
// Add a color palette for random selection
const colorPalette = [
  { color: '#3b82f6', darkColor: '#1e40af' }, // blue
  { color: '#10b981', darkColor: '#047857' }, // green
  { color: '#f59e42', darkColor: '#b45309' }, // orange
  { color: '#ef4444', darkColor: '#991b1b' }, // red
  { color: '#a855f7', darkColor: '#6d28d9' }, // purple
  { color: '#fbbf24', darkColor: '#b45309' }, // yellow
  { color: '#6366f1', darkColor: '#3730a3' }, // indigo
  { color: '#14b8a6', darkColor: '#0f766e' }, // teal
];

// Helper to get a random color from the palette
function getRandomColor() {
  return colorPalette[Math.floor(Math.random() * colorPalette.length)];
}

const SimpleLineChart = ({
  data,
  color,
  darkColor,
}: {
  data: any[];
  color: string;
  darkColor?: string;
}) => {
  // Use the provided darkColor or default to the regular color
  const finalDarkColor = darkColor || color;

  return (
    <ResponsiveContainer width='100%' height={50}>
      <LineChart data={data}>
        <Line
          type='monotone'
          dataKey='value'
          stroke={color}
          strokeWidth={2}
          dot={false}
          className='dark:stroke-[var(--dark-color)]'
          style={{ '--dark-color': finalDarkColor } as React.CSSProperties}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default function OwnerDashboardPage() {
  const { userPromise } = useUser();
  const user = use(userPromise);
  const [statsData, setStatsData] = useState<any>({});
  const [recentActivity, setRecentActivity] = useState<
    { id: number; date: string; title: string; description: string }[]
  >([]);

  // Route protection is now handled by the layout

  useEffect(() => {
    // Note: Advanced analytics feature has been removed from all plans
    // This will show a message about the feature being unavailable
    const fetchStatsData = async () => {
      try {
        const response = await fetch('/api/analytics');
        if (!response.ok) {
          // Analytics feature has been removed, show appropriate message
          if (response.status === 410) {
            const errorData = await response.json();
            console.log('Analytics feature removed:', errorData.message);
            // Set empty stats data to show placeholder content
            setStatsData({});
            setRecentActivity([]);
            return;
          }
          throw new Error('Failed to fetch stats data');
        }
        const data = await response.json();

        console.log(data);

        setStatsData(data.data);
        setRecentActivity(data.recentActivity);
      } catch (error) {
        console.error('Error fetching stats data:', error);
        // Set empty data on error
        setStatsData({});
        setRecentActivity([]);
      }
    };

    fetchStatsData();
  }, [user]);

  const generateGraphAnalyticsCard = (analyticsData: any) => {
    const { color, darkColor } = getRandomColor();
    return (
      <Card className='p-6'>
        <div className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <h2 className='text-sm font-medium'>{analyticsData.label}</h2>
        </div>
        <div className='flex flex-col'>
          <div className='text-2xl font-bold'>{analyticsData.value}</div>
          {/* <p className='text-xs text-muted-foreground'>
            {analyticsData.change}
          </p> */}
        </div>
        <div className='h-[50px]'>
          <SimpleLineChart
            data={analyticsData.data}
            color={color}
            darkColor={darkColor}
          />
        </div>
      </Card>
    );
  };

  return (
    <div className='space-y-8'>
      {/* Welcome Section */}
      <div className='relative overflow-hidden rounded-lg bg-blue-600 dark:bg-blue-800 p-8 text-white'>
        <div className='relative z-10'>
          <h1 className='text-3xl font-semibold'>
            Welcome, {(user as any)?.teamRole} 👋
            <br />
            {user?.name}
          </h1>
          <p className='mt-2 text-blue-100 dark:text-blue-200'>
            Manage your law firm and team from here
          </p>
        </div>
        <div className='absolute right-0 top-0 h-full w-1/3'>
          <svg
            className='absolute right-0 h-full w-full transform text-blue-500 dark:text-blue-700'
            viewBox='0 0 100 100'
            preserveAspectRatio='none'
            fill='currentColor'
            opacity='0.08'
          >
            <polygon points='0,0 100,0 100,100' />
          </svg>
        </div>
      </div>

      {/* Stats Overview */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {statsData.totalCases &&
          generateGraphAnalyticsCard(statsData.totalCases)}
        {statsData.totalTeamMembers &&
          generateGraphAnalyticsCard(statsData.totalTeamMembers)}
        {statsData.successRate &&
          generateGraphAnalyticsCard(statsData.successRate)}
        {statsData.totalRevenue &&
          generateGraphAnalyticsCard(statsData.totalRevenue)}
      </div>

      {/* Recent Activity */}
      <Card className='p-6'>
        <h2 className='text-lg font-semibold mb-4'>Recent Activity</h2>
        {(user as any)?.teamRole === 'owner' ? (
          <Accordion type='single' collapsible>
            {recentActivity.map((activity) => (
              <AccordionItem key={activity.id} value={`item-${activity.id}`}>
                <AccordionTrigger>{activity.title}</AccordionTrigger>
                <AccordionContent>{activity.description}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <p>You do not have access to view recent activity.</p>
        )}
      </Card>
    </div>
  );
}
