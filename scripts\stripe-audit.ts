/**
 * <PERSON>e Audit Script - View current plans without making changes
 */

import <PERSON><PERSON> from 'stripe';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

async function auditStripe(): Promise<void> {
  console.log('🔍 Stripe Account Audit');
  console.log('========================\n');

  try {
    // Get account info
    const account = await stripe.accounts.retrieve();
    console.log(`📊 Account: ${account.display_name || account.id}`);
    console.log(`🌍 Country: ${account.country}`);
    console.log(`💰 Currency: ${account.default_currency?.toUpperCase()}`);
    console.log('');

    // Get all products
    console.log('📦 PRODUCTS:');
    console.log('============');
    const products = await stripe.products.list({ limit: 100 });
    
    if (products.data.length === 0) {
      console.log('No products found.');
    } else {
      products.data.forEach((product, index) => {
        console.log(`${index + 1}. ${product.name}`);
        console.log(`   ID: ${product.id}`);
        console.log(`   Status: ${product.active ? '✅ Active' : '❌ Inactive'}`);
        console.log(`   Created: ${new Date(product.created * 1000).toLocaleDateString()}`);
        if (product.description) {
          console.log(`   Description: ${product.description}`);
        }
        if (product.metadata && Object.keys(product.metadata).length > 0) {
          console.log(`   Metadata: ${JSON.stringify(product.metadata, null, 2)}`);
        }
        console.log('');
      });
    }

    // Get all prices
    console.log('💰 PRICES:');
    console.log('===========');
    const prices = await stripe.prices.list({ limit: 100 });
    
    if (prices.data.length === 0) {
      console.log('No prices found.');
    } else {
      prices.data.forEach((price, index) => {
        const amount = price.unit_amount ? `$${(price.unit_amount / 100).toFixed(2)}` : 'Free';
        const interval = price.recurring?.interval || 'one-time';
        console.log(`${index + 1}. ${amount}/${interval}`);
        console.log(`   ID: ${price.id}`);
        console.log(`   Status: ${price.active ? '✅ Active' : '❌ Inactive'}`);
        console.log(`   Product: ${price.product}`);
        console.log(`   Currency: ${price.currency.toUpperCase()}`);
        if (price.metadata && Object.keys(price.metadata).length > 0) {
          console.log(`   Metadata: ${JSON.stringify(price.metadata, null, 2)}`);
        }
        console.log('');
      });
    }

    // Get active subscriptions
    console.log('🔄 ACTIVE SUBSCRIPTIONS:');
    console.log('========================');
    const subscriptions = await stripe.subscriptions.list({ 
      status: 'active',
      limit: 100 
    });
    
    if (subscriptions.data.length === 0) {
      console.log('No active subscriptions found. ✅');
    } else {
      console.log(`⚠️  Found ${subscriptions.data.length} active subscriptions:`);
      subscriptions.data.forEach((sub, index) => {
        console.log(`${index + 1}. Subscription ${sub.id}`);
        console.log(`   Customer: ${sub.customer}`);
        console.log(`   Status: ${sub.status}`);
        console.log(`   Created: ${new Date(sub.created * 1000).toLocaleDateString()}`);
        console.log(`   Current period: ${new Date(sub.current_period_start * 1000).toLocaleDateString()} - ${new Date(sub.current_period_end * 1000).toLocaleDateString()}`);
        
        if (sub.items.data.length > 0) {
          console.log(`   Items:`);
          sub.items.data.forEach(item => {
            console.log(`     - Price: ${item.price.id} (${item.quantity}x)`);
          });
        }
        console.log('');
      });
    }

    // Get customers
    console.log('👥 CUSTOMERS:');
    console.log('=============');
    const customers = await stripe.customers.list({ limit: 10 });
    
    if (customers.data.length === 0) {
      console.log('No customers found.');
    } else {
      console.log(`Found ${customers.data.length} customers (showing first 10):`);
      customers.data.forEach((customer, index) => {
        console.log(`${index + 1}. ${customer.email || customer.id}`);
        console.log(`   ID: ${customer.id}`);
        console.log(`   Created: ${new Date(customer.created * 1000).toLocaleDateString()}`);
        if (customer.metadata && Object.keys(customer.metadata).length > 0) {
          console.log(`   Metadata: ${JSON.stringify(customer.metadata, null, 2)}`);
        }
        console.log('');
      });
    }

    console.log('✅ Audit completed successfully!');
    console.log('\n📝 Summary:');
    console.log(`   Products: ${products.data.length} (${products.data.filter(p => p.active).length} active)`);
    console.log(`   Prices: ${prices.data.length} (${prices.data.filter(p => p.active).length} active)`);
    console.log(`   Active Subscriptions: ${subscriptions.data.length}`);
    console.log(`   Customers: ${customers.data.length}`);

  } catch (error: any) {
    console.error('❌ Error during audit:', error.message);
    if (error.type === 'StripeAuthenticationError') {
      console.error('   Check your STRIPE_SECRET_KEY in .env file');
    }
    process.exit(1);
  }
}

// Run the audit
if (require.main === module) {
  auditStripe();
}

export { auditStripe };
