import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { caseStudiesApi } from './api/caseStudiesApi';
import { teamApi } from './api/teamApi';

export const store = configureStore({
  reducer: {
    [caseStudiesApi.reducerPath]: caseStudiesApi.reducer,
    [teamApi.reducerPath]: teamApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      // Allow FormData objects to be passed to RTK Query mutations
      serializableCheck: {
        ignoredActions: ['caseStudiesApi/executeMutation', 'teamApi/executeMutation', 'caseStudiesApi/executeQuery', 'teamApi/executeQuery'],
        ignoredActionPaths: [
          'meta.arg.body',
          'payload.body',
          'meta.baseQueryMeta.request',
          'meta.baseQueryMeta.response',
          'meta.arg.originalArgs', // Add this to fix the FormData serialization issue
          'meta.baseQueryMeta.prepareHeaders' // Add this to fix headers serialization issues
        ],
        ignoredPaths: [
          'caseStudiesApi.mutations',
          'teamApi.mutations',
          'caseStudiesApi.queries',
          'teamApi.queries'
        ],
      },
    }).concat(caseStudiesApi.middleware, teamApi.middleware),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
