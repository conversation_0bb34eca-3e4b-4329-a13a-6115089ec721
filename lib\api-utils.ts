import { cache } from 'react';

type FetchOptions = RequestInit & {
  timeout?: number;
  retry?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
  cacheKey?: string;
  cacheTtl?: number;
};

// Simple in-memory cache for API responses
const apiCache = new Map<string, { data: any; expiry: number }>();

/**
 * Enhanced fetch function with:
 * - Timeout support
 * - Automatic retries with exponential backoff
 * - Error handling with helpful messages
 * - Response caching
 * - Abort controller integration
 * 
 * @param url URL to fetch from
 * @param options Fetch options with enhancements
 * @returns Promise with the fetch response
 */
export async function fetchWithEnhancements<T>(
  url: string,
  options: FetchOptions = {}
): Promise<T> {
  const {
    timeout = 10000, // Default timeout: 10 seconds
    retry = 2, // Default retries: 2 times (3 attempts total)
    retryDelay = 300, // Base delay: 300ms
    exponentialBackoff = true,
    cacheKey,
    cacheTtl = 60000, // Default cache TTL: 1 minute
    ...fetchOptions
  } = options;

  // Check cache first if cacheKey provided
  if (cacheKey) {
    const cached = apiCache.get(cacheKey);
    if (cached && cached.expiry > Date.now()) {
      return cached.data;
    }
  }

  // Set up abort controller for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  // Add signal to fetch options
  const finalOptions = {
    ...fetchOptions,
    signal: controller.signal,
  };

  let lastError: Error | null = null;
  
  // Retry loop
  for (let attempt = 0; attempt <= retry; attempt++) {
    try {
      const response = await fetch(url, finalOptions);
      
      // Clear timeout since fetch completed
      clearTimeout(timeoutId);
      
      // Handle HTTP errors
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }
      
      // Parse response
      const data = await response.json();
      
      // Cache successful response if cacheKey provided
      if (cacheKey) {
        apiCache.set(cacheKey, {
          data,
          expiry: Date.now() + cacheTtl,
        });
      }
      
      return data;
    } catch (error: any) {
      lastError = error;
      
      // Don't retry if this was the last attempt
      if (attempt === retry) break;
      
      // Don't retry for certain error types
      if (error.name === 'AbortError') {
        throw new Error(`Request timed out after ${timeout}ms`);
      }
      
      // Calculate delay with optional exponential backoff
      const delay = exponentialBackoff
        ? retryDelay * Math.pow(2, attempt)
        : retryDelay;
      
      // Wait before the next retry
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // Clear timeout if all retries failed
  clearTimeout(timeoutId);
  
  // If we got here, all retries failed
  throw lastError || new Error('Request failed');
}

/**
 * Cached fetch function for React Server Components
 * Uses React's cache() function to deduplicate requests
 */
export const cachedFetch = cache(
  async (url: string, options: FetchOptions = {}) => {
    return fetchWithEnhancements(url, options);
  }
);

/**
 * Prefetch data for critical paths
 * Call this function for important routes to improve perceived performance
 * 
 * @param urls URLs to prefetch
 * @param options Fetch options
 */
export function prefetchData(urls: string[], options: FetchOptions = {}): void {
  if (typeof window === 'undefined') return;
  
  // Use requestIdleCallback to prefetch during browser idle time
  const prefetch = () => {
    urls.forEach(url => {
      fetchWithEnhancements(url, {
        ...options,
        priority: 'low', // Use low priority for prefetch
      }).catch(() => {
        // Silently catch errors for prefetch requests
      });
    });
  };
  
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(prefetch);
  } else {
    setTimeout(prefetch, 1000); // Fallback for browsers without requestIdleCallback
  }
}

/**
 * Clear all cached API responses
 * Useful after mutations that invalidate previously cached data
 */
export function clearApiCache(): void {
  apiCache.clear();
}

/**
 * Get API cache statistics for monitoring
 */
export function getApiCacheStats() {
  let size = 0;
  let oldestEntry = Date.now();
  let newestEntry = 0;
  
  apiCache.forEach(({ expiry }) => {
    size++;
    const createdAt = expiry - 60000; // Assuming default TTL
    oldestEntry = Math.min(oldestEntry, createdAt);
    newestEntry = Math.max(newestEntry, createdAt);
  });
  
  return {
    size,
    oldestEntry: new Date(oldestEntry),
    newestEntry: new Date(newestEntry),
    hitRate: '---', // Would require tracking in production
  };
}
