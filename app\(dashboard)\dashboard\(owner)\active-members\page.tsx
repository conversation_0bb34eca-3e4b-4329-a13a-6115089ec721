import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { getMembersWithActivePlans } from '@/lib/db/member-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { cookies } from 'next/headers';
import { verifyToken } from '@/lib/auth/session';

async function getUser() {
  const cookieStore = cookies();
  const sessionCookie = cookieStore.get('session');
  if (!sessionCookie || !sessionCookie.value) {
    return null;
  }

  const sessionData = await verifyToken(sessionCookie.value);
  if (!sessionData?.user?.id || new Date(sessionData.expires) < new Date()) {
    return null;
  }

  return { id: sessionData.user.id };
}

async function getActiveMembersData() {
  const user = await getUser();
  if (!user) {
    return [];
  }
  
  const team = await getTeamForUser(user.id);
  if (!team) {
    return [];
  }
  
  return getMembersWithActivePlans(team.id);
}

export default async function ActiveMembersPage() {
  const members = await getActiveMembersData();

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Members with Active Plans</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Active Subscriptions</CardTitle>
          <CardDescription>
            View all team members with active subscription plans
          </CardDescription>
        </CardHeader>
        <CardContent>
          {members.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-gray-500">No members with active plans found</p>
            </div>
          ) : (
            <div className="space-y-6">
              {members.map((member) => (
                <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarFallback>{member.name?.charAt(0) || member.email.charAt(0)}</AvatarFallback>
                      <AvatarImage src={`https://avatar.vercel.sh/${member.email}`} />
                    </Avatar>
                    <div>
                      <p className="font-medium">{member.name || 'Unnamed Member'}</p>
                      <p className="text-sm text-gray-500">{member.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="font-medium">{member.planName}</p>
                      <p className="text-sm">
                        <Badge 
                          variant={
                            member.subscriptionStatus === 'active' ? 'default' : 
                            member.subscriptionStatus === 'trialing' ? 'outline' : 'destructive'
                          }
                        >
                          {member.subscriptionStatus}
                        </Badge>
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
