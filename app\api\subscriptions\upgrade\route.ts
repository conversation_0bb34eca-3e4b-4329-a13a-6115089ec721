import { NextRequest, NextResponse } from 'next/server';
import { initiateSubscriptionUpgrade, getCustomerPortalUrl } from '@/lib/services/subscription-upgrade';
import { getUser } from '@/lib/db/server-queries';

/**
 * POST /api/subscriptions/upgrade - Initiate subscription upgrade
 * Industry standard: RESTful API with proper validation and error handling
 */
export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
          message: 'Authentication required'
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid JSON',
          message: 'Request body must be valid JSON'
        },
        { status: 400 }
      );
    }

    const { plan, billingCycle = 'monthly' } = body;

    // Validate plan parameter
    const validPlans = ['starter', 'pro', 'enterprise'];
    if (!plan || !validPlans.includes(plan)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid plan',
          message: 'Plan must be one of: starter, pro, enterprise'
        },
        { status: 400 }
      );
    }

    // Validate billing cycle parameter
    const validCycles = ['monthly', 'yearly'];
    if (!validCycles.includes(billingCycle)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid billing cycle',
          message: 'Billing cycle must be monthly or yearly'
        },
        { status: 400 }
      );
    }

    console.log(`[Upgrade API] User ${user.email} requesting upgrade to ${plan} (${billingCycle})`);

    // Initiate the upgrade process
    const result = await initiateSubscriptionUpgrade(plan, billingCycle);

    if (result.success) {
      const responseData: any = {
        success: true,
        requiresPayment: result.requiresPayment,
        message: result.message,
        plan,
        billingCycle,
      };

      // Add appropriate URL based on whether payment is required
      if (result.requiresPayment && result.checkoutUrl) {
        responseData.checkoutUrl = result.checkoutUrl;
        responseData.action = 'redirect_to_checkout';
      } else if (result.subscriptionId) {
        responseData.subscriptionId = result.subscriptionId;
        responseData.action = 'subscription_updated';
      }

      return NextResponse.json(responseData);
    } else {
      // Handle different types of failures
      const statusCode = result.requiresPayment ? 402 : 400; // 402 Payment Required for payment issues

      return NextResponse.json({
        success: false,
        error: result.error,
        message: result.message,
        requiresPayment: result.requiresPayment,
      }, { status: statusCode });
    }

  } catch (error: any) {
    console.error('[Upgrade API] Unexpected error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Internal server error during upgrade process'
    }, { status: 500 });
  }
}

/**
 * GET /api/subscriptions/upgrade - Get available upgrade options
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Return available plans and pricing information
    const plans = [
      {
        id: 'starter',
        name: 'Starter',
        description: 'Perfect for individuals getting started - 14 day free trial',
        features: [
          '14-day free trial',
          'Basic AI use case access',
          'Limited case studies',
          'Community support'
        ],
        pricing: {
          trial: {
            amount: 0, // Free trial
            currency: 'usd',
            interval: 'month',
            trial_period_days: 14
          }
        }
      },
      {
        id: 'pro',
        name: 'Pro',
        description: 'For teams and businesses ready to scale with AI',
        features: [
          'Full access to all features',
          'Unlimited case studies',
          'Advanced analytics',
          'Priority support',
          'Team collaboration'
        ],
        pricing: {
          monthly: {
            amount: 2900, // $29.00 - UPDATE WITH YOUR ACTUAL PRICE
            currency: 'usd',
            interval: 'month'
          },
          yearly: {
            amount: 29000, // $290.00 - UPDATE WITH YOUR ACTUAL PRICE
            currency: 'usd',
            interval: 'year'
          }
        }
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        description: 'For large organizations with advanced needs',
        features: [
          'Custom pricing and features',
          'Dedicated account management',
          'Custom integrations',
          'SLA guarantees'
        ],
        pricing: {
          contact: {
            amount: null,
            currency: 'usd',
            interval: 'contact',
            description: 'Contact us for custom pricing'
          }
        }
      }
    ];

    return NextResponse.json({
      plans,
      currency: 'usd',
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('[Upgrade API] Error getting upgrade options:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to get upgrade options'
    }, { status: 500 });
  }
}

/**
 * PUT /api/subscriptions/upgrade - Manage existing subscription (portal)
 */
export async function PUT(request: NextRequest) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Upgrade API] User ${user.email} requesting customer portal access`);

    const result = await getCustomerPortalUrl();

    if (result.success && result.url) {
      return NextResponse.json({
        success: true,
        portalUrl: result.url,
        message: 'Redirecting to customer portal'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error,
        message: 'Failed to create customer portal session'
      }, { status: 400 });
    }

  } catch (error: any) {
    console.error('[Upgrade API] Error creating portal session:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to create customer portal session'
    }, { status: 500 });
  }
}
