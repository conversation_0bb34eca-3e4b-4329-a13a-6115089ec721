import { sql } from 'drizzle-orm';
import dotenv from 'dotenv';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';

// Load environment variables from .env file
dotenv.config();

async function updatePlansSchema() {
  console.log('Updating plans schema to allow NULL values...');

  // Check if POSTGRES_URL is set
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not set');
  }

  console.log('Connecting to database...');

  // Create a new connection to the database
  const migrationClient = postgres(process.env.POSTGRES_URL, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Alter the plans table to allow NULL values for Enterprise plan
    await db.execute(sql`
      ALTER TABLE plans 
      ALTER COLUMN price_monthly DROP NOT NULL,
      ALTER COLUMN stripe_price_id_monthly DROP NOT NULL,
      ALTER COLUMN stripe_product_id DROP NOT NULL
    `);
    console.log('Plans table schema updated successfully');

    // Clear existing plans and insert updated ones
    await db.execute(sql`DELETE FROM plans WHERE name IN ('starter', 'pro', 'enterprise')`);
    console.log('Cleared existing plans');

    // Insert updated plans with correct pricing and features
    await db.execute(sql`
      INSERT INTO plans (
        name,
        display_name,
        description,
        price_monthly,
        price_yearly,
        stripe_price_id_monthly,
        stripe_price_id_yearly,
        stripe_product_id,
        features,
        max_users,
        is_active,
        is_public
      ) VALUES
      (
        'starter',
        'Starter',
        'Perfect for individuals getting started with AI - 14 day free trial',
        1200,
        NULL,
        'price_1RKdR9IiRJwYTHShansVCelM',
        NULL,
        'prod_SF7gtzlTQKtCGy',
        '{"trial": {"enabled": true, "days": 14, "description": "14-day free trial"}, "caseStudies": {"limit": 10, "description": "Access to 10 curated AI use cases"}, "users": {"limit": 1, "description": "1 registered user"}, "features": [
          {"name": "14-day free trial", "included": true},
          {"name": "Access to 10 curated AI use cases", "included": true},
          {"name": "Preview of Market Intelligence Unit (MIU)", "included": true},
          {"name": "1 registered user", "included": true},
          {"name": "Unlimited use case access", "included": false},
          {"name": "Full MIU access", "included": false},
          {"name": "Roadmap tools & prioritization framework", "included": false},
          {"name": "Team collaboration features", "included": false},
          {"name": "Dedicated account support", "included": false}
        ]}',
        1,
        true,
        true
      ),
      (
        'pro',
        'Pro',
        'For teams ready to scale with AI - unlimited access',
        2900,
        29000,
        'price_1RKdRAIiRJwYTHShF0hsJ0Yi',
        'price_pro_yearly',
        'prod_SF7gkIcBmxEIXk',
        '{"trial": {"enabled": false, "days": 0, "description": "No trial period"}, "caseStudies": {"limit": -1, "description": "Unlimited case study access"}, "users": {"limit": 3, "description": "Up to 3 users"}, "features": [
          {"name": "Unlimited case study access", "included": true},
          {"name": "Full MIU access", "included": true},
          {"name": "Roadmap tools & prioritization framework", "included": true},
          {"name": "Team collaboration features", "included": true},
          {"name": "Dedicated account support", "included": true},
          {"name": "Up to 3 users", "included": true},
          {"name": "Priority email support", "included": true}
        ]}',
        3,
        true,
        true
      ),
      (
        'enterprise',
        'Enterprise',
        'For large organizations - contact us for pricing',
        NULL,
        NULL,
        NULL,
        NULL,
        'prod_enterprise',
        '{"trial": {"enabled": false, "days": 0, "description": "Custom trial available"}, "caseStudies": {"limit": -1, "description": "Unlimited case study access"}, "users": {"limit": 7, "description": "Up to 7 CXO-level users"}, "features": [
          {"name": "Everything in Pro", "included": true},
          {"name": "Up to 7 CXO-level users", "included": true},
          {"name": "Custom integrations", "included": true},
          {"name": "Dedicated account manager", "included": true},
          {"name": "SLA guarantees", "included": true},
          {"name": "Custom branding", "included": true},
          {"name": "SSO integration", "included": true}
        ]}',
        7,
        true,
        true
      )
    `);
    console.log('Updated plans inserted successfully');

    console.log('Plans schema updated successfully');
  } catch (error) {
    console.error('Error updating plans schema:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    console.log('Closing database connection...');
    await migrationClient.end();
  }
}

// Run the migration
updatePlansSchema().catch(console.error);
