'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useActionState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CircleIcon, Eye, EyeOff, Loader2 } from 'lucide-react';
import { signIn, signUp } from './actions';
import { ActionState } from '@/lib/auth/middleware';
import Image from 'next/image';
import { ForgotPasswordDialog } from '@/components/ForgotPasswordDialog';

export function Login({ mode = 'signin' }: { mode?: 'signin' | 'signup' }) {
  const searchParams = useSearchParams();
  const redirect = searchParams.get('redirect');
  const priceId = searchParams.get('priceId');
  const inviteId = searchParams.get('inviteId');
  const [forgotPasswordOpen, setForgotPasswordOpen] = useState(false);
  const [state, formAction, pending] = useActionState<ActionState, FormData>(
    mode === 'signin' ? signIn : signUp,
    { error: '' }
  );
  const [seePassword, setSeePassword] = useState(false);

  return (
    <div className='min-h-[100dvh] flex'>
      <div className='flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8 bg-white dark:bg-black'>
        <div className='sm:mx-auto sm:w-full sm:max-w-md'>
          <div className='flex items-center mb-6'>
            <div className='block dark:hidden h-[110px] overflow-y-hidden'>
              <Image
                src='/images/logos/main-logo.svg'
                alt='Turinos AI Logo'
                width={180}
                height={110}
                className='w-[180px] h-auto object-contain'
              />
            </div>
            <div className='hidden dark:block'>
              <Image
                src='https://res.cloudinary.com/dyiso4ohk/image/upload/v1746113097/image_veff0p.png'
                alt='Turinos AI Logo'
                width={180}
                height={40}
                className='w-[180px] h-auto object-contain invert'
              />
            </div>
          </div>
          <h2 className='text-xl font-medium text-gray-900 dark:text-white'>
            Welcome back to Turinos AI👋
          </h2>
          <p className='mt-2 text-sm text-gray-600 dark:text-white'>
            Login to access all your data
          </p>
        </div>

        <div className='mt-8 sm:mx-auto sm:w-full sm:max-w-md'>
          <form className='space-y-6' action={formAction}>
            <input type='hidden' name='redirect' value={redirect || ''} />
            <input type='hidden' name='priceId' value={priceId || ''} />
            <input type='hidden' name='inviteId' value={inviteId || ''} />

            <div>
              <Label
                htmlFor='email'
                className='block text-sm font-medium text-gray-700 dark:text-white'
              >
                Business Email
              </Label>
              <div className='mt-1'>
                <Input
                  id='email'
                  name='email'
                  type='email'
                  autoComplete='email'
                  defaultValue={state.email}
                  required
                  maxLength={50}
                  className='appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 dark:border-white/20 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white dark:bg-black focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
                  placeholder='Enter your business email'
                />
              </div>
            </div>

            <div>
              <Label
                htmlFor='password'
                className='block text-sm font-medium text-gray-700 dark:text-white'
              >
                Password
              </Label>
              <div className='mt-1 relative'>
                <Input
                  id='password'
                  name='password'
                  type={seePassword ? 'text' : 'password'}
                  autoComplete={
                    mode === 'signin' ? 'current-password' : 'new-password'
                  }
                  defaultValue={state.password}
                  required
                  minLength={8}
                  maxLength={100}
                  className='appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 dark:border-white/20 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white dark:bg-black focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
                  placeholder='Enter your password'
                />
                <button
                  type='button'
                  onClick={() => setSeePassword(!seePassword)}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center text-sm text-gray-500 dark:text-white'
                >
                  {seePassword ? <Eye size={16} /> : <EyeOff size={16} />}
                </button>
              </div>
              <div className='flex justify-end mt-2'>
                <button
                  type='button'
                  onClick={() => setForgotPasswordOpen(true)}
                  className='text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300'
                >
                  Forgot password?
                </button>
              </div>
            </div>

            {state?.error && (
              <div className='text-red-500 text-sm'>{state.error}</div>
            )}

            <div>
              <Button
                type='submit'
                className='w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                disabled={pending}
              >
                {pending ? (
                  <>
                    <Loader2 className='animate-spin mr-2 h-4 w-4' />
                    Loading...
                  </>
                ) : (
                  'Login'
                )}
              </Button>
            </div>

            {/* Divider commented out
            <div className="relative mt-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Continue with</span>
              </div>
            </div>
            */}

            {/* Google login button removed */}

            <div className='text-center text-sm'>
              <span className='text-gray-600 dark:text-white'>
                Don't have an account?{' '}
              </span>
              <Link
                href='/sign-up'
                className='text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300'
              >
                Register
              </Link>
            </div>
          </form>
        </div>
      </div>

      {/* Right side with blue background and content */}
      <div className='relative hidden lg:flex flex-1 items-center justify-center overflow-hidden rounded-l-4xl bg-[#3384FF] '>
        {/* Background Image */}
        <Image
          src='/sky.png'
          alt='Background'
          fill
          className='object-cover z-0 opacity-60 bg-blend-multiply'
          priority
        />

        {/* Content */}
        <div className='relative z-10 max-w-lg text-center text-white px-8'>
          {/* Semi-transparent backdrop box */}
          <div className='relative p-12 backdrop-blur-[15px] bg-white/10 '>
            <h2 className='text-4xl font-semibold mb-6 text-white'>
              Start your AI journey with Turinos AI
            </h2>
            <p className='text-xl text-blue-100'>
              Upload samples of your work to impress potential clients
            </p>
          </div>

          {/* Description Text - Positioned below box */}
          <div className='mt-24 text-center'>
            <p className='text-md text-white font-light'>
              Upload samples of your work to impress potential clients
            </p>
          </div>
          {/* Carousel Navigation */}
        </div>
      </div>

      {/* Forgot Password Dialog */}
      <ForgotPasswordDialog
        isOpen={forgotPasswordOpen}
        onOpenChange={setForgotPasswordOpen}
      />
    </div>
  );
}
