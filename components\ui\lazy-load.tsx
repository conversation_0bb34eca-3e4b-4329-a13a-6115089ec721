'use client';

import { Suspense, lazy, ComponentType, ReactNode } from 'react';

// Simple loading spinner component
const LoadingSpinner = () => (
  <div className="w-full flex justify-center items-center p-4">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
  </div>
);

/**
 * Creates a lazily loaded component that only loads when needed
 * 
 * @param importFunc - Dynamic import function for the component
 * @param fallback - Optional loading component to show while loading
 * @returns Lazy loaded component
 */
export function lazyLoad<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback: ReactNode = <LoadingSpinner />
) {
  const LazyComponent = lazy(importFunc);
  
  // Return a wrapper component that handles loading state
  return function LazyLoadWrapper(props: React.ComponentProps<T>) {
    return (
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

/**
 * Higher-order component that adds lazy loading to a component
 * 
 * @param Component - Component to be lazily loaded
 * @param fallback - Optional loading component to show while loading
 * @returns Lazy loaded component
 */
export function withLazyLoading<T extends ComponentType<any>>(
  Component: T,
  fallback: ReactNode = <LoadingSpinner />
) {
  return function LazyLoadedComponent(props: React.ComponentProps<T>) {
    return (
      <Suspense fallback={fallback}>
        <Component {...props} />
      </Suspense>
    );
  };
}

/**
 * Component that delays rendering of children until browser is idle
 * 
 * @param props - Component props
 * @returns Component that delays rendering
 */
export function DeferredRender({
  children,
  fallback = null,
  delay = 0
}: {
  children: ReactNode;
  fallback?: ReactNode;
  delay?: number;
}) {
  // Only relevant in client-side rendering
  if (typeof window === 'undefined') {
    return <>{children}</>;
  }
  
  // Create a lazy component that resolves after a delay
  const DeferredComponent = lazy(() => 
    new Promise<{ default: ComponentType<{}> }>((resolve) => {
      const wait = delay > 0 
        ? () => setTimeout(doResolve, delay)
        : 'requestIdleCallback' in window
          ? () => (window as any).requestIdleCallback(doResolve)
          : () => setTimeout(doResolve, 1);
          
      const doResolve = () => {
        resolve({
          default: () => <>{children}</>
        });
      };
      
      wait();
    })
  );
  
  return (
    <Suspense fallback={fallback}>
      <DeferredComponent />
    </Suspense>
  );
}

export default lazyLoad;
