import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { couponCodes } from '@/lib/db/schema';
import { eq, and, isNull, gt, lte, or } from 'drizzle-orm';

// This endpoint is public and doesn't require authentication

// POST validate a coupon code
export async function POST(request: NextRequest) {
  try {
    console.log('Validating coupon code...');

    // Parse the request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      console.error('Error parsing request body:', error);
      return NextResponse.json({
        valid: false,
        message: 'Invalid request format'
      }, { status: 400 });
    }

    const { code } = body;
    console.log('Received coupon code:', code);

    if (!code) {
      return NextResponse.json({
        valid: false,
        message: 'Coupon code is required'
      }, { status: 400 });
    }

    const now = new Date();

    // First check if the coupon code exists at all
    const allCoupons = await db.select()
      .from(couponCodes)
      .where(eq(couponCodes.code, code.toUpperCase()))
      .limit(1);

    if (!allCoupons.length) {
      return NextResponse.json({
        valid: false,
        message: 'Invalid coupon code'
      });
    }

    // If it exists but is inactive
    if (!allCoupons[0].isActive) {
      return NextResponse.json({
        valid: false,
        message: 'This coupon code is inactive'
      });
    }

    // If it exists but hasn't started yet
    if (allCoupons[0].validFrom > now) {
      return NextResponse.json({
        valid: false,
        message: 'This coupon code is not valid yet'
      });
    }

    // If it exists but has expired
    if (allCoupons[0].validUntil && allCoupons[0].validUntil < now) {
      return NextResponse.json({
        valid: false,
        message: 'This coupon code has expired'
      });
    }

    // If it exists but has reached its usage limit
    if (allCoupons[0].maxUses !== null && allCoupons[0].currentUses >= allCoupons[0].maxUses) {
      return NextResponse.json({
        valid: false,
        message: 'This coupon code has reached its usage limit'
      });
    }

    // If we get here, the coupon is valid
    const coupon = allCoupons;
    console.log('Valid coupon found:', coupon[0]);

    // Return the coupon details
    const response = {
      valid: true,
      coupon: {
        id: coupon[0].id,
        code: coupon[0].code,
        discountType: coupon[0].discountType,
        discountAmount: coupon[0].discountAmount,
        description: coupon[0].description,
      }
    };

    console.log('Returning response:', response);
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error validating coupon code:', error);
    return NextResponse.json({
      valid: false,
      message: 'An error occurred while validating the coupon code. Please try again.'
    }, { status: 500 });
  }
}
