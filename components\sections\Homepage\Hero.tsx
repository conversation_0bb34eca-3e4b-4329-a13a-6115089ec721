import { AnimatedGridPattern } from '@/components/magicui/animated-grid-pattern';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowRight, Play } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';

export default function Hero() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Function to handle play/pause
  const togglePlayPause = () => {
    if (videoRef.current) {
      if (videoRef.current.paused) {
        videoRef.current.play();
        setIsPlaying(true);
      } else {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    }
  };

  // Helper function to hide controls after a delay
  const hideControlsAfterDelay = () => {
    // Clear existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // Set new timer to hide controls after 3 seconds
    timerRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false);
      }
    }, 3000);
  };

  // Auto-play video when component mounts
  useEffect(() => {
    // Small delay to ensure the video is loaded
    const autoPlayTimer = setTimeout(() => {
      if (videoRef.current) {
        videoRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
            // Hide controls after video starts playing
            hideControlsAfterDelay();
          })
          .catch((error) => {
            console.error('Autoplay failed:', error);
            // Keep controls visible if autoplay fails
            setShowControls(true);
          });
      }
    }, 1000);

    return () => {
      clearTimeout(autoPlayTimer);
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return (
    <section className='relative w-full min-h-[90vh] pt-20 md:pt-32 flex items-center justify-center overflow-hidden'>
      {/* Animated Grid Background */}
      <div className='absolute inset-0'>
        <AnimatedGridPattern
          width={80}
          height={80}
          className='text-gray-200/80 md:hidden'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={12}
          x={0}
          y={0}
        />
        <AnimatedGridPattern
          width={150}
          height={150}
          className='text-gray-200/80 hidden md:block'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={20}
          x={0}
          y={0}
        />
      </div>

      {/* Updated Radial Gradients */}
      <div className='absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0)_0%,rgba(255,255,255,0.75)_25%,rgba(255,255,255,0.9)_50%,rgba(255,255,255,1)_75%)] pointer-events-none'></div>
      <div className='absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(59,130,246,0.08)_0%,rgba(59,130,246,0)_50%)] pointer-events-none'></div>

      <div className='absolute inset-x-0 bottom-0 h-40 bg-gradient-to-t from-white via-white/80 to-transparent pointer-events-none'></div>

      {/* Content */}
      <div className='relative z-10 container mx-auto px-4 md:px-6 lg:px-8'>
        <div className='text-center space-y-6'>
          {/* Trial Badge - Clickable and linked to pricing page */}
          <div className='inline-block'>
            <Link href='/pricing' className='inline-block'>
              <Badge
                variant='secondary'
                className='px-4 md:px-6 py-2 md:py-2.5 text-sm bg-white/95 backdrop-blur-sm border-none shadow-xl hover:shadow-2xl transition-all duration-300 text-gray-600 cursor-pointer hover:bg-white'
              >
                14 Day Free Trial - No Credit card required
              </Badge>
            </Link>
          </div>

          {/* Heading */}
          <div className='space-y-4'>
            <h1 className='text-4xl sm:text-5xl md:text-7xl font-bold text-gray-900 tracking-tight px-4 md:px-0'>
              Your Vision
              <span className='relative block text-blue-500 mt-2'>
                AI-fied
                <span className='absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 md:w-32 h-1.5 bg-blue-500/20 rounded-full' />
              </span>
            </h1>

            {/* Subtitle */}
            <p className='text-lg sm:text-xl md:text-2xl text-gray-600 max-w-2xl mx-auto leading-relaxed px-4 md:px-0'>
              Reimagine Leadership with AI at the Core
            </p>
          </div>

          {/* CTA Button */}
          <div className='flex flex-col items-center justify-center gap-6 pt-4'>
            <Link href='/sign-in'>
              <Button
                size='lg'
                className='bg-blue-500 hover:bg-blue-600 text-white rounded-full px-6 md:px-8 py-5 md:py-6 text-base md:text-lg shadow-lg hover:shadow-xl transition-all duration-300'
              >
                Explore Now
                <ArrowRight className='ml-2 h-4 w-4 md:h-5 md:w-5' />
              </Button>
            </Link>
          </div>

          {/* Dashboard Preview with Video Player */}
          <div className='mt-11 md:mt-24 mb-12 md:mb-16 rounded-[40px] border-[6px] md:mx-28 relative group cursor-pointer'>
            <video
              ref={videoRef}
              src='https://res.cloudinary.com/dyiso4ohk/video/upload/v1746508575/clou_w4a6bc.mp4'
              className='w-full h-full object-cover rounded-[34px]'
              controlsList='nodownload'
              playsInline
              muted
              autoPlay
              loop
              preload='auto'
              onClick={togglePlayPause}
              onMouseEnter={() => setShowControls(true)}
              onMouseLeave={() => isPlaying && hideControlsAfterDelay()}
            />

            {/* Play Button Overlay - Only visible when paused */}
            {!isPlaying && (
              <div
                className='absolute inset-0 flex items-center justify-center transition-opacity duration-300'
                onClick={togglePlayPause}
              >
                <div className='bg-black/30 rounded-full p-4 backdrop-blur-sm transition-transform duration-300 hover:scale-110'>
                  <Play className='h-8 w-8 text-white' />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
