import { db } from './drizzle';
import { couponCodes } from './schema';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function seedCouponCodes() {
  console.log('Seeding coupon codes...');

  try {
    // Check if there are any existing coupon codes
    const existingCoupons = await db.select({ code: couponCodes.code }).from(couponCodes);
    
    if (existingCoupons.length > 0) {
      console.log('Coupon codes already exist in the database:');
      existingCoupons.forEach(coupon => console.log(`- ${coupon.code}`));
      return;
    }

    // Insert sample coupon codes
    const sampleCoupons = [
      {
        code: 'FREE50',
        discountType: 'percentage',
        discountAmount: 50,
        description: '50% off your first month',
        maxUses: 100,
        currentUses: 0,
        validFrom: new Date(),
        validUntil: new Date(new Date().setFullYear(new Date().getFullYear() + 1)), // Valid for 1 year
        isActive: true,
      },
      {
        code: 'WELCOME25',
        discountType: 'percentage',
        discountAmount: 25,
        description: '25% off for new users',
        maxUses: 200,
        currentUses: 0,
        validFrom: new Date(),
        validUntil: null, // No expiration
        isActive: true,
      },
      {
        code: 'FLAT10',
        discountType: 'fixed',
        discountAmount: 10,
        description: '$10 off your subscription',
        maxUses: null, // Unlimited uses
        currentUses: 0,
        validFrom: new Date(),
        validUntil: new Date(new Date().setMonth(new Date().getMonth() + 3)), // Valid for 3 months
        isActive: true,
      }
    ];

    // Insert the sample coupon codes
    await db.insert(couponCodes).values(sampleCoupons);

    console.log('Successfully seeded coupon codes!');
  } catch (error) {
    console.error('Error seeding coupon codes:', error);
  }
}

// Run the seeding function
seedCouponCodes().catch(console.error);
