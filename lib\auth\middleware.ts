import { z } from 'zod';
import { TeamDataWithMembers, User } from '@/lib/db/schema';
import { getTeamForUser } from '@/lib/db/queries';
import { getUser } from '@/lib/db/server-queries';
import { redirect } from 'next/navigation';

export type ActionState = {
  error?: string;
  success?: string;
  [key: string]: any; // This allows for additional properties
};

type ValidatedActionFunction<S extends z.ZodType<any, any>, T> = (
  data: z.infer<S>,
  formData: FormData
) => Promise<T>;

export function validatedAction<S extends z.ZodType<any, any>, T>(
  schema: S,
  action: ValidatedActionFunction<S, T>
) {
  return async (prevState: ActionState, formData: FormData): Promise<T> => {
    // Handle case where formData is undefined or null
    if (!formData) {
      return { error: "Invalid form data" } as T;
    }

    try {
      const formEntries = Object.fromEntries(formData);
      const result = schema.safeParse(formEntries);

      if (!result.success) {
        // Handle case where errors array might be empty
        const errorMessage = result.error.errors && result.error.errors.length > 0
          ? result.error.errors[0].message
          : "Validation failed";

        return { error: errorMessage } as T;
      }

      return action(result.data, formData);
    } catch (error) {
      console.error("Validation error:", error);
      return { error: "An unexpected error occurred during validation" } as T;
    }
  };
}

type ValidatedActionWithUserFunction<S extends z.ZodType<any, any>, T> = (
  data: z.infer<S>,
  formData: FormData,
  user: User
) => Promise<T>;

export function validatedActionWithUser<S extends z.ZodType<any, any>, T>(
  schema: S,
  action: ValidatedActionWithUserFunction<S, T>
) {
  return async (prevState: ActionState, formData: FormData): Promise<T> => {
    try {
      const user = await getUser();
      if (!user) {
        throw new Error('User is not authenticated');
      }

      // Handle case where formData is undefined or null
      if (!formData) {
        return { error: "Invalid form data" } as T;
      }

      const formEntries = Object.fromEntries(formData);
      const result = schema.safeParse(formEntries);

      if (!result.success) {
        // Handle case where errors array might be empty
        const errorMessage = result.error.errors && result.error.errors.length > 0
          ? result.error.errors[0].message
          : "Validation failed";

        return { error: errorMessage } as T;
      }

      return action(result.data, formData, user);
    } catch (error) {
      console.error("Validation error:", error);
      return { error: error instanceof Error ? error.message : "An unexpected error occurred" } as T;
    }
  };
}

type ActionWithTeamFunction<T> = (
  formData: FormData,
  team: TeamDataWithMembers
) => Promise<T>;

export function withTeam<T>(action: ActionWithTeamFunction<T>) {
  return async (formData: FormData): Promise<T> => {
    const user = await getUser();
    if (!user) {
      redirect('/sign-in');
    }

    const team = await getTeamForUser(user.id);
    if (!team) {
      throw new Error('Team not found');
    }

    return action(formData, team);
  };
}
