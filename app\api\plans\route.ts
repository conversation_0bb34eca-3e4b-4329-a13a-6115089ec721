import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { plans } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET /api/plans - Get all plans
export async function GET(request: NextRequest) {
  try {
    // Check if we should include non-public plans
    const includeNonPublic = request.nextUrl.searchParams.get('includeNonPublic') === 'true';
    
    // Get the session to check if the user is an admin
    const session = await getServerSession(authOptions);
    const isAdmin = session?.user?.role === 'admin';
    
    // If the user is not an admin and trying to access non-public plans, return 403
    if (includeNonPublic && !isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    // Query to get plans
    let query = db.select().from(plans);
    
    // If not including non-public plans, filter to only public plans
    if (!includeNonPublic) {
      query = query.where(eq(plans.isPublic, true));
    }
    
    // Also filter to only active plans unless explicitly requested
    const includeInactive = request.nextUrl.searchParams.get('includeInactive') === 'true';
    if (!includeInactive) {
      query = query.where(eq(plans.isActive, true));
    }
    
    const allPlans = await query;
    
    return NextResponse.json(allPlans);
  } catch (error) {
    console.error('Error fetching plans:', error);
    return NextResponse.json({ error: 'Failed to fetch plans' }, { status: 500 });
  }
}

// POST /api/plans - Create a new plan (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check if the user is an admin
    const session = await getServerSession(authOptions);
    if (session?.user?.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    const data = await request.json();
    
    // Validate required fields
    if (!data.name || !data.displayName || !data.priceMonthly || !data.stripePriceIdMonthly || !data.stripeProductId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Create the plan
    const newPlan = await db.insert(plans).values({
      name: data.name,
      displayName: data.displayName,
      description: data.description,
      priceMonthly: data.priceMonthly,
      priceYearly: data.priceYearly,
      stripePriceIdMonthly: data.stripePriceIdMonthly,
      stripePriceIdYearly: data.stripePriceIdYearly,
      stripeProductId: data.stripeProductId,
      features: data.features,
      maxUsers: data.maxUsers || 1,
      isActive: data.isActive !== false, // Default to true
      isPublic: data.isPublic !== false, // Default to true
    }).returning();
    
    return NextResponse.json(newPlan[0], { status: 201 });
  } catch (error) {
    console.error('Error creating plan:', error);
    return NextResponse.json({ error: 'Failed to create plan' }, { status: 500 });
  }
}
