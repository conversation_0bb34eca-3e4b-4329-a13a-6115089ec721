import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { teamMembers, users, invitations } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { eq, and } from 'drizzle-orm';

// POST /api/team/users/delete - Delete a user from the team
export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a team owner
    const userTeamMember = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, session.user.id),
        eq(teamMembers.role, 'owner')
      ),
      with: {
        team: true,
      },
    });

    if (!userTeamMember?.team) {
      return NextResponse.json({ error: 'Team not found or you are not an owner' }, { status: 403 });
    }

    const body = await req.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Check if the user exists and belongs to the user's team
    const memberToDelete = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, userId),
        eq(teamMembers.teamId, userTeamMember.team.id)
      ),
    });

    if (!memberToDelete) {
      return NextResponse.json({ error: 'User not found in your team' }, { status: 404 });
    }

    // Prevent deleting yourself
    if (memberToDelete.userId === session.user.id) {
      return NextResponse.json({ error: 'You cannot delete yourself from the team' }, { status: 400 });
    }

    // Get the user's email before deleting
    const userToDelete = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        email: true
      }
    });

    if (!userToDelete) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Remove the user from the team
    await db
      .delete(teamMembers)
      .where(
        and(
          eq(teamMembers.userId, userId),
          eq(teamMembers.teamId, userTeamMember.team.id)
        )
      );

    // Cancel any pending invitations for this user's email
    await db
      .update(invitations)
      .set({ status: 'canceled' })
      .where(
        and(
          eq(invitations.email, userToDelete.email),
          eq(invitations.teamId, userTeamMember.team.id),
          eq(invitations.status, 'pending')
        )
      );

    // Delete the user account
    await db
      .delete(users)
      .where(eq(users.id, userId));

    return NextResponse.json({ success: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
