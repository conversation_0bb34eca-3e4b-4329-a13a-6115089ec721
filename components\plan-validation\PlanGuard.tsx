'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@/lib/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Lock, Crown, ArrowRight, RefreshCw } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

/**
 * Plan validation and access control component
 * Industry best practice: Frontend validation with proper UX
 */

interface PlanGuardProps {
  children: React.ReactNode;
  requiredPlan?: 'starter' | 'pro' | 'enterprise';
  requiredFeature?: 'caseStudies' | 'unlimitedAccess' | 'teamCollaboration';
  fallbackComponent?: React.ReactNode;
  redirectTo?: string;
}

interface SubscriptionData {
  hasSubscription: boolean;
  subscription?: {
    planName: string;
    planDisplayName: string;
    status: string;
    isActive: boolean;
    isTrialing: boolean;
    caseStudyLimit: number;
    features: Array<{ name: string; included: boolean }>;
  };
  redirectTo?: string;
}

export function PlanGuard({ 
  children, 
  requiredPlan, 
  requiredFeature, 
  fallbackComponent,
  redirectTo 
}: PlanGuardProps) {
  const { user } = useUser();
  const router = useRouter();
  const { toast } = useToast();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch subscription data
  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    fetchSubscriptionData();
  }, [user]);

  const fetchSubscriptionData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/user/subscription');
      const data = await response.json();

      if (data.success) {
        setSubscriptionData(data);

        // Handle redirect for users without subscription
        if (!data.hasSubscription && data.redirectTo) {
          toast({
            title: 'Subscription Required',
            description: data.message,
            variant: 'default'
          });
          router.push(data.redirectTo);
          return;
        }
      } else {
        setError(data.message || 'Failed to load subscription data');
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
      setError('Unable to verify subscription status');
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user has required access
  const hasRequiredAccess = (): boolean => {
    if (!subscriptionData?.hasSubscription || !subscriptionData.subscription) {
      return false;
    }

    const { subscription } = subscriptionData;

    // Check if subscription is active
    if (!subscription.isActive) {
      return false;
    }

    // Check required plan
    if (requiredPlan) {
      const planHierarchy = { starter: 1, pro: 2, enterprise: 3 };
      const userPlanLevel = planHierarchy[subscription.planName as keyof typeof planHierarchy] || 0;
      const requiredPlanLevel = planHierarchy[requiredPlan];
      
      if (userPlanLevel < requiredPlanLevel) {
        return false;
      }
    }

    // Check required feature
    if (requiredFeature) {
      switch (requiredFeature) {
        case 'caseStudies':
          return subscription.caseStudyLimit > 0 || subscription.caseStudyLimit === -1;
        case 'unlimitedAccess':
          return subscription.caseStudyLimit === -1;
        case 'teamCollaboration':
          return subscription.planName === 'pro' || subscription.planName === 'enterprise';
        default:
          return true;
      }
    }

    return true;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        <span>Verifying subscription...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive" className="m-4">
        <AlertDescription>
          {error}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchSubscriptionData}
            className="ml-2"
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  // User not authenticated
  if (!user) {
    return (
      <Card className="max-w-md mx-auto mt-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lock className="h-5 w-5 mr-2" />
            Authentication Required
          </CardTitle>
          <CardDescription>
            Please sign in to access this feature
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => router.push('/sign-in')} className="w-full">
            Sign In
          </Button>
        </CardContent>
      </Card>
    );
  }

  // User has required access
  if (hasRequiredAccess()) {
    return <>{children}</>;
  }

  // User doesn't have required access - show upgrade prompt
  const subscription = subscriptionData?.subscription;
  const currentPlan = subscription?.planDisplayName || 'No Plan';
  const isStarter = subscription?.planName === 'starter';

  if (fallbackComponent) {
    return <>{fallbackComponent}</>;
  }

  return (
    <Card className="max-w-2xl mx-auto mt-8">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Crown className="h-5 w-5 mr-2 text-yellow-500" />
          Upgrade Required
        </CardTitle>
        <CardDescription>
          Your current plan ({currentPlan}) doesn't include access to this feature
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {requiredFeature === 'unlimitedAccess' && isStarter && (
          <Alert>
            <AlertDescription>
              Your Starter plan includes access to 10 curated case studies. 
              Upgrade to Pro for unlimited access to all case studies.
            </AlertDescription>
          </Alert>
        )}

        {requiredPlan && (
          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              This feature requires a {requiredPlan} plan or higher.
            </p>
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium mb-2">What you'll get with Pro:</h4>
              <ul className="text-sm space-y-1">
                <li>• Unlimited case study access</li>
                <li>• Full MIU access</li>
                <li>• Team collaboration features</li>
                <li>• Dedicated account support</li>
              </ul>
            </div>
          </div>
        )}

        <div className="flex gap-3">
          <Button 
            onClick={() => router.push(redirectTo || '/pricing')}
            className="flex-1"
          >
            Upgrade Plan
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
          <Button 
            variant="outline" 
            onClick={() => router.push('/dashboard/billing')}
          >
            View Current Plan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Hook for checking plan access
 */
export function usePlanAccess() {
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/user/subscription');
      const data = await response.json();
      
      if (data.success) {
        setSubscriptionData(data);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const hasFeatureAccess = (feature: string): boolean => {
    if (!subscriptionData?.hasSubscription || !subscriptionData.subscription) {
      return false;
    }

    const { subscription } = subscriptionData;
    
    switch (feature) {
      case 'unlimitedCaseStudies':
        return subscription.caseStudyLimit === -1;
      case 'limitedCaseStudies':
        return subscription.caseStudyLimit > 0;
      default:
        return false;
    }
  };

  return {
    subscriptionData,
    isLoading,
    hasFeatureAccess,
    refresh: fetchSubscriptionData
  };
}
