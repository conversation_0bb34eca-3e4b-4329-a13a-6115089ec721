'use client';

import { use, useEffect, useState } from 'react';
import { useUser } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { ArrowDown, ArrowUpDown, Star, StarOff } from 'lucide-react';
import { Loader2 } from 'lucide-react';

interface TrendingCase {
  id: number;
  useCaseTitle: string;
  createdAt: string;
  trendingStatus: boolean;
  views: number;
  industry: string;
  role: string;
  vector: string;
}

export default function TrendingCasesPage() {
  const { userPromise } = useUser();
  const user = use(userPromise);

  if (user?.role !== 'owner') {
    return (
      <div className='p-4'>
        <h1 className='text-2xl font-bold text-red-600'>Access Denied</h1>
        <p className='mt-2'>You do not have permission to access this page.</p>
      </div>
    );
  }
  const [cases, setCases] = useState<TrendingCase[]>([]);
  const [loadingId, setLoadingId] = useState<number | null>(null);

  useEffect(() => {
    const fetchCases = async () => {
      try {
        const response = await fetch('/api/case-studies/trending-status');
        const data = await response.json();
        console.log(data);

        setCases(data);
      } catch (error) {
        console.error('Error fetching cases:', error);
      }
    };

    fetchCases();
  }, []);

  const handleCheckChange = async (id: number, checked: boolean) => {
    setLoadingId(id);
    try {
      const response = await fetch('/api/case-studies/trending-status', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: checked ? 'on' : 'off',
          caseStudyId: id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update trending status');
      }

      setCases((prevCases) =>
        prevCases.map((caseStudy) =>
          caseStudy.id === id
            ? { ...caseStudy, trendingStatus: checked }
            : caseStudy
        )
      );
    } catch (error) {
      console.error('Error updating case:', error);
    } finally {
      setLoadingId(null);
    }
  };

  return (
    <div className='p-2 md:p-4 space-y-4'>
      <h1 className='text-2xl font-bold'>Trending Cases Management</h1>

      <Card>
        <CardHeader>
          <CardTitle>All Cases</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='!pl-0'>
                  <Button variant='ghost' className='flex items-center'>
                    Id
                  </Button>
                </TableHead>
                <TableHead className='!pl-0'>
                  <Button variant='ghost' className='flex items-center'>
                    Title
                    {/* <ArrowUpDown className='ml-2 h-4 w-4' /> */}
                  </Button>
                </TableHead>
                <TableHead>Industry</TableHead>
                <TableHead className='!pl-0 '>
                  <Button
                    variant='ghost'
                    className='flex justify-start items-center'
                  >
                    Views
                    <ArrowDown className='ml-2 h-4 w-4' />
                  </Button>
                </TableHead>
                <TableHead>Trending Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cases.map((caseStudy: TrendingCase) => (
                <TableRow key={caseStudy.id} className='relative'>
                  <TableCell className='font-medium'>{caseStudy.id}</TableCell>
                  <TableCell className='font-medium'>
                    {caseStudy.useCaseTitle}
                  </TableCell>
                  <TableCell>{caseStudy.industry}</TableCell>
                  <TableCell>{caseStudy.views}</TableCell>
                  <TableCell>
                    <div className='relative'>
                      <Switch
                        checked={caseStudy.trendingStatus}
                        onCheckedChange={() =>
                          handleCheckChange(
                            caseStudy.id,
                            !caseStudy.trendingStatus
                          )
                        }
                        aria-label='Toggle trending'
                        disabled={loadingId !== null}
                      />
                      {loadingId === caseStudy.id && (
                        <span className='absolute inset-0 flex items-center justify-center bg-white/60 dark:bg-black/60 z-10 rounded'>
                          <Loader2 className='animate-spin w-5 h-5 text-slate-600' />
                        </span>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
