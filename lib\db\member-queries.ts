import { db } from './drizzle';
import { users, teamMembers, teams } from './schema';
import { eq, and, not, isNull } from 'drizzle-orm';

/**
 * Get all members with active subscription plans
 */
export async function getMembersWithActivePlans(teamId: number) {
  const result = await db
    .select({
      id: users.id,
      name: users.name,
      email: users.email,
      role: users.role,
      joinedAt: teamMembers.joinedAt,
      teamId: teamMembers.teamId,
      planName: teams.planName,
      subscriptionStatus: teams.subscriptionStatus
    })
    .from(users)
    .innerJoin(teamMembers, eq(users.id, teamMembers.userId))
    .innerJoin(teams, eq(teamMembers.teamId, teams.id))
    .where(
      and(
        eq(teamMembers.teamId, teamId),
        eq(users.role, 'member'),
        not(isNull(teams.stripeSubscriptionId)),
        not(isNull(teams.planName))
      )
    );

  return result;
}
