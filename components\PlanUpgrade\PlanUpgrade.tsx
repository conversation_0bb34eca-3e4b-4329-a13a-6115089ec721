'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Check, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { planFeaturesConfig, type PlanFeature } from '@/lib/config/plan-features';

// PlanFeature interface is now imported from shared configuration

interface PlanProps {
  id: string;
  name: string;
  description: string;
  price: number;
  priceId: string;
  productId: string;
  features: PlanFeature[];
  isCurrentPlan?: boolean;
  isPopular?: boolean;
  maxUsers: number;
}

interface PlanUpgradeProps {
  currentPlan: string | null;
  onUpgrade: (planId: string, priceId?: string) => Promise<boolean>;
  subscriptionStatus?: string | null;
  hasStripeCustomerId?: boolean;
}

// Plan features configuration is now imported from shared file

// Default plans to show while loading - Updated to match pricing page exactly
const defaultPlans: PlanProps[] = [
  {
    id: 'starter',
    name: 'Starter',
    description: planFeaturesConfig.starter.description,
    price: 12, // $12/month after trial
    priceId: process.env.NEXT_PUBLIC_STRIPE_STARTER_MONTHLY_PRICE_ID || '',
    productId: process.env.NEXT_PUBLIC_STRIPE_STARTER_PRODUCT_ID || '',
    maxUsers: planFeaturesConfig.starter.maxUsers,
    features: planFeaturesConfig.starter.features,
    isPopular: planFeaturesConfig.starter.isPopular,
  },
  {
    id: 'pro',
    name: 'Pro',
    description: planFeaturesConfig.pro.description,
    price: 60, // $60/month
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRO_MONTHLY_PRICE_ID || '',
    productId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRODUCT_ID || '',
    maxUsers: planFeaturesConfig.pro.maxUsers,
    features: planFeaturesConfig.pro.features,
    isPopular: planFeaturesConfig.pro.isPopular,
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: planFeaturesConfig.enterprise.description,
    price: 0, // Contact us
    priceId: '',
    productId: '',
    maxUsers: planFeaturesConfig.enterprise.maxUsers,
    features: planFeaturesConfig.enterprise.features,
    isPopular: planFeaturesConfig.enterprise.isPopular,
  },
];

export function PlanUpgrade({
  currentPlan,
  onUpgrade,
  subscriptionStatus = null,
  hasStripeCustomerId = false
}: PlanUpgradeProps) {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [plans, setPlans] = useState<PlanProps[]>(defaultPlans);
  const [isLoadingPlans, setIsLoadingPlans] = useState(true);
  const router = useRouter();
  const { toast } = useToast();

  // Determine if the user has an active subscription
  const hasActiveSubscription = subscriptionStatus === 'active' || subscriptionStatus === 'trialing';

  // Fetch plans from the API
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setIsLoadingPlans(true);
        const response = await fetch('/api/stripe/plans');

        // Handle non-OK responses
        if (!response.ok) {
          console.warn(`Failed to fetch plans: ${response.status} ${response.statusText}`);
          // Fall back to default plans (which now have correct features)
          setPlans(defaultPlans);
          return;
        }

        // Parse the response data
        let data;
        try {
          data = await response.json();
        } catch (parseError) {
          console.error('Error parsing plans response:', parseError);
          // Fall back to default plans if JSON parsing fails
          setPlans(defaultPlans);
          return;
        }

        // Check if data is an array and has items
        if (!Array.isArray(data) || data.length === 0) {
          console.warn('No plans returned from API or invalid data format');
          setPlans(defaultPlans);
          return;
        }

        // Transform the data to match our PlanProps interface
        const transformedPlans = data.map((plan: any) => {
          try {
            console.log('Processing plan:', plan.name, 'with prices:', plan.prices?.map((p: any) => ({
              id: p.id,
              amount: p.unit_amount,
              interval: p.recurring?.interval,
              trial_days: p.recurring?.trial_period_days
            })));

            // Determine plan type from metadata or product ID
            let planType = plan.metadata?.plan_type;

            // If no plan_type metadata, try to determine from product ID
            if (!planType) {
              if (plan.id === process.env.NEXT_PUBLIC_STRIPE_STARTER_PRODUCT_ID) {
                planType = 'starter';
              } else if (plan.id === process.env.NEXT_PUBLIC_STRIPE_PRO_PRODUCT_ID) {
                planType = 'pro';
              } else {
                // Fallback: try to determine from name
                const name = plan.name?.toLowerCase() || '';
                if (name.includes('starter')) {
                  planType = 'starter';
                } else if (name.includes('pro')) {
                  planType = 'pro';
                } else {
                  planType = 'starter'; // Default fallback
                }
              }
            }

            // Get the plan configuration
            const planConfig = planFeaturesConfig[planType as keyof typeof planFeaturesConfig];
            if (!planConfig) {
              console.warn(`Unknown plan type: ${planType}`);
              return null;
            }

            // Find the matching default plan for fallback values
            const defaultPlan = defaultPlans.find(p => p.id === planType) || defaultPlans[0];

            // Check if plan has prices
            if (!plan.prices || !Array.isArray(plan.prices) || plan.prices.length === 0) {
              console.warn(`Plan ${plan.id} has no prices, using default values`);
              return {
                ...defaultPlan,
                productId: plan.id,
              };
            }

            // For both plans, prioritize monthly pricing
            let selectedPrice = plan.prices[0]; // Default to first price

            if (planType === 'starter') {
              // Look for monthly price with trial (should be $12/month)
              const monthlyTrialPrice = plan.prices.find((p: any) =>
                p.recurring?.interval === 'month' && p.recurring?.trial_period_days
              );
              if (monthlyTrialPrice) {
                selectedPrice = monthlyTrialPrice;
                console.log('Selected monthly trial price for Starter:', selectedPrice.unit_amount, 'cents');
              } else {
                // Fallback: look for any monthly price
                const monthlyPrice = plan.prices.find((p: any) =>
                  p.recurring?.interval === 'month'
                );
                if (monthlyPrice) {
                  selectedPrice = monthlyPrice;
                  console.log('Selected fallback monthly price for Starter:', selectedPrice.unit_amount, 'cents');
                }
              }
            } else if (planType === 'pro') {
              // Look for monthly price (should be $60/month)
              const monthlyPrice = plan.prices.find((p: any) =>
                p.recurring?.interval === 'month'
              );
              if (monthlyPrice) {
                selectedPrice = monthlyPrice;
                console.log('Selected monthly price for Pro:', selectedPrice.unit_amount, 'cents');
              }
            }

            console.log(`Final price for ${planType}:`, selectedPrice.unit_amount, 'cents = $' + (selectedPrice.unit_amount / 100));

            return {
              id: planType,
              name: (plan.name || defaultPlan.name).replace(' Plan', ''),
              description: plan.description || planConfig.description,
              price: (selectedPrice?.unit_amount || 0) / 100,
              priceId: selectedPrice?.id || '',
              productId: plan.id,
              maxUsers: planConfig.maxUsers,
              features: planConfig.features,
              isPopular: planConfig.isPopular
            };
          } catch (transformError) {
            console.error('Error transforming plan:', transformError, plan);
            // Return the default plan for this plan type if transformation fails
            const defaultPlan = defaultPlans.find(p =>
              p.id === (plan.metadata?.plan_type || 'starter')
            ) || defaultPlans[0];
            return defaultPlan;
          }
        }).filter(Boolean); // Remove any undefined plans

        // Always add enterprise plan (Contact Us)
        if (!transformedPlans.some(plan => plan.id === 'enterprise')) {
          transformedPlans.push(defaultPlans.find(p => p.id === 'enterprise')!);
        }

        setPlans(transformedPlans);
      } catch (error) {
        console.error('Error fetching plans:', error);
        // Fall back to default plans (which now have correct features)
        setPlans(defaultPlans);
      } finally {
        setIsLoadingPlans(false);
      }
    };

    fetchPlans();
  }, []);

  // Log the current state for debugging
  console.log('PlanUpgrade state:', {
    currentPlan,
    subscriptionStatus,
    hasStripeCustomerId,
    hasActiveSubscription,
    plans
  });

  const handleUpgrade = async (planId: string) => {
    if (planId === 'enterprise') {
      // For enterprise, redirect to contact page
      router.push('/contact?subject=Enterprise%20Plan%20Inquiry');
      return;
    }

    // Find the plan object
    const plan = plans.find(p => p.id === planId);
    if (!plan) {
      toast({
        title: 'Error',
        description: 'Invalid plan selected.',
        variant: 'destructive',
      });
      return;
    }

    // Don't allow upgrading to the same plan
    if (currentPlan === planId) {
      toast({
        title: 'Already subscribed',
        description: `You are already subscribed to the ${planId.charAt(0).toUpperCase() + planId.slice(1)} plan.`,
        variant: 'default',
      });
      return;
    }

    // Confirm before downgrading from Pro to Starter
    if (currentPlan === 'pro' && planId === 'starter') {
      const confirmed = window.confirm(
        'Are you sure you want to downgrade from Pro to Starter? You will lose access to Pro features immediately after the current billing period ends.'
      );
      if (!confirmed) {
        return;
      }
    }

    // Confirm before upgrading from Starter to Pro
    if (currentPlan === 'starter' && planId === 'pro') {
      const confirmed = window.confirm(
        'Are you sure you want to upgrade to the Pro plan? Your billing will be updated immediately.'
      );
      if (!confirmed) {
        return;
      }
    }

    setIsLoading(planId);
    try {
      // Pass both planId and priceId to the onUpgrade function
      const success = await onUpgrade(planId, plan.priceId);
      if (success) {
        toast({
          title: currentPlan === 'starter' && planId === 'pro'
            ? 'Upgrading to Pro'
            : currentPlan === 'pro' && planId === 'starter'
              ? 'Downgrading to Starter'
              : 'Plan update initiated',
          description: 'You will be redirected to complete the process.',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update plan. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error upgrading plan:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(null);
    }
  };

  // Helper function to determine if a plan should be disabled
  const shouldDisablePlan = (planId: string) => {
    // If there's no current plan, nothing should be disabled
    if (!currentPlan) return false;

    // If this is the current plan, it should be disabled
    if (currentPlan === planId) return true;

    // Enterprise plan is always available for contact
    if (planId === 'enterprise') return false;

    // If current plan is Pro and trying to downgrade to Starter, allow it
    if (currentPlan === 'pro' && planId === 'starter') return false;

    // If current plan is Starter and trying to upgrade to Pro, allow it
    if (currentPlan === 'starter' && planId === 'pro') return false;

    // For any other case, allow the plan
    return false;
  };

  // Log the current plan for debugging
  console.log('Current plan in PlanUpgrade component:', currentPlan);

  // Helper function to get button text
  const getButtonText = (plan: PlanProps, isCurrentPlan: boolean) => {
    if (isLoading === plan.id) {
      return (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Processing...
        </>
      );
    }

    if (isCurrentPlan && hasActiveSubscription) {
      return 'Current Plan';
    }

    if (plan.id === 'enterprise') {
      return 'Contact Sales';
    }

    // For users with an inactive subscription to the same plan
    if (isCurrentPlan && !hasActiveSubscription && hasStripeCustomerId) {
      return (
        <>
          Reactivate Plan
          <ArrowRight className="ml-2 h-4 w-4" />
        </>
      );
    }

    // For upgrade/downgrade scenarios with active subscription
    if (currentPlan && hasActiveSubscription) {
      if (currentPlan === 'starter' && plan.id === 'pro') {
        return (
          <>
            Upgrade to Pro
            <ArrowRight className="ml-2 h-4 w-4" />
          </>
        );
      }

      if (currentPlan === 'pro' && plan.id === 'starter') {
        return (
          <>
            Downgrade to Starter
            <ArrowRight className="ml-2 h-4 w-4" />
          </>
        );
      }
    }

    // For users with a Stripe customer ID but no active subscription
    if (hasStripeCustomerId && !hasActiveSubscription) {
      return (
        <>
          Subscribe to {plan.name}
          <ArrowRight className="ml-2 h-4 w-4" />
        </>
      );
    }

    // For new users with no subscription
    return (
      <>
        {currentPlan ? 'Switch to' : 'Get'} {plan.name}
        <ArrowRight className="ml-2 h-4 w-4" />
      </>
    );
  };

  if (isLoadingPlans) {
    return (
      <div className="flex justify-center items-center h-40">
        <Loader2 className="h-8 w-8 animate-spin text-primary dark:text-primary" />
        <span className="ml-2 text-gray-600 dark:text-white">Loading plans...</span>
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {plans.map((plan) => {
        const isCurrentPlan = currentPlan === plan.id;
        const isDisabled = shouldDisablePlan(plan.id);

        return (
          <Card
            key={plan.id}
            className={`flex flex-col
              ${plan.isPopular ? 'border-primary shadow-lg dark:border-blue-500' : 'dark:border-white/20'}
              ${isCurrentPlan ? 'bg-gray-50 dark:bg-black' : 'dark:bg-black'}
            `}
          >
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="dark:text-white">{plan.name}</CardTitle>
                  <CardDescription className="mt-1 dark:text-white">{plan.description}</CardDescription>
                </div>
                {isCurrentPlan && (
                  <Badge className="bg-green-500 text-white dark:bg-green-600">Current</Badge>
                )}
                {!isCurrentPlan && plan.isPopular && (
                  <Badge className="bg-primary text-primary-foreground dark:bg-blue-600 dark:text-white">Popular</Badge>
                )}
              </div>
              <div className="mt-4">
                {plan.id === 'enterprise' ? (
                  <div className="text-2xl font-bold dark:text-white">Contact Us</div>
                ) : plan.id === 'starter' ? (
                  <div>
                    <div className="flex items-baseline">
                      <span className="text-3xl font-bold dark:text-white">${plan.price}</span>
                      <span className="ml-1 text-muted-foreground dark:text-white/70">/month</span>
                    </div>
                    <div className="text-sm text-green-600 dark:text-green-400 font-medium mt-1">
                      14-day free trial included
                    </div>
                  </div>
                ) : (
                  <div className="flex items-baseline">
                    <span className="text-3xl font-bold dark:text-white">${plan.price}</span>
                    <span className="ml-1 text-muted-foreground dark:text-white/70">/month</span>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="flex-grow">
              <ul className="space-y-3">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <Check
                      className={`mr-2 h-4 w-4 ${feature.included ? 'text-green-500 dark:text-green-400' : 'text-muted-foreground dark:text-white/40'}`}
                    />
                    <span className={feature.included ? 'dark:text-white' : 'text-muted-foreground dark:text-white/40 line-through'}>
                      {feature.name}
                    </span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                className={`w-full
                  ${plan.isPopular ? 'bg-primary dark:bg-blue-600 dark:hover:bg-blue-700 dark:text-white' : 'dark:bg-white/10 dark:text-white dark:hover:bg-white/20'}
                  ${isCurrentPlan ? 'dark:border-white/20' : ''}
                `}
                variant={isCurrentPlan ? 'outline' : 'default'}
                disabled={isLoading !== null || isDisabled}
                onClick={() => handleUpgrade(plan.id)}
              >
                {getButtonText(plan, isCurrentPlan)}
              </Button>

              {isCurrentPlan && (
                <div className="w-full text-center text-sm text-muted-foreground dark:text-white/70 mt-2">
                  Your current active plan
                </div>
              )}
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );
}
