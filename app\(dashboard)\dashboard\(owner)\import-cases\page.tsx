'use client';

import { useState, useRef, useEffect, use } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useRouter } from 'next/navigation';
import {
  ArrowLeftIcon,
  DownloadIcon,
  UploadIcon,
  FileIcon,
  CheckCircledIcon,
  CrossCircledIcon,
  Pencil1Icon,
  TrashIcon,
  MagnifyingGlassIcon,
} from '@radix-ui/react-icons';
import {
  useImportCaseStudiesMutation,
  useLazyExportCaseStudiesQuery,
  useGetCaseStudiesQuery,
  useDeleteCaseStudyMutation,
  useUpdateCaseStudyMutation,
  CaseStudyResponse,
  CaseStudyData as OriginalCaseStudyData,
} from '@/lib/redux/api/caseStudiesApi';

// Extend the CaseStudyData type to include our new icon properties
interface CaseStudyData extends OriginalCaseStudyData {
  challengeIcons?: { index: number; url: string }[];
  questionIcons?: { index: number; url: string }[];
}
import { CloudinaryUploader } from '@/components/ui/cloudinary-uploader';
import { CloudinaryImage } from '@/components/ui/cloudinary-image';
import { createImportFormData } from '@/lib/utils/form-data-helper';
import { toast } from '@/components/ui/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { AddCaseForm } from '@/components/AddCaseForm';
import { Loading } from '@/components/ui/loading';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import SafeReduxWrapper from '@/components/SafeReduxWrapper';
import { Pagination } from '@/components/ui/pagination';
import { industries } from '@/constants';
import { useUser } from '@/lib/auth';

export default function ImportCasesPage() {
  return (
    <SafeReduxWrapper
      fallback={<Loading text='Loading case management...' size='lg' />}
    >
      <ImportCasesContent />
    </SafeReduxWrapper>
  );
}

function ImportCasesContent() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [importCaseStudies, { isLoading: isImporting }] =
    useImportCaseStudiesMutation();
  const [exportCaseStudies, { isLoading: isExporting }] =
    useLazyExportCaseStudiesQuery();
  const [deleteCaseStudy] = useDeleteCaseStudyMutation();
  const [updateCaseStudy, { isLoading: isUpdating }] =
    useUpdateCaseStudyMutation();
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 0,
  });
  const { userPromise } = useUser();
  const user: any = use(userPromise);
  const isActualOwner = user?.role === 'owner' && user?.teamRole === 'owner';
  const {
    data: casesData,
    isLoading: isLoadingCases,
    refetch,
  } = useGetCaseStudiesQuery({
    page: pagination.page,
    pageSize: pagination.pageSize,
  });
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    if (casesData) {
      const totalData = Number(casesData.pagination.total);
      setPagination((prev) => ({
        ...prev,
        total: totalData,
        totalPages: Math.ceil(totalData / prev.pageSize),
      }));
    }
  }, [casesData, pagination.page]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  // Check for editId in URL query parameters
  const [searchParams, setSearchParams] = useState<URLSearchParams | null>(
    null
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleEditCaseFromUrl = async (id: number) => {
    await handleEditCase(id);
  };

  useEffect(() => {
    // Get URL search params on client side
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      setSearchParams(params);

      // Check if we have an editId parameter
      const editId = params.get('editId');
      if (editId && !isNaN(Number(editId))) {
        // Automatically open the edit dialog for this case study
        handleEditCaseFromUrl(Number(editId));

        // Update the URL to remove the editId parameter (to avoid reopening on refresh)
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importResult, setImportResult] = useState<{
    success: boolean;
    imported: number;
    errors?: Array<{ row: any; error: string }>;
  } | null>(null);

  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('import');
  const [editingCase, setEditingCase] = useState<CaseStudyData | null>(null);

  // Helper function to safely update nested objects with type assertion
  const updateEditingCase = (updatedCase: Partial<CaseStudyData>) => {
    if (!editingCase) return;
    console.log('Updating editing case with:', updatedCase);
    const newState = { ...editingCase, ...updatedCase } as CaseStudyData;
    setEditingCase(newState);
    console.log('New editing case state:', newState);
  };
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      setSelectedFile(e.target.files[0]);
      setImportResult(null);
    }
  };

  const handleDownloadTemplate = () => {
    const link = document.createElement('a');
    link.href = '/case-study-template.csv';
    link.download = 'case-study-template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: 'Template Downloaded',
      description: 'CSV template has been downloaded successfully.',
    });
  };

  const handleImport = async () => {
    if (!selectedFile) {
      toast({
        title: 'No File Selected',
        description: 'Please select a CSV file to import.',
        variant: 'destructive',
      });
      return;
    }

    // Validate file type
    if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
      toast({
        title: 'Invalid File Type',
        description: 'Please select a valid CSV file.',
        variant: 'destructive',
      });
      return;
    }

    // Validate file size (10MB max)
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
    if (selectedFile.size > MAX_FILE_SIZE) {
      toast({
        title: 'File Too Large',
        description: 'The selected file exceeds the maximum size of 10MB.',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Use the helper function to create FormData
      const formData = createImportFormData(selectedFile);

      // Set a timeout to handle long-running imports
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Import request timed out')), 60000); // 60 seconds timeout
      });

      // Race between the import and the timeout
      const result = (await Promise.race([
        importCaseStudies(formData).unwrap(),
        timeoutPromise,
      ])) as {
        success: boolean;
        imported: number;
        errors?: Array<{ row: any; error: string }>;
      };

      setImportResult(result);

      // Show appropriate toast based on import results
      if (result.errors && result.errors.length > 0) {
        toast({
          title: 'Import Completed with Warnings',
          description: `Imported ${result.imported} case studies with ${result.errors.length} errors. See details below.`,
          variant: 'default',
        });
      } else {
        toast({
          title: 'Import Completed Successfully',
          description: `Successfully imported ${result.imported} case studies.`,
        });
      }
    } catch (error: any) {
      // Import from error-handler.ts
      const { logError, getUserFriendlyMessage, ErrorType, parseError } =
        await import('@/lib/utils/error-handler');

      // Log the error with context
      logError(error, 'Case studies import');

      // Parse the error to get detailed information
      const parsedError = parseError(error);

      // Determine specific error message based on error type
      let errorMessage = 'Failed to import case studies. ';

      if (parsedError.type === ErrorType.NETWORK) {
        errorMessage += 'Please check your network connection and try again.';
      } else if (parsedError.type === ErrorType.FILE_PROCESSING) {
        errorMessage +=
          'Please check your file format and ensure it matches the template.';
      } else if (parsedError.message.includes('timeout')) {
        errorMessage +=
          'The import process took too long. Try importing fewer records at a time.';
      } else {
        errorMessage += getUserFriendlyMessage(error);
      }

      toast({
        title: 'Import Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Filter cases based on search term
  const filteredCases = casesData?.caseStudies.filter(
    (caseStudy) =>
      caseStudy.useCaseTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (caseStudy.industry &&
        caseStudy.industry.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (caseStudy.role &&
        caseStudy.role.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleUpdateCase = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingCase || !editingCase.id) return;

    // Use the isUpdating state from the mutation

    try {
      console.log('Preparing to update case study with ID:', editingCase.id);
      console.log(
        'Current editingCase state:',
        JSON.stringify(editingCase, null, 2)
      );

      // Map the form data to the API format
      const apiData = {
        useCaseTitle: editingCase.useCaseTitle,
        industry: editingCase.industry,
        role: editingCase.role,
        vector: editingCase.vector,
        headerImage: editingCase.headerImage,
        previewImageUrl: editingCase.thumbnailImage,

        // Use the raw potentiallyImpactedKpis string if available
        potentiallyImpactedKpis: editingCase.potentiallyImpactedKpis || null,

        // Map Introduction
        introductionTitle: editingCase.introduction?.title,
        introductionText: editingCase.introduction?.text,
        challange1: editingCase.introduction?.problems[0],
        challange2: editingCase.introduction?.problems[1],
        challange3: editingCase.introduction?.problems[2],
        question1: editingCase.introduction?.questions[0],
        question2: editingCase.introduction?.questions[1],
        question3: editingCase.introduction?.questions[2],

        // Map Process
        processTitle: editingCase.process?.title,
        processStep1Title: editingCase.process?.steps[0]?.title,
        processStep1Description: editingCase.process?.steps[0]?.description,
        processStep2Title: editingCase.process?.steps[1]?.title,
        processStep2Description: editingCase.process?.steps[1]?.description,
        processStep3Title: editingCase.process?.steps[2]?.title,
        processStep3Description: editingCase.process?.steps[2]?.description,

        // Map Solution
        solutionTitle: editingCase.solution?.title,
        solutionDescription: editingCase.solution?.description,
        solution1Title: editingCase.solution?.items[0]?.title,
        solution1Description: editingCase.solution?.items[0]?.description,
        solution2Title: editingCase.solution?.items[1]?.title,
        solution2Description: editingCase.solution?.items[1]?.description,
        solution3Title: editingCase.solution?.items[2]?.title,
        solution3Description: editingCase.solution?.items[2]?.description,
        solution4Title: editingCase.solution?.items[3]?.title,
        solution4Description: editingCase.solution?.items[3]?.description,
        solution5Title: editingCase.solution?.items[4]?.title,
        solution5Description: editingCase.solution?.items[4]?.description,

        // Map Impact
        impactTitle: editingCase.impact?.title,
        potentialImpact1: editingCase.impact?.metrics[0]?.description,
        potentialImpact1Quantitative: editingCase.impact?.metrics[0]?.value,
        potentialImpact2: editingCase.impact?.metrics[1]?.description,
        potentialImpact2Quantitative: editingCase.impact?.metrics[1]?.value,
        potentialImpact3: editingCase.impact?.metrics[2]?.description,
        potentialImpact3Quantitative: editingCase.impact?.metrics[2]?.value,
        potentialImpact4: editingCase.impact?.metrics[3]?.description,
        potentialImpact4Quantitative: editingCase.impact?.metrics[3]?.value,

        // Map Conclusion
        conclusionTitle: editingCase.conclusion?.title,
        conclusionText: editingCase.conclusion?.text,
        conclusionResultTitle: editingCase.conclusion?.resultTitle,
        conclusionResult: editingCase.conclusion?.resultDescription, // Fix: Map to conclusionResult instead of conclusionResultDec

        // Map Impact Quantitative Values and Icon URLs
        potentialImpact1IconImageUrl: editingCase.impact?.metrics[0]?.icon,
        potentialImpact2IconImageUrl: editingCase.impact?.metrics[1]?.icon,
        potentialImpact3IconImageUrl: editingCase.impact?.metrics[2]?.icon,
        potentialImpact4IconImageUrl: editingCase.impact?.metrics[3]?.icon,

        // Prepare the icons array
        icons: [
          // Process icons
          ...(editingCase.process?.steps || [])
            .map((step, index) => ({
              iconType: 'process',
              iconUrl: step.icon,
              order: index,
            }))
            .filter((icon) => icon.iconUrl && icon.iconUrl.trim() !== ''),

          // Solution icons
          ...(editingCase.solution?.items || [])
            .map((item, index) => ({
              iconType: 'solution',
              iconUrl: item.icon,
              order: index,
            }))
            .filter((icon) => icon.iconUrl && icon.iconUrl.trim() !== ''),

          // Impact icons
          ...(editingCase.impact?.metrics || [])
            .map((metric, index) => ({
              iconType: 'impact',
              iconUrl: metric.icon,
              order: index,
            }))
            .filter((icon) => icon.iconUrl && icon.iconUrl.trim() !== ''),

          // Challenge icons
          ...(editingCase.challengeIcons || [])
            .map((icon, index) => ({
              iconType: 'challenge',
              iconUrl: icon.url,
              order: icon.index,
            }))
            .filter((icon) => icon.iconUrl && icon.iconUrl.trim() !== ''),

          // Question icons
          ...(editingCase.questionIcons || [])
            .map((icon, index) => ({
              iconType: 'question',
              iconUrl: icon.url,
              order: icon.index,
            }))
            .filter((icon) => icon.iconUrl && icon.iconUrl.trim() !== ''),

          // Header icon
          ...(editingCase.headerImage && editingCase.headerImage.trim() !== ''
            ? [
                {
                  iconType: 'header',
                  iconUrl: editingCase.headerImage,
                  order: 0,
                },
              ]
            : []),

          // Thumbnail icon
          ...(editingCase.thumbnailImage &&
          editingCase.thumbnailImage.trim() !== ''
            ? [
                {
                  iconType: 'icon',
                  iconUrl: editingCase.thumbnailImage,
                  order: 0,
                },
              ]
            : []),
        ],
      };

      // Log the icons being sent for debugging
      console.log(
        'Sending API data for update:',
        JSON.stringify(apiData, null, 2)
      );
      console.log('Icons being sent:', JSON.stringify(apiData.icons, null, 2));

      // Log the icons by type for easier debugging
      console.log('Icons by type:', {
        header: apiData.icons.filter((i) => i.iconType === 'header').length,
        thumbnail: apiData.icons.filter((i) => i.iconType === 'icon').length,
        process: apiData.icons.filter((i) => i.iconType === 'process').length,
        solution: apiData.icons.filter((i) => i.iconType === 'solution').length,
        impact: apiData.icons.filter((i) => i.iconType === 'impact').length,
      });

      // Validate all icon URLs before sending
      apiData.icons = apiData.icons.filter((icon) => {
        if (!icon.iconUrl || icon.iconUrl.trim() === '') {
          console.log(
            `Filtering out icon with empty URL: ${JSON.stringify(icon)}`
          );
          return false;
        }

        try {
          new URL(icon.iconUrl);
          return true;
        } catch (e) {
          console.error(`Invalid icon URL: ${icon.iconUrl}`, e);
          return false;
        }
      });

      console.log(
        'Filtered icons being sent:',
        JSON.stringify(apiData.icons, null, 2)
      );

      // Convert null to undefined for TypeScript compatibility
      const apiDataFixed = {
        ...apiData,
        potentiallyImpactedKpis:
          apiData.potentiallyImpactedKpis === null
            ? undefined
            : apiData.potentiallyImpactedKpis,
      };

      const result = await updateCaseStudy({
        id: editingCase.id,
        data: apiDataFixed,
      }).unwrap();
      console.log('Update result:', result);

      toast({
        title: 'Success',
        description: 'Case study updated successfully',
      });
      setEditingCase(null);
      setIsDialogOpen(false);
      refetch();
    } catch (error) {
      console.error('Failed to update case study:', error);
      toast({
        title: 'Error',
        description: 'Failed to update case study',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteCase = async (id: number) => {
    if (confirm('Are you sure you want to delete this case study?')) {
      try {
        await deleteCaseStudy(id).unwrap();
        toast({
          title: 'Success',
          description: 'Case study deleted successfully',
        });
        refetch();
      } catch (error) {
        console.error('Failed to delete case study:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete case study',
          variant: 'destructive',
        });
      }
    }
  };

  // Define an interface for the icon structure
  interface CaseStudyIcon {
    id: number;
    caseStudyId: number;
    iconType: string;
    iconUrl: string;
    order: number;
  }

  // Extended response type that includes all fields
  interface FullCaseStudyResponse extends CaseStudyResponse {
    introductionTitle?: string;
    introductionText?: string;
    challange1?: string;
    challange2?: string;
    challange3?: string;
    question1?: string;
    question2?: string;
    question3?: string;
    processTitle?: string;
    processStep1Title?: string;
    processStep1Description?: string;
    processStep2Title?: string;
    processStep2Description?: string;
    processStep3Title?: string;
    processStep3Description?: string;
    solutionTitle?: string;
    solutionDescription?: string;
    solution1Title?: string;
    solution1Description?: string;
    solution2Title?: string;
    solution2Description?: string;
    solution3Title?: string;
    solution3Description?: string;
    solution4Title?: string;
    solution4Description?: string;
    solution5Title?: string;
    solution5Description?: string;
    impactTitle?: string;
    potentialImpact1?: string;
    potentialImpact1Quantitative?: string;
    potentialImpact1IconImageUrl?: string;
    potentialImpact2?: string;
    potentialImpact2Quantitative?: string;
    potentialImpact2IconImageUrl?: string;
    potentialImpact3?: string;
    potentialImpact3Quantitative?: string;
    potentialImpact3IconImageUrl?: string;
    potentialImpact4?: string;
    potentialImpact4Quantitative?: string;
    potentialImpact4IconImageUrl?: string;
    conclusionTitle?: string;
    conclusionText?: string;
    conclusionResultTitle?: string;
    conclusionResultDec?: string;
    conclusionResult?: string;
    potentiallyImpactedKpis?: string;
    featureImageUrl?: string;
    previewImageUrl?: string;
    vector?: string;
    icons?: CaseStudyIcon[];
  }

  // Handle header image upload success
  const handleHeaderImageUploadSuccess = (url: string) => {
    if (!editingCase) return;

    updateEditingCase({
      headerImage: url,
    });

    toast({
      title: 'Success',
      description: 'Header image uploaded successfully',
    });
  };

  // Handle thumbnail image upload success
  const handleThumbnailImageUploadSuccess = (url: string) => {
    if (!editingCase) return;

    updateEditingCase({
      thumbnailImage: url,
    });

    toast({
      title: 'Success',
      description: 'Thumbnail image uploaded successfully',
    });
  };

  // Handle image upload error
  const handleImageUploadError = (error: any, type: string) => {
    console.error(`Error uploading ${type} image:`, error);
    toast({
      title: 'Error',
      description: `Failed to upload ${type} image. Please try again.`,
      variant: 'destructive',
    });
  };

  // Handle process step icon upload success
  const handleProcessIconUploadSuccess = (url: string, stepIndex: number) => {
    if (!editingCase || !editingCase.process?.steps) return;

    console.log(`Process icon upload success for step ${stepIndex}:`, url);

    const newSteps = [...editingCase.process.steps];
    // Ensure we have enough items in the array
    while (newSteps.length <= stepIndex) {
      newSteps.push({ title: '', description: '', icon: '' });
    }
    newSteps[stepIndex] = { ...newSteps[stepIndex], icon: url };

    // Update the case study with the new process steps
    updateEditingCase({
      process: {
        ...editingCase.process,
        steps: newSteps,
        title: editingCase.process.title || '',
      },
    });

    // Log the updated state for debugging
    console.log(
      `Updated process steps after icon upload for step ${stepIndex}:`,
      newSteps
    );

    toast({
      title: 'Success',
      description: `Process step ${stepIndex + 1} icon uploaded successfully`,
    });
  };

  // Handle solution item icon upload success
  const handleSolutionIconUploadSuccess = (url: string, itemIndex: number) => {
    if (!editingCase || !editingCase.solution?.items) return;

    console.log(`Solution icon upload success for item ${itemIndex}:`, url);

    const newItems = [...editingCase.solution.items];
    // Ensure we have enough items in the array
    while (newItems.length <= itemIndex) {
      newItems.push({ title: '', description: '', icon: '' });
    }
    newItems[itemIndex] = { ...newItems[itemIndex], icon: url };

    // Update the case study with the new solution items
    updateEditingCase({
      solution: {
        ...editingCase.solution,
        items: newItems,
        title: editingCase.solution.title || '',
        description: editingCase.solution.description || '',
      },
    });

    // Log the updated state for debugging
    console.log(
      `Updated solution items after icon upload for item ${itemIndex}:`,
      newItems
    );

    toast({
      title: 'Success',
      description: `Solution item ${itemIndex + 1} icon uploaded successfully`,
    });
  };

  // Handle impact metric icon upload success
  const handleImpactIconUploadSuccess = (url: string, metricIndex: number) => {
    if (!editingCase || !editingCase.impact?.metrics) return;

    console.log(`Impact icon upload success for metric ${metricIndex}:`, url);

    const newMetrics = [...editingCase.impact.metrics];
    // Ensure we have enough items in the array
    while (newMetrics.length <= metricIndex) {
      newMetrics.push({ metric: '', value: '', description: '', icon: '' });
    }
    newMetrics[metricIndex] = { ...newMetrics[metricIndex], icon: url };

    // Update the case study with the new impact metrics
    updateEditingCase({
      impact: {
        ...editingCase.impact,
        metrics: newMetrics,
        title: editingCase.impact.title || '',
      },
    });

    // Log the updated state for debugging
    console.log(
      `Updated impact metrics after icon upload for metric ${metricIndex}:`,
      newMetrics
    );

    toast({
      title: 'Success',
      description: `Impact metric ${
        metricIndex + 1
      } icon uploaded successfully`,
    });
  };

  // Handle challenge icon upload success
  const handleChallengeIconUploadSuccess = (
    url: string,
    challengeIndex: number
  ) => {
    if (!editingCase || !editingCase.introduction?.problems) return;

    console.log(
      `Challenge icon upload success for challenge ${challengeIndex}:`,
      url
    );

    const newProblems = [...editingCase.introduction.problems];
    // Ensure we have enough items in the array
    while (newProblems.length <= challengeIndex) {
      newProblems.push('');
    }

    // Get current icons or initialize empty array
    const currentIcons = editingCase.challengeIcons || [];
    const newIcons = [...currentIcons];

    // Find existing icon for this challenge or create a new one
    const existingIconIndex = newIcons.findIndex(
      (icon) => icon.index === challengeIndex
    );
    if (existingIconIndex >= 0) {
      newIcons[existingIconIndex] = { ...newIcons[existingIconIndex], url };
    } else {
      newIcons.push({ index: challengeIndex, url });
    }

    // Update the case study with the new challenge icons
    updateEditingCase({
      introduction: {
        ...editingCase.introduction,
        problems: newProblems,
        title: editingCase.introduction.title || '',
        text: editingCase.introduction.text || '',
        questions: editingCase.introduction.questions || [],
      },
      challengeIcons: newIcons,
    });

    // Log the updated state for debugging
    console.log(
      `Updated challenge icons after upload for challenge ${challengeIndex}:`,
      newIcons
    );

    toast({
      title: 'Success',
      description: `Challenge ${challengeIndex + 1} icon uploaded successfully`,
    });
  };

  // Handle question icon upload success
  const handleQuestionIconUploadSuccess = (
    url: string,
    questionIndex: number
  ) => {
    if (!editingCase || !editingCase.introduction?.questions) return;

    console.log(
      `Question icon upload success for question ${questionIndex}:`,
      url
    );

    const newQuestions = [...editingCase.introduction.questions];
    // Ensure we have enough items in the array
    while (newQuestions.length <= questionIndex) {
      newQuestions.push('');
    }

    // Get current icons or initialize empty array
    const currentIcons = editingCase.questionIcons || [];
    const newIcons = [...currentIcons];

    // Find existing icon for this question or create a new one
    const existingIconIndex = newIcons.findIndex(
      (icon) => icon.index === questionIndex
    );
    if (existingIconIndex >= 0) {
      newIcons[existingIconIndex] = { ...newIcons[existingIconIndex], url };
    } else {
      newIcons.push({ index: questionIndex, url });
    }

    // Update the case study with the new question icons
    updateEditingCase({
      introduction: {
        ...editingCase.introduction,
        questions: newQuestions,
        title: editingCase.introduction.title || '',
        text: editingCase.introduction.text || '',
        problems: editingCase.introduction.problems || [],
      },
      questionIcons: newIcons,
    });

    // Log the updated state for debugging
    console.log(
      `Updated question icons after upload for question ${questionIndex}:`,
      newIcons
    );

    toast({
      title: 'Success',
      description: `Question ${questionIndex + 1} icon uploaded successfully`,
    });
  };

  const handleEditCase = async (caseStudyId: number) => {
    try {
      // Fetch the full case study details
      const fullCaseStudy: FullCaseStudyResponse = await fetch(
        `/api/case-studies/${caseStudyId}`
      ).then((res) => res.json());

      console.log('Fetched case study details:', {
        id: fullCaseStudy.id,
        title: fullCaseStudy.useCaseTitle,
        headerImage: fullCaseStudy.headerImage,
        featureImageUrl: fullCaseStudy.featureImageUrl,
        previewImageUrl: fullCaseStudy.previewImageUrl,
        iconCount: fullCaseStudy.icons?.length || 0,
        headerIcon: fullCaseStudy.icons?.find(
          (i: CaseStudyIcon) => i.iconType === 'header'
        )?.iconUrl,
        thumbnailIcon: fullCaseStudy.icons?.find(
          (i: CaseStudyIcon) => i.iconType === 'icon'
        )?.iconUrl,
        processIcons:
          fullCaseStudy.icons?.filter(
            (i: CaseStudyIcon) => i.iconType === 'process'
          ).length || 0,
        solutionIcons:
          fullCaseStudy.icons?.filter(
            (i: CaseStudyIcon) => i.iconType === 'solution'
          ).length || 0,
        impactIcons:
          fullCaseStudy.icons?.filter(
            (i: CaseStudyIcon) => i.iconType === 'impact'
          ).length || 0,
      });

      // Log all icons for debugging
      console.log('All icons:', fullCaseStudy.icons);

      // Get all icons from the API response
      const icons = fullCaseStudy.icons || [];
      console.log('All icons from API:', icons);

      // Get header image from icons first, then fallback to other sources
      const headerImage =
        fullCaseStudy.icons?.find((i: CaseStudyIcon) => i.iconType === 'header')
          ?.iconUrl ||
        fullCaseStudy.headerImage ||
        fullCaseStudy.featureImageUrl ||
        '';

      // Get thumbnail image from icons first, then fallback to other sources
      const thumbnailImage =
        fullCaseStudy.icons?.find((i: CaseStudyIcon) => i.iconType === 'icon')
          ?.iconUrl ||
        fullCaseStudy.previewImageUrl ||
        '';

      console.log('Using images:', { headerImage, thumbnailImage });

      // Extract challenge and question icons from the icons array
      const challengeIcons =
        fullCaseStudy.icons
          ?.filter((i: CaseStudyIcon) => i.iconType === 'challenge')
          .map((i: CaseStudyIcon) => ({ index: i.order, url: i.iconUrl })) ||
        [];

      const questionIcons =
        fullCaseStudy.icons
          ?.filter((i: CaseStudyIcon) => i.iconType === 'question')
          .map((i: CaseStudyIcon) => ({ index: i.order, url: i.iconUrl })) ||
        [];

      console.log('Challenge icons:', challengeIcons);
      console.log('Question icons:', questionIcons);

      console.log('Full case study data:', fullCaseStudy);

      // Map the API response to our form data structure
      setEditingCase({
        id: fullCaseStudy.id,
        useCaseTitle: fullCaseStudy.useCaseTitle,
        headerImage: headerImage,
        thumbnailImage: thumbnailImage,
        industry: fullCaseStudy.industry || '',
        role: fullCaseStudy.role || '',
        vector: fullCaseStudy.vector || '',
        challengeIcons,
        questionIcons,

        // Map KPIs
        kpis: {
          claimCycleTime: '',
          straightThroughRate: '',
          customerComplaintVolume: '',
        },

        // Store the raw potentiallyImpactedKpis string for display
        potentiallyImpactedKpis: fullCaseStudy.potentiallyImpactedKpis || '',

        // Map Introduction
        introduction: {
          title: fullCaseStudy.introductionTitle || '',
          text: fullCaseStudy.introductionText || '',
          problems: [
            fullCaseStudy.challange1 || '',
            fullCaseStudy.challange2 || '',
            fullCaseStudy.challange3 || '',
          ],
          questions: [
            fullCaseStudy.question1 || '',
            fullCaseStudy.question2 || '',
            fullCaseStudy.question3 || '',
          ],
        },

        // Map Process
        process: {
          title: fullCaseStudy.processTitle || '',
          steps: [
            {
              title: fullCaseStudy.processStep1Title || '',
              description: fullCaseStudy.processStep1Description || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) =>
                    i.iconType === 'process' && i.order === 0
                )?.iconUrl || '',
            },
            {
              title: fullCaseStudy.processStep2Title || '',
              description: fullCaseStudy.processStep2Description || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) =>
                    i.iconType === 'process' && i.order === 1
                )?.iconUrl || '',
            },
            {
              title: fullCaseStudy.processStep3Title || '',
              description: fullCaseStudy.processStep3Description || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) =>
                    i.iconType === 'process' && i.order === 2
                )?.iconUrl || '',
            },
          ],
        },

        // Map Solution
        solution: {
          title: fullCaseStudy.solutionTitle || '',
          description: fullCaseStudy.solutionDescription || '',
          items: [
            {
              title: fullCaseStudy.solution1Title || '',
              description: fullCaseStudy.solution1Description || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) =>
                    i.iconType === 'solution' && i.order === 0
                )?.iconUrl || '',
            },
            {
              title: fullCaseStudy.solution2Title || '',
              description: fullCaseStudy.solution2Description || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) =>
                    i.iconType === 'solution' && i.order === 1
                )?.iconUrl || '',
            },
            {
              title: fullCaseStudy.solution3Title || '',
              description: fullCaseStudy.solution3Description || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) =>
                    i.iconType === 'solution' && i.order === 2
                )?.iconUrl || '',
            },
            {
              title: fullCaseStudy.solution4Title || '',
              description: fullCaseStudy.solution4Description || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) =>
                    i.iconType === 'solution' && i.order === 3
                )?.iconUrl || '',
            },
            {
              title: fullCaseStudy.solution5Title || '',
              description: fullCaseStudy.solution5Description || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) =>
                    i.iconType === 'solution' && i.order === 4
                )?.iconUrl || '',
            },
          ],
        },

        // Map Impact
        impact: {
          title: fullCaseStudy.impactTitle || '',
          metrics: [
            {
              metric: 'Impact 1',
              value: fullCaseStudy.potentialImpact1Quantitative || '',
              description: fullCaseStudy.potentialImpact1 || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) => i.iconType === 'impact' && i.order === 0
                )?.iconUrl ||
                fullCaseStudy.potentialImpact1IconImageUrl ||
                '',
            },
            {
              metric: 'Impact 2',
              value: fullCaseStudy.potentialImpact2Quantitative || '',
              description: fullCaseStudy.potentialImpact2 || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) => i.iconType === 'impact' && i.order === 1
                )?.iconUrl ||
                fullCaseStudy.potentialImpact2IconImageUrl ||
                '',
            },
            {
              metric: 'Impact 3',
              value: fullCaseStudy.potentialImpact3Quantitative || '',
              description: fullCaseStudy.potentialImpact3 || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) => i.iconType === 'impact' && i.order === 2
                )?.iconUrl ||
                fullCaseStudy.potentialImpact3IconImageUrl ||
                '',
            },
            {
              metric: 'Impact 4',
              value: fullCaseStudy.potentialImpact4Quantitative || '',
              description: fullCaseStudy.potentialImpact4 || '',
              icon:
                fullCaseStudy.icons?.find(
                  (i: CaseStudyIcon) => i.iconType === 'impact' && i.order === 3
                )?.iconUrl ||
                fullCaseStudy.potentialImpact4IconImageUrl ||
                '',
            },
          ],
        },

        // Map Conclusion
        conclusion: {
          title: fullCaseStudy.conclusionTitle || '',
          text: fullCaseStudy.conclusionText || '',
          resultTitle: fullCaseStudy.conclusionResultTitle || '',
          // Use conclusionResult if available, fall back to conclusionResultDec for backward compatibility
          resultDescription:
            fullCaseStudy.conclusionResult ||
            fullCaseStudy.conclusionResultDec ||
            '',
        },
      });

      setIsDialogOpen(true);
    } catch (error) {
      console.error('Error fetching case study details:', error);
      toast({
        title: 'Error',
        description: 'Failed to load case study details',
        variant: 'destructive',
      });
    }
  };

  console.log('Editing case study:', editingCase);

  return (
    <div className='container mx-auto p-0 md:p-4'>
      <div className='flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-10 md:mb-6'>
        <div className='flex items-center'>
          <Button
            variant='outline'
            onClick={() => router.push('/dashboard/case-studies')}
            className='mr-4'
          >
            <ArrowLeftIcon className='mr-2 h-4 w-4' />
            Back to Case Studies
          </Button>
        </div>
        <div className='flex w-full items-center justify-between gap-8'>
          <h1 className='text-[1.5rem] md:text-2xl font-bold'>
            Case Management
          </h1>
          <Button
            variant='outline'
            onClick={async () => {
              try {
                const blob = await exportCaseStudies().unwrap();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'case-studies.csv';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                toast({
                  title: 'Export Successful',
                  description: 'Case studies exported successfully',
                });
              } catch (error) {
                console.error('Failed to export case studies:', error);
                toast({
                  title: 'Export Failed',
                  description: 'Failed to export case studies',
                  variant: 'destructive',
                });
              }
            }}
            disabled={isExporting}
          >
            <DownloadIcon className='mr-2 h-4 w-4' />
            {isExporting ? (
              'Exporting...'
            ) : (
              <>
                <span className='hidden md:inline'>
                  Export All Case Studies
                </span>
                <span className='md:hidden'>Export</span>
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs
        defaultValue='import'
        value={activeTab}
        onValueChange={setActiveTab}
        className='space-y-6'
      >
        <TabsList>
          <TabsTrigger value='import'>Import CSV</TabsTrigger>
          <TabsTrigger value='add'>Add Manually</TabsTrigger>
          <TabsTrigger value='manage'>Manage Cases</TabsTrigger>
        </TabsList>

        <TabsContent value='import' className='space-y-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <Card>
              <CardHeader>
                <CardTitle>Download Template</CardTitle>
                <CardDescription>
                  Download our CSV template to ensure your data is formatted
                  correctly for import.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='flex items-center p-4 bg-muted rounded-lg border border-dashed'>
                  <FileIcon className='h-8 w-8 text-blue-500 mr-4' />
                  <div>
                    <p className='font-medium'>case-study-template.csv</p>
                    <p className='text-sm text-muted-foreground'>
                      CSV Template with role, vector, and image URL columns
                    </p>
                  </div>
                </div>
                <div className='mt-4 text-sm text-muted-foreground'>
                  <p className='font-medium mb-2'>Image Support:</p>
                  <ul className='list-disc pl-5 space-y-1'>
                    <li>
                      The template includes columns for image URLs and
                      additional fields
                    </li>
                    <li>You can use direct URLs to images (https://...)</li>
                    <li>Images will be stored in Cloudinary when editing</li>
                    <li>All fields can be edited after import</li>
                    <li>New fields include role, vector, and impact metrics</li>
                  </ul>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleDownloadTemplate}
                  className='w-full'
                  disabled={isExporting}
                >
                  {isExporting ? (
                    <>
                      <span className='mr-2 h-4 w-4 animate-spin'>⟳</span>
                      Downloading...
                    </>
                  ) : (
                    <>
                      <DownloadIcon className='mr-2 h-4 w-4' />
                      Download Template
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Import Case Studies</CardTitle>
                <CardDescription>
                  Upload your CSV file containing case study data to import in
                  bulk.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  className='flex flex-col items-center justify-center p-6 bg-muted rounded-lg border border-dashed cursor-pointer'
                  onClick={() => fileInputRef.current?.click()}
                >
                  <UploadIcon className='h-10 w-10 text-muted-foreground mb-4' />
                  <p className='font-medium text-center'>
                    {selectedFile
                      ? selectedFile.name
                      : 'Click to select a CSV file'}
                  </p>
                  <p className='text-sm text-muted-foreground mt-1'>
                    {selectedFile
                      ? `${(selectedFile.size / 1024).toFixed(2)} KB`
                      : 'CSV files only, max 10MB'}
                  </p>
                  <input
                    type='file'
                    ref={fileInputRef}
                    onChange={handleFileSelect}
                    accept='.csv'
                    className='hidden'
                  />
                </div>
              </CardContent>
              <CardFooter className='flex flex-col'>
                <Button
                  onClick={handleImport}
                  disabled={!selectedFile || isImporting}
                  className='w-full mb-4'
                >
                  {isImporting ? (
                    <>
                      <span className='mr-2 h-4 w-4 animate-spin'>⟳</span>
                      <span>Importing...</span>
                    </>
                  ) : (
                    <>
                      <UploadIcon className='mr-2 h-4 w-4' />
                      Import Case Studies
                    </>
                  )}
                </Button>

                {importResult && (
                  <Alert
                    variant={
                      importResult.errors && importResult.errors.length > 0
                        ? 'destructive'
                        : 'default'
                    }
                  >
                    {importResult.errors && importResult.errors.length > 0 ? (
                      <CrossCircledIcon className='h-4 w-4' />
                    ) : (
                      <CheckCircledIcon className='h-4 w-4' />
                    )}
                    <AlertTitle>
                      {importResult.errors && importResult.errors.length > 0
                        ? 'Import completed with errors'
                        : 'Import successful'}
                    </AlertTitle>
                    <AlertDescription>
                      Successfully imported {importResult.imported} case
                      studies.
                      {importResult.errors &&
                        importResult.errors.length > 0 && (
                          <div className='mt-2'>
                            {importResult.errors.length} errors occurred during
                            import.
                          </div>
                        )}
                    </AlertDescription>
                  </Alert>
                )}
              </CardFooter>
            </Card>
          </div>

          {importResult?.errors && importResult.errors.length > 0 && (
            <Card className='mt-6'>
              <CardHeader>
                <CardTitle>Import Errors</CardTitle>
                <CardDescription>
                  The following errors occurred during the import process.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='max-h-60 overflow-y-auto'>
                  <table className='w-full border-collapse'>
                    <thead>
                      <tr className='bg-muted'>
                        <th className='p-2 text-left'>Row</th>
                        <th className='p-2 text-left'>Error</th>
                      </tr>
                    </thead>
                    <tbody>
                      {importResult.errors.map((error, index) => (
                        <tr key={index} className='border-t'>
                          <td className='p-2 text-sm'>
                            {error.row.use_case_title || `Row ${index + 1}`}
                          </td>
                          <td className='p-2 text-sm text-red-600'>
                            {error.error}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value='add' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Add New Case Study</CardTitle>
              <CardDescription>
                Fill in the form below to manually add a new case study.
              </CardDescription>
            </CardHeader>
          </Card>
          <AddCaseForm
            onSuccess={() => {
              toast({
                title: 'Success',
                description: 'Case study created successfully',
              });
              refetch();
              setActiveTab('manage');
            }}
          />
        </TabsContent>

        <TabsContent value='manage' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Manage Case Studies</CardTitle>
              <CardDescription>
                View, edit, and delete existing case studies.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center'>
                  <div className='relative flex-1'>
                    <MagnifyingGlassIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                    <Input
                      placeholder='Search cases...'
                      className='pl-10'
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                {isLoadingCases ? (
                  <div className='py-8'>
                    <Loading text='Loading cases...' size='md' />
                  </div>
                ) : filteredCases && filteredCases.length > 0 ? (
                  <div className='rounded-md border overflow-hidden'>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Title</TableHead>
                          <TableHead>Industry</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead>Uploaded By</TableHead>
                          <TableHead>Uploaded</TableHead>
                          <TableHead className='text-right'>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredCases.map((caseStudy) => (
                          <TableRow key={caseStudy.id}>
                            <TableCell className='font-medium'>
                              {caseStudy.useCaseTitle}
                            </TableCell>
                            <TableCell>
                              {caseStudy.industry ? (
                                <Badge variant='outline'>
                                  {caseStudy.industry}
                                </Badge>
                              ) : (
                                <span className='text-muted-foreground'>-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {caseStudy.role ? (
                                <Badge variant='outline'>
                                  {caseStudy.role}
                                </Badge>
                              ) : (
                                <span className='text-muted-foreground'>-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {caseStudy.user?.name ? (
                                <span className='text-sm'>
                                  {caseStudy.user.name}
                                </span>
                              ) : caseStudy.user?.id ? (
                                <span className='text-sm text-amber-600'>
                                  User ID: {caseStudy.user.id}
                                </span>
                              ) : (
                                <span className='text-muted-foreground'>
                                  Unknown
                                </span>
                              )}
                            </TableCell>
                            <TableCell>
                              {caseStudy.createdAt ? (
                                <span className='text-sm text-muted-foreground'>
                                  {new Date(
                                    caseStudy.createdAt
                                  ).toLocaleDateString()}{' '}
                                  {new Date(
                                    caseStudy.createdAt
                                  ).toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                  })}
                                </span>
                              ) : (
                                <span className='text-muted-foreground'>-</span>
                              )}
                            </TableCell>
                            <TableCell className='text-right'>
                              <div className='flex justify-end space-x-2'>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() =>
                                    router.push(
                                      `/dashboard/case-studies/${caseStudy.id}`
                                    )
                                  }
                                >
                                  View
                                </Button>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() => handleEditCase(caseStudy.id)}
                                >
                                  <Pencil1Icon className='h-4 w-4' />
                                </Button>
                                {isActualOwner && (
                                  <Button
                                    variant='outline'
                                    size='sm'
                                    className='text-red-500'
                                    onClick={() =>
                                      handleDeleteCase(caseStudy.id)
                                    }
                                  >
                                    <TrashIcon className='h-4 w-4' />
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className='text-center py-8 border rounded-md'>
                    <p className='text-muted-foreground'>
                      No case studies found
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {pagination.totalPages > 0 && (
            <div className='flex justify-center mt-8'>
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </TabsContent>
      </Tabs>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className='sm:max-w-[800px] max-h-[90vh] overflow-y-auto'
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle>Edit Case Study</DialogTitle>
            <DialogDescription>
              Update the case study details below.
            </DialogDescription>
          </DialogHeader>
          {editingCase && (
            <form onSubmit={handleUpdateCase} className='space-y-8 pr-2'>
              {/* Basic Information Section */}
              <div className='space-y-4 border-b pb-6'>
                <h3 className='text-lg font-semibold'>Basic Information</h3>
                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='edit-title'>Title *</Label>
                    <Input
                      id='edit-title'
                      value={editingCase.useCaseTitle}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        updateEditingCase({ useCaseTitle: e.target.value })
                      }
                      required
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='edit-industry'>Industry</Label>
                    <Select
                      value={editingCase.industry}
                      onValueChange={(value) =>
                        updateEditingCase({ industry: value })
                      }
                    >
                      <SelectTrigger id='edit-industry' className='w-full'>
                        <SelectValue placeholder='Select industry' />
                      </SelectTrigger>

                      <SelectContent>
                        {industries.map((industry, index) => (
                          <SelectItem key={index} value={industry.title}>
                            {industry.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='edit-role'>Role</Label>
                    <Select
                      value={editingCase.role?.toLowerCase()}
                      onValueChange={(value) =>
                        updateEditingCase({ role: value })
                      }
                    >
                      <SelectTrigger id='edit-role' className='w-full'>
                        <SelectValue placeholder='Select role' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='ceo'>CEO</SelectItem>
                        <SelectItem value='coo'>COO</SelectItem>
                        <SelectItem value='cfo'>CFO</SelectItem>
                        <SelectItem value='cmo'>CMO</SelectItem>
                        <SelectItem value='cto'>CTO</SelectItem>
                        <SelectItem value='chro'>CHRO</SelectItem>
                        <SelectItem value='cro'>CRO</SelectItem>
                        <SelectItem value='cxo'>CXO</SelectItem>
                        <SelectItem value='manager'>Manager</SelectItem>
                        <SelectItem value='founder'>Founder</SelectItem>
                        <SelectItem value='other'>Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='edit-vector'>Vector</Label>
                    <Input
                      id='edit-vector'
                      value={editingCase.vector || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        updateEditingCase({ vector: e.target.value })
                      }
                    />
                  </div>
                </div>

                <div className='space-y-2 mt-4'>
                  <Label htmlFor='edit-kpis'>Potentially Impacted KPIs</Label>
                  <Input
                    id='edit-kpis'
                    value={editingCase.potentiallyImpactedKpis || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      updateEditingCase({
                        potentiallyImpactedKpis: e.target.value,
                      })
                    }
                    placeholder='Enter KPIs separated by commas (e.g., KPI 1, KPI 2, KPI 3)'
                  />
                  <p className='text-xs text-muted-foreground mt-1'>
                    Enter KPIs separated by commas (e.g., "Manual Error Rate,
                    Time to Complete Reports, Compliance Score")
                  </p>
                </div>

                <div className='grid grid-cols-2 gap-4 mt-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='edit-header-image'>Header Image</Label>
                    <div className='flex flex-col space-y-2'>
                      {editingCase.headerImage && (
                        <div className='relative h-40 bg-muted rounded-md overflow-hidden mb-2'>
                          <CloudinaryImage
                            src={editingCase.headerImage}
                            alt='Header Preview'
                            width={800}
                            height={300}
                            className='object-cover w-full h-full'
                            fallbackSrc='/placeholder-image.jpg'
                          />
                        </div>
                      )}
                      <div className='flex space-x-2'>
                        <Input
                          id='edit-header-image'
                          value={editingCase.headerImage}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            updateEditingCase({ headerImage: e.target.value })
                          }
                          placeholder='https://example.com/image.jpg'
                          className='flex-1'
                        />
                        <CloudinaryUploader
                          onSuccess={(url) =>
                            handleHeaderImageUploadSuccess(url)
                          }
                          onError={(error) =>
                            handleImageUploadError(error, 'header')
                          }
                          folder='case-studies/headers'
                          resourceType='image'
                          buttonText='Upload'
                          variant='outline'
                        />
                      </div>
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='edit-thumbnail-image'>
                      Thumbnail Image
                    </Label>
                    <div className='flex flex-col space-y-2'>
                      {editingCase.thumbnailImage && (
                        <div className='relative h-40 bg-muted rounded-md overflow-hidden mb-2'>
                          <CloudinaryImage
                            src={editingCase.thumbnailImage}
                            alt='Thumbnail Preview'
                            width={300}
                            height={300}
                            className='object-cover w-full h-full'
                            fallbackSrc='/placeholder-icon.png'
                          />
                        </div>
                      )}
                      <div className='flex space-x-2'>
                        <Input
                          id='edit-thumbnail-image'
                          value={editingCase.thumbnailImage || ''}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            updateEditingCase({
                              thumbnailImage: e.target.value,
                            })
                          }
                          placeholder='https://example.com/thumbnail.jpg'
                          className='flex-1'
                        />
                        <CloudinaryUploader
                          onSuccess={(url) =>
                            handleThumbnailImageUploadSuccess(url)
                          }
                          onError={(error) =>
                            handleImageUploadError(error, 'thumbnail')
                          }
                          folder='case-studies/thumbnails'
                          resourceType='image'
                          buttonText='Upload'
                          variant='outline'
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Introduction Section */}
              <div className='space-y-4 border-b pb-6'>
                <h3 className='text-lg font-semibold'>Introduction</h3>
                <div className='space-y-2'>
                  <Label htmlFor='edit-intro-title'>Introduction Title</Label>
                  <Input
                    id='edit-intro-title'
                    value={editingCase.introduction?.title}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      if (!editingCase?.introduction) return;
                      updateEditingCase({
                        introduction: {
                          ...editingCase.introduction,
                          title: e.target.value,
                          text: editingCase.introduction.text || '',
                          problems: editingCase.introduction.problems || [],
                          questions: editingCase.introduction.questions || [],
                        },
                      });
                    }}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='edit-intro-text'>Introduction Text</Label>
                  <Textarea
                    id='edit-intro-text'
                    value={editingCase.introduction?.text}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                      if (!editingCase?.introduction) return;
                      updateEditingCase({
                        introduction: {
                          ...editingCase.introduction,
                          text: e.target.value,
                          title: editingCase.introduction.title || '',
                          problems: editingCase.introduction.problems || [],
                          questions: editingCase.introduction.questions || [],
                        },
                      });
                    }}
                    rows={4}
                  />
                </div>

                <div className='space-y-2'>
                  <Label>Challenges</Label>
                  {editingCase.introduction?.problems.map((problem, index) => (
                    <div key={`problem-${index}`} className='mt-2'>
                      <Label htmlFor={`edit-problem-${index}`}>
                        Challenge {index + 1}
                      </Label>
                      <Textarea
                        id={`edit-problem-${index}`}
                        value={problem}
                        onChange={(
                          e: React.ChangeEvent<HTMLTextAreaElement>
                        ) => {
                          if (!editingCase?.introduction?.problems) return;
                          const newProblems = [
                            ...editingCase.introduction.problems,
                          ];
                          newProblems[index] = e.target.value;
                          updateEditingCase({
                            introduction: {
                              ...editingCase.introduction,
                              problems: newProblems,
                              title: editingCase.introduction.title || '',
                              text: editingCase.introduction.text || '',
                              questions:
                                editingCase.introduction.questions || [],
                            },
                          });
                        }}
                        rows={2}
                      />
                      <div className='space-y-2 mt-2'>
                        <Label htmlFor={`edit-challenge-${index}-icon`}>
                          Icon
                        </Label>
                        <div className='flex flex-col space-y-2'>
                          {editingCase.challengeIcons?.find(
                            (icon) => icon.index === index
                          )?.url && (
                            <div className='flex items-center mt-2'>
                              <div className='w-16 h-16 bg-muted rounded-md overflow-hidden'>
                                <CloudinaryImage
                                  src={
                                    editingCase.challengeIcons.find(
                                      (icon) => icon.index === index
                                    )?.url || ''
                                  }
                                  alt={`Challenge ${index + 1} Icon`}
                                  width={64}
                                  height={64}
                                  className='object-contain w-full h-full'
                                  fallbackSrc='/placeholder-icon.png'
                                />
                              </div>
                            </div>
                          )}
                          <div className='flex space-x-2'>
                            <Input
                              id={`edit-challenge-${index}-icon`}
                              value={
                                editingCase.challengeIcons?.find(
                                  (icon) => icon.index === index
                                )?.url || ''
                              }
                              onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                              ) => {
                                const currentIcons =
                                  editingCase.challengeIcons || [];
                                const newIcons = [...currentIcons];
                                const existingIconIndex = newIcons.findIndex(
                                  (icon) => icon.index === index
                                );

                                if (existingIconIndex >= 0) {
                                  newIcons[existingIconIndex] = {
                                    ...newIcons[existingIconIndex],
                                    url: e.target.value,
                                  };
                                } else {
                                  newIcons.push({ index, url: e.target.value });
                                }

                                updateEditingCase({
                                  challengeIcons: newIcons,
                                });
                              }}
                              placeholder='https://example.com/icon.png'
                              className='flex-1'
                            />
                            <CloudinaryUploader
                              onSuccess={(url) =>
                                handleChallengeIconUploadSuccess(url, index)
                              }
                              onError={(error) =>
                                handleImageUploadError(error, 'challenge')
                              }
                              folder={`case-studies/challenges/${index}`}
                              resourceType='image'
                              buttonText='Upload'
                              variant='outline'
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className='space-y-2'>
                  <Label>Questions</Label>
                  {editingCase.introduction?.questions.map(
                    (question, index) => (
                      <div key={`question-${index}`} className='mt-2'>
                        <Label htmlFor={`edit-question-${index}`}>
                          Question {index + 1}
                        </Label>
                        <Textarea
                          id={`edit-question-${index}`}
                          value={question}
                          onChange={(
                            e: React.ChangeEvent<HTMLTextAreaElement>
                          ) => {
                            if (!editingCase?.introduction?.questions) return;
                            const newQuestions = [
                              ...editingCase.introduction.questions,
                            ];
                            newQuestions[index] = e.target.value;
                            updateEditingCase({
                              introduction: {
                                ...editingCase.introduction,
                                questions: newQuestions,
                                title: editingCase.introduction.title || '',
                                text: editingCase.introduction.text || '',
                                problems:
                                  editingCase.introduction.problems || [],
                              },
                            });
                          }}
                          rows={2}
                        />
                        <div className='space-y-2 mt-2'>
                          <Label htmlFor={`edit-question-${index}-icon`}>
                            Icon
                          </Label>
                          <div className='flex flex-col space-y-2'>
                            {editingCase.questionIcons?.find(
                              (icon) => icon.index === index
                            )?.url && (
                              <div className='flex items-center mt-2'>
                                <div className='w-16 h-16 bg-muted rounded-md overflow-hidden'>
                                  <CloudinaryImage
                                    src={
                                      editingCase.questionIcons.find(
                                        (icon) => icon.index === index
                                      )?.url || ''
                                    }
                                    alt={`Question ${index + 1} Icon`}
                                    width={64}
                                    height={64}
                                    className='object-contain w-full h-full'
                                    fallbackSrc='/placeholder-icon.png'
                                  />
                                </div>
                              </div>
                            )}
                            <div className='flex space-x-2'>
                              <Input
                                id={`edit-question-${index}-icon`}
                                value={
                                  editingCase.questionIcons?.find(
                                    (icon) => icon.index === index
                                  )?.url || ''
                                }
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) => {
                                  const currentIcons =
                                    editingCase.questionIcons || [];
                                  const newIcons = [...currentIcons];
                                  const existingIconIndex = newIcons.findIndex(
                                    (icon) => icon.index === index
                                  );

                                  if (existingIconIndex >= 0) {
                                    newIcons[existingIconIndex] = {
                                      ...newIcons[existingIconIndex],
                                      url: e.target.value,
                                    };
                                  } else {
                                    newIcons.push({
                                      index,
                                      url: e.target.value,
                                    });
                                  }

                                  updateEditingCase({
                                    questionIcons: newIcons,
                                  });
                                }}
                                placeholder='https://example.com/icon.png'
                                className='flex-1'
                              />
                              <CloudinaryUploader
                                onSuccess={(url) =>
                                  handleQuestionIconUploadSuccess(url, index)
                                }
                                onError={(error) =>
                                  handleImageUploadError(error, 'question')
                                }
                                folder={`case-studies/questions/${index}`}
                                resourceType='image'
                                buttonText='Upload'
                                variant='outline'
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>

              {/* Process Section */}
              <div className='space-y-4 border-b pb-6'>
                <h3 className='text-lg font-semibold'>Process</h3>
                <div className='space-y-2'>
                  <Label htmlFor='edit-process-title'>Process Title</Label>
                  <Input
                    id='edit-process-title'
                    value={editingCase.process?.title}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      if (!editingCase?.process) return;
                      updateEditingCase({
                        process: {
                          ...editingCase.process,
                          title: e.target.value,
                          steps: editingCase.process.steps || [],
                        },
                      });
                    }}
                  />
                </div>

                {editingCase.process?.steps.map((step, index) => (
                  <div
                    key={`step-${index}`}
                    className='p-4 border rounded-md space-y-3'
                  >
                    <h4 className='font-medium'>Step {index + 1}</h4>
                    <div className='space-y-2'>
                      <Label htmlFor={`edit-step-${index}-title`}>Title</Label>
                      <Input
                        id={`edit-step-${index}-title`}
                        value={step.title}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          if (!editingCase?.process?.steps) return;
                          const newSteps = [...editingCase.process.steps];
                          newSteps[index] = {
                            ...newSteps[index],
                            title: e.target.value,
                          };
                          updateEditingCase({
                            process: {
                              ...editingCase.process,
                              steps: newSteps,
                              title: editingCase.process.title || '',
                            },
                          });
                        }}
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor={`edit-step-${index}-desc`}>
                        Description
                      </Label>
                      <Textarea
                        id={`edit-step-${index}-desc`}
                        value={step.description}
                        onChange={(
                          e: React.ChangeEvent<HTMLTextAreaElement>
                        ) => {
                          if (!editingCase?.process?.steps) return;
                          const newSteps = [...editingCase.process.steps];
                          newSteps[index] = {
                            ...newSteps[index],
                            description: e.target.value,
                          };
                          updateEditingCase({
                            process: {
                              ...editingCase.process,
                              steps: newSteps,
                              title: editingCase.process.title || '',
                            },
                          });
                        }}
                        rows={3}
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor={`edit-step-${index}-icon`}>Icon</Label>
                      <div className='flex flex-col space-y-2'>
                        {step.icon && (
                          <div className='flex items-center mt-2'>
                            <div className='w-16 h-16 bg-muted rounded-md overflow-hidden'>
                              <CloudinaryImage
                                src={step.icon}
                                alt={`Step ${index + 1} Icon`}
                                width={64}
                                height={64}
                                className='object-contain w-full h-full'
                                fallbackSrc='/placeholder-icon.png'
                              />
                            </div>
                          </div>
                        )}
                        <div className='flex space-x-2'>
                          <Input
                            id={`edit-step-${index}-icon`}
                            value={step.icon}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              if (!editingCase?.process?.steps) return;
                              const newSteps = [...editingCase.process.steps];
                              newSteps[index] = {
                                ...newSteps[index],
                                icon: e.target.value,
                              };
                              updateEditingCase({
                                process: {
                                  ...editingCase.process,
                                  steps: newSteps,
                                  title: editingCase.process.title || '',
                                },
                              });
                            }}
                            placeholder='https://example.com/icon.png'
                            className='flex-1'
                          />
                          <CloudinaryUploader
                            onSuccess={(url) =>
                              handleProcessIconUploadSuccess(url, index)
                            }
                            onError={(error) =>
                              handleImageUploadError(error, 'process')
                            }
                            folder={`case-studies/process/${index}`}
                            resourceType='image'
                            buttonText='Upload'
                            variant='outline'
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Solution Section */}
              <div className='space-y-4 border-b pb-6'>
                <h3 className='text-lg font-semibold'>Solution</h3>
                <div className='space-y-2'>
                  <Label htmlFor='edit-solution-title'>Solution Title</Label>
                  <Input
                    id='edit-solution-title'
                    value={editingCase.solution?.title}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      if (!editingCase?.solution) return;
                      updateEditingCase({
                        solution: {
                          ...editingCase.solution,
                          title: e.target.value,
                          description: editingCase.solution.description || '',
                          items: editingCase.solution.items || [],
                        },
                      });
                    }}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='edit-solution-desc'>
                    Solution Description
                  </Label>
                  <Textarea
                    id='edit-solution-desc'
                    value={editingCase.solution?.description}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                      if (!editingCase?.solution) return;
                      updateEditingCase({
                        solution: {
                          ...editingCase.solution,
                          description: e.target.value,
                          title: editingCase.solution.title || '',
                          items: editingCase.solution.items || [],
                        },
                      });
                    }}
                    rows={3}
                  />
                </div>

                {editingCase.solution?.items.map((item, index) => (
                  <div
                    key={`solution-${index}`}
                    className='p-4 border rounded-md space-y-3'
                  >
                    <h4 className='font-medium'>Solution Item {index + 1}</h4>
                    <div className='space-y-2'>
                      <Label htmlFor={`edit-solution-${index}-title`}>
                        Title
                      </Label>
                      <Input
                        id={`edit-solution-${index}-title`}
                        value={item.title}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          if (!editingCase?.solution?.items) return;
                          const newItems = [...editingCase.solution.items];
                          newItems[index] = {
                            ...newItems[index],
                            title: e.target.value,
                          };
                          updateEditingCase({
                            solution: {
                              ...editingCase.solution,
                              items: newItems,
                              title: editingCase.solution.title || '',
                              description:
                                editingCase.solution.description || '',
                            },
                          });
                        }}
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor={`edit-solution-${index}-desc`}>
                        Description
                      </Label>
                      <Textarea
                        id={`edit-solution-${index}-desc`}
                        value={item.description}
                        onChange={(
                          e: React.ChangeEvent<HTMLTextAreaElement>
                        ) => {
                          if (!editingCase?.solution?.items) return;
                          const newItems = [...editingCase.solution.items];
                          newItems[index] = {
                            ...newItems[index],
                            description: e.target.value,
                          };
                          updateEditingCase({
                            solution: {
                              ...editingCase.solution,
                              items: newItems,
                              title: editingCase.solution.title || '',
                              description:
                                editingCase.solution.description || '',
                            },
                          });
                        }}
                        rows={3}
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor={`edit-solution-${index}-icon`}>
                        Icon
                      </Label>
                      <div className='flex flex-col space-y-2'>
                        {item.icon && (
                          <div className='flex items-center mt-2'>
                            <div className='w-16 h-16 bg-gray-100 rounded-md overflow-hidden'>
                              <CloudinaryImage
                                src={item.icon}
                                alt={`Solution ${index + 1} Icon`}
                                width={64}
                                height={64}
                                className='object-contain w-full h-full'
                                fallbackSrc='/placeholder-icon.png'
                              />
                            </div>
                          </div>
                        )}
                        <div className='flex space-x-2'>
                          <Input
                            id={`edit-solution-${index}-icon`}
                            value={item.icon}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              if (!editingCase?.solution?.items) return;
                              const newItems = [...editingCase.solution.items];
                              newItems[index] = {
                                ...newItems[index],
                                icon: e.target.value,
                              };
                              updateEditingCase({
                                solution: {
                                  ...editingCase.solution,
                                  items: newItems,
                                  title: editingCase.solution.title || '',
                                  description:
                                    editingCase.solution.description || '',
                                },
                              });
                            }}
                            placeholder='https://example.com/icon.png'
                            className='flex-1'
                          />
                          <CloudinaryUploader
                            onSuccess={(url) =>
                              handleSolutionIconUploadSuccess(url, index)
                            }
                            onError={(error) =>
                              handleImageUploadError(error, 'solution')
                            }
                            folder={`case-studies/solution/${index}`}
                            resourceType='image'
                            buttonText='Upload'
                            variant='outline'
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Impact Section */}
              <div className='space-y-4 border-b pb-6'>
                <h3 className='text-lg font-semibold'>Impact</h3>
                <div className='space-y-2'>
                  <Label htmlFor='edit-impact-title'>Impact Title</Label>
                  <Input
                    id='edit-impact-title'
                    value={editingCase.impact?.title}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      if (!editingCase?.impact) return;
                      updateEditingCase({
                        impact: {
                          ...editingCase.impact,
                          title: e.target.value,
                          metrics: editingCase.impact.metrics || [],
                        },
                      });
                    }}
                  />
                </div>

                {editingCase.impact?.metrics.map((metric, index) => (
                  <div
                    key={`impact-${index}`}
                    className='p-4 border rounded-md space-y-3'
                  >
                    <h4 className='font-medium'>Impact Metric {index + 1}</h4>
                    <div className='space-y-2'>
                      <Label htmlFor={`edit-impact-${index}-desc`}>
                        Qualitative Description
                      </Label>
                      <Textarea
                        id={`edit-impact-${index}-desc`}
                        value={metric.description}
                        onChange={(
                          e: React.ChangeEvent<HTMLTextAreaElement>
                        ) => {
                          if (!editingCase?.impact?.metrics) return;
                          const newMetrics = [...editingCase.impact.metrics];
                          newMetrics[index] = {
                            ...newMetrics[index],
                            description: e.target.value,
                          };
                          updateEditingCase({
                            impact: {
                              ...editingCase.impact,
                              metrics: newMetrics,
                              title: editingCase.impact.title || '',
                            },
                          });
                        }}
                        rows={2}
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor={`edit-impact-${index}-value`}>
                        Quantitative Value
                      </Label>
                      <Input
                        id={`edit-impact-${index}-value`}
                        value={metric.value}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          if (!editingCase?.impact?.metrics) return;
                          const newMetrics = [...editingCase.impact.metrics];
                          newMetrics[index] = {
                            ...newMetrics[index],
                            value: e.target.value,
                          };
                          updateEditingCase({
                            impact: {
                              ...editingCase.impact,
                              metrics: newMetrics,
                              title: editingCase.impact.title || '',
                            },
                          });
                        }}
                        placeholder='e.g., 30% reduction, $500K savings'
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor={`edit-impact-${index}-icon`}>Icon</Label>
                      <div className='flex flex-col space-y-2'>
                        {metric.icon && (
                          <div className='flex items-center mt-2'>
                            <div className='w-16 h-16 bg-gray-100 rounded-md overflow-hidden'>
                              <CloudinaryImage
                                src={metric.icon}
                                alt={`Impact ${index + 1} Icon`}
                                width={64}
                                height={64}
                                className='object-contain w-full h-full'
                                fallbackSrc='/placeholder-icon.png'
                              />
                            </div>
                          </div>
                        )}
                        <div className='flex space-x-2'>
                          <Input
                            id={`edit-impact-${index}-icon`}
                            value={metric.icon}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              if (!editingCase?.impact?.metrics) return;
                              const newMetrics = [
                                ...editingCase.impact.metrics,
                              ];
                              newMetrics[index] = {
                                ...newMetrics[index],
                                icon: e.target.value,
                              };
                              updateEditingCase({
                                impact: {
                                  ...editingCase.impact,
                                  metrics: newMetrics,
                                  title: editingCase.impact.title || '',
                                },
                              });
                            }}
                            placeholder='https://example.com/icon.png'
                            className='flex-1'
                          />
                          <CloudinaryUploader
                            onSuccess={(url) =>
                              handleImpactIconUploadSuccess(url, index)
                            }
                            onError={(error) =>
                              handleImageUploadError(error, 'impact')
                            }
                            folder={`case-studies/impact/${index}`}
                            resourceType='image'
                            buttonText='Upload'
                            variant='outline'
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Conclusion Section */}
              <div className='space-y-4 border-b pb-6'>
                <h3 className='text-lg font-semibold'>Conclusion</h3>
                <div className='space-y-2'>
                  <Label htmlFor='edit-conclusion-title'>
                    Conclusion Title
                  </Label>
                  <Input
                    id='edit-conclusion-title'
                    value={editingCase.conclusion?.title}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      if (!editingCase?.conclusion) return;
                      updateEditingCase({
                        conclusion: {
                          ...editingCase.conclusion,
                          title: e.target.value,
                          text: editingCase.conclusion.text || '',
                          resultTitle: editingCase.conclusion.resultTitle || '',
                          resultDescription:
                            editingCase.conclusion.resultDescription || '',
                        },
                      });
                    }}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='edit-conclusion-text'>Conclusion Text</Label>
                  <Textarea
                    id='edit-conclusion-text'
                    value={editingCase.conclusion?.text}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                      if (!editingCase?.conclusion) return;
                      updateEditingCase({
                        conclusion: {
                          ...editingCase.conclusion,
                          text: e.target.value,
                          title: editingCase.conclusion.title || '',
                          resultTitle: editingCase.conclusion.resultTitle || '',
                          resultDescription:
                            editingCase.conclusion.resultDescription || '',
                        },
                      });
                    }}
                    rows={4}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='edit-conclusion-result-title'>
                    Result Title
                  </Label>
                  <Input
                    id='edit-conclusion-result-title'
                    value={editingCase.conclusion?.resultTitle}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      if (!editingCase?.conclusion) return;
                      updateEditingCase({
                        conclusion: {
                          ...editingCase.conclusion,
                          resultTitle: e.target.value,
                          title: editingCase.conclusion.title || '',
                          text: editingCase.conclusion.text || '',
                          resultDescription:
                            editingCase.conclusion.resultDescription || '',
                        },
                      });
                    }}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='edit-conclusion-result-desc'>
                    Result Description
                  </Label>
                  <Textarea
                    id='edit-conclusion-result-desc'
                    value={editingCase.conclusion?.resultDescription}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                      if (!editingCase?.conclusion) return;
                      updateEditingCase({
                        conclusion: {
                          ...editingCase.conclusion,
                          resultDescription: e.target.value,
                          title: editingCase.conclusion.title || '',
                          text: editingCase.conclusion.text || '',
                          resultTitle: editingCase.conclusion.resultTitle || '',
                        },
                      });
                    }}
                    rows={3}
                  />
                </div>
              </div>

              <DialogFooter>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setIsDialogOpen(false)}
                  className='mr-2'
                >
                  Cancel
                </Button>
                <Button type='submit' disabled={isUpdating}>
                  {isUpdating ? (
                    <>
                      <span className='mr-2 h-4 w-4 animate-spin'>⟳</span>
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
