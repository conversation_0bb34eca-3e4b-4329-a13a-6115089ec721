'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image, { ImageProps } from 'next/image';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends Omit<ImageProps, 'onLoadingComplete'> {
  fallbackSrc?: string;
  showPlaceholder?: boolean;
  placeholderClassName?: string;
  timeout?: number; // Timeout in milliseconds
}

/**
 * A wrapper around Next.js Image component with optimizations:
 * - Proper loading placeholders
 * - Error handling with fallback
 * - Optimized quality settings
 * - Performance monitoring
 */
const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className,
  fallbackSrc = '/placeholder.svg',
  showPlaceholder = true,
  placeholderClassName,
  priority = false,
  quality = 80, // Default to slightly higher quality
  timeout = 10000, // Default timeout of 10 seconds
  ...props
}: OptimizedImageProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Set up timeout for image loading
  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Reset states when src changes
    setIsLoading(true);
    setError(false);

    // Set a new timeout
    timeoutRef.current = setTimeout(() => {
      if (isLoading) {
        console.warn(`Image load timeout for: ${src}`);
        setError(true);
        setIsLoading(false);
      }
    }, timeout);

    // Cleanup on unmount or when src changes
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [src, timeout]);

  // Handle successful image load
  const handleLoad = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsLoading(false);
  };

  // Handle image load error
  const handleError = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    console.error(`Image failed to load: ${src}`);
    setError(true);
    setIsLoading(false);
  };

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {/* Show loading placeholder */}
      {isLoading && showPlaceholder && (
        <div
          className={cn(
            "absolute inset-0 bg-gray-100 animate-pulse",
            placeholderClassName
          )}
          style={{ width, height }}
        />
      )}

      {/* Image component */}
      <Image
        src={error ? fallbackSrc : src}
        alt={alt || ""}
        width={width}
        height={height}
        onLoadingComplete={handleLoad}
        onError={handleError}
        className={cn(
          isLoading ? "opacity-0" : "opacity-100",
          "transition-opacity duration-200",
          className
        )}
        priority={priority}
        quality={quality}
        {...props}
      />
    </div>
  );
};

export { OptimizedImage };
