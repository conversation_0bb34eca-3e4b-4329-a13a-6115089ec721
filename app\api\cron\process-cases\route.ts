import { NextResponse } from 'next/server';
import { logger } from '@/lib/utils/logger';

export async function GET(req: Request) {
  const startTime = Date.now();
  try {
    // Verify the request is from a cron job
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const apiKey = authHeader.split(' ')[1];
    if (apiKey !== process.env.CRON_SECRET) {
      return new NextResponse('Invalid API key', { status: 401 });
    }

    logger.info('Starting cron job for case processing', {
      module: 'cron',
      context: { source: 'process-cases' }
    });

    // Call the batch processing API
    const apiUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/api/case-studies/process-batch`;
    logger.info(`Calling batch processing API at: ${apiUrl}`, { module: 'cron' });

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.BATCH_PROCESSING_API_KEY || ''}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`Batch processing failed: ${response.status}`, {
        module: 'cron',
        context: { status: response.status, error: errorText }
      });
      throw new Error(`Batch processing failed: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    const duration = Date.now() - startTime;

    logger.info(`Cron job completed successfully in ${duration}ms`, {
      module: 'cron',
      context: {
        duration,
        processed: result.processed,
        successful: result.successful,
        failed: result.failed
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Cron job executed successfully',
      result,
      duration: `${duration}ms`,
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    logger.error(`Error executing cron job: ${errorMessage}`, {
      module: 'cron',
      context: {
        error: errorMessage,
        duration,
        stack: error instanceof Error ? error.stack : undefined
      }
    });

    return new NextResponse(
      JSON.stringify({
        success: false,
        error: 'Internal Error',
        message: errorMessage,
        duration: `${duration}ms`,
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
