import postgres from 'postgres';
import fs from 'fs';
import path from 'path';

// Next.js automatically loads environment variables from .env files

const resetDatabase = async () => {
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not set');
  }

  const sql = postgres(process.env.POSTGRES_URL, {
    max: 1,
    idle_timeout: 60,
    connect_timeout: 60,
  });

  try {
    console.log('Starting database reset...');

    // Drop existing tables
    await sql`DROP TABLE IF EXISTS case_study_icons CASCADE`;
    await sql`DROP TABLE IF EXISTS case_studies CASCADE`;

    console.log('Dropped existing tables');

    // Create tables
    await sql`
      CREATE TABLE case_studies (
        id SERIAL PRIMARY KEY,
        use_case_title VARCHAR(255) NOT NULL,
        industry VARCHAR(100),
        role VARCHAR(100),
        vector VARCHAR(100),
        potentially_impacted_kpis TEXT,
        introduction_title VARCHAR(255),
        introduction_text TEXT,
        transition_to_challange TEXT,
        challange_1 TEXT,
        challange_2 TEXT,
        challange_3 TEXT,
        transition_to_questions TEXT,
        question_1 TEXT,
        question_2 TEXT,
        question_3 TEXT,
        process_section_title TEXT,
        process_step_1 TEXT,
        process_step_2 TEXT,
        process_step_3 TEXT,
        solution_section_title TEXT,
        solution_1 TEXT,
        solution_2 TEXT,
        solution_3 TEXT,
        solution_4 TEXT,
        solution_5 TEXT,
        potential_impact_section_title TEXT,
        potential_impact_1_qualitative TEXT,
        potential_impact_1_quantitative TEXT,
        potential_impact_2_qualitative TEXT,
        potential_impact_2_quantitative TEXT,
        potential_impact_3_qualitative TEXT,
        potential_impact_3_quantitative TEXT,
        potential_impact_4_qualitative TEXT,
        potential_impact_4_quantitative TEXT,
        conclusion_title VARCHAR(255),
        conclusion_text TEXT,
        conclusion_result TEXT,
        feature_image_url VARCHAR(255),
        preview_image_url VARCHAR(255),
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        deleted_at TIMESTAMP
      )
    `;

    await sql`
      CREATE TABLE case_study_icons (
        id SERIAL PRIMARY KEY,
        case_study_id INTEGER NOT NULL REFERENCES case_studies(id),
        icon_type VARCHAR(50) NOT NULL,
        icon_url VARCHAR(255) NOT NULL,
        "order" INTEGER NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `;

    console.log('Created new tables');
    console.log('Database reset completed successfully');
  } catch (error) {
    console.error('Error during database reset:', error);
    throw error;
  } finally {
    await sql.end();
  }
};

console.log('Starting database reset process...');
resetDatabase()
  .then(() => {
    console.log('Database reset completed');
    process.exit(0);
  })
  .catch((err) => {
    console.error('Database reset failed:', err);
    process.exit(1);
  });