'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  MagnifyingGlassIcon,
  Cross1Icon,
  ArrowRightIcon,
  BookmarkIcon,
  BarChartIcon,
} from '@radix-ui/react-icons';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { updateCaseStudyViewsOnClick } from '@/lib/api/views';

interface CaseStudy {
  id: number;
  useCaseTitle: string;
  industry: string | null;
  role: string | null;
  featureImageUrl: string | null;
  introductionText: string | null;
  marketIntelligenceData: any;
  marketMetricsData: any;
  isBookmarked: boolean;
  previewImageUrl: string | null;
  icons: {
    iconUrl: string | null;
    order: number;
  }[];
}

interface SearchBarProps {
  placeholder?: string;
  className?: string;
  userRole: string;
  dropdownPosition?: 'top' | 'bottom';
}

export function SearchBar({
  placeholder = 'Search across the use cases library',
  className = '',
  userRole,
  dropdownPosition = 'bottom',
}: SearchBarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<CaseStudy[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const router = useRouter();

  // Handle dropdown positioning to prevent overflow
  useEffect(() => {
    const adjustDropdownPosition = () => {
      if (showResults && dropdownRef.current) {
        const dropdown = dropdownRef.current;
        const viewportHeight = window.innerHeight;
        const dropdownRect = dropdown.getBoundingClientRect();

        const searchRect = searchRef.current?.getBoundingClientRect();
        const availableSpaceAbove = searchRect ? searchRect.top : 0;
        const availableSpaceBelow = searchRect
          ? viewportHeight - searchRect.bottom
          : 0;

        // For top position
        if (dropdownPosition === 'top') {
          // If not enough space above and more space below, switch to bottom
          if (
            availableSpaceAbove < 200 &&
            availableSpaceBelow > availableSpaceAbove
          ) {
            dropdown.classList.remove('bottom-full', 'mb-2');
            dropdown.classList.add('top-full', 'mt-2');
          } else if (dropdownRect.top < 0) {
            // If still using top position but overflowing, adjust max height
            dropdown.style.maxHeight = `${availableSpaceAbove - 10}px`;
          }
        }

        // For bottom position
        if (dropdownPosition === 'bottom') {
          // If not enough space below and more space above, switch to top
          if (
            availableSpaceBelow < 200 &&
            availableSpaceAbove > availableSpaceBelow
          ) {
            dropdown.classList.remove('top-full', 'mt-2');
            dropdown.classList.add('bottom-full', 'mb-2');
          } else if (dropdownRect.bottom > viewportHeight) {
            // If still using bottom position but overflowing, adjust max height
            dropdown.style.maxHeight = `${availableSpaceBelow - 10}px`;
          }
        }
      }
    };

    // Adjust position initially
    adjustDropdownPosition();

    // Add resize and scroll listeners
    window.addEventListener('resize', adjustDropdownPosition);
    window.addEventListener('scroll', adjustDropdownPosition, true);

    // Cleanup
    return () => {
      window.removeEventListener('resize', adjustDropdownPosition);
      window.removeEventListener('scroll', adjustDropdownPosition, true);
    };
  }, [showResults, dropdownPosition, searchResults]);

  // Handle click outside to close search results and clean up debounce timer
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setShowResults(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      // Clean up debounce timer on unmount
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Function to perform the actual search
  const performSearch = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    setIsSearching(true);
    setShowResults(true);
    setError(null);

    try {
      // Log the search query for debugging
      console.log(`Searching for: "${query}"`);

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(
        `/api/case-studies/search-inline?q=${encodeURIComponent(
          query
        )}&limit=8&t=${timestamp}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch search results');
      }

      const data = await response.json();
      console.log(
        `Search results for "${query}": ${data.caseStudies.length} results`
      );

      setSearchResults(data.caseStudies);
      setTotalResults(data.total);
    } catch (err) {
      console.error('Error searching case studies:', err);
      setError('Failed to fetch search results');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Handle search input change with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Clear any existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // If empty query, clear results immediately
    if (!query.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    // Set a new timer for debounce (200ms for better responsiveness)
    debounceTimerRef.current = setTimeout(() => {
      performSearch(query);
    }, 200);
  };

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim().length > 0) {
      // First perform an immediate search
      performSearch(searchQuery);

      // Then navigate to the search page if needed
      const searchPath =
        userRole === 'owner'
          ? `/dashboard/search?q=${encodeURIComponent(searchQuery)}`
          : `/dashboard/user/search?q=${encodeURIComponent(searchQuery)}`;
      router.push(searchPath);
    }
  };

  // Function to search immediately (for testing)
  const searchImmediately = () => {
    if (searchQuery.trim()) {
      performSearch(searchQuery);
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setShowResults(false);
  };

  // Get case study view URL - now using the shared route for all users
  const getCaseStudyViewUrl = (id: number) => {
    return `/dashboard/case-studies/${id}`;
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <form onSubmit={handleSearchSubmit} className='relative'>
        <Input
          className='pl-10 pr-10 py-2 bg-white text-black rounded-full border-none h-12'
          placeholder={placeholder}
          value={searchQuery}
          onChange={handleSearchChange}
          onClick={() => searchQuery.trim() && setShowResults(true)}
        />
        <MagnifyingGlassIcon className='absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400' />

        {searchQuery && (
          <Button
            type='button'
            size='icon'
            variant='ghost'
            className='absolute right-12 top-1/2 -translate-y-1/2 h-8 w-8 rounded-full'
            onClick={clearSearch}
          >
            <Cross1Icon className='h-4 w-4' />
          </Button>
        )}

        <Button
          type='button'
          size='icon'
          className='absolute right-1 top-1/2 -translate-y-1/2 bg-blue-500 hover:bg-blue-600 rounded-full h-10 w-10'
          onClick={searchImmediately}
        >
          <MagnifyingGlassIcon className='h-5 w-5 text-white' />
        </Button>
      </form>

      {/* Search Results Dropdown */}
      {showResults && (
        <div
          ref={dropdownRef}
          className={` ${
            dropdownPosition === 'top' ? 'bottom-full mb-2' : 'top-full !mt-2'
          } left-0 right-0 bg-white rounded-lg shadow-xl border border-gray-200 z-50 h-[60vh] max-h-[400px] relative overflow-y-auto overscroll-contain`}
          style={{ minHeight: '200px' }}
        >
          <div className='p-4'>
            {isSearching ? (
              // Loading state
              <div className='space-y-4'>
                <div className='flex justify-between'>
                  <Skeleton className='h-6 w-40' />
                  <Skeleton className='h-6 w-20' />
                </div>
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className='flex gap-4'>
                    <Skeleton className='h-20 w-20 rounded' />
                    <div className='flex-1 space-y-2'>
                      <Skeleton className='h-4 w-3/4' />
                      <Skeleton className='h-4 w-1/2' />
                      <Skeleton className='h-4 w-full' />
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              // Error state
              <div className='text-center py-4'>
                <p className='text-red-500'>{error}</p>
              </div>
            ) : searchResults.length === 0 ? (
              // No results
              <div className='text-center py-4'>
                <p className='text-gray-500'>
                  No results found for "{searchQuery}"
                </p>
              </div>
            ) : (
              // Results
              <div className='space-y-4'>
                <div className='flex justify-between items-center'>
                  <h3 className='font-medium'>Search Results</h3>
                  <span className='text-sm text-gray-500'>
                    {totalResults} results
                  </span>
                </div>

                <div className='space-y-3'>
                  {searchResults.map((result) => {
                    const imageURL =
                      result.featureImageUrl ||
                      result.previewImageUrl ||
                      result.icons[0]?.iconUrl;

                    return (
                      <Link
                        key={result.id}
                        href={getCaseStudyViewUrl(result.id)}
                        className='flex gap-3 p-2 hover:bg-gray-50 rounded-lg transition-colors'
                        onClick={() => {
                          updateCaseStudyViewsOnClick(result.id);
                          setShowResults(false);
                        }}
                      >
                        <div className='relative h-18 w-20 flex-shrink-0 bg-gray-100 rounded overflow-hidden'>
                          {imageURL ? (
                            <Image
                              src={imageURL}
                              alt={result.useCaseTitle || 'Case study'}
                              fill
                              className='object-cover'
                            />
                          ) : (
                            <div className='flex h-full w-full items-center justify-center'>
                              <span className='text-xs text-gray-400'>
                                No image
                              </span>
                            </div>
                          )}
                        </div>

                        <div className='flex-1 min-w-0 flex flex-col gap-1 justify-between'>
                          <h4 className='font-semibold text-base line-clamp-1 text-black'>
                            {result.useCaseTitle}
                          </h4>

                          <div className='flex flex-wrap flex-row gap-1.5'>
                            {result.industry && (
                              <Badge
                                variant='outline'
                                className='text-[10px] py-0 bg-gray-200 dark:text-gray-600 dark:!border-gray-300'
                              >
                                {result.industry}
                              </Badge>
                            )}
                            {Boolean(result.marketIntelligenceData) && (
                              <Badge className='bg-blue-600 text-white text-[10px] py-0'>
                                MI
                              </Badge>
                            )}
                            {Boolean(result.role) && (
                              <Badge className='bg-gray-200 text-gray-700 dark:text-gray-600 dark:!border-gray-300 text-[10px] py-0'>
                                {result.role}
                              </Badge>
                            )}
                            {result.isBookmarked && (
                              <Badge className='bg-green-600 text-white text-[10px] py-0'>
                                Saved
                              </Badge>
                            )}
                          </div>

                          <p className='text-[12px] text-gray-500 mt-1 line-clamp-1'>
                            {result.introductionText ||
                              'No description available'}
                          </p>
                        </div>
                      </Link>
                    );
                  })}
                </div>

                {totalResults > searchResults.length && (
                  <div className='text-center pt-2 border-t'>
                    <Button
                      variant='ghost'
                      size='sm'
                      className='text-blue-600 text-sm'
                      onClick={handleSearchSubmit}
                    >
                      View all {totalResults} results
                      <ArrowRightIcon className='ml-1 h-3 w-3' />
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
