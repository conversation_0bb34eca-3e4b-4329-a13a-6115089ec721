-- Add any missing fields to the caseStudies table
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "process_title" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "process_step_1_title" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "process_step_1_description" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "process_step_2_title" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "process_step_2_description" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "process_step_3_title" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "process_step_3_description" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "solution_title" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "solution_description" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "solution_1_title" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "solution_1_description" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "solution_2_title" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "solution_2_description" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "solution_3_title" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "solution_3_description" text;
ALTER TABLE IF EXISTS "caseStudies" ADD COLUMN IF NOT EXISTS "impact_title" text;

-- Rename columns if they exist
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'caseStudies' AND column_name = 'process_section_title') THEN
        ALTER TABLE "caseStudies" RENAME COLUMN "process_section_title" TO "process_title";
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'caseStudies' AND column_name = 'process_step_1') THEN
        ALTER TABLE "caseStudies" RENAME COLUMN "process_step_1" TO "process_step_1_description";
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'caseStudies' AND column_name = 'process_step_2') THEN
        ALTER TABLE "caseStudies" RENAME COLUMN "process_step_2" TO "process_step_2_description";
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'caseStudies' AND column_name = 'process_step_3') THEN
        ALTER TABLE "caseStudies" RENAME COLUMN "process_step_3" TO "process_step_3_description";
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'caseStudies' AND column_name = 'solution_section_title') THEN
        ALTER TABLE "caseStudies" RENAME COLUMN "solution_section_title" TO "solution_title";
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'caseStudies' AND column_name = 'solution_section_text') THEN
        ALTER TABLE "caseStudies" RENAME COLUMN "solution_section_text" TO "solution_description";
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'caseStudies' AND column_name = 'potential_impact_section_title') THEN
        ALTER TABLE "caseStudies" RENAME COLUMN "potential_impact_section_title" TO "impact_title";
    END IF;
END $$;
