'use server';

import { z } from 'zod';
import { and, eq, sql, desc } from 'drizzle-orm';
import { db } from '@/lib/db/drizzle';
import {
  users,
  teams,
  teamMembers,
  activityLogs,
  type NewUser,
  type NewTeam,
  type NewTeamMember,
  type NewActivityLog,
  ActivityType,
  invitations,
  otpVerifications,
} from '@/lib/db/schema';
import { comparePasswords, hashPassword, setSession } from '@/lib/auth/session';
import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';
import { createCheckoutSession } from '@/lib/payments/stripe';
import { getUser } from '@/lib/db/server-queries';
import { getUserWithTeam } from '@/lib/db/queries';
import {
  validatedAction,
  validatedActionWithUser,
} from '@/lib/auth/middleware';

async function logActivity(
  teamId: number | null | undefined,
  userId: number,
  type: ActivityType,
  ipAddress?: string
) {
  if (teamId === null || teamId === undefined) {
    return;
  }
  const newActivity: NewActivityLog = {
    teamId,
    userId,
    action: type,
    ipAddress: ipAddress || '',
  };
  await db.insert(activityLogs).values(newActivity);
}

const signInSchema = z.object({
  email: z.string().email().min(3).max(255),
  password: z.string().min(8).max(100),
});

export const signIn = validatedAction(signInSchema, async (data, formData) => {
  const { email, password } = data;

  console.log(`Sign-in attempt for email: ${email}`);

  const userWithTeam = await db
    .select({
      user: users,
      team: teams,
    })
    .from(users)
    .leftJoin(teamMembers, eq(users.id, teamMembers.userId))
    .leftJoin(teams, eq(teamMembers.teamId, teams.id))
    .where(eq(users.email, email))
    .limit(1);

  console.log('Sign-in - Query result:', JSON.stringify(userWithTeam, null, 2));

  if (userWithTeam.length === 0) {
    return {
      error: 'Invalid email or password. Please try again.',
      email,
      password,
    };
  }

  const { user: foundUser, team: foundTeam } = userWithTeam[0];

  const isPasswordValid = await comparePasswords(
    password,
    foundUser.passwordHash
  );

  if (!isPasswordValid) {
    return {
      error: 'Invalid email or password. Please try again.',
      email,
      password,
    };
  }

  console.log('Sign-in - User found:', JSON.stringify(foundUser, null, 2));
  console.log(`Sign-in - User role: ${foundUser.role}`);

  await Promise.all([
    setSession(foundUser),
    logActivity(foundTeam?.id, foundUser.id, ActivityType.SIGN_IN),
  ]);

  console.log('Sign-in - Session set successfully');

  const redirectTo = formData.get('redirect') as string | null;
  if (redirectTo === 'checkout') {
    const priceId = formData.get('priceId') as string;
    return createCheckoutSession({ team: foundTeam, priceId });
  }

  // Redirect to role-specific dashboard
  redirect(
    foundUser.role === 'owner' ? '/dashboard/owner' : '/dashboard/member'
  );
});

const signUpSchema = z.object({
  fullName: z.string().min(2, 'Full name is required').max(100),
  email: z.string().email('Invalid email address').min(3).max(255),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(100),
  industry: z.string().min(1, 'Industry is required').max(50),
  companyName: z.string().min(1, 'Company name is required').max(50),
  agreeToTerms: z.enum(['true', 'false']).transform((val) => val === 'true'),
  redirect: z.string().optional(),
  priceId: z.string().optional(),
  inviteId: z.string().optional(),
});

export const signUp = validatedAction(signUpSchema, async (data, formData) => {
  const {
    fullName,
    email,
    password,
    industry,
    companyName,
    agreeToTerms,
    inviteId,
  } = data;

  if (!agreeToTerms) {
    return {
      error: 'You must agree to the terms of service',
      email,
      password,
    };
  }

  const existingUser = await db
    .select()
    .from(users)
    .where(eq(users.email, email))
    .limit(1);

  if (existingUser.length > 0 && existingUser[0].isVerified) {
    return {
      error: 'An account with this email already exists.',
      email,
      password,
    };
  }

  // If user exists but is not verified, we'll update their information and mark as verified
  if (existingUser.length > 0 && !existingUser[0].isVerified) {
    const passwordHash = await hashPassword(password);

    const [updatedUser] = await db
      .update(users)
      .set({
        name: fullName,
        passwordHash,
        industry,
        companyName,
        agreedToTerms: new Date(),
        isVerified: false, // TEMPORARILY SET TO TRUE TO BYPASS OTP VERIFICATION
      })
      .where(eq(users.id, existingUser[0].id))
      .returning();

    // COMMENTED OUT: Send OTP for verification

    try {
      await fetch(
        `${process.env.BASE_URL || 'http://localhost:3000'}/api/auth/otp`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            purpose: 'signup',
          }),
        }
      );
      return {
        success: true,
        message: 'OTP sent successfully. Please check your email.',
        verificationNeeded: true,
        email,
        formData: updatedUser,
      };
    } catch (error) {
      console.error('Error sending OTP:', error);
    }

    // Skip verification and proceed directly to sign-in
    // await setSession(updatedUser);
    // return {
    //   success:
    //     updatedUser.role === 'owner' ? '/dashboard/owner' : '/dashboard/member',
    // };
  }

  const passwordHash = await hashPassword(password);

  const newUser: NewUser = {
    name: fullName,
    email,
    passwordHash,
    industry,
    companyName,
    agreedToTerms: new Date(),
    isVerified: false, // TEMPORARILY SET TO TRUE TO BYPASS OTP VERIFICATION
  };

  const [user] = await db.insert(users).values(newUser).returning();

  // COMMENTED OUT: Send OTP for verification
  try {
    await fetch(
      `${process.env.BASE_URL || 'http://localhost:3000'}/api/auth/otp`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          purpose: 'signup',
        }),
      }
    );
    return {
      success: true,
      message: 'OTP sent successfully. Please check your email.',
      verificationNeeded: true,
      email,
      formData: user,
    };
  } catch (error) {
    console.error('Error sending OTP:', error);
    throw new Error('Failed to send OTP');
  }

  // Skip verification and directly signin user
  // await setSession(user);
  // return {
  //   success: user.role === 'owner' ? '/dashboard/owner' : '/dashboard/member',
  // };
});

// Schema for completing signup after OTP verification
const completeSignupSchema = z.object({
  fullName: z.string().min(1),
  email: z.string().email(),
  industry: z.string().optional(),
  companyName: z.string().optional(),
  inviteId: z.string().optional(),
  redirect: z.string().optional(),
  priceId: z.string().optional(),
});

// This function is called after OTP verification to complete the signup process
export const completeSignup = validatedAction(
  completeSignupSchema,
  async (prevState, formData) => {
    // Extract data from the form
    const fullName = formData.get('fullName') as string;
    const email = formData.get('email') as string;
    const industry = formData.get('industry') as string;
    const companyName = formData.get('companyName') as string;
    const inviteId = formData.get('inviteId') as string;

    // Get the user that should now be verified
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (existingUser.length === 0) {
      throw new Error('User not found');
    }

    const user = existingUser[0];

    // Check if there's an invitation
    let invitationTeamId: number | null = null;

    if (inviteId) {
      try {
        const invitationId = parseInt(inviteId);
        if (!isNaN(invitationId)) {
          const invitation = await db.query.invitations.findFirst({
            where: and(
              eq(invitations.id, invitationId),
              eq(invitations.email, email),
              eq(invitations.status, 'pending')
            ),
          });

          if (invitation) {
            // Add the user to the invited team with the specified role
            await db.insert(teamMembers).values({
              userId: user.id,
              teamId: invitation.teamId,
              role: invitation.role,
            });

            // If the invitation role is 'owner', update the user's role in the users table
            if (invitation.role === 'owner') {
              await db
                .update(users)
                .set({ role: 'owner' })
                .where(eq(users.id, user.id));

              console.log(`Updated new user ${user.id} global role to owner`);
            }

            // Mark the invitation as accepted
            await db
              .update(invitations)
              .set({ status: 'accepted' })
              .where(eq(invitations.id, invitationId));

            // Log the activity
            await logActivity(
              invitation.teamId,
              user.id,
              ActivityType.ACCEPT_INVITATION
            );

            invitationTeamId = invitation.teamId;
          }
        }
      } catch (error) {
        console.error('Error processing invitation during signup:', error);
      }
    }

    // If no invitation was processed, create a personal team
    if (!invitationTeamId) {
      const newTeam: NewTeam = {
        name: `${fullName}'s Team`,
      };

      const [team] = await db.insert(teams).values(newTeam).returning();

      const newTeamMember: NewTeamMember = {
        userId: user.id,
        teamId: team.id,
        role: 'member',
      };

      await Promise.all([
        db.insert(teamMembers).values(newTeamMember),
        logActivity(team.id, user.id, ActivityType.SIGN_UP),
      ]);
    }

    // Handle redirect if needed
    const redirect = formData.get('redirect') as string;
    const priceId = formData.get('priceId') as string;

    if (redirect === 'checkout' && priceId) {
      // We need to get the team for checkout
      const userTeam = await db.query.teamMembers.findFirst({
        where: eq(teamMembers.userId, user.id),
        with: { team: true },
      });

      if (userTeam?.team) {
        return createCheckoutSession({
          team: userTeam.team,
          priceId: priceId,
        });
      }
    }

    // Redirect to sign-in page instead of directly logging in
    return { success: '/sign-in' };
  }
);

// Schema for verifying and completing signup
const verifySignupSchema = z.object({
  email: z.string().email(),
  userData: z.any(),
});

// This function is used to verify a user's email and complete the signup process
export const verifyAndCompleteSignup = validatedAction(
  verifySignupSchema,
  async (prevState, formData) => {
    try {
      // Extract data from the form
      const email = formData.get('email') as string;
      const userDataStr = formData.get('userData') as string;

      if (!email) {
        return { error: 'Email is required' };
      }

      // Parse userData from the string
      let parsedUserData;
      try {
        parsedUserData = userDataStr ? JSON.parse(userDataStr) : {};
      } catch (e) {
        console.error('Error parsing userData:', e);
        return { error: 'Invalid user data format' };
      }

      // Get the user
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      if (existingUser.length === 0) {
        return { error: 'User not found' };
      }

      // Mark the user as verified
      await db
        .update(users)
        .set({ isVerified: true })
        .where(eq(users.id, existingUser[0].id));

      // Create a new FormData for completeSignup
      const completeSignupFormData = new FormData();

      // Add the required fields
      completeSignupFormData.append('email', email);
      completeSignupFormData.append(
        'fullName',
        parsedUserData.fullName || existingUser[0].name
      );

      // Add optional fields if they exist
      if (parsedUserData.industry) {
        completeSignupFormData.append('industry', parsedUserData.industry);
      }

      if (parsedUserData.companyName) {
        completeSignupFormData.append(
          'companyName',
          parsedUserData.companyName
        );
      }

      if (parsedUserData.inviteId) {
        completeSignupFormData.append('inviteId', parsedUserData.inviteId);
      }

      if (parsedUserData.redirect) {
        completeSignupFormData.append('redirect', parsedUserData.redirect);
      }

      if (parsedUserData.priceId) {
        completeSignupFormData.append('priceId', parsedUserData.priceId);
      }

      // Complete the signup process
      await completeSignup({ success: '' }, completeSignupFormData);

      return { success: true };
    } catch (error) {
      console.error('Error completing signup:', error);
      return { error: 'Failed to complete signup' };
    }
  }
);

export async function signOut() {
  try {
    // Get the current user, but handle the case where it might be null
    const user = await getUser();

    // Only log activity if we have a valid user
    if (user) {
      try {
        const userWithTeam = await getUserWithTeam(user.id);
        await logActivity(userWithTeam?.teamId, user.id, ActivityType.SIGN_OUT);
      } catch (error) {
        console.error('Error logging sign out activity:', error);
        // Continue with sign out even if logging fails
      }
    }

    // Always delete the session cookie
    (await cookies()).delete('session');
  } catch (error) {
    console.error('Error during sign out:', error);
    // Always delete the session cookie even if there was an error
    try {
      (await cookies()).delete('session');
    } catch (cookieError) {
      console.error('Error deleting session cookie:', cookieError);
    }
  }
}

const updatePasswordSchema = z
  .object({
    currentPassword: z.string().min(8).max(100),
    newPassword: z.string().min(8).max(100),
    confirmPassword: z.string().min(8).max(100),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

const updatePasswordSimpleSchema = z
  .object({
    newPassword: z.string().min(8).max(100),
    confirmPassword: z.string().min(8).max(100),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// Schema for resetting password after OTP verification
const resetPasswordSchema = z.object({
  email: z.string().email(),
  newPassword: z.string().min(8).max(100),
});

export const updatePassword = validatedActionWithUser(
  updatePasswordSchema,
  async (data, _, user) => {
    const { currentPassword, newPassword } = data;

    const isPasswordValid = await comparePasswords(
      currentPassword,
      user.passwordHash
    );

    if (!isPasswordValid) {
      return { error: 'Current password is incorrect.' };
    }

    if (currentPassword === newPassword) {
      return {
        error: 'New password must be different from the current password.',
      };
    }

    const newPasswordHash = await hashPassword(newPassword);
    const userWithTeam = await getUserWithTeam(user.id);

    await Promise.all([
      db
        .update(users)
        .set({ passwordHash: newPasswordHash })
        .where(eq(users.id, user.id)),
      logActivity(userWithTeam?.teamId, user.id, ActivityType.UPDATE_PASSWORD),
    ]);

    return { success: 'Password updated successfully.' };
  }
);

// Simplified password update without requiring current password
// Used in settings page where user is already authenticated
export const updatePasswordSimple = validatedActionWithUser(
  updatePasswordSimpleSchema,
  async (data, _, user) => {
    const { newPassword } = data;

    const newPasswordHash = await hashPassword(newPassword);
    const userWithTeam = await getUserWithTeam(user.id);

    await Promise.all([
      db
        .update(users)
        .set({ passwordHash: newPasswordHash })
        .where(eq(users.id, user.id)),
      logActivity(userWithTeam?.teamId, user.id, ActivityType.UPDATE_PASSWORD),
    ]);

    return { success: 'Password updated successfully.' };
  }
);

const deleteAccountSchema = z.object({
  password: z.string().min(8).max(100),
});

export const deleteAccount = validatedActionWithUser(
  deleteAccountSchema,
  async (data, _, user) => {
    const { password } = data;

    const isPasswordValid = await comparePasswords(password, user.passwordHash);
    if (!isPasswordValid) {
      return { error: 'Incorrect password. Account deletion failed.' };
    }

    const userWithTeam = await getUserWithTeam(user.id);

    await logActivity(
      userWithTeam?.teamId,
      user.id,
      ActivityType.DELETE_ACCOUNT
    );

    // Soft delete
    await db
      .update(users)
      .set({
        // deletedAt: sql`CURRENT_TIMESTAMP`,
        email: sql`CONCAT(email, '-', id, '-deleted')`, // Ensure email uniqueness
      })
      .where(eq(users.id, user.id));

    if (userWithTeam?.teamId) {
      await db
        .delete(teamMembers)
        .where(
          and(
            eq(teamMembers.userId, user.id),
            eq(teamMembers.teamId, userWithTeam.teamId)
          )
        );
    }

    (await cookies()).delete('session');
    redirect('/sign-in');
  }
);

const updateAccountSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  email: z.string().email('Invalid email address'),
  industry: z.string().optional(),
  companyName: z.string().optional(),
});

export const updateAccount = validatedActionWithUser(
  updateAccountSchema,
  async (data, _, user) => {
    const { name, email, industry, companyName } = data;
    const userWithTeam = await getUserWithTeam(user.id);

    await Promise.all([
      db
        .update(users)
        .set({
          name,
          email,
          industry: industry || user.industry,
          companyName: companyName || user.companyName,
        })
        .where(eq(users.id, user.id)),
      logActivity(userWithTeam?.teamId, user.id, ActivityType.UPDATE_ACCOUNT),
    ]);

    return { success: 'Account updated successfully.' };
  }
);

const removeTeamMemberSchema = z.object({
  memberId: z.number(),
});

export const removeTeamMember = validatedActionWithUser(
  removeTeamMemberSchema,
  async (data, _, user) => {
    const { memberId } = data;
    const userWithTeam = await getUserWithTeam(user.id);

    if (!userWithTeam?.teamId) {
      return { error: 'User is not part of a team' };
    }

    await db
      .delete(teamMembers)
      .where(
        and(
          eq(teamMembers.id, memberId),
          eq(teamMembers.teamId, userWithTeam.teamId)
        )
      );

    await logActivity(
      userWithTeam.teamId,
      user.id,
      ActivityType.REMOVE_TEAM_MEMBER
    );

    return { success: 'Team member removed successfully' };
  }
);

const inviteTeamMemberSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.enum(['member', 'manager', 'owner']),
});

// Reset password function (used after OTP verification)
export const resetPassword = validatedAction(
  resetPasswordSchema,
  async (data) => {
    const { email, newPassword } = data;

    // Find the user by email
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (existingUser.length === 0) {
      return { error: 'User not found' };
    }

    const user = existingUser[0];

    // Check if there's a verified OTP for password reset
    const verifiedOtp = await db
      .select()
      .from(otpVerifications)
      .where(
        and(
          eq(otpVerifications.email, email),
          eq(otpVerifications.purpose, 'password-reset'),
          eq(otpVerifications.verified, true)
        )
      )
      .orderBy(desc(otpVerifications.createdAt))
      .limit(1);

    if (verifiedOtp.length === 0) {
      return { error: 'Email verification required' };
    }

    // Hash the new password
    const newPasswordHash = await hashPassword(newPassword);

    // Update the user's password
    await db
      .update(users)
      .set({ passwordHash: newPasswordHash })
      .where(eq(users.id, user.id));

    // Get the user's team for activity logging
    const userWithTeam = await getUserWithTeam(user.id);

    // Log the password update activity
    if (userWithTeam?.teamId) {
      await logActivity(
        userWithTeam.teamId,
        user.id,
        ActivityType.UPDATE_PASSWORD
      );
    }

    return { success: 'Password reset successfully' };
  }
);

export const inviteTeamMember = validatedActionWithUser(
  inviteTeamMemberSchema,
  async (data, _, user) => {
    const { email, role } = data;
    const userWithTeam = await getUserWithTeam(user.id);

    if (!userWithTeam?.teamId) {
      return { error: 'User is not part of a team' };
    }

    const existingMember = await db
      .select()
      .from(users)
      .leftJoin(teamMembers, eq(users.id, teamMembers.userId))
      .where(
        and(eq(users.email, email), eq(teamMembers.teamId, userWithTeam.teamId))
      )
      .limit(1);

    if (existingMember.length > 0) {
      return { error: 'User is already a member of this team' };
    }

    // Check if there's an existing invitation
    const existingInvitation = await db
      .select()
      .from(invitations)
      .where(
        and(
          eq(invitations.email, email),
          eq(invitations.teamId, userWithTeam.teamId),
          eq(invitations.status, 'pending')
        )
      )
      .limit(1);

    if (existingInvitation.length > 0) {
      return { error: 'An invitation has already been sent to this email' };
    }

    // Create a new invitation
    await db.insert(invitations).values({
      teamId: userWithTeam.teamId,
      email,
      role,
      invitedBy: user.id,
      status: 'pending',
    });

    await logActivity(
      userWithTeam.teamId,
      user.id,
      ActivityType.INVITE_TEAM_MEMBER
    );

    // TODO: Send invitation email and include ?inviteId={id} to sign-up URL
    // await sendInvitationEmail(email, userWithTeam.team.name, role)

    return { success: 'Invitation sent successfully' };
  }
);
