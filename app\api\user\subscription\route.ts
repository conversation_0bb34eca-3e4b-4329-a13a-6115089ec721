import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { getPlanByName } from '@/lib/db/seeders/pricing-plans';
import { db } from '@/lib/db/drizzle';
import { userSubscriptions } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

/**
 * Simple subscription retrieval API
 * Industry best practice: Single responsibility - only retrieve subscription data
 */

export async function GET(request: NextRequest) {
  try {
    // Authentication check
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized',
          message: 'Please sign in to view subscription'
        },
        { status: 401 }
      );
    }

    // Get user's team
    const team = await getTeamForUser(user.id);
    if (!team) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'No team found',
          message: 'Unable to find your team'
        },
        { status: 400 }
      );
    }

    // Check if user has no subscription - redirect to pricing
    if (!team.planName || !team.subscriptionStatus) {
      return NextResponse.json({
        success: true,
        hasSubscription: false,
        redirectTo: '/pricing',
        message: 'No active subscription found. Please choose a plan to get started.'
      });
    }

    // Get plan details from database
    const plan = await getPlanByName(team.planName);
    if (!plan) {
      return NextResponse.json({
        success: true,
        hasSubscription: false,
        redirectTo: '/pricing',
        message: 'Plan not found. Please choose a new plan.'
      });
    }

    // Get subscription details from database
    const subscription = await db.query.userSubscriptions.findFirst({
      where: and(
        eq(userSubscriptions.teamId, team.id),
        eq(userSubscriptions.userId, user.id)
      ),
      with: {
        plan: true
      }
    });

    // Determine subscription status
    const isActive = team.subscriptionStatus === 'active' || team.subscriptionStatus === 'trialing';
    const isTrialing = team.subscriptionStatus === 'trialing';
    const isCanceled = team.subscriptionStatus === 'canceled';

    // Calculate trial information
    let trialInfo = null;
    if (isTrialing && subscription?.trialEnd) {
      const trialEndDate = new Date(subscription.trialEnd);
      const now = new Date();
      const daysRemaining = Math.ceil((trialEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      trialInfo = {
        isTrialing: true,
        trialEndDate: trialEndDate.toISOString(),
        daysRemaining: Math.max(0, daysRemaining)
      };
    }

    // Calculate billing information
    let billingInfo = null;
    if (subscription?.currentPeriodEnd) {
      const nextBillingDate = new Date(subscription.currentPeriodEnd);
      billingInfo = {
        nextBillingDate: nextBillingDate.toISOString(),
        amount: plan.priceMonthly,
        currency: 'USD'
      };
    }

    // Format plan features for display
    const planFeatures = plan.features as any;
    const formattedFeatures = planFeatures?.features || [];

    return NextResponse.json({
      success: true,
      hasSubscription: true,
      subscription: {
        planName: plan.name,
        planDisplayName: plan.displayName,
        planDescription: plan.description,
        status: team.subscriptionStatus,
        isActive,
        isTrialing,
        isCanceled,
        features: formattedFeatures,
        maxUsers: plan.maxUsers,
        caseStudyLimit: planFeatures?.caseStudies?.limit || 10,
        trialInfo,
        billingInfo,
        stripeCustomerId: team.stripeCustomerId,
        stripeSubscriptionId: team.stripeSubscriptionId
      },
      team: {
        id: team.id,
        name: team.name,
        memberCount: 1 // You can expand this to get actual member count
      }
    });

  } catch (error: any) {
    console.error('[Subscription API] Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Unable to retrieve subscription information'
      },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint to refresh subscription data from Stripe
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const team = await getTeamForUser(user.id);
    if (!team || !team.stripeSubscriptionId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'No subscription found',
          message: 'No active subscription to refresh'
        },
        { status: 400 }
      );
    }

    // Use existing sync service to refresh subscription data
    const { syncSubscriptionById } = await import('@/lib/services/subscription-sync');
    const syncResult = await syncSubscriptionById(team.stripeSubscriptionId);

    if (syncResult.success) {
      return NextResponse.json({
        success: true,
        message: 'Subscription data refreshed successfully',
        planName: syncResult.planName,
        status: syncResult.subscriptionStatus
      });
    } else {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Sync failed',
          message: syncResult.error || 'Failed to refresh subscription data'
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('[Subscription API] Refresh error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Unable to refresh subscription data'
      },
      { status: 500 }
    );
  }
}
