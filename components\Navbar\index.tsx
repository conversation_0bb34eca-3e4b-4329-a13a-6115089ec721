'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import Image from 'next/image';
import { Menu, X, User, LogOut, LayoutDashboard } from 'lucide-react';
import { useUser } from '@/lib/auth';
import { signOut } from '@/app/(login)/actions';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { ThemeToggle } from '@/components/theme-toggle';

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { user } = useUser();

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when pathname changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  const navLinks = [
    { href: '/pricing', label: 'Pricing' },
    { href: '/about', label: 'About Us' },
    { href: '/contact', label: 'Contact Us' },
  ];

  const handleSignOut = async () => {
    try {
      await signOut();
      router.refresh();
      router.push('/');
    } catch (error) {
      console.error('Error during sign out:', error);
      // Still try to redirect to home page even if there was an error
      router.push('/');
    }
  };

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 text-white transition-all duration-1000 ease-in-out ${
        isScrolled
          ? 'bg-blue-400 dark:bg-blue-600 transition-[border-radius] duration-0'
          : 'mt-2 mx-2 sm:mx-4 md:mx-8 lg:mx-16 xl:mx-32 2xl:mx-72 bg-blue-400 dark:bg-blue-600 backdrop-blur-md shadow-lg transition-[border-radius] duration-0 rounded-2xl'
      }`}
    >
      <div className='max-w-7xl mx-auto px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8'>
        <div className='flex items-center justify-between h-14 sm:h-16'>
          {/* Logo */}
          <div className='flex-shrink-0'>
            <Link href='/' className='text-xl font-bold select-none'>
              <Image
                src='/images/logos/main-logo.svg'
                alt='Turinos AI Logo'
                width={160}
                height={35}
                className='w-[110px] xs:w-[130px] sm:w-[140px] md:w-[160px] h-auto object-contain'
              />
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <div className='flex md:hidden'>
            <button
              type='button'
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className='inline-flex items-center justify-center p-2.5 rounded-md text-white hover:bg-blue-500/50 focus:outline-none transition-colors duration-200'
              aria-expanded={isMobileMenuOpen}
            >
              <span className='sr-only'>Open main menu</span>
              {isMobileMenuOpen ? (
                <X className='h-6 w-6 transition-transform duration-200 rotate-0' />
              ) : (
                <Menu className='h-6 w-6 transition-transform duration-200 rotate-0' />
              )}
            </button>
          </div>

          {/* Desktop Navigation */}
          <div className='hidden md:flex md:items-center md:space-x-2 lg:space-x-4'>
            <div className='flex items-baseline space-x-2 lg:space-x-4'>
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`px-2 lg:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-all duration-300 ${
                    pathname === link.href
                      ? 'text-white bg-blue-700'
                      : 'text-white hover:text-black'
                  }`}
                >
                  {link.label}
                </Link>
              ))}
            </div>

            {/* Theme Toggle */}
            {/* <div className="ml-2">
              <ThemeToggle className="text-white hover:text-blue-600 hover:bg-white/20" />
            </div> */}

            {/* Desktop Auth Button or User Profile */}
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger className='ml-2 md:ml-4 flex items-center gap-2 rounded-full p-1 hover:bg-blue-500 transition-colors duration-300'>
                  <Avatar className='h-7 w-7 md:h-8 md:w-8 border-2 border-white'>
                    <AvatarImage src={user.image || ''} alt={user.name || ''} />
                    <AvatarFallback className='bg-blue-700 text-white text-xs md:text-sm'>
                      {user.name?.charAt(0) || user.email?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end' className='w-48 sm:w-56'>
                  <div className='p-2 text-sm font-medium text-gray-900 border-b'>
                    <p>{user.name}</p>
                    <p className='text-xs text-gray-500'>{user.email}</p>
                  </div>
                  <DropdownMenuItem
                    onClick={() =>
                      router.push(
                        user.role === 'owner'
                          ? '/dashboard/owner'
                          : '/dashboard'
                      )
                    }
                  >
                    <LayoutDashboard className='mr-2 h-4 w-4' />
                    Dashboard
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleSignOut}
                    className='text-red-600'
                  >
                    <LogOut className='mr-2 h-4 w-4' />
                    Sign out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link
                href='/sign-up'
                className='ml-2 md:ml-4 py-2 md:py-3 lg:py-4 px-4 md:px-6 lg:px-8 rounded-xl md:rounded-2xl text-xs font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors duration-300'
              >
                Take the first Step
              </Link>
            )}
          </div>
        </div>

        {/* Mobile Menu */}
        <div
          className={`transform transition-all duration-300 ease-in-out ${
            isMobileMenuOpen
              ? 'opacity-100 translate-y-0 max-h-[500px]'
              : 'opacity-0 -translate-y-4 max-h-0'
          } md:hidden overflow-hidden`}
        >
          <div className='px-2 pt-2 pb-8 space-y-3 bg-blue-500 dark:bg-blue-700 rounded-b-2xl'>
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`block px-4 py-3 rounded-md text-base font-medium transition-all duration-300 ${
                  pathname === link.href
                    ? 'text-white bg-blue-700'
                    : 'text-white hover:bg-blue-600/30'
                }`}
              >
                {link.label}
              </Link>
            ))}
            {/* Mobile Theme Toggle */}
            <div className='px-3 py-2 flex justify-center'>
              <ThemeToggle className='text-white hover:text-blue-600 hover:bg-white/20' />
            </div>

            {/* Mobile Auth Button */}
            <div className='pt-3 pb-3 px-4'>
              {user ? (
                <>
                  <Link
                    href={
                      user.role === 'owner'
                        ? '/dashboard/owner'
                        : '/dashboard/member'
                    }
                    className='block w-full text-center py-3 px-4 mb-3 rounded-md text-white bg-blue-600 hover:bg-blue-700 font-medium transition-colors duration-300'
                  >
                    Dashboard
                  </Link>
                  <button
                    onClick={handleSignOut}
                    className='block w-full text-center py-3 px-4 rounded-md text-red-500 hover:bg-red-50 dark:hover:bg-red-950/30 font-medium transition-colors duration-300'
                  >
                    Sign Out
                  </button>
                </>
              ) : (
                <Link
                  href='/sign-up'
                  className='block w-full text-center py-3 px-6 rounded-xl text-sm font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors duration-300'
                >
                  Take the first Step
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
