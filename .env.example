POSTGRES_URL=******************************************************************************************************/postgres
STRIPE_SECRET_KEY=sk_test_51QGGNAIiRJwYTHShiNPuPEHrHf619wVHeiZtCVdoghzTjjZA7decGXQyE16L2lpUscgByb9NImKUKiNFYwR9zyev00urwzaYRT
STRIPE_WEBHOOK_SECRET=whsec_205132cb2f65fcdce4cf9189a2c83432a86501ca7ddc0a7c11ba834a06892a4c
BASE_URL=http://localhost:3000
AUTH_SECRET=879f79504eec6da25943f8839e8a1cea8efa48e4a64ea9b96d6ede7a6bfa9f46
# Wasabi configuration removed - using Cloudinary instead

# MIUAgent API Configuration
MIUAGENT_API_KEY=your_miuagent_api_key
MIUAGENT_BASE_URL=https://miuagent-api.leafcraftstudios.com/

# Cron Job Security
CRON_SECRET=your_cron_secret_key

# SMTP Configuration
SMTP_HOST=smtp.emailit.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_USERNAME=emailit
SMTP_PASS=em_x2sNVVpgcXB2PGYnBjKfHjxN23NjZLnF

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=daf8hefrg
CLOUDINARY_API_KEY=614559198916115
CLOUDINARY_API_SECRET=OV5jQ9RurrjEubiLdOTpUB0VMFo