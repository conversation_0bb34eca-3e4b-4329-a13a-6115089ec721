'use server';

// Import dynamic config
import './SimilarCaseStudies.config';
import { db } from '@/lib/db/drizzle';
import { caseStudies, bookmarks } from '@/lib/db/schema';
import { eq, and, or, not, isNull } from 'drizzle-orm';
import Link from 'next/link';
import { SafeImage } from '@/components/SafeImage';
import { Button } from '@/components/ui/button';
import { getUser } from '@/lib/db/server-queries';

interface SimilarCaseStudiesProps {
  currentCaseStudyId: number;
  industry: string;
  role: string;
}

export async function SimilarCaseStudies({ currentCaseStudyId, industry, role }: SimilarCaseStudiesProps) {
  // Get the current user
  const user = await getUser();
  const userId = user?.id;

  // Fetch similar case studies based on industry and role
  const similarCaseStudies = await db.query.caseStudies.findMany({
    where: and(
      not(eq(caseStudies.id, currentCaseStudyId)),
      // isNull(caseStudies.deletedAt),
      or(
        eq(caseStudies.industry, industry),
        eq(caseStudies.role, role)
      )
    ),
    limit: 4,
    with: {
      icons: {
        orderBy: (icons, { asc }) => [asc(icons.order)]
      }
    }
  });

  // If no similar case studies found, show a message
  if (similarCaseStudies.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No similar case studies found.</p>
      </div>
    );
  }

  // Initialize bookmarkedIds
  let bookmarkedIds = new Set<number>();

  // Fetch user's bookmarks if user is logged in
  if (userId) {
    try {
      const userBookmarks = await db.query.bookmarks.findMany({
        where: eq(bookmarks.userId, userId),
      });

      // Create a set of bookmarked case study IDs for quick lookup
      bookmarkedIds = new Set(userBookmarks.map(bookmark => bookmark.caseStudyId));
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
      // If there's an error, we'll just use an empty set
    }
  } else {
    console.log('No user ID available, skipping bookmark fetch');
  }

  // Add isBookmarked property to case studies
  // Process case studies to add bookmark info and ensure all fields have fallbacks
  const caseStudiesWithBookmarkInfo = similarCaseStudies.map(caseStudy => ({
    ...caseStudy,
    isBookmarked: bookmarkedIds.has(caseStudy.id),
    // Add fallbacks for missing fields
    featureImageUrl: caseStudy.featureImageUrl || '',
    previewImageUrl: caseStudy.previewImageUrl || '',
    headerImage: '', // Add this field for type safety
    title: '', // Add this field for type safety
    useCaseTitle: caseStudy.useCaseTitle || 'Untitled Case Study',
    industry: caseStudy.industry || 'General'
  }));

  // Get the featured case study (first one)
  const featuredCaseStudy = caseStudiesWithBookmarkInfo[0];
  // Get the remaining case studies
  const otherCaseStudies = caseStudiesWithBookmarkInfo.slice(1);

  return (
    <div className="border border-gray-100 dark:border-gray-900 rounded-lg p-6 bg-white dark:bg-black">
      <div className="mb-4">
        <h2 className="text-2xl font-bold text-black dark:text-white">Suggested Case Studies</h2>
        {/* <p className="text-sm text-black dark:text-white">Real-World AI Success Stories & Lessons Learned</p> */}
      </div>

      {/* Featured case study - full width */}
      {featuredCaseStudy && (
        <div className="mb-6 bg-white dark:bg-black rounded-lg overflow-hidden border border-gray-100 dark:border-gray-900">
          <div className="flex flex-col md:flex-row">
            <div className="relative w-full md:w-2/5 h-64 md:h-auto">
              {featuredCaseStudy.featureImageUrl || featuredCaseStudy.previewImageUrl || (featuredCaseStudy.icons && featuredCaseStudy.icons.find(icon => icon.iconType === 'icon')?.iconUrl) ? (
                <SafeImage
                  src={featuredCaseStudy.featureImageUrl || featuredCaseStudy.previewImageUrl || (featuredCaseStudy.icons && featuredCaseStudy.icons.find(icon => icon.iconType === 'icon')?.iconUrl) || featuredCaseStudy.headerImage || ''}
                  alt={featuredCaseStudy.useCaseTitle || 'Case study'}
                  fill
                  className="object-cover"
                  fallbackSrc="/placeholder-image.jpg"
                />
              ) : (
                <div className="w-full h-full bg-white dark:bg-black flex items-center justify-center border border-gray-100 dark:border-gray-900">
                  <span className="text-black dark:text-white">No Image</span>
                </div>
              )}
              <div className="absolute top-4 left-4 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-white text-xs px-2 py-1 rounded">
                FEATURED CASE STUDY
              </div>
            </div>
            <div className="p-6 md:w-3/5">
              <h3 className="text-xl font-bold mb-2 text-black dark:text-white">{featuredCaseStudy.useCaseTitle}</h3>
              <p className="text-black dark:text-white text-sm mb-4">
                {featuredCaseStudy.introductionText || 'No description available.'}
              </p>
              <div className="mb-4">
                <h4 className="font-semibold mb-2 text-black dark:text-white">Key Findings:</h4>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 text-blue-500 dark:text-blue-400 rounded-full w-2 h-2 flex-shrink-0"></div>
                    <span className="text-sm text-black dark:text-white">AI agents range from reactive (instant decision-making) to proactive (strategic planning)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 text-blue-500 dark:text-blue-400 rounded-full w-2 h-2 flex-shrink-0"></div>
                    <span className="text-sm text-black dark:text-white">Widespread adoption expected within 5 years across industries</span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 text-blue-500 dark:text-blue-400 rounded-full w-2 h-2 flex-shrink-0"></div>
                    <span className="text-sm text-black dark:text-white">Continuous learning capabilities allow for expertise development</span>
                  </li>
                </ul>
              </div>
              <Link href={`/dashboard/case-studies/${featuredCaseStudy.id}`}>
                <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                  Read Full Case Study
                </Button>
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Three smaller case studies in a row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {otherCaseStudies.map((caseStudy) => (
          <Link
            key={caseStudy.id}
            href={`/dashboard/case-studies/${caseStudy.id}`}
            className="block hover:no-underline"
          >
            <div className="bg-white dark:bg-black rounded-lg overflow-hidden border border-gray-100 dark:border-gray-900 h-full">
              <div className="relative w-full h-40">
                {caseStudy.featureImageUrl || caseStudy.previewImageUrl || (caseStudy.icons && caseStudy.icons.find(icon => icon.iconType === 'icon')?.iconUrl) ? (
                  <SafeImage
                    src={caseStudy.featureImageUrl || caseStudy.previewImageUrl || (caseStudy.icons && caseStudy.icons.find(icon => icon.iconType === 'icon')?.iconUrl) || caseStudy.headerImage || ''}
                    alt={caseStudy.useCaseTitle || 'Case study'}
                    fill
                    className="object-cover"
                    fallbackSrc="/placeholder-image.jpg"
                  />
                ) : (
                  <div className="w-full h-full bg-white dark:bg-black flex items-center justify-center border border-gray-100 dark:border-gray-900">
                    <span className="text-black dark:text-white">No Image</span>
                  </div>
                )}
              </div>
              <div className="p-4">
                <div className="mb-1">
                  <span className="text-xs text-amber-600 dark:text-amber-400 font-medium uppercase">
                    {caseStudy.industry || 'Leadership'}
                  </span>
                </div>
                <h3 className="text-sm font-semibold mb-1 line-clamp-2 text-black dark:text-white">{caseStudy.useCaseTitle}</h3>
                <p className="text-xs text-black dark:text-white line-clamp-3">
                  {caseStudy.introductionText || 'No description available.'}
                </p>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
