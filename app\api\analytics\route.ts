import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import {
  caseStudies,
  plans,
  teamMembers,
  teams,
  users,
  userSubscriptions,
  activityLogs,
} from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { sql } from 'drizzle-orm';

// Helper to generate an ascending array of random values up to maxValue
function generateLineGraphData(
  maxValue: number,
  points = 8
): { value: number }[] {
  if (maxValue <= 0) return Array(points).fill({ value: 0 });
  let arr: number[] = [];
  let prev = 0;
  for (let i = 0; i < points - 1; i++) {
    // Ensure each value is greater than the previous, but not exceeding maxValue
    const remaining = maxValue - prev - (points - i - 1);
    const next =
      prev + Math.floor(Math.random() * (remaining > 0 ? remaining : 1) + 1);
    arr.push(next);
    prev = next;
  }
  arr.push(maxValue);
  return arr.map((value) => ({ value }));
}

export async function GET(req: Request) {
  try {
    // Advanced analytics feature has been removed from all plans
    return new NextResponse(
      JSON.stringify({
        error: 'Feature Not Available',
        message: 'Advanced analytics has been removed from all subscription plans.',
        details: 'This feature is no longer available. Please contact support if you have questions.'
      }),
      {
        status: 410, // Gone - indicates the resource is no longer available
        headers: { 'Content-Type': 'application/json' }
      }
    );

    // Legacy code below - keeping for reference but not executed
    /*
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    if (session.user.role !== 'owner') {
      return new NextResponse(
        'Forbidden: Only owners can access analytics data',
        { status: 403 }
      );
    }

    // Fetch user info for name, etc.
    const userInfoResult = await db.execute(
      sql`
        SELECT id, name FROM ${users}
        WHERE id = ${session.user.id}
        LIMIT 1
      `
    );
    const sessionUser = (userInfoResult as any)[0] || { name: 'User' };

    // Get all team IDs associated with the current user
    const userTeamsResult = await db.execute(
      sql`
        SELECT "team_id" FROM ${teamMembers}
        WHERE "user_id" = ${session.user.id}
      `
    );

    const userTeamIds =
      (userTeamsResult as any).map((row: any) => row.team_id) ?? [];

    // Count all team members for these teams
    let totalTeamMembers = 0;
    if (userTeamIds.length > 0) {
      const teamMembersCountResult = await db.execute(
        sql`
          SELECT COUNT(*)::int AS count FROM ${teamMembers}
          WHERE "team_id" IN (${sql.join(
            userTeamIds.map((id: any) => sql`${id}`),
            sql`,`
          )})
        `
      );

      totalTeamMembers = (teamMembersCountResult as any)[0]?.count ?? 0;
    }

    // Fetch all counts in a single query (users, teams, caseStudies)
    const countsResult = await db.execute(
      sql`
        SELECT 'users' AS type, COUNT(*)::int AS count FROM ${users}
        UNION ALL
        SELECT 'caseStudies' AS type, COUNT(*)::int AS count FROM ${caseStudies}
      `
    );
    const countsArray = [...countsResult] as { type: string; count: number }[];

    const counts = Object.fromEntries(
      countsArray.map((row) => [row.type, row.count])
    );

    // Total revenue
    const revenueResult = await db.execute(
      sql`
        SELECT COALESCE(SUM(p."price_monthly"),0)::int AS "totalRevenue"
        FROM ${userSubscriptions} as us
        JOIN ${plans} as p ON us."plan_id" = p."id"
        WHERE us."status" = 'active'
      `
    );
    const totalRevenue =
      (revenueResult as any).rows?.[0]?.totalRevenue ??
      (revenueResult as any)[0]?.totalRevenue ??
      0;

    // Active users (with active subscriptions)
    const activeSubsResult = await db.execute(
      sql`
        SELECT COUNT(DISTINCT us."user_id")::int AS "activeUsers"
        FROM ${userSubscriptions} us
        WHERE us."status" = 'active'
      `
    );
    const activeUsers =
      (activeSubsResult as any).rows?.[0]?.activeUsers ??
      (activeSubsResult as any)[0]?.activeUsers ??
      0;

    // Total users
    const totalUsers = counts.users ?? 0;

    // Calculate success rate (as a percentage)
    const successRate =
      totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0;

    // Prepare response in required format
    const data = {
      totalCases: {
        label: 'Total Cases',
        value: counts.caseStudies ?? 0,
        change: '+10% from last month',
        data: generateLineGraphData(counts.caseStudies ?? 0),
      },
      // totalTeams: {
      //   label: 'Total Teams',
      //   value: counts.teams ?? 0,
      //   change: '+8% from last month',
      //   data: generateLineGraphData(counts.teams ?? 0),
      // },
      totalTeamMembers: {
        label: 'Total Team Members',
        value: totalTeamMembers,
        change: '+5% from last month',
        data: generateLineGraphData(totalTeamMembers),
      },
      totalUsers: {
        label: 'Total Users',
        value: totalUsers,
        change: '+12% from last month',
        data: generateLineGraphData(totalUsers),
      },
      totalRevenue: {
        label: 'Total Revenue',
        value: totalRevenue,
        change: '+15% from last month',
        data: generateLineGraphData(totalRevenue),
      },
      successRate: {
        label: 'Success Rate',
        value: successRate,
        change: '+2% from last month',
        data: generateLineGraphData(successRate),
      },
    };

    // --- Recent Activity Section ---

    // 1. Latest case added by session.user.id
    const latestCaseResult = await db.execute(
      sql`
        SELECT id, use_case_title as title, created_at
        FROM ${caseStudies}
        ORDER BY "created_at" DESC
        LIMIT 1
      `
    );
    const latestCase = (latestCaseResult as any)[0];

    // 2. Latest team member joined in user's team (if any), else latest team owner joined
    let latestTeamMember: any = null;
    if (userTeamIds.length > 0) {
      const latestMemberResult = await db.execute(
        sql`
          SELECT tm.id, tm.user_id, tm.joined_at, u.name as user_name, t.name as team_name
          FROM ${teamMembers} as tm
          JOIN ${users} u ON tm.user_id = u.id
          JOIN ${teams} t ON tm.team_id = t.id
          WHERE tm.team_id IN (${sql.join(
            userTeamIds.map((id: any) => sql`${id}`),
            sql`,`
          )})
          ORDER BY tm.joined_at DESC
          LIMIT 1
        `
      );
      latestTeamMember = (latestMemberResult as any)[0];
    } else {
      // If user has no team, show latest team the owner joined
      const latestOwnerTeamResult = await db.execute(
        sql`
          SELECT tm.id, tm.team_id, tm.joined_at, t.name as team_name
          FROM ${teamMembers} tm
          JOIN ${teams} t ON tm.team_id = t.id
          WHERE tm.user_id = ${session.user.id}
          ORDER BY tm.joined_at DESC
          LIMIT 1
        `
      );
      latestTeamMember = (latestOwnerTeamResult as any)[0];
    }

    // 3. Latest activity log for session user
    const latestActivityResult = await db.execute(
      sql`
        SELECT id, action, timestamp
        FROM ${activityLogs}
        WHERE "user_id" = ${session.user.id}
        ORDER BY "timestamp" DESC
        LIMIT 1
      `
    );
    const latestActivity = (latestActivityResult as any)[0];

    // Prepare recentActivity array
    const recentActivity = [];

    if (latestCase) {
      recentActivity.push({
        id: latestCase.id,
        date: latestCase.created_at,
        title: 'New Case Added',
        description: `"${latestCase.title}" case was added by ${
          sessionUser.name
        } on ${new Date(latestCase.created_at).toLocaleDateString()}`,
      });
    }

    if (latestTeamMember) {
      if (userTeamIds.length > 0) {
        recentActivity.push({
          id: latestTeamMember.id,
          date: latestTeamMember.joined_at,
          title: 'Team Member Joined',
          description: `"${latestTeamMember.user_name}" joined team "${
            latestTeamMember.team_name
          }" on ${new Date(latestTeamMember.joined_at).toLocaleDateString()}`,
        });
      } else {
        recentActivity.push({
          id: latestTeamMember.id,
          date: latestTeamMember.joined_at,
          title: 'Joined Team',
          description: `You joined team ${
            latestTeamMember.team_name
          } on ${new Date(latestTeamMember.joined_at).toLocaleDateString()}`,
        });
      }
    }

    if (latestActivity) {
      let activityDescription = '';
      let activityTitle = '';
      switch (latestActivity.action) {
        case 'SIGN_IN':
          activityDescription = 'You logged in.';
          activityTitle = 'Logged In';
          break;
        case 'SIGN_OUT':
          activityDescription = 'You logged out.';
          activityTitle = 'Logged Out';
          break;
        case 'CREATE_CASE':
          activityDescription = 'You created a new case.';
          activityTitle = 'Created Case';
          break;
        case 'UPDATE_PROFILE':
          activityDescription = 'You updated your profile.';
          activityTitle = 'Updated Profile';
          break;
        case 'ADD_TEAM_MEMBER':
          activityDescription = 'You added a new team member.';
          activityTitle = 'Added Team Member';
          break;
        case 'CREATE_TEAM':
          activityDescription = 'You created a new team.';
          activityTitle = 'Created Team';
          break;
        // Add more cases as needed
        default:
          activityDescription = `Action performed: ${latestActivity.action}`;
      }
      recentActivity.push({
        id: latestActivity.id,
        date: latestActivity.timestamp,
        title: activityTitle,
        description: activityDescription,
      });
    }

    return NextResponse.json({ success: true, data, recentActivity });
    */
  } catch (error) {
    console.error('Error accessing removed analytics feature:', error);
    return new NextResponse(
      JSON.stringify({
        error: 'Feature Not Available',
        message: 'Advanced analytics has been removed from all subscription plans.'
      }),
      {
        status: 410,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
