'use client';

import { use } from 'react';
import { useUser } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ArrowRight, Search, Filter } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from '@/components/ui/input';
import { MarketIntelligenceCard } from '@/components/MarketIntelligenceCard';
import { Skeleton } from '@/components/ui/skeleton';
import { useSubscription } from '@/lib/hooks/use-subscription';
import { SubscriptionGuard } from '@/components/subscription/subscription-guard';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Lock } from 'lucide-react';

interface UseCase {
  title: string;
  industry: string;
  views: number;
  description: string;
  problemStatement?: string;
  solutionOverview?: string;
  roiAchieved?: string;
  implementationSteps?: string[];
  industryInsights?: string;
}

interface CaseStudy {
  id: number;
  useCaseTitle: string;
  industry: string | null;
  role: string | null;
  featureImageUrl: string | null;
  headerImage?: string | null;
  marketIntelligenceData?: string | null;
  marketMetricsData?: string | null;
}

export default function MiuPage() {
  const router = useRouter();
  const { userPromise } = useUser();
  const user = use(userPromise);
  const { hasActiveSubscription, planFeatures, planName } = useSubscription();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [selectedCase, setSelectedCase] = useState<UseCase | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // New state for real-time case studies
  const [caseStudies, setCaseStudies] = useState<CaseStudy[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCaseStudies, setFilteredCaseStudies] = useState<CaseStudy[]>([]);

  // Fetch case studies with market intelligence data
  useEffect(() => {
    const fetchCaseStudies = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/case-studies?withMarketData=true');
        if (!response.ok) throw new Error('Failed to fetch case studies');

        const data = await response.json();
        setCaseStudies(data.caseStudies || []);
        setFilteredCaseStudies(data.caseStudies || []);
      } catch (error) {
        console.error('Error fetching case studies:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCaseStudies();
  }, []);

  // Filter case studies based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCaseStudies(caseStudies);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = caseStudies.filter(cs =>
      cs.useCaseTitle?.toLowerCase().includes(query) ||
      cs.industry?.toLowerCase().includes(query) ||
      cs.role?.toLowerCase().includes(query)
    );

    setFilteredCaseStudies(filtered);
  }, [searchQuery, caseStudies]);

  if (user?.role === 'owner') {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
        <p className="mt-2">This page is only accessible to regular users.</p>
      </div>
    );
  }

  // Check if user has access to MIU features
  const hasMiuPreview = planFeatures.miuPreview;
  const hasFullMiuAccess = planFeatures.fullMiuAccess;

  // If user doesn't have any MIU access, show upgrade prompt
  if (!hasMiuPreview && !hasFullMiuAccess) {
    return (
      <div className="p-4 space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h1 className="text-2xl font-bold">Market Intelligence</h1>
        </div>

        <Alert>
          <Lock className="h-4 w-4" />
          <AlertDescription>
            Market Intelligence Unit (MIU) access is not available in your current plan.
            <Button
              variant="link"
              className="p-0 h-auto font-semibold text-blue-600"
              onClick={() => router.push('/dashboard/billing')}
            >
              Upgrade to Pro
            </Button> to access MIU features.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Sample trending use cases data with detailed information
  const trendingUseCases = [
    {
      id: 1,
      title: "Top 3 Trending Use Cases in AI-Driven Customer Engagement",
      cases: [
        {
          title: "Automated Legal Document Review",
          industry: "Legal Tech",
          views: 1500,
          description: "AI-powered system for reviewing and analyzing legal documents, contracts, and case files.",
          problemStatement: "Low efficiency in document review process leading to increased costs.",
          solutionOverview: "Use AI to automate the initial review of legal documents and highlight key areas of concern.",
          roiAchieved: "Reduced document review time by 60%",
          implementationSteps: [
            "Train AI models on existing document database",
            "Implement document classification system",
            "Set up automated flagging for key terms",
            "Monitor accuracy metrics"
          ],
          industryInsights: "Law firms have reported significant time savings and improved accuracy in document review processes."
        },
        {
          title: "Predictive Case Outcome Analysis",
          industry: "Law Firms",
          views: 1200,
          description: "Machine learning models to predict case outcomes based on historical data and precedents.",
          problemStatement: "Low customer engagement and retention rates.",
          solutionOverview: "Use AI to personalize customer interactions and improve engagement.",
          roiAchieved: "Increased customer retention by 20%.",
          implementationSteps: [
            "Collect customer data",
            "Use AI to analyze preferences",
            "Personalize marketing campaigns",
            "Monitor customer metrics"
          ],
          industryInsights: "Healthcare and finance industries have seen the most success with this use case."
        },
        {
          title: "Intelligent Client Intake System",
          industry: "Legal Services",
          views: 1100,
          description: "Automated system for client onboarding and case classification.",
          problemStatement: "Manual client intake process leading to delays and errors.",
          solutionOverview: "Implement AI-powered client intake system to automate data collection and case classification.",
          roiAchieved: "Reduced client intake time by 50%",
          implementationSteps: [
            "Develop client intake questionnaire",
            "Implement AI-powered data analysis",
            "Set up automated case classification",
            "Monitor client satisfaction metrics"
          ],
          industryInsights: "Law firms have reported improved client satisfaction and reduced intake time with this use case."
        }
      ]
    },
    {
      id: 2,
      title: "Popular Use Cases in Corporate Law",
      cases: [
        {
          title: "Merger & Acquisition Analysis",
          industry: "Corporate",
          views: 980,
          description: "AI-driven analysis of M&A opportunities and risks.",
          problemStatement: "Manual analysis of M&A opportunities leading to missed deals and increased costs.",
          solutionOverview: "Use AI to analyze M&A opportunities and identify potential risks.",
          roiAchieved: "Increased M&A deal success rate by 30%.",
          implementationSteps: [
            "Collect M&A data",
            "Use AI to analyze market trends",
            "Identify potential risks and opportunities",
            "Monitor deal success metrics"
          ],
          industryInsights: "Corporate law firms have seen improved M&A deal success rates with this use case."
        },
        {
          title: "Compliance Monitoring System",
          industry: "Regulatory",
          views: 850,
          description: "Automated compliance checking and reporting system.",
          problemStatement: "Manual compliance checking leading to errors and fines.",
          solutionOverview: "Implement AI-powered compliance monitoring system to automate checking and reporting.",
          roiAchieved: "Reduced compliance fines by 40%",
          implementationSteps: [
            "Develop compliance checklist",
            "Implement AI-powered data analysis",
            "Set up automated reporting",
            "Monitor compliance metrics"
          ],
          industryInsights: "Regulatory bodies have reported improved compliance rates with this use case."
        },
        {
          title: "Contract Risk Assessment",
          industry: "Legal Risk",
          views: 800,
          description: "AI-based contract risk evaluation and mitigation.",
          problemStatement: "Manual contract review leading to missed risks and increased costs.",
          solutionOverview: "Use AI to evaluate contract risks and identify potential mitigation strategies.",
          roiAchieved: "Reduced contract risk by 25%.",
          implementationSteps: [
            "Collect contract data",
            "Use AI to analyze contract terms",
            "Identify potential risks and mitigation strategies",
            "Monitor contract risk metrics"
          ],
          industryInsights: "Law firms have reported improved contract risk management with this use case."
        }
      ]
    },
    {
      id: 3,
      title: "Emerging Trends in Legal Technology",
      cases: [
        {
          title: "Virtual Legal Assistant",
          industry: "Legal Tech",
          views: 750,
          description: "AI-powered virtual assistant for basic legal queries and support.",
          problemStatement: "Limited access to legal support for small businesses and individuals.",
          solutionOverview: "Implement AI-powered virtual legal assistant to provide basic legal support.",
          roiAchieved: "Increased access to legal support by 50%.",
          implementationSteps: [
            "Develop virtual assistant platform",
            "Implement AI-powered data analysis",
            "Set up automated support system",
            "Monitor user satisfaction metrics"
          ],
          industryInsights: "Small businesses and individuals have reported improved access to legal support with this use case."
        },
        {
          title: "Legal Research Automation",
          industry: "Research",
          views: 700,
          description: "Automated legal research and case law analysis system.",
          problemStatement: "Manual legal research leading to delays and errors.",
          solutionOverview: "Implement AI-powered legal research automation system to automate research and analysis.",
          roiAchieved: "Reduced research time by 60%",
          implementationSteps: [
            "Develop research platform",
            "Implement AI-powered data analysis",
            "Set up automated case law analysis",
            "Monitor research metrics"
          ],
          industryInsights: "Law firms have reported improved research efficiency with this use case."
        },
        {
          title: "Smart Contract Analysis",
          industry: "Blockchain",
          views: 650,
          description: "Intelligent system for analyzing and validating smart contracts.",
          problemStatement: "Manual smart contract analysis leading to errors and security risks.",
          solutionOverview: "Use AI to analyze and validate smart contracts.",
          roiAchieved: "Reduced smart contract risk by 40%.",
          implementationSteps: [
            "Collect smart contract data",
            "Use AI to analyze contract terms",
            "Identify potential risks and validation strategies",
            "Monitor contract risk metrics"
          ],
          industryInsights: "Blockchain companies have reported improved smart contract security with this use case."
        }
      ]
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) =>
      prev === trendingUseCases.length - 1 ? 0 : prev + 1
    );
  };

  const prevSlide = () => {
    setCurrentSlide((prev) =>
      prev === 0 ? trendingUseCases.length - 1 : prev - 1
    );
  };

  const handleCaseClick = (useCase: UseCase) => {
    setSelectedCase(useCase);
    setIsDialogOpen(true);
  };

  // Helper function to parse market data
  const parseMarketData = (dataString: string | null) => {
    if (!dataString) return null;
    try {
      return JSON.parse(dataString);
    } catch (error) {
      console.error('Error parsing market data:', error);
      return null;
    }
  };

  return (
    <div className="p-4 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Market Intelligence</h1>
          {!hasFullMiuAccess && hasMiuPreview && (
            <p className="text-sm text-muted-foreground mt-1">
              Preview Mode - <Button
                variant="link"
                className="p-0 h-auto text-blue-600"
                onClick={() => router.push('/dashboard/billing')}
              >
                Upgrade to Pro
              </Button> for full access
            </p>
          )}
        </div>

        {/* Search bar - only for full access */}
        {hasFullMiuAccess && (
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Search case studies..."
              className="pl-10 pr-4"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        )}
      </div>

      {/* Trending Use Cases Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-muted-foreground">
            {trendingUseCases[currentSlide].title}
          </h2>
          <div className="flex gap-2">
            <Button variant="outline" size="icon" onClick={prevSlide}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={nextSlide}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Use Cases Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {trendingUseCases[currentSlide].cases.map((useCase, index) => (
            <Card
              key={index}
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => handleCaseClick(useCase)}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{useCase.title}</CardTitle>
                  <Badge variant="secondary">{useCase.industry}</Badge>
                </div>
                <CardDescription className="text-sm mt-2">
                  {useCase.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Views</span>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold">{useCase.views}</span>
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Real-time Case Studies with Market Intelligence */}
      <SubscriptionGuard feature="fullMiuAccess" fallback={
        <div className="mt-8 space-y-4">
          <h2 className="text-xl font-semibold">Case Studies with Market Intelligence</h2>
          <Alert>
            <Lock className="h-4 w-4" />
            <AlertDescription>
              Full Market Intelligence access requires a Pro plan.
              <Button
                variant="link"
                className="p-0 h-auto font-semibold text-blue-600"
                onClick={() => router.push('/dashboard/billing')}
              >
                Upgrade now
              </Button> to access detailed market intelligence data.
            </AlertDescription>
          </Alert>
        </div>
      }>
        <div className="mt-8 space-y-4">
          <h2 className="text-xl font-semibold">Case Studies with Market Intelligence</h2>

          {loading ? (
            // Loading skeleton
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="overflow-hidden">
                  <Skeleton className="h-48 w-full" />
                  <div className="p-4 space-y-3">
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-2/3" />
                    <div className="flex justify-between pt-4">
                      <Skeleton className="h-8 w-20" />
                      <Skeleton className="h-8 w-20" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : filteredCaseStudies.length > 0 ? (
            // Case studies grid
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {filteredCaseStudies.map((caseStudy) => (
                <MarketIntelligenceCard
                  key={caseStudy.id}
                  id={caseStudy.id}
                  title={caseStudy.useCaseTitle}
                  industry={caseStudy.industry || undefined}
                  imageUrl={caseStudy.featureImageUrl || caseStudy.headerImage || undefined}
                  marketIntelligence={parseMarketData(caseStudy.marketIntelligenceData)}
                  marketMetrics={parseMarketData(caseStudy.marketMetricsData)}
                />
              ))}
            </div>
          ) : (
            // No results
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">
                {searchQuery
                  ? 'No case studies found matching your search.'
                  : 'No case studies with market intelligence data available.'}
              </p>
              {searchQuery && (
                <Button
                  variant="link"
                  onClick={() => setSearchQuery('')}
                  className="mt-2"
                >
                  Clear search
                </Button>
              )}
              {!searchQuery && (
                <div className="mt-4">
                  <p className="text-sm text-gray-500 mb-2">Market intelligence data will appear here when available.</p>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/dashboard/case-studies')}
                    className="mt-2"
                  >
                    Browse All Case Studies
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </SubscriptionGuard>

      {/* Case Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          {selectedCase && (
            <>
              <DialogHeader>
                <div className="flex justify-between items-start">
                  <DialogTitle>{selectedCase.title}</DialogTitle>
                  <Badge variant="secondary">{selectedCase.industry}</Badge>
                </div>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-1">Problem Statement</h3>
                  <p className="text-muted-foreground">{selectedCase.problemStatement}</p>
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Solution Overview</h3>
                  <p className="text-muted-foreground">{selectedCase.solutionOverview}</p>
                </div>
                <div>
                  <h3 className="font-semibold mb-1">ROI Achieved</h3>
                  <p className="text-muted-foreground">{selectedCase.roiAchieved}</p>
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Implementation Steps</h3>
                  <ul className="list-decimal pl-4 text-muted-foreground">
                    {selectedCase.implementationSteps?.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Industry-Specific Insights</h3>
                  <p className="text-muted-foreground">{selectedCase.industryInsights}</p>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Carousel Indicators */}
      <div className="flex justify-center gap-2 mt-4">
        {trendingUseCases.map((_, index) => (
          <button
            key={index}
            className={`w-2 h-2 rounded-full transition-colors ${currentSlide === index ? 'bg-primary' : 'bg-gray-300'
              }`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </div>
  );
}
