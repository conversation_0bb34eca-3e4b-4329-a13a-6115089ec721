{"version": "7", "dialect": "postgresql", "entries": [{"idx": 0, "version": "7", "when": 1744569090714, "tag": "0000_low_vapor", "breakpoints": true}, {"idx": 3, "version": "7", "when": 1744142839104, "tag": "0003_case_management_updates", "breakpoints": true}, {"idx": 4, "version": "7", "when": 1746171019894, "tag": "0005_wealthy_the_santerians", "breakpoints": true}, {"idx": 5, "version": "7", "when": 1747171019894, "tag": "0006_add_otp_verification", "breakpoints": true}]}