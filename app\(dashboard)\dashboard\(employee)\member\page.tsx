// Force dynamic rendering since we use cookies
export const dynamic = 'force-dynamic';

import { db } from '@/lib/db/drizzle';
import { caseStudies, bookmarks } from '@/lib/db/schema';
import { isNull, eq } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';
import MemberDashboardClient from './client-page';

export default async function MemberDashboardPage() {
  const user = await getUser();
  const userId = user?.id;

  // console.log('Member Dashboard user............', user);

  // Fetch all case studies from the database
  const allCaseStudies = await db.query.caseStudies.findMany({
    orderBy: (caseStudies, { desc }) => [desc(caseStudies.createdAt)],
    columns: {
      id: true,
      useCaseTitle: true,
      industry: true,
      role: true,
      vector: true,
      marketIntelligenceData: true,
      marketMetricsData: true,
      createdAt: true,
      views: true,
      marketSize: true,
      featureImageUrl: true,
      previewImageUrl: true,
      introductionText: true,
      trendingStatus: true,
    },
    with: {
      icons: {
        orderBy: (icons, { asc }) => [asc(icons.order)],
      },
    },
  });

  // Fetch user's bookmarks if the table exists and user is logged in
  let userBookmarks = [];
  let bookmarkedIds = new Set<number>();

  if (userId) {
    try {
      userBookmarks = await db.query.bookmarks.findMany({
        where: eq(bookmarks.userId, userId),
      });

      // Create a set of bookmarked case study IDs for quick lookup
      bookmarkedIds = new Set(
        userBookmarks.map((bookmark) => bookmark.caseStudyId)
      );
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
      // If the bookmarks table doesn't exist yet, we'll just use an empty set
    }
  } else {
    console.log('No user ID available, skipping bookmark fetch');
  }

  // Helper function to normalize industry names for better matching
  const normalizeIndustry = (industry: string | null): string => {
    if (!industry) return 'other';

    // Convert to lowercase and trim whitespace
    let normalized = industry.toLowerCase().trim();

    // Replace common abbreviations and variations
    const replacements: Record<string, string> = {
      fin: 'finance',
      financial: 'finance',
      banking: 'finance',
      bank: 'finance',
      investment: 'finance',
      tech: 'technology',
      it: 'technology',
      software: 'technology',
      healthcare: 'health',
      'health care': 'health',
      medical: 'health',
      retail: 'commerce',
      ecommerce: 'commerce',
      'e-commerce': 'commerce',
      manufacturing: 'industrial',
      industry: 'industrial',
      telecom: 'telecommunications',
      education: 'edu',
      academic: 'edu',
      university: 'edu',
      school: 'edu',
      hospitality: 'hotel',
      tourism: 'travel',
      insurance: 'finance',
      'real estate': 'property',
      realty: 'property',
      automotive: 'auto',
      automobile: 'auto',
      transport: 'logistics',
      shipping: 'logistics',
      media: 'entertainment',
      pharma: 'pharmaceutical',
      energy: 'utilities',
      power: 'utilities',
      agriculture: 'farming',
      govt: 'government',
      gov: 'government',
    };

    // Apply replacements for better matching
    for (const [key, value] of Object.entries(replacements)) {
      if (normalized.includes(key)) {
        normalized = normalized.replace(key, value);
      }
    }

    return normalized;
  };

  // Optimized industry match function for large datasets
  const isIndustryMatch = (
    caseStudyIndustry: string | null,
    userIndustry: string | null
  ): boolean => {
    if (!caseStudyIndustry || !userIndustry) return false;
    if (caseStudyIndustry === 'Other' || userIndustry === 'Other') {
      return caseStudyIndustry === userIndustry;
    }

    // Normalize once
    const normCase = normalizeIndustry(caseStudyIndustry);
    const normUser = normalizeIndustry(userIndustry);

    if (normCase === normUser) return true;
    if (normCase.includes(normUser) || normUser.includes(normCase)) return true;

    // Split into words
    const caseWordsArr = normCase.split(/\s+/);
    const userWordsArr = normUser.split(/\s+/);

    // First N words match (ordered, partial allowed)
    const firstWordsMatch = (n: number): boolean => {
      if (caseWordsArr.length < n || userWordsArr.length < n) {
        const minLen = Math.min(caseWordsArr.length, userWordsArr.length);
        for (let i = 0; i < minLen; i++) {
          if (
            !caseWordsArr[i].includes(userWordsArr[i]) &&
            !userWordsArr[i].includes(caseWordsArr[i])
          ) {
            return false;
          }
        }
        return true;
      }
      for (let i = 0; i < n; i++) {
        if (
          !caseWordsArr[i].includes(userWordsArr[i]) &&
          !userWordsArr[i].includes(caseWordsArr[i])
        ) {
          return false;
        }
      }
      return true;
    };

    if (firstWordsMatch(1)) return true;
    if (
      firstWordsMatch(2) &&
      caseWordsArr.length >= 2 &&
      userWordsArr.length >= 2
    )
      return true;
    if (
      firstWordsMatch(3) &&
      caseWordsArr.length >= 3 &&
      userWordsArr.length >= 3
    )
      return true;

    // Use sets for fast word comparison
    const caseWords = new Set(caseWordsArr);
    const userWords = new Set(userWordsArr);

    // Fast intersection count (exact word matches)
    let matchCount = 0;
    for (const word of caseWords) {
      if (userWords.has(word)) {
        matchCount++;
      }
    }

    // Substring/partial matches (any word in one includes any word in the other)
    let partialCount = 0;
    for (const cWord of caseWordsArr) {
      for (const uWord of userWordsArr) {
        if (
          cWord !== uWord &&
          (cWord.includes(uWord) || uWord.includes(cWord))
        ) {
          partialCount++;
        }
      }
    }

    // Fast lenient matching
    const maxLen = Math.max(caseWords.size, userWords.size);
    const matchRatio = maxLen > 0 ? (matchCount + partialCount) / maxLen : 0;

    return (
      (matchCount + partialCount >= 1 &&
        (caseWords.size === 1 || userWords.size === 1)) ||
      matchCount + partialCount >= 2 ||
      matchRatio >= 0.3
    );
  };

  // Map of related industries for better matching
  const relatedIndustries: Record<string, string[]> = {
    Finance: [
      'Banking',
      'Investment',
      'Insurance',
      'Financial Services',
      'FinTech',
    ],
    Technology: [
      'IT',
      'Software',
      'Hardware',
      'Tech',
      'Information Technology',
      'SaaS',
    ],
    Healthcare: [
      'Health',
      'Medical',
      'Pharma',
      'Pharmaceutical',
      'Health Tech',
    ],
    Retail: ['E-commerce', 'Commerce', 'Shopping', 'Consumer Goods'],
    Manufacturing: ['Industrial', 'Production', 'Factory', 'Industry'],
    Education: ['Academic', 'EdTech', 'University', 'School', 'Learning'],
    Telecommunications: ['Telecom', 'Communications', 'Network'],
    Hospitality: ['Hotel', 'Tourism', 'Travel', 'Leisure'],
    'Real Estate': ['Property', 'Realty', 'Construction', 'Housing'],
    Automotive: ['Auto', 'Car', 'Vehicle', 'Transport'],
    Logistics: ['Supply Chain', 'Transport', 'Shipping', 'Delivery'],
    Media: ['Entertainment', 'Publishing', 'News', 'Content'],
    Energy: ['Utilities', 'Power', 'Electricity', 'Oil', 'Gas'],
    Agriculture: ['Farming', 'Food Production', 'Agritech'],
    Government: ['Public Sector', 'Gov', 'Govt', 'Public Services'],
  };

  // Helper function to check if an industry is related to the user's industry
  const isRelatedIndustry = (
    caseStudyIndustry: string | null,
    userIndustry: string | null
  ): boolean => {
    if (!caseStudyIndustry || !userIndustry) return false;

    // If we already determined it's a direct match, don't consider it a related industry
    if (isIndustryMatch(caseStudyIndustry, userIndustry)) return false;

    // Normalize both industries for comparison
    const normalizedCaseStudyIndustry = normalizeIndustry(caseStudyIndustry);
    const normalizedUserIndustry = normalizeIndustry(userIndustry);

    // Check first words of both industries
    const caseStudyFirstWord = normalizedCaseStudyIndustry.split(/\s+/)[0];
    const userFirstWord = normalizedUserIndustry.split(/\s+/)[0];

    // If the first words are similar but not exactly the same, consider it related
    if (
      caseStudyFirstWord.includes(userFirstWord.substring(0, 3)) ||
      userFirstWord.includes(caseStudyFirstWord.substring(0, 3))
    ) {
      return true;
    }

    // Check if the case study industry is in the related industries list for the user's industry
    for (const [key, relatedList] of Object.entries(relatedIndustries)) {
      // If user's industry matches this key or is in its related list
      if (
        normalizeIndustry(key).includes(normalizedUserIndustry) ||
        normalizedUserIndustry.includes(normalizeIndustry(key)) ||
        relatedList.some((related) => {
          const normalizedRelated = normalizeIndustry(related);
          return (
            normalizedRelated.includes(normalizedUserIndustry) ||
            normalizedUserIndustry.includes(normalizedRelated) ||
            // Check if first 3 letters match
            (normalizedRelated.length >= 3 &&
              normalizedUserIndustry.length >= 3 &&
              normalizedRelated.substring(0, 3) ===
                normalizedUserIndustry.substring(0, 3))
          );
        })
      ) {
        // Check if case study industry is in this related list
        if (
          normalizeIndustry(key).includes(normalizedCaseStudyIndustry) ||
          normalizedCaseStudyIndustry.includes(normalizeIndustry(key)) ||
          relatedList.some((related) => {
            const normalizedRelated = normalizeIndustry(related);
            return (
              normalizedRelated.includes(normalizedCaseStudyIndustry) ||
              normalizedCaseStudyIndustry.includes(normalizedRelated) ||
              // Check if first 3 letters match
              (normalizedRelated.length >= 3 &&
                normalizedCaseStudyIndustry.length >= 3 &&
                normalizedRelated.substring(0, 3) ===
                  normalizedCaseStudyIndustry.substring(0, 3))
            );
          })
        ) {
          return true;
        }
      }
    }

    // Additional check for first 3 letters matching
    if (
      normalizedCaseStudyIndustry.length >= 3 &&
      normalizedUserIndustry.length >= 3
    ) {
      if (
        normalizedCaseStudyIndustry.substring(0, 3) ===
        normalizedUserIndustry.substring(0, 3)
      ) {
        return true;
      }
    }

    return false;
  };

  // --- Helper: Get high and related industry matches ---
  const getIndustryMatches = (caseStudiesArr: any[], userIndustry: string) => {
    const high: any[] = [];
    const related: any[] = [];
    for (const cs of caseStudiesArr) {
      if (!cs.industry) continue;
      if (isIndustryMatch(cs.industry, userIndustry)) {
        high.push(cs);
      } else if (isRelatedIndustry(cs.industry, userIndustry)) {
        related.push(cs);
      }
    }
    return { high, related };
  };

  // Get user's industry
  const userIndustry = user?.industry || 'Other';

  // --- 1. Industry Specific Case Studies (no fallback, can be <3) ---
  const { high: industryHigh, related: industryRelated } = getIndustryMatches(
    allCaseStudies,
    userIndustry
  );

  const industrySpecificCaseStudies = industryHigh.map((caseStudy) => ({
    ...caseStudy,
    isBookmarked: bookmarkedIds.has(caseStudy.id),
  }));

  // Helper function to check if roles match or are related
  const isRoleMatch = (
    caseStudyRole: string | null,
    userRole: string | null
  ): boolean => {
    if (!caseStudyRole || !userRole) return false;

    // Normalize roles for comparison
    const normalizedCaseStudyRole = caseStudyRole.toLowerCase().trim();
    const normalizedUserRole = userRole.toLowerCase().trim();

    // Check for exact match
    if (normalizedCaseStudyRole === normalizedUserRole) return true;

    // Check if one contains the other
    if (normalizedCaseStudyRole.includes(normalizedUserRole)) return true;
    if (normalizedUserRole.includes(normalizedCaseStudyRole)) return true;

    // Check for common role patterns
    const executiveRoles = [
      'ceo',
      'cto',
      'cfo',
      'coo',
      'chief',
      'executive',
      'president',
      'founder',
      'director',
    ];
    const managerRoles = [
      'manager',
      'head',
      'lead',
      'supervisor',
      'coordinator',
    ];
    const specialistRoles = [
      'specialist',
      'analyst',
      'consultant',
      'engineer',
      'developer',
    ];

    const isCaseStudyExecutive = executiveRoles.some((role) =>
      normalizedCaseStudyRole.includes(role)
    );
    const isUserExecutive = executiveRoles.some((role) =>
      normalizedUserRole.includes(role)
    );

    const isCaseStudyManager = managerRoles.some((role) =>
      normalizedCaseStudyRole.includes(role)
    );
    const isUserManager = managerRoles.some((role) =>
      normalizedUserRole.includes(role)
    );

    const isCaseStudySpecialist = specialistRoles.some((role) =>
      normalizedCaseStudyRole.includes(role)
    );
    const isUserSpecialist = specialistRoles.some((role) =>
      normalizedUserRole.includes(role)
    );

    // Match within the same role category
    if (
      (isCaseStudyExecutive && isUserExecutive) ||
      (isCaseStudyManager && isUserManager) ||
      (isCaseStudySpecialist && isUserSpecialist)
    ) {
      return true;
    }

    return false;
  };

  // Get user's role
  const userRole = user?.role || null;

  // --- 2. Role Related Case Studies (ensure at least 3, prioritize industry first) ---
  let roleRelatedCaseStudies: any[] = [];
  if (userRole) {
    // First, get role matches within industryHigh and industryRelated
    const roleHigh = industryHigh.filter(
      (cs) => cs.role && isRoleMatch(cs.role, userRole)
    );
    const roleRelated = industryRelated.filter(
      (cs) => cs.role && isRoleMatch(cs.role, userRole)
    );
    // Then, get role matches from allCaseStudies not already included
    const others = allCaseStudies.filter(
      (cs) =>
        cs.role &&
        isRoleMatch(cs.role, userRole) &&
        !industryHigh.includes(cs) &&
        !industryRelated.includes(cs)
    );
    // Combine, prioritize high, then related, then others
    roleRelatedCaseStudies = [...roleHigh, ...roleRelated, ...others].map(
      (caseStudy) => ({
        ...caseStudy,
        isBookmarked: bookmarkedIds.has(caseStudy.id),
        matchType: 'role',
      })
    );
    // Ensure at least 3
    if (roleRelatedCaseStudies.length < 3) {
      // Fill with trending or recent
      const fill = allCaseStudies
        .filter(
          (cs) =>
            !roleRelatedCaseStudies.includes(cs) &&
            (cs.marketIntelligenceData || cs.createdAt)
        )
        .slice(0, 3 - roleRelatedCaseStudies.length)
        .map((caseStudy) => ({
          ...caseStudy,
          isBookmarked: bookmarkedIds.has(caseStudy.id),
          matchType: 'fill',
        }));
      roleRelatedCaseStudies.push(...fill);
    }
    // Only keep 3 minimum
    roleRelatedCaseStudies = roleRelatedCaseStudies.slice(
      0,
      Math.max(3, roleRelatedCaseStudies.length)
    );
  }

  // --- 3. Industry Related Case Studies (ensure at least 3, prioritize high/related) ---
  let industryRelatedCaseStudies: any[] = [];
  // Exclude already in industrySpecificCaseStudies
  const notInIndustry = allCaseStudies.filter(
    (cs) => !industrySpecificCaseStudies.some((ics) => ics.id === cs.id)
  );
  const { high: relHigh, related: relRelated } = getIndustryMatches(
    notInIndustry,
    userIndustry
  );
  industryRelatedCaseStudies = [...relHigh, ...relRelated].map((caseStudy) => ({
    ...caseStudy,
    isBookmarked: bookmarkedIds.has(caseStudy.id),
    matchType: 'industry',
  }));
  // Ensure at least 3
  if (industryRelatedCaseStudies.length < 3) {
    // Fill with trending or recent
    const fill = allCaseStudies
      .filter(
        (cs) =>
          !industryRelatedCaseStudies.some((ics) => ics.id === cs.id) &&
          (cs.marketIntelligenceData || cs.createdAt)
      )
      .slice(0, 3 - industryRelatedCaseStudies.length)
      .map((caseStudy) => ({
        ...caseStudy,
        isBookmarked: bookmarkedIds.has(caseStudy.id),
        matchType: 'fill',
      }));
    industryRelatedCaseStudies.push(...fill);
  }
  industryRelatedCaseStudies = industryRelatedCaseStudies.slice(
    0,
    Math.max(3, industryRelatedCaseStudies.length)
  );

  // --- 4. Vector Specific Case Studies (ensure at least 3, prioritize industry first) ---
  const predefinedVectors = [
    'Sustainability',
    'Cost',
    'Customer Experience',
    'Data',
    'Risk',
    'Growth',
  ];
  const vectorSpecificCaseStudies = predefinedVectors.reduce((acc, vector) => {
    // High: vector match within industryHigh
    const high = industryHigh.filter(
      (cs) =>
        cs.vector &&
        (cs.vector.toLowerCase().trim() === vector.toLowerCase().trim() ||
          cs.vector.toLowerCase().includes(vector.toLowerCase()))
    );
    // Related: vector match within industryRelated
    const related = industryRelated.filter(
      (cs) =>
        cs.vector &&
        (cs.vector.toLowerCase().trim() === vector.toLowerCase().trim() ||
          cs.vector.toLowerCase().includes(vector.toLowerCase()))
    );
    // Others: vector match in allCaseStudies not already included
    const others = allCaseStudies.filter(
      (cs) =>
        cs.vector &&
        (cs.vector.toLowerCase().trim() === vector.toLowerCase().trim() ||
          cs.vector.toLowerCase().includes(vector.toLowerCase())) &&
        !high.includes(cs) &&
        !related.includes(cs)
    );
    let arr = [...high, ...related, ...others].map((caseStudy) => ({
      ...caseStudy,
      isBookmarked: bookmarkedIds.has(caseStudy.id),
    }));
    // Ensure at least 3
    if (arr.length < 3) {
      const fill = allCaseStudies
        .filter(
          (cs) =>
            !arr.some((ics) => ics.id === cs.id) &&
            (cs.marketIntelligenceData || cs.createdAt)
        )
        .slice(0, 3 - arr.length)
        .map((caseStudy) => ({
          ...caseStudy,
          isBookmarked: bookmarkedIds.has(caseStudy.id),
        }));
      arr.push(...fill);
    }
    acc[vector] = arr.slice(0, Math.max(3, arr.length));
    return acc;
  }, {} as Record<string, (typeof caseStudies.$inferSelect & { isBookmarked: boolean })[]>);

  // --- 5. Role Specific Case Studies (do NOT fill to 3 for these roles) ---
  const predefinedRoles = [
    'CEO',
    'COO',
    'CFO',
    'CMO',
    'CTO',
    'CHRO',
    'CRO',
    'CXO',
  ];
  const roleSpecificCaseStudies = predefinedRoles.reduce((acc, role) => {
    // High: role match within industryHigh
    const high = industryHigh.filter(
      (cs) =>
        cs.role &&
        (cs.role.toLowerCase().trim() === role.toLowerCase().trim() ||
          cs.role.toLowerCase().includes(role.toLowerCase()))
    );
    // Related: role match within industryRelated
    const related = industryRelated.filter(
      (cs) =>
        cs.role &&
        (cs.role.toLowerCase().trim() === role.toLowerCase().trim() ||
          cs.role.toLowerCase().includes(role.toLowerCase()))
    );
    // Others: role match in allCaseStudies not already included
    const others = allCaseStudies.filter(
      (cs) =>
        cs.role &&
        (cs.role.toLowerCase().trim() === role.toLowerCase().trim() ||
          cs.role.toLowerCase().includes(role.toLowerCase())) &&
        !high.includes(cs) &&
        !related.includes(cs)
    );
    // Do NOT fill to 3, just return what is found
    acc[role] = [...high, ...related, ...others].map((caseStudy) => ({
      ...caseStudy,
      isBookmarked: bookmarkedIds.has(caseStudy.id),
    }));
    return acc;
  }, {} as Record<string, (typeof caseStudies.$inferSelect & { isBookmarked: boolean })[]>);

  // Updated trendingCaseStudies logic: only use views and market_size
  const trendingCaseStudies = (() => {
    const getViews = (cs: any) => (typeof cs.views === 'number' ? cs.views : 0);
    const getMarketSize = (cs: any) =>
      typeof cs.marketSize === 'number' ? cs.marketSize : 0;

    // Only consider case studies with trendingStatus === true
    const trendingCaseStudiesArr = allCaseStudies.filter(
      (cs) => cs.trendingStatus === true
    );

    // 1. High industry match, sorted by views desc, then marketSize desc
    const high = industryHigh
      .filter((cs) => cs.trendingStatus === true)
      .sort((a, b) => {
        const v = getViews(b) - getViews(a);
        if (v !== 0) return v;
        return getMarketSize(b) - getMarketSize(a);
      });

    // 2. Related industry match, sorted by views desc, then marketSize desc, not in high
    const related = industryRelated
      .filter(
        (cs) => cs.trendingStatus === true && !high.some((h) => h.id === cs.id)
      )
      .sort((a, b) => {
        const v = getViews(b) - getViews(a);
        if (v !== 0) return v;
        return getMarketSize(b) - getMarketSize(a);
      });

    // 3. Others: not in high or related, sorted by views desc, then marketSize desc
    const others = trendingCaseStudiesArr
      .filter(
        (cs) =>
          !high.some((h) => h.id === cs.id) &&
          !related.some((r) => r.id === cs.id)
      )
      .sort((a, b) => {
        const v = getViews(b) - getViews(a);
        if (v !== 0) return v;
        return getMarketSize(b) - getMarketSize(a);
      });

    // Combine, keep at least 3 and at most 8
    const combined = [...high, ...related, ...others].slice(0, 8);

    // Ensure at least 3 (if not enough, fill from allCaseStudies by views/marketSize and trendingStatus === true)
    let result = combined;
    if (result.length < 3) {
      const fill = trendingCaseStudiesArr
        .filter((cs) => !result.some((r) => r.id === cs.id))
        .sort((a, b) => {
          const v = getViews(b) - getViews(a);
          if (v !== 0) return v;
          return getMarketSize(b) - getMarketSize(a);
        })
        .slice(0, 3 - result.length);
      result = [...result, ...fill];
    }

    return result.map((caseStudy) => ({
      ...caseStudy,
      isBookmarked: bookmarkedIds.has(caseStudy.id),
    }));
  })();

  const recentCaseStudies = [...allCaseStudies]
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
    .slice(0, 8)
    .map((caseStudy) => ({
      ...caseStudy,
      isBookmarked: bookmarkedIds.has(caseStudy.id),
    }));

  const bookmarkedCaseStudies = allCaseStudies
    .filter((caseStudy) => bookmarkedIds.has(caseStudy.id))
    .map((caseStudy) => ({
      ...caseStudy,
      isBookmarked: true,
    }));

  return (
    <MemberDashboardClient
      trendingCaseStudies={trendingCaseStudies}
      recentCaseStudies={recentCaseStudies}
      bookmarkedCaseStudies={bookmarkedCaseStudies}
      industrySpecificCaseStudies={industrySpecificCaseStudies}
      relatedCaseStudies={industryRelatedCaseStudies}
      roleRelatedCaseStudies={roleRelatedCaseStudies}
      industryRelatedCaseStudies={industryRelatedCaseStudies}
      roleSpecificCaseStudies={roleSpecificCaseStudies}
      vectorSpecificCaseStudies={vectorSpecificCaseStudies}
      predefinedRoles={predefinedRoles}
      predefinedVectors={predefinedVectors}
      userIndustry={userIndustry}
      userRole={user?.role}
      userName={user?.name}
    />
  );
}
