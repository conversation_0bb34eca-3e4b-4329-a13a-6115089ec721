{"id": "b5c1d82e-674f-4543-856b-e3577fbb8ee5", "prevId": "adc1c81e-674f-4543-856b-e3577fbb8ee4", "version": "7", "dialect": "postgresql", "tables": {"public.caseStudies": {"name": "caseStudies", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "use_case_title": {"name": "use_case_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "industry": {"name": "industry", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "vector": {"name": "vector", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "potentially_impacted_kpis": {"name": "potentially_impacted_kpis", "type": "text", "primaryKey": false, "notNull": false}, "introduction_title": {"name": "introduction_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "introduction_text": {"name": "introduction_text", "type": "text", "primaryKey": false, "notNull": false}, "transition_to_problems": {"name": "transition_to_problems", "type": "text", "primaryKey": false, "notNull": false}, "problem_1": {"name": "problem_1", "type": "text", "primaryKey": false, "notNull": false}, "problem_2": {"name": "problem_2", "type": "text", "primaryKey": false, "notNull": false}, "problem_3": {"name": "problem_3", "type": "text", "primaryKey": false, "notNull": false}, "transition_to_questions": {"name": "transition_to_questions", "type": "text", "primaryKey": false, "notNull": false}, "question_1": {"name": "question_1", "type": "text", "primaryKey": false, "notNull": false}, "question_2": {"name": "question_2", "type": "text", "primaryKey": false, "notNull": false}, "question_3": {"name": "question_3", "type": "text", "primaryKey": false, "notNull": false}, "process_title": {"name": "process_title", "type": "text", "primaryKey": false, "notNull": false}, "process_step_1_title": {"name": "process_step_1_title", "type": "text", "primaryKey": false, "notNull": false}, "process_step_1_description": {"name": "process_step_1_description", "type": "text", "primaryKey": false, "notNull": false}, "process_step_2_title": {"name": "process_step_2_title", "type": "text", "primaryKey": false, "notNull": false}, "process_step_2_description": {"name": "process_step_2_description", "type": "text", "primaryKey": false, "notNull": false}, "process_step_3_title": {"name": "process_step_3_title", "type": "text", "primaryKey": false, "notNull": false}, "process_step_3_description": {"name": "process_step_3_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_title": {"name": "solution_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_description": {"name": "solution_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_1_title": {"name": "solution_1_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_1_description": {"name": "solution_1_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_2_title": {"name": "solution_2_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_2_description": {"name": "solution_2_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_3_title": {"name": "solution_3_title", "type": "text", "primaryKey": false, "notNull": false}, "solution_3_description": {"name": "solution_3_description", "type": "text", "primaryKey": false, "notNull": false}, "solution_1": {"name": "solution_1", "type": "text", "primaryKey": false, "notNull": false}, "solution_2": {"name": "solution_2", "type": "text", "primaryKey": false, "notNull": false}, "solution_3": {"name": "solution_3", "type": "text", "primaryKey": false, "notNull": false}, "solution_4": {"name": "solution_4", "type": "text", "primaryKey": false, "notNull": false}, "solution_5": {"name": "solution_5", "type": "text", "primaryKey": false, "notNull": false}, "impact_title": {"name": "impact_title", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_1": {"name": "potential_impact_1", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_2": {"name": "potential_impact_2", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_3": {"name": "potential_impact_3", "type": "text", "primaryKey": false, "notNull": false}, "potential_impact_4": {"name": "potential_impact_4", "type": "text", "primaryKey": false, "notNull": false}, "conclusion_title": {"name": "conclusion_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "conclusion_text": {"name": "conclusion_text", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}