import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { invitations } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/invitations/:id - Get invitation details
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid invitation ID' }, { status: 400 });
    }

    // Get the invitation with team details
    const invitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, id),
      with: {
        team: true,
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found' }, { status: 404 });
    }

    // Return only necessary information
    return NextResponse.json({
      id: invitation.id,
      email: invitation.email,
      role: invitation.role,
      status: invitation.status,
      teamId: invitation.teamId,
      teamName: invitation.team.name,
    });
  } catch (error) {
    console.error('Error fetching invitation:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
