import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { getUserSubscription } from '@/lib/services/subscription-service';
import { PLAN_LIMITS, PlanFeatures } from '@/lib/auth/subscription-middleware';

// Routes that require specific plan features
// Note: Advanced analytics and API access have been removed from all plans
const FEATURE_RESTRICTED_ROUTES = {
  // '/dashboard/analytics': 'advancedAnalytics', // Feature removed from all plans
  // '/dashboard/api': 'apiAccess', // Feature removed from all plans
  '/dashboard/branding': 'customBranding',
} as const;

// Grace period routes (accessible during grace period)
const GRACE_PERIOD_ROUTES = [
  '/dashboard/billing',
  '/dashboard/settings',
  '/dashboard/help',
];

/**
 * Check if a route requires specific plan features
 */
function getRequiredFeature(pathname: string): keyof PlanFeatures | null {
  for (const [route, feature] of Object.entries(FEATURE_RESTRICTED_ROUTES)) {
    if (pathname === route || pathname.startsWith(`${route}/`)) {
      return feature as keyof PlanFeatures;
    }
  }
  return null;
}

/**
 * Check if a route is accessible during grace period
 */
function isGracePeriodRoute(pathname: string): boolean {
  return GRACE_PERIOD_ROUTES.some(route =>
    pathname === route || pathname.startsWith(`${route}/`)
  );
}

/**
 * Get plan features for a given plan name
 */
function getPlanFeatures(planName: string | null): PlanFeatures {
  if (!planName) return PLAN_LIMITS.starter; // Default to starter if no plan

  const normalizedPlan = planName.toLowerCase() as keyof typeof PLAN_LIMITS;
  return PLAN_LIMITS[normalizedPlan] || PLAN_LIMITS.starter;
}

/**
 * Check if subscription is in a valid state
 */
function isValidSubscriptionStatus(status: string | null): boolean {
  if (!status) return false;
  return ['active', 'trialing'].includes(status);
}

/**
 * Check if subscription is in grace period (past_due but still accessible)
 */
function isGracePeriodStatus(status: string | null): boolean {
  if (!status) return false;
  return ['past_due'].includes(status);
}

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const pathname = url.searchParams.get('path');
  const redirectTo = url.searchParams.get('redirect_to');

  if (!pathname) {
    return NextResponse.json({ error: 'Path parameter required' }, { status: 400 });
  }

  console.log(`[Subscription Validation API] Validating path: ${pathname}`);

  try {
    // Get current user
    const user = await getUser();
    if (!user) {
      console.log(`[Subscription Validation API] No user found, redirecting to sign-in`);
      return NextResponse.redirect(new URL('/sign-in', request.url));
    }

    console.log(`[Subscription Validation API] User found: ${user.email} (role: ${user.role})`);

    // Owners don't need subscription validation
    if (user.role === 'owner') {
      console.log(`[Subscription Validation API] User is owner, allowing access`);
      const targetUrl = new URL(redirectTo || pathname, request.url);
      targetUrl.searchParams.set('_validated', 'true');
      return NextResponse.redirect(targetUrl);
    }

    // Get user's team and subscription
    const team = await getTeamForUser(user.id);
    if (!team) {
      console.log(`[Subscription Validation API] No team found for user ${user.id}`);
      return NextResponse.redirect(new URL('/dashboard/billing?error=no-team', request.url));
    }

    console.log(`[Subscription Validation API] Team found: ${team.id}, subscription status: ${team.subscriptionStatus}, plan: ${team.planName}`);

    const subscription = await getUserSubscription(user.id);
    const subscriptionStatus = team.subscriptionStatus;
    const planName = team.planName;

    // Special handling for recently created subscriptions
    // If the subscription was just created (within last 5 minutes), be more lenient
    const isRecentSubscription = subscription && subscription.createdAt &&
      (Date.now() - new Date(subscription.createdAt).getTime()) < 5 * 60 * 1000; // 5 minutes

    if (isRecentSubscription) {
      console.log(`[Subscription Validation API] Recent subscription detected, allowing access`);
      const targetUrl = new URL(redirectTo || pathname, request.url);
      targetUrl.searchParams.set('_validated', 'true');
      return NextResponse.redirect(targetUrl);
    }

    // Check subscription status
    if (!isValidSubscriptionStatus(subscriptionStatus)) {
      console.log(`[Subscription Validation API] Invalid subscription status: ${subscriptionStatus}`);

      // If in grace period, allow access to limited routes
      if (isGracePeriodStatus(subscriptionStatus) && isGracePeriodRoute(pathname)) {
        console.log(`[Subscription Validation API] Grace period access allowed for route: ${pathname}`);
        const targetUrl = new URL(redirectTo || pathname, request.url);
        targetUrl.searchParams.set('_validated', 'true');
        return NextResponse.redirect(targetUrl);
      }

      // Redirect to billing page for inactive subscriptions
      const errorParam = subscriptionStatus === 'past_due' ? 'past-due' : 'inactive';
      console.log(`[Subscription Validation API] Redirecting to billing with error: ${errorParam}`);
      return NextResponse.redirect(
        new URL(`/dashboard/billing?error=${errorParam}&redirect=${encodeURIComponent(pathname)}`, request.url)
      );
    }

    // Check feature-specific restrictions
    const requiredFeature = getRequiredFeature(pathname);
    if (requiredFeature) {
      console.log(`[Subscription Validation API] Checking feature access: ${requiredFeature}`);
      const planFeatures = getPlanFeatures(planName);

      if (!planFeatures[requiredFeature]) {
        console.log(`[Subscription Validation API] Feature ${requiredFeature} not available in plan ${planName}`);
        return NextResponse.redirect(
          new URL(`/dashboard/billing?error=feature-restricted&feature=${requiredFeature}&redirect=${encodeURIComponent(pathname)}`, request.url)
        );
      }
    }

    console.log(`[Subscription Validation API] Validation passed, allowing access`);

    // All checks passed, redirect to the original destination with validation token
    const targetUrl = new URL(redirectTo || pathname, request.url);
    targetUrl.searchParams.set('_validated', 'true');
    return NextResponse.redirect(targetUrl);

  } catch (error) {
    console.error('[Subscription Validation API] Validation error:', error);

    // For database connection errors or other temporary issues, allow access
    // to prevent blocking users during temporary outages
    if (error instanceof Error && (
      error.message.includes('connection') ||
      error.message.includes('timeout') ||
      error.message.includes('ECONNREFUSED')
    )) {
      console.log('[Subscription Validation API] Database error detected, allowing access');
      const targetUrl = new URL(redirectTo || pathname, request.url);
      targetUrl.searchParams.set('_validated', 'true');
      return NextResponse.redirect(targetUrl);
    }

    // On other errors, redirect to billing page
    return NextResponse.redirect(
      new URL(`/dashboard/billing?error=validation-failed&redirect=${encodeURIComponent(pathname)}`, request.url)
    );
  }
}
