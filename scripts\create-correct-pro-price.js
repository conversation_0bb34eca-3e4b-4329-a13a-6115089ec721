const Stripe = require('stripe');
require('dotenv').config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function createCorrectProPrice() {
  console.log('Creating correct Pro plan price ($29/month)...');

  try {
    // Use the existing Pro product ID
    const proProductId = 'prod_SPEGG1BQbfMbMW';

    // Create correct Pro price ($29/month)
    const proPrice = await stripe.prices.create({
      product: proProductId,
      unit_amount: 2900, // $29 in cents
      currency: 'usd',
      recurring: {
        interval: 'month',
      },
      metadata: {
        plan_type: 'pro'
      }
    });
    console.log('Created correct Pro price:', proPrice.id);

    // Create Pro yearly price ($290/year - 10 months pricing)
    const proYearlyPrice = await stripe.prices.create({
      product: proProductId,
      unit_amount: 29000, // $290 in cents
      currency: 'usd',
      recurring: {
        interval: 'year',
      },
      metadata: {
        plan_type: 'pro',
        billing_cycle: 'yearly'
      }
    });
    console.log('Created Pro yearly price:', proYearlyPrice.id);

    console.log('\nUpdated Pro Plan Prices:');
    console.log('  Monthly Price ID:', proPrice.id);
    console.log('  Yearly Price ID:', proYearlyPrice.id);
    
  } catch (error) {
    console.error('Error creating Pro prices:', error);
  }
}

createCorrectProPrice();
