'use server';

import { stripe } from '@/lib/payments/stripe';
import { db } from '@/lib/db/drizzle';
import { teams, userSubscriptions, subscriptionHistory } from '@/lib/db/schema';
import { eq, or } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { syncTeamSubscription } from './subscription-sync';

/**
 * Industry-standard subscription upgrade service
 * Handles plan changes, prorations, and billing cycles properly
 */

export interface UpgradeResult {
  success: boolean;
  checkoutUrl?: string;
  portalUrl?: string;
  subscriptionId?: string;
  error?: string;
  requiresPayment: boolean;
  message: string;
}

/**
 * Initiate subscription upgrade with proper proration handling
 */
export async function initiateSubscriptionUpgrade(
  targetPlan: 'starter' | 'pro' | 'enterprise',
  billingCycle: 'monthly' | 'yearly' = 'monthly'
): Promise<UpgradeResult> {
  try {
    const user = await getUser();
    if (!user) {
      return {
        success: false,
        requiresPayment: false,
        error: 'User not authenticated',
        message: 'Please log in to upgrade your subscription',
      };
    }

    const team = await getTeamForUser(user.id);
    if (!team) {
      return {
        success: false,
        requiresPayment: false,
        error: 'Team not found',
        message: 'No team associated with your account',
      };
    }

    console.log(`[Upgrade Service] User ${user.email} requesting upgrade to ${targetPlan} for team ${team.id}`);

    // Get price IDs from environment variables
    const priceId = getPriceId(targetPlan, billingCycle);
    if (!priceId) {
      return {
        success: false,
        requiresPayment: false,
        error: 'Invalid plan configuration',
        message: 'The selected plan is not available',
      };
    }

    // Check if user has existing subscription
    if (team.stripeSubscriptionId) {
      // Handle subscription modification
      return await handleSubscriptionModification(team, targetPlan, priceId);
    } else {
      // Handle new subscription creation
      return await handleNewSubscription(team, user, targetPlan, priceId);
    }

  } catch (error: any) {
    console.error('[Upgrade Service] Unexpected error:', error);
    return {
      success: false,
      requiresPayment: false,
      error: error.message,
      message: 'An unexpected error occurred during upgrade',
    };
  }
}

/**
 * Handle modification of existing subscription
 */
async function handleSubscriptionModification(
  team: any,
  targetPlan: string,
  newPriceId: string
): Promise<UpgradeResult> {
  try {
    // Get current subscription from Stripe
    const subscription = await stripe.subscriptions.retrieve(team.stripeSubscriptionId, {
      expand: ['items.data.price'],
    });

    if (!subscription) {
      return {
        success: false,
        requiresPayment: false,
        error: 'Subscription not found',
        message: 'Current subscription not found in Stripe',
      };
    }

    const currentItem = subscription.items.data[0];
    const currentPriceId = currentItem.price.id;

    // Check if already on the target plan
    if (currentPriceId === newPriceId) {
      return {
        success: true,
        requiresPayment: false,
        subscriptionId: subscription.id,
        message: `You are already on the ${targetPlan} plan`,
      };
    }

    // Update the subscription with proration
    const updatedSubscription = await stripe.subscriptions.update(subscription.id, {
      items: [
        {
          id: currentItem.id,
          price: newPriceId,
        },
      ],
      proration_behavior: 'create_prorations', // Industry standard: prorate immediately
      billing_cycle_anchor: 'unchanged', // Keep current billing cycle
    });

    console.log(`[Upgrade Service] Subscription updated: ${subscription.id} -> ${targetPlan}`);

    // Sync the updated subscription
    await syncTeamSubscription(team.id);

    return {
      success: true,
      requiresPayment: false, // Stripe handles payment automatically for upgrades
      subscriptionId: updatedSubscription.id,
      message: `Successfully upgraded to ${targetPlan} plan. Changes will be reflected in your next invoice.`,
    };

  } catch (error: any) {
    console.error('[Upgrade Service] Error modifying subscription:', error);

    // Handle specific Stripe errors
    if (error.type === 'StripeCardError') {
      return {
        success: false,
        requiresPayment: true,
        error: 'Payment method declined',
        message: 'Please update your payment method and try again',
      };
    }

    return {
      success: false,
      requiresPayment: false,
      error: error.message,
      message: 'Failed to upgrade subscription. Please try again or contact support.',
    };
  }
}

/**
 * Handle creation of new subscription
 */
async function handleNewSubscription(
  team: any,
  user: any,
  targetPlan: string,
  priceId: string
): Promise<UpgradeResult> {
  try {
    // Ensure customer exists in Stripe
    let customerId = team.stripeCustomerId;

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name || user.email,
        metadata: {
          teamId: team.id.toString(),
          userId: user.id.toString(),
        },
      });

      customerId = customer.id;

      // Update team with customer ID
      await db
        .update(teams)
        .set({ stripeCustomerId: customerId })
        .where(eq(teams.id, team.id));
    }

    // Check if user has ever had a subscription to prevent trial abuse
    const hasSubscriptionHistory = await checkSubscriptionHistory(team.id);

    // Determine if trial should be included
    // Only include trial for truly new customers with no subscription history
    const shouldIncludeTrial = !hasSubscriptionHistory && targetPlan === 'starter';

    console.log(`[Upgrade Service] Creating new subscription for team ${team.id}, plan: ${targetPlan}, includeTrial: ${shouldIncludeTrial}`);

    // Prepare session parameters
    const sessionParams: any = {
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing?canceled=true`,
      metadata: {
        teamId: team.id.toString(),
        userId: user.id.toString(),
        planName: targetPlan,
      },
      subscription_data: {
        metadata: {
          teamId: team.id.toString(),
          planName: targetPlan,
        },
      },
      // Industry best practice: collect billing address for tax compliance
      billing_address_collection: 'auto',
      // Allow promotion codes
      allow_promotion_codes: true,
    };

    // Only add trial period for new starter subscriptions
    if (shouldIncludeTrial) {
      sessionParams.subscription_data.trial_period_days = 14;
      console.log(`[Upgrade Service] Adding 14-day trial for new starter subscription`);
    }

    // Create checkout session for new subscription
    const session = await stripe.checkout.sessions.create(sessionParams);

    console.log(`[Upgrade Service] Checkout session created: ${session.id} for ${targetPlan}`);

    return {
      success: true,
      requiresPayment: true,
      checkoutUrl: session.url!,
      message: `Redirecting to secure checkout for ${targetPlan} plan`,
    };

  } catch (error: any) {
    console.error('[Upgrade Service] Error creating new subscription:', error);
    return {
      success: false,
      requiresPayment: false,
      error: error.message,
      message: 'Failed to create checkout session. Please try again.',
    };
  }
}

/**
 * Get price ID for plan and billing cycle
 */
function getPriceId(plan: string, billingCycle: string): string | null {
  const priceMap: Record<string, Record<string, string>> = {
    starter: {
      monthly: process.env.STRIPE_STARTER_MONTHLY_PRICE_ID || '',
      yearly: process.env.STRIPE_STARTER_YEARLY_PRICE_ID || '',
    },
    pro: {
      monthly: process.env.STRIPE_PRO_MONTHLY_PRICE_ID || '',
      yearly: process.env.STRIPE_PRO_YEARLY_PRICE_ID || '',
    },
    enterprise: {
      monthly: process.env.STRIPE_ENTERPRISE_MONTHLY_PRICE_ID || '',
      yearly: process.env.STRIPE_ENTERPRISE_YEARLY_PRICE_ID || '',
    },
  };

  return priceMap[plan]?.[billingCycle] || null;
}

/**
 * Check if team has any subscription history to prevent trial abuse
 */
async function checkSubscriptionHistory(teamId: number): Promise<boolean> {
  try {
    // Check for any existing or past subscriptions
    const existingSubscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.teamId, teamId),
    });

    // Check subscription history
    const historyRecord = await db.query.subscriptionHistory.findFirst({
      where: eq(subscriptionHistory.teamId, teamId),
    });

    // If there's any record of subscriptions, they've had a subscription before
    return !!(existingSubscription || historyRecord);
  } catch (error) {
    console.error('[Upgrade Service] Error checking subscription history:', error);
    // If we can't check history, err on the side of caution and don't give trial
    return true;
  }
}

/**
 * Get customer portal URL for subscription management
 */
export async function getCustomerPortalUrl(): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    const user = await getUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const team = await getTeamForUser(user.id);
    if (!team || !team.stripeCustomerId) {
      return { success: false, error: 'No Stripe customer found' };
    }

    const session = await stripe.billingPortal.sessions.create({
      customer: team.stripeCustomerId,
      return_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing`,
    });

    return { success: true, url: session.url };

  } catch (error: any) {
    console.error('[Upgrade Service] Error creating portal session:', error);
    return { success: false, error: error.message };
  }
}
