'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import InvitationProcessor from '@/components/InvitationProcessor';

export default function InvitationsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const inviteId = searchParams.get('inviteId');

  // If no invitation ID is provided, redirect to dashboard
  useEffect(() => {
    if (!inviteId) {
      router.push('/dashboard');
    }
  }, [inviteId, router]);

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Team Invitation</h1>
      <InvitationProcessor />
    </div>
  );
}
