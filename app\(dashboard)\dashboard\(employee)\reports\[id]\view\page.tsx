'use client';

import { use, useEffect, useState } from 'react';
import { useUser } from '@/lib/auth';
import { Button } from '@/components/ui/button';
import { ChevronRight, ChevronUp, X, Bot } from 'lucide-react';
import { formatCaseStudyForDisplay } from '@/lib/utils/format-case-study';
import InlineChat from '@/components/InlineChat';
import { SimilarCaseStudiesClient } from '@/components/SimilarCaseStudiesClient';
import ReactMarkdown from 'react-markdown';

export default function CaseStudyViewPage({
  params: paramsPromise,
}: {
  params: { id: string };
}) {
  const { userPromise } = useUser();
  // We need to use the userPromise to ensure authentication, but don't need the user object
  use(userPromise);
  const params = use(paramsPromise as any) as { id: string };
  const [caseStudy, setCaseStudy] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [expandedTopics, setExpandedTopics] = useState<{
    [key: string]: boolean;
  }>({});
  const [expandedCards, setExpandedCards] = useState<{
    [key: string]: boolean;
  }>({});
  const [similarCaseStudies, setSimilarCaseStudies] = useState<any[]>([]);
  const [showPopup, setShowPopup] = useState(false);
  const [popupContent, setPopupContent] = useState<{
    title: string;
    content: string;
  }>({ title: '', content: '' });

  useEffect(() => {
    const fetchCaseStudy = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/case-studies/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch case study');
        }

        const data = await response.json();
        setCaseStudy(formatCaseStudyForDisplay(data));

        // Fetch similar case studies
        fetchSimilarCaseStudies(data.industry, data.id);
      } catch (error) {
        console.error('Error fetching case study:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCaseStudy();
  }, [params.id]);

  useEffect(() => {
    if (!showPopup) return;

    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closePopup();
      }
    };

    window.addEventListener('keydown', handleEsc);
    return () => window.removeEventListener('keydown', handleEsc);
  }, [showPopup]);

  // Function to fetch similar case studies
  const fetchSimilarCaseStudies = async (
    industry: string,
    currentId: string
  ) => {
    try {
      // Fetch case studies with the same industry, excluding the current one
      // Limit to 4 case studies to match the SimilarCaseStudies component
      const response = await fetch(
        `/api/case-studies?industry=${encodeURIComponent(
          industry || ''
        )}&limit=4&exclude=${currentId}`
      );
      if (!response.ok) throw new Error('Failed to fetch similar case studies');

      const data = await response.json();
      // Ensure we only use up to 4 case studies (1 featured + 3 smaller ones)
      const limitedCaseStudies = data.caseStudies
        ? data.caseStudies.slice(0, 4)
        : [];
      setSimilarCaseStudies(limitedCaseStudies);
    } catch (error) {
      console.error('Error fetching similar case studies:', error);
      setSimilarCaseStudies([]);
    }
  };

  // Helper function to parse market intelligence data
  const parseMarketData = (data: any) => {
    if (!data) return null;

    // If it's already an object, return it
    if (typeof data === 'object') {
      return data;
    }

    // If it's a string, try to parse it as JSON
    if (typeof data === 'string') {
      try {
        return JSON.parse(data);
      } catch (error) {
        console.error('Error parsing market data string:', error);
        return null;
      }
    }

    console.error('Unknown market data type:', typeof data);
    return null;
  };

  if (loading) {
    // Loading skeleton code remains the same
    return (
      <div className='p-6 space-y-8 max-w-7xl mx-auto'>
        {/* Loading UI skeleton preserved from original */}
      </div>
    );
  }

  if (!caseStudy) {
    // Not found UI remains the same
    return (
      <div className='p-6 space-y-8 max-w-7xl mx-auto'>
        {/* Not found UI preserved from original */}
      </div>
    );
  }

  // Parse market intelligence data

  let parsedMarketIntelligence = null;
  let parsedMarketMetrics = null;

  if (caseStudy.marketIntelligenceData) {
    parsedMarketIntelligence = parseMarketData(
      caseStudy.marketIntelligenceData
    );
  } else if (caseStudy.marketIntelligence) {
    parsedMarketIntelligence = caseStudy.marketIntelligence;
  }

  if (caseStudy.marketMetricsData) {
    parsedMarketMetrics = parseMarketData(caseStudy.marketMetricsData);
  } else if (caseStudy.marketMetrics) {
    parsedMarketMetrics = caseStudy.marketMetrics;
  }

  // Define market intelligence and metrics for use in the UI
  const marketIntelligence =
    parsedMarketIntelligence || caseStudy.marketIntelligence || [];
  const marketMetrics = parsedMarketMetrics || caseStudy.marketMetrics || {};

  // Helper function to get card content - returns actual content for truncation
  const getCardMdxContent = (index: number) => {
    if (!marketIntelligence || !marketIntelligence[index]) {
      return ''; // Return empty string if no data available
    }

    const section = marketIntelligence[index];
    let content = '';

    // Start with the description
    if (section.description) {
      content += section.description;
    }

    // Add insights if available
    if (section.insights && section.insights.length > 0) {
      // Add a space if we already have content
      if (content) content += ' ';

      // Add the first insight's summary
      const insight = section.insights[0];
      if (insight.summary) {
        content += insight.summary;
      }
    }

    return content || '';
  };

  // Helper function to get popup content from marketIntelligence data
  const getPopupMdxContent = (
    index: number,
    fallbackTitle: string,
    fallbackContent: string
  ) => {
    if (!marketIntelligence || !marketIntelligence[index]) {
      return fallbackContent; // Return fallback content if no data available
    }

    const section = marketIntelligence[index];
    let content = `# ${section.title || fallbackTitle}\n\n${
      section.description || ''
    }\n\n`;

    // Add insights if available
    if (section.insights && section.insights.length > 0) {
      content += '## Key Insights\n\n';
      section.insights.forEach((insight: any) => {
        // Add source URL as a link if available
        const sourceLink = insight.source_url
          ? `[Source](${insight.source_url})`
          : '';

        content += `- **${insight.type || 'Insight'}**: ${
          insight.summary || ''
        }\n`;
        if (sourceLink) {
          content += `  ${sourceLink}\n`;
        }
      });
    }

    return content;
  };

  // Generate detailed content for popup from marketIntelligence data
  const getDetailedCardContent = (index: number) => {
    if (!marketIntelligence || !marketIntelligence[index]) {
      return {
        title: 'No Data Available',
        content: 'No detailed information available.',
      };
    }

    const section = marketIntelligence[index];
    const title = section.title || 'Market Intelligence';

    // Start with a styled title and description
    let content = `# ${title}\n\n`;

    // Add description with highlighting
    if (section.description) {
      content += `## Description\n\n${section.description}\n\n`;
    }

    // Add insights with full details and better formatting
    if (section.insights && section.insights.length > 0) {
      content += '## Key Insights\n\n';

      section.insights.forEach((insight: any, i: number) => {
        // Format insight type
        content += `### ${i + 1}. ${insight.type || 'Information'}\n\n`;

        // Format the summary with better styling
        if (insight.summary) {
          // Extract key metrics and percentages
          let summaryText = insight.summary;

          // Embed source URL in text if available
          if (insight.source_url) {
            // Find a good spot to embed the link - usually at the end of the summary
            const lastSentenceIndex = summaryText.lastIndexOf('.');
            if (
              lastSentenceIndex > 0 &&
              lastSentenceIndex < summaryText.length - 1
            ) {
              // Insert before the last period
              const sourceName = insight.source || 'Source';
              summaryText =
                summaryText.substring(0, lastSentenceIndex) +
                ` ([${sourceName}](${insight.source_url}))` +
                summaryText.substring(lastSentenceIndex);
            } else {
              // Append at the end
              const sourceName = insight.source || 'Source';
              summaryText += ` ([${sourceName}](${insight.source_url}))`;
            }
          }

          // Create bullet points for better readability
          const sentences = summaryText
            .split('. ')
            .filter((s: any) => s.trim().length > 0);
          if (sentences.length > 1) {
            content +=
              sentences
                .map((s: any) => `- ${s.trim()}${!s.endsWith('.') ? '.' : ''}`)
                .join('\n') + '\n\n';
          } else {
            content += `${summaryText}\n\n`;
          }
        }

        // Add a separator between insights for better readability
        if (i < section.insights.length - 1) {
          content += `---\n\n`;
        }
      });
    }

    return { title, content };
  };

  // Toggle topic expansion
  const toggleTopic = (topicId: string) => {
    setExpandedTopics((prev) => ({
      ...prev,
      [topicId]: !prev[topicId],
    }));
  };

  // Function to open popup with content
  const openPopup = (title: string, content: string) => {
    setPopupContent({ title, content });
    setShowPopup(true);
  };

  // Function to toggle card text expansion
  const toggleCardExpansion = (cardId: string) => {
    setExpandedCards((prev) => ({
      ...prev,
      [cardId]: !prev[cardId],
    }));
  };

  // Function to close popup
  const closePopup = () => {
    setShowPopup(false);
  };

  // Get related topics from case study data
  const relatedTopics = [
    {
      id: 'solution-overview',
      question: caseStudy.solution?.title || 'Solution Overview',
      answer:
        caseStudy.solution?.description ||
        'A comprehensive approach to addressing identified challenges in the industry.',
    },
    {
      id: 'key-challenges',
      question: 'Key Challenges',
      answer:
        caseStudy.introduction?.problems
          ?.map((p: string) => `-- ${p}`)
          .join('\n\n') ||
        'Organizations face multiple challenges in implementing effective solutions.',
    },
    {
      id: 'implementation-approach',
      question: caseStudy.process?.title || 'Implementation Approach',
      answer:
        caseStudy.process?.steps
          ?.map((step: any) => `-- ${step.title}: ${step.description}`)
          .join('\n\n') ||
        'A structured approach to implementation ensures successful outcomes.',
    },
    {
      id: 'business-impact',
      question: caseStudy.impact?.title || 'Business Impact',
      answer:
        caseStudy.impact?.metrics
          ?.map(
            (metric: any) =>
              `-- ${metric.metric}: ${metric.value} - ${metric.description}`
          )
          .join('\n\n') ||
        'Implementation delivers measurable business impact across multiple dimensions.',
    },
  ];

  return (
    <div className='container mx-auto max-w-7xl space-y-6 pb-8 overflow-hidden'>
      <div className='space-y-6'>
        {/* Header and back button */}
        <h1 className='text-3xl font-bold text-gray-900 dark:text-white'>
          {caseStudy.title || 'Case Study'}
        </h1>

        {/* Top metrics section - matching exactly as in the image */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          {/* Market Size */}
          <div className='bg-white dark:bg-black rounded-xl p-6 shadow-sm dark:shadow-gray-800 relative border border-gray-100 dark:border-gray-800 flex flex-col h-[250px]'>
            <div className='mb-2'>
              <div className='text-gray-700 dark:text-gray-300 mb-1'>
                Market Size
              </div>
              <div className='flex items-center gap-2'>
                <span className='text-3xl font-bold text-gray-900 dark:text-white'>
                  {marketMetrics?.marketSize
                    ? `$${(
                        Number(marketMetrics.marketSize) / 1000000000
                      ).toFixed(1)}B`
                    : marketMetrics?.['Market Size ($ Value)']
                    ? `$${(
                        Number(marketMetrics['Market Size ($ Value)']) /
                        1000000000
                      ).toFixed(1)}B`
                    : '$210B'}
                </span>
              </div>
            </div>

            <div
              className='flex-grow overflow-auto'
              style={{ maxHeight: '170px' }}
            >
              <p
                className={`text-sm text-gray-700 dark:text-gray-300 ${
                  !expandedCards['marketSize'] ? 'line-clamp-2' : ''
                }`}
              >
                {getCardMdxContent(0) ||
                  caseStudy.introduction?.text ||
                  'Market size represents the total addressable market for this solution.'}
              </p>
              <Button
                variant='link'
                className='text-blue-600 dark:text-blue-400 p-0 h-auto flex items-center mt-1'
                onClick={() => toggleCardExpansion('marketSize')}
              >
                {expandedCards['marketSize'] ? 'Show Less' : 'Read More'}
              </Button>
            </div>
            <div className='text-sm text-gray-600 dark:text-gray-400 mt-2'>
              {marketIntelligence?.[0]?.insights?.[0]?.source ||
                'Industry Analysis'}
            </div>
            {/* <div className="absolute top-6 right-6">
              <div className="bg-purple-100 dark:bg-purple-900 rounded-xl p-3">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="dark:hidden">
                  <path d="M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z" fill="#D8B4FE" />
                  <path d="M12 8V16M8 12H16" stroke="#6B21A8" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="hidden dark:block">
                  <path d="M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z" fill="#9333EA" />
                  <path d="M12 8V16M8 12H16" stroke="#F5F3FF" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
              </div>
            </div> */}
          </div>

          {/* CAGR */}
          <div className='bg-white dark:bg-black rounded-xl p-6 shadow-sm dark:shadow-gray-800 relative border border-gray-100 dark:border-gray-800 flex flex-col h-[250px]'>
            <div className='mb-2'>
              <div className='text-gray-700 dark:text-gray-300 mb-1'>CAGR</div>
              <div className='flex items-center gap-2'>
                <span className='text-3xl font-bold text-gray-900 dark:text-white'>
                  {marketMetrics?.marketCAGR
                    ? `${marketMetrics.marketCAGR}%`
                    : marketMetrics?.CAGR
                    ? `${marketMetrics.CAGR}%`
                    : '18.3%'}
                </span>
              </div>
            </div>

            <div
              className='flex-grow overflow-auto'
              style={{ maxHeight: '170px' }}
            >
              <p
                className={`text-sm text-gray-700 dark:text-gray-300 ${
                  !expandedCards['cagr'] ? 'line-clamp-2' : ''
                }`}
              >
                {marketIntelligence?.[0]?.insights?.[1]?.summary ||
                  caseStudy.introduction?.problems?.[0] ||
                  'Compound Annual Growth Rate shows strong market expansion potential.'}
              </p>
              <Button
                variant='link'
                className='text-blue-600 dark:text-blue-400 p-0 h-auto flex items-center mt-1'
                onClick={() => toggleCardExpansion('cagr')}
              >
                {expandedCards['cagr'] ? 'Show Less' : 'Read More'}
              </Button>
            </div>
            <div className='text-sm text-gray-600 dark:text-gray-400 mt-2'>
              {marketIntelligence?.[0]?.insights?.[1]?.source ||
                'Market Forecast'}
            </div>
            {/* <div className="absolute top-6 right-6">
              <div className="bg-amber-100 dark:bg-amber-900 rounded-xl p-3">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="dark:hidden">
                  <path d="M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z" fill="#FDE68A" />
                  <path d="M12 8L12 16M8 12H16" stroke="#B45309" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="hidden dark:block">
                  <path d="M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z" fill="#92400E" />
                  <path d="M12 8L12 16M8 12H16" stroke="#FEF3C7" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
              </div>
            </div> */}
          </div>

          {/* ROI Range */}
          <div className='bg-white dark:bg-black rounded-xl p-6 shadow-sm dark:shadow-gray-800 relative border border-gray-100 dark:border-gray-800 flex flex-col h-[250px]'>
            <div className='mb-2'>
              <div className='text-gray-700 dark:text-gray-300 mb-1'>
                ROI Range
              </div>
              <div className='flex items-center gap-2'>
                <span className='text-3xl font-bold text-gray-900 dark:text-white'>
                  {marketMetrics?.marketROI
                    ? `${marketMetrics.marketROI}%`
                    : marketMetrics?.['ROI Range']
                    ? `${marketMetrics['ROI Range']}%`
                    : '60%'}
                </span>
              </div>
            </div>

            <div
              className='flex-grow overflow-auto'
              style={{ maxHeight: '170px' }}
            >
              <p
                className={`text-sm text-gray-700 dark:text-gray-300 ${
                  !expandedCards['roi'] ? 'line-clamp-2' : ''
                }`}
              >
                {marketIntelligence?.[0]?.insights?.[2]?.summary ||
                  caseStudy.conclusion?.text ||
                  'Companies implementing this solution see significant return on investment.'}
              </p>
              <Button
                variant='link'
                className='text-blue-600 dark:text-blue-400 p-0 h-auto flex items-center mt-1'
                onClick={() => toggleCardExpansion('roi')}
              >
                {expandedCards['roi'] ? 'Show Less' : 'Read More'}
              </Button>
            </div>
            <div className='text-sm text-gray-600 dark:text-gray-400 mt-2'>
              {marketIntelligence?.[0]?.insights?.[2]?.source || 'ROI Analysis'}
            </div>
            {/* <div className="absolute top-6 right-6">
              <div className="bg-green-100 dark:bg-green-900 rounded-xl p-3">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="dark:hidden">
                  <path d="M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z" fill="#A7F3D0" />
                  <path d="M8 12L12 16L16 8" stroke="#065F46" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="hidden dark:block">
                  <path d="M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z" fill="#065F46" />
                  <path d="M8 12L12 16L16 8" stroke="#D1FAE5" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
            </div> */}
          </div>
        </div>

        {/* Second row - 3 content cards */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-8'>
          {/* Global Pulse */}
          <div className='bg-white dark:bg-black rounded-xl overflow-hidden shadow-sm dark:shadow-gray-800 border border-gray-100 dark:border-gray-800 border-t-4 border-t-blue-500 dark:border-t-blue-600'>
            <div className='p-6 flex flex-col h-full'>
              <div>
                <div className='flex items-center gap-3 mb-2'>
                  <svg
                    className='h-6 w-6 text-blue-500 dark:text-blue-400'
                    viewBox='0 0 24 24'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M3 13.2L8.5 19L21 5'
                      stroke='currentColor'
                      strokeWidth='2.5'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                  </svg>
                  <h2 className='text-lg font-semibold text-gray-900 dark:text-white'>
                    {marketIntelligence?.[0]?.title || 'Business Impact & ROI'}
                  </h2>
                </div>
                <div className='text-xs text-gray-500 dark:text-gray-400 mb-4'>
                  {marketIntelligence?.[0]?.subtitle ||
                    "Quantifying AI's Value Across Industries"}
                </div>

                <div className='text-sm text-gray-700 dark:text-gray-300 line-clamp-2 mb-1'>
                  {getCardMdxContent(0) ||
                    caseStudy.introduction?.problems?.[1] ||
                    `The ${
                      caseStudy.industry || 'industry'
                    } market is evolving rapidly with new technologies and approaches.`}
                </div>
              </div>

              <div className='flex justify-end items-center mt-auto pt-6'>
                <Button
                  variant='link'
                  className='text-blue-600 dark:text-blue-400 p-0 h-auto flex items-center'
                  onClick={() => {
                    const { title, content } = getDetailedCardContent(0);
                    openPopup(title, content);
                  }}
                >
                  Read More <ChevronRight className='h-4 w-4 ml-1' />
                </Button>
              </div>
            </div>
          </div>

          {/* Business Impact & ROI */}
          <div className='bg-white dark:bg-black rounded-xl overflow-hidden shadow-sm dark:shadow-gray-800 border border-gray-100 dark:border-gray-800 border-t-4 border-t-purple-500 dark:border-t-purple-600'>
            <div className='p-6 flex flex-col h-full'>
              <div>
                <div className='flex items-center gap-3 mb-2'>
                  <svg
                    className='h-6 w-6 text-purple-500 dark:text-purple-400'
                    viewBox='0 0 24 24'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <circle
                      cx='12'
                      cy='12'
                      r='10'
                      stroke='currentColor'
                      strokeWidth='2'
                    />
                    <path
                      d='M12 8V16M8 12H16'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeLinecap='round'
                    />
                  </svg>
                  <h2 className='text-lg font-semibold text-gray-900 dark:text-white'>
                    {marketIntelligence?.[1]?.title || 'NS Insights Hub'}
                  </h2>
                </div>
                <div className='text-xs text-gray-500 dark:text-gray-400 mb-4'>
                  {marketIntelligence?.[1]?.subtitle ||
                    'Expert Perspectives and Deep Dives'}
                </div>

                <div className='text-sm text-gray-700 dark:text-gray-300 line-clamp-2 mb-1'>
                  {getCardMdxContent(1) ||
                    caseStudy.introduction?.problems?.[2] ||
                    `Organizations in the ${
                      caseStudy.industry || 'industry'
                    } sector are seeing significant efficiency gains and cost reductions.`}
                </div>
              </div>

              <div className='flex justify-end items-center mt-auto pt-6'>
                <Button
                  variant='link'
                  className='text-purple-600 dark:text-purple-400 p-0 h-auto flex items-center'
                  onClick={() => {
                    const { title, content } = getDetailedCardContent(1);
                    openPopup(title, content);
                  }}
                >
                  Read More <ChevronRight className='h-4 w-4 ml-1' />
                </Button>
              </div>
            </div>
          </div>

          {/* Investment in (AI Agent) */}
          <div className='bg-white dark:bg-black rounded-xl overflow-hidden shadow-sm dark:shadow-gray-800 border border-gray-100 dark:border-gray-800 border-t-4 border-t-pink-500 dark:border-t-pink-600'>
            <div className='p-6 flex flex-col h-full'>
              <div>
                <div className='flex items-center gap-3 mb-2'>
                  <svg
                    className='h-6 w-6 text-pink-500 dark:text-pink-400'
                    viewBox='0 0 24 24'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <rect
                      x='3'
                      y='3'
                      width='18'
                      height='18'
                      rx='2'
                      stroke='currentColor'
                      strokeWidth='2'
                    />
                    <path
                      d='M8 12H16M12 8V16'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeLinecap='round'
                    />
                  </svg>
                  <h2 className='text-lg font-semibold text-gray-900 dark:text-white'>
                    {marketIntelligence?.[2]?.title ||
                      'Investment in (AI Agent)'}
                  </h2>
                </div>
                <div className='text-xs text-gray-500 dark:text-gray-400 mb-4'>
                  {marketIntelligence?.[2]?.subtitle ||
                    'Tracking AI Trends, Market Growth & Adoption'}
                </div>

                <div className='text-sm text-gray-700 dark:text-gray-300 line-clamp-2 mb-1'>
                  {getCardMdxContent(2) ||
                    caseStudy.introduction?.questions?.[0] ||
                    `Investment in ${
                      caseStudy.title || 'this solution'
                    } is growing as organizations recognize the potential benefits and competitive advantages.`}
                </div>
              </div>

              <div className='flex justify-end items-center mt-auto pt-6'>
                <Button
                  variant='link'
                  className='text-pink-600 dark:text-pink-400 p-0 h-auto flex items-center'
                  onClick={() => {
                    const { title, content } = getDetailedCardContent(2);
                    openPopup(title, content);
                  }}
                >
                  Read More <ChevronRight className='h-4 w-4 ml-1' />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Third row - 3 more content cards */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-8'>
          {/* NS Insights Hub */}
          <div className='bg-white dark:bg-black rounded-xl overflow-hidden shadow-sm dark:shadow-gray-800 border border-gray-100 dark:border-gray-800 border-t-4 border-t-blue-500 dark:border-t-blue-600'>
            <div className='p-6 flex flex-col h-full'>
              <div>
                <div className='flex items-center gap-3 mb-2'>
                  <svg
                    className='h-6 w-6 text-blue-500 dark:text-blue-400'
                    viewBox='0 0 24 24'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M12 3.5V20.5M3.5 12H20.5'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeLinecap='round'
                    />
                  </svg>
                  <h2 className='text-lg font-semibold text-gray-900 dark:text-white'>
                    {marketIntelligence?.[3]?.title || 'Case Studies'}
                  </h2>
                </div>
                <div className='text-xs text-gray-500 dark:text-gray-400 mb-4'>
                  {marketIntelligence?.[3]?.subtitle ||
                    'Case Studies With Realtime Data'}
                </div>

                <div className='text-sm text-gray-700 dark:text-gray-300 line-clamp-2 mb-1'>
                  {getCardMdxContent(3) ||
                    caseStudy.introduction?.questions?.[1] ||
                    `${
                      caseStudy.industry || 'Industry'
                    } leaders are adopting new approaches to gain competitive advantages and improve outcomes.`}
                </div>
              </div>

              <div className='flex justify-end items-center mt-auto pt-6'>
                <Button
                  variant='link'
                  className='text-blue-600 dark:text-blue-400 p-0 h-auto flex items-center'
                  onClick={() => {
                    const { title, content } = getDetailedCardContent(3);
                    openPopup(title, content);
                  }}
                >
                  Read More <ChevronRight className='h-4 w-4 ml-1' />
                </Button>
              </div>
            </div>
          </div>

          {/* Case Studies */}
          <div className='bg-white dark:bg-black rounded-xl overflow-hidden shadow-sm dark:shadow-gray-800 border border-gray-100 dark:border-gray-800 border-t-4 border-t-purple-500 dark:border-t-purple-600'>
            <div className='p-6 flex flex-col h-full'>
              <div>
                <div className='flex items-center gap-3 mb-2'>
                  <svg
                    className='h-6 w-6 text-purple-500 dark:text-purple-400'
                    viewBox='0 0 24 24'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M19 4H5C3.89543 4 3 4.89543 3 6V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V6C21 4.89543 20.1046 4 19 4Z'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                    <path
                      d='M16 2V6M8 2V6M3 10H21'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                  </svg>
                  <h2 className='text-lg font-semibold text-gray-900 dark:text-white'>
                    {marketIntelligence?.[4]?.title || 'Global Pulse'}
                  </h2>
                </div>
                <div className='text-xs text-gray-500 dark:text-gray-400 mb-4'>
                  {marketIntelligence?.[4]?.subtitle ||
                    'Real-World AI Success Stories & Lessons Learned'}
                </div>

                <div className='text-sm text-gray-700 dark:text-gray-300 line-clamp-2 mb-1'>
                  {getCardMdxContent(4) ||
                    caseStudy.process?.steps
                      ?.map((step: any) => step.description)
                      .join(' ') ||
                    `Case studies in the ${
                      caseStudy.industry || 'industry'
                    } sector demonstrate significant benefits from implementing similar solutions.`}
                </div>
              </div>

              <div className='flex justify-end items-center mt-auto pt-6'>
                <Button
                  variant='link'
                  className='text-purple-600 dark:text-purple-400 p-0 h-auto flex items-center'
                  onClick={() => {
                    const { title, content } = getDetailedCardContent(4);
                    openPopup(title, content);
                  }}
                >
                  Read More <ChevronRight className='h-4 w-4 ml-1' />
                </Button>
              </div>
            </div>
          </div>

          {/* Market Intelligence */}
          <div className='bg-white dark:bg-black rounded-xl overflow-hidden shadow-sm dark:shadow-gray-800 border border-gray-100 dark:border-gray-800 border-t-4 border-t-pink-500 dark:border-t-pink-600'>
            <div className='p-6 flex flex-col h-full'>
              <div>
                <div className='flex items-center gap-3 mb-2'>
                  <svg
                    className='h-6 w-6 text-pink-500 dark:text-pink-400'
                    viewBox='0 0 24 24'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M8 19H5C3.89543 19 3 18.1046 3 17V7C3 5.89543 3.89543 5 5 5H19C20.1046 5 21 5.89543 21 7V17C21 18.1046 20.1046 19 19 19H16M12 15V3M12 15L8 11M12 15L16 11'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                  </svg>
                  <h2 className='text-lg font-semibold text-gray-900 dark:text-white'>
                    {marketIntelligence?.[5]?.title || 'Market Intelligence'}
                  </h2>
                </div>
                <div className='text-xs text-gray-500 dark:text-gray-400 mb-4'>
                  {marketIntelligence?.[5]?.subtitle || 'AI Agents'}
                </div>

                <div className='text-sm text-gray-700 dark:text-gray-300 line-clamp-2 mb-1'>
                  {getCardMdxContent(5) ||
                    'AI agents are specialized intelligent systems designed to automate repetitive tasks, adapt to contexts, and enhance efficiency in specific domains.'}
                </div>
              </div>

              <div className='flex justify-end items-center mt-auto pt-6'>
                <Button
                  variant='link'
                  className='text-pink-600 dark:text-pink-400 p-0 h-auto flex items-center'
                  onClick={() => {
                    const { title, content } = getDetailedCardContent(5);
                    openPopup(title, content);
                  }}
                >
                  Read More <ChevronRight className='h-4 w-4 ml-1' />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Related topics section with expandable answers */}
        <div className='bg-white dark:bg-black rounded-xl p-6 mt-8 border border-gray-100 dark:border-gray-900'>
          <h2 className='text-lg font-semibold mb-4 text-black dark:text-white'>
            Other related topics/ Users also search for
          </h2>
          <div className='space-y-2'>
            {relatedTopics.map((topic) => (
              <div
                key={topic.id}
                className='bg-white dark:bg-black rounded-lg shadow-sm dark:shadow-gray-900 overflow-hidden border border-gray-100 dark:border-gray-900'
              >
                <div
                  className='p-4 flex items-center justify-between cursor-pointer'
                  onClick={() => toggleTopic(topic.id)}
                >
                  <span className='text-black dark:text-white'>
                    {topic.question}
                  </span>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='h-8 w-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-900'
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleTopic(topic.id);
                    }}
                  >
                    {expandedTopics[topic.id] ? (
                      <ChevronUp className='h-5 w-5 text-blue-600 dark:text-blue-400' />
                    ) : (
                      <span className='text-blue-600 dark:text-blue-400 text-xl font-semibold'>
                        +
                      </span>
                    )}
                  </Button>
                </div>

                {expandedTopics[topic.id] && (
                  <div className='px-4 pb-4 pt-0 border-t border-gray-100 dark:border-gray-900'>
                    <div className='text-sm text-black dark:text-white prose dark:prose-invert max-w-none'>
                      <ReactMarkdown>{topic.answer}</ReactMarkdown>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        {/* Inline AI Chat section */}
        <div className='mt-6 mb-6'>
          <div className='flex items-center mb-3'>
            <div className='bg-blue-100 dark:bg-blue-900 p-1.5 rounded-full mr-2'>
              <Bot className='h-4 w-4 text-blue-600 dark:text-blue-400' />
            </div>
            <h2 className='text-lg font-bold text-black dark:text-white'>
              Interactive AI Assistant
            </h2>
          </div>
          <div className='bg-white dark:bg-black rounded-xl p-3 border border-gray-100 dark:border-gray-900'>
            <div className='mb-2'>
              <p className='text-sm text-black dark:text-white'>
                Ask questions about this case study to get immediate insights.
                <span className='block mt-1 text-xs text-black dark:text-white italic'>
                  Your conversation is not saved and will be lost when you leave
                  this page.
                </span>
              </p>
            </div>

            <div className='h-[400px]'>
              <InlineChat
                caseStudyId={params.id}
                caseStudyTitle={
                  caseStudy?.title || caseStudy?.useCaseTitle || 'AI Agents'
                }
                caseStudyContent={JSON.stringify({
                  title: caseStudy?.title || caseStudy?.useCaseTitle,
                  description:
                    caseStudy?.description || caseStudy?.shortDescription,
                  problemStatement: caseStudy?.problemStatement,
                  solution: caseStudy?.solution,
                  results: caseStudy?.results,
                  industry: caseStudy?.industry,
                  challenges: caseStudy?.challenges,
                  keyInsights: caseStudy?.keyInsights,
                  marketIntelligence: caseStudy?.marketIntelligenceData,
                  marketMetrics: caseStudy?.marketMetricsData,
                })}
              />
            </div>
          </div>
        </div>

        {/* Similar Case Studies Section */}
        {similarCaseStudies.length > 0 && (
          <div className='mt-6 mb-6'>
            <SimilarCaseStudiesClient similarCaseStudies={similarCaseStudies} />
          </div>
        )}

        {/* Display the actual market intelligence data if needed */}
        {marketIntelligence && marketIntelligence.length > 0 && (
          <div className='hidden'>
            <pre>{JSON.stringify(marketIntelligence, null, 2)}</pre>
          </div>
        )}

        {/* Popup for detailed content */}
        {showPopup && (
          <div
            className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'
            onClick={closePopup}
          >
            <div
              className='bg-white dark:bg-black rounded-lg max-w-3xl w-full max-h-[90vh] overflow-auto border border-gray-100 dark:border-gray-900'
              onClick={(e) => e.stopPropagation()}
            >
              <div className='p-6'>
                <div className='flex justify-between items-center mb-4'>
                  <h2 className='text-xl font-bold text-black dark:text-white'>
                    {popupContent.title}
                  </h2>
                  <Button
                    variant='ghost'
                    size='icon'
                    onClick={closePopup}
                    className='h-8 w-8 text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-900'
                  >
                    <X className='h-5 w-5' />
                  </Button>
                </div>
                <div className='prose dark:prose-invert max-w-none prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-a:no-underline hover:prose-a:underline prose-headings:text-gray-900 dark:prose-headings:text-white prose-strong:text-gray-900 dark:prose-strong:text-white prose-hr:border-gray-200 dark:prose-hr:border-gray-700'>
                  <div className='markdown-content'>
                    <ReactMarkdown
                      components={{
                        a: ({ node, ...props }) => (
                          <a
                            {...props}
                            className='text-blue-600 dark:text-blue-400 font-medium hover:underline'
                            target='_blank'
                            rel='noopener noreferrer'
                          />
                        ),
                        em: ({ node, ...props }) => (
                          <em
                            {...props}
                            className='text-gray-900 dark:text-white font-medium not-italic'
                          />
                        ),
                        strong: ({ node, ...props }) => (
                          <strong
                            {...props}
                            className='text-gray-900 dark:text-white font-bold'
                          />
                        ),
                        h3: ({ node, ...props }) => (
                          <h3
                            {...props}
                            className='text-lg font-bold mt-6 mb-2 text-gray-900 dark:text-white'
                          />
                        ),
                        li: ({ node, ...props }) => (
                          <li
                            {...props}
                            className='my-1 text-gray-700 dark:text-gray-300'
                          />
                        ),
                      }}
                    >
                      {popupContent.content}
                    </ReactMarkdown>
                  </div>
                </div>
                <div className='mt-6 flex justify-end'>
                  <Button
                    onClick={closePopup}
                    className='bg-blue-500 hover:bg-blue-600 text-white'
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
