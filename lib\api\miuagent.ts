import { CaseStudyData } from "@/hooks/useCaseStudies";

// MIUAgent API configuration
const MIUAGENT_API_CONFIG = {
  baseUrl:
    process.env.MIUAGENT_BASE_URL ||
    process.env.MIUAGENT_API_URL ||
    "https://miuagent-api.leafcraftstudios.com",
  apiKey: process.env.MIUAGENT_API_KEY || "",
};

// Fix for typo in domain name if it occurs
if (MIUAGENT_API_CONFIG.baseUrl.includes("leafcraftstudio.com")) {
  MIUAGENT_API_CONFIG.baseUrl = MIUAGENT_API_CONFIG.baseUrl.replace(
    "leafcraftstudio.com",
    "leafcraftstudios.com"
  );
  console.log("Fixed typo in MIUAgent API domain name");
}

// Log MIUAgent configuration on startup
console.log("\n==== MIUAGENT API CONFIGURATION ====");
console.log("Base URL:", MIUAGENT_API_CONFIG.baseUrl);
console.log("API Key configured:", !!MIUAGENT_API_CONFIG.apiKey);
console.log("====================================\n");

// API endpoints based on the server response
const API_ENDPOINTS = {
  triggerInsights: "trigger",
  chat: "/chat",
  health: "/health",
  metrics: "/metrics",
};

// Log API endpoints
console.log("\n==== MIUAGENT API ENDPOINTS ====");
console.log("Trigger Insights:", API_ENDPOINTS.triggerInsights);
console.log(
  "Full Trigger URL:",
  `${MIUAGENT_API_CONFIG.baseUrl}${API_ENDPOINTS.triggerInsights}`
);
console.log("====================================\n");

// Interface for the MIUAgent API request
interface MIUAgentRequest {
  id: string;
  data: string; // Stringified JSON
}

// Interface for the MIUAgent API response
interface MIUAgentResponse {
  success: boolean;
  request_id: string;
  data?: any;
  error?: string;
}

// Convert case study to MIUAgent use case format
export function convertCaseStudyToUseCase(caseStudy: any): any {
  // Parse impact metrics if they exist
  let impactMetrics: Record<string, string> = {};
  try {
    if (caseStudy.potentiallyImpactedKpis) {
      // Try to parse as JSON if it's a string
      if (typeof caseStudy.potentiallyImpactedKpis === 'string') {
        try {
          impactMetrics = JSON.parse(caseStudy.potentiallyImpactedKpis);
        } catch (e) {
          // If it's not valid JSON, treat it as a comma-separated string
          const kpiValues = caseStudy.potentiallyImpactedKpis.split(',').map((kpi: string) => kpi.trim());
          kpiValues.forEach((kpi: string, index: number) => {
            if (kpi) {
              impactMetrics[`kpi_${index + 1}`] = kpi;
            }
          });
        }
      } else if (typeof caseStudy.potentiallyImpactedKpis === 'object') {
        // If it's already an object, use it directly
        impactMetrics = caseStudy.potentiallyImpactedKpis as Record<string, string>;
      }
    }

    // Add potential impact metrics if they exist
    if (caseStudy.potentialImpact1) {
      impactMetrics['impact_1'] = caseStudy.potentialImpact1;
    }
    if (caseStudy.potentialImpact2) {
      impactMetrics['impact_2'] = caseStudy.potentialImpact2;
    }
    if (caseStudy.potentialImpact3) {
      impactMetrics['impact_3'] = caseStudy.potentialImpact3;
    }
    if (caseStudy.potentialImpact4) {
      impactMetrics['impact_4'] = caseStudy.potentialImpact4;
    }
  } catch (error) {
    console.error('Error parsing impact metrics:', error);
    impactMetrics = {};
  }

  // Get current date for created_at if not provided
  const createdAt = caseStudy.createdAt || new Date().toISOString();

  // Determine status based on available data
  const status = caseStudy.aiProcessed ? 'processed' : 'pending';

  // Construct the use case object according to the required format
  return {
    use_case_id: `case-${caseStudy.id}`,
    title: caseStudy.useCaseTitle || '',
    short_description: caseStudy.introductionText || '',
    problem_statement: [
      caseStudy.challange1,
      caseStudy.challange2,
      caseStudy.challange3,
    ]
      .filter(Boolean)
      .join("\n\n"),
    expected_impact: caseStudy.impactTitle || '',
    stakeholders: caseStudy.role || '',
    category_id: caseStudy.vector || '',
    tags: caseStudy.industry ? [caseStudy.industry] : [],
    target_audience: caseStudy.role || '',
    objectives: caseStudy.introductionTitle || '',
    location_scope: 'global', // Default value
    industry: caseStudy.industry || '',
    timeframe: '3-6 months', // Default value
    complexity_level: 'medium', // Default value
    resources_needed: '',
    impact_metrics: impactMetrics,
    status: status,
    created_at: createdAt
  };
}

// Update case study with MIUAgent response data
export function updateCaseStudyWithMIUAgentData(
  caseStudy: any,
  miuAgentData: any
): any {
  try {
    // Log the received data for debugging
    console.log("\n==== UPDATING CASE STUDY WITH MIUAGENT DATA ====");
    console.log("Case Study ID:", caseStudy.id);
    console.log("MIUAgent Data Type:", typeof miuAgentData);

    // If miuAgentData is null or undefined, return the original case study
    if (!miuAgentData) {
      console.log('No response data to update with');
      return caseStudy;
    }

    // If miuAgentData is a string, try to parse it as JSON
    let parsedData = miuAgentData;
    if (typeof miuAgentData === 'string') {
      try {
        console.log('Parsing response data as JSON');
        parsedData = JSON.parse(miuAgentData);
        console.log('Successfully parsed response data');
        console.log("Parsed Data Structure:", Object.keys(parsedData));
      } catch (error) {
        console.error('Error parsing response data as JSON:', error);
        return caseStudy;
      }
    } else {
      console.log("MIUAgent Data Structure:", Object.keys(miuAgentData));
    }

    // Map MIUAgent response data to case study fields
    const updatedCaseStudy = {
      ...caseStudy,
      // Update fields based on MIUAgent response
      industry: parsedData.industry || caseStudy.industry,
      role: parsedData.role || caseStudy.role,

      // Update introduction and challenges if provided
      introductionTitle:
        parsedData.introduction?.title || caseStudy.introductionTitle,
      introductionText:
        parsedData.introduction?.text || caseStudy.introductionText,

      // Update challenges if provided
      challange1: parsedData.challenges?.[0] || caseStudy.challange1,
      challange2: parsedData.challenges?.[1] || caseStudy.challange2,
      challange3: parsedData.challenges?.[2] || caseStudy.challange3,

    // Update process steps if provided
    processTitle: parsedData.process?.title || caseStudy.processTitle,
    processStep1Title:
      parsedData.process?.steps?.[0]?.title || caseStudy.processStep1Title,
    processStep1Description:
      parsedData.process?.steps?.[0]?.description ||
      caseStudy.processStep1Description,
    processStep2Title:
      parsedData.process?.steps?.[1]?.title || caseStudy.processStep2Title,
    processStep2Description:
      parsedData.process?.steps?.[1]?.description ||
      caseStudy.processStep2Description,
    processStep3Title:
      parsedData.process?.steps?.[2]?.title || caseStudy.processStep3Title,
    processStep3Description:
      parsedData.process?.steps?.[2]?.description ||
      caseStudy.processStep3Description,

    // Update solution if provided
    solutionTitle: parsedData.solution?.title || caseStudy.solutionTitle,
    solutionDescription:
      parsedData.solution?.description || caseStudy.solutionDescription,
    solution1Title:
      parsedData.solution?.items?.[0]?.title || caseStudy.solution1Title,
    solution1Description:
      parsedData.solution?.items?.[0]?.description ||
      caseStudy.solution1Description,
    solution2Title:
      parsedData.solution?.items?.[1]?.title || caseStudy.solution2Title,
    solution2Description:
      parsedData.solution?.items?.[1]?.description ||
      caseStudy.solution2Description,
    solution3Title:
      parsedData.solution?.items?.[2]?.title || caseStudy.solution3Title,
    solution3Description:
      parsedData.solution?.items?.[2]?.description ||
      caseStudy.solution3Description,

    // Update impact if provided
    impactTitle: parsedData.impact?.title || caseStudy.impactTitle,
    potentialImpact1:
      parsedData.impact?.metrics?.[0]?.description ||
      caseStudy.potentialImpact1,
    potentialImpact2:
      parsedData.impact?.metrics?.[1]?.description ||
      caseStudy.potentialImpact2,
    potentialImpact3:
      parsedData.impact?.metrics?.[2]?.description ||
      caseStudy.potentialImpact3,
    potentialImpact4:
      parsedData.impact?.metrics?.[3]?.description ||
      caseStudy.potentialImpact4,

    // Update conclusion if provided
    conclusionTitle:
      parsedData.conclusion?.title || caseStudy.conclusionTitle,
    conclusionText: parsedData.conclusion?.text || caseStudy.conclusionText,

    // Store the market intelligence data
    marketIntelligenceData: (() => {
      try {
        // First check for MarketIntelligenceSection
        if (parsedData.MarketIntelligenceSection) {
          // Handle both array and string formats
          if (typeof parsedData.MarketIntelligenceSection === "string") {
            return parsedData.MarketIntelligenceSection;
          } else {
            // Ensure we have valid JSON by sanitizing any problematic characters
            const sanitized = JSON.stringify(parsedData.MarketIntelligenceSection)
              .replace(/\\u0000/g, '') // Remove null bytes
              .replace(/\\u0008/g, '') // Remove backspace
              .replace(/\\u000B/g, '') // Remove vertical tab
              .replace(/\\u000C/g, '') // Remove form feed
              .replace(/[\x00-\x1F\x7F]/g, ''); // Remove control characters

            return sanitized;
          }
        }
        // Then check for market_intelligence
        else if (parsedData.market_intelligence) {
          if (typeof parsedData.market_intelligence === "string") {
            return parsedData.market_intelligence;
          } else {
            const sanitized = JSON.stringify(parsedData.market_intelligence)
              .replace(/\\u0000/g, '')
              .replace(/\\u0008/g, '')
              .replace(/\\u000B/g, '')
              .replace(/\\u000C/g, '')
              .replace(/[\x00-\x1F\x7F]/g, '');

            return sanitized;
          }
        }
        return null;
      } catch (error) {
        console.error('Error processing market intelligence data:', error);
        return null;
      }
    })(),

    // Store the market metrics data
    marketMetricsData: (() => {
      try {
        // First check for MetricsDashboardSection
        if (parsedData.MetricsDashboardSection) {
          // Handle both object and string formats
          if (typeof parsedData.MetricsDashboardSection === "string") {
            return parsedData.MetricsDashboardSection;
          } else {
            // Ensure we have valid JSON by sanitizing any problematic characters
            const sanitized = JSON.stringify(parsedData.MetricsDashboardSection)
              .replace(/\\u0000/g, '')
              .replace(/\\u0008/g, '')
              .replace(/\\u000B/g, '')
              .replace(/\\u000C/g, '')
              .replace(/[\x00-\x1F\x7F]/g, '');

            return sanitized;
          }
        }
        // Then check for market_metrics
        else if (parsedData.market_metrics) {
          if (typeof parsedData.market_metrics === "string") {
            return parsedData.market_metrics;
          } else {
            const sanitized = JSON.stringify(parsedData.market_metrics)
              .replace(/\\u0000/g, '')
              .replace(/\\u0008/g, '')
              .replace(/\\u000B/g, '')
              .replace(/\\u000C/g, '')
              .replace(/[\x00-\x1F\x7F]/g, '');

            return sanitized;
          }
        }
        return null;
      } catch (error) {
        console.error('Error processing market metrics data:', error);
        return null;
      }
    })(),

    // Store individual market metrics for easier querying
    // Convert market size to integer to avoid PostgreSQL bigint error
    marketSize: (() => {
      const marketSizeValue = parsedData.MetricsDashboardSection?.["Market Size ($ Value)"] ||
        parsedData.market_size;

      if (marketSizeValue === null || marketSizeValue === undefined) {
        return null;
      }

      try {
        // If it's a number with decimal places, convert to integer
        if (typeof marketSizeValue === 'number') {
          return Math.floor(marketSizeValue);
        }
        // If it's a string, parse it and convert to integer
        else if (typeof marketSizeValue === 'string') {
          return Math.floor(parseFloat(marketSizeValue));
        }
        return null;
      } catch (error) {
        console.error('Error converting market size to integer:', error);
        return null;
      }
    })(),

    // Handle CAGR as numeric value
    marketCAGR: (() => {
      const cagrValue = parsedData.MetricsDashboardSection?.["CAGR"] ||
        parsedData.market_cagr;

      if (cagrValue === null || cagrValue === undefined) {
        return null;
      }

      try {
        // Ensure it's a valid numeric value
        if (typeof cagrValue === 'number') {
          return cagrValue;
        } else if (typeof cagrValue === 'string') {
          return parseFloat(cagrValue);
        }
        return null;
      } catch (error) {
        console.error('Error converting CAGR to numeric:', error);
        return null;
      }
    })(),

    // Handle ROI as numeric value
    marketROI: (() => {
      const roiValue = parsedData.MetricsDashboardSection?.["ROI Range"] ||
        parsedData.market_roi;

      if (roiValue === null || roiValue === undefined) {
        return null;
      }

      try {
        // Ensure it's a valid numeric value
        if (typeof roiValue === 'number') {
          return roiValue;
        } else if (typeof roiValue === 'string') {
          return parseFloat(roiValue);
        }
        return null;
      } catch (error) {
        console.error('Error converting ROI to numeric:', error);
        return null;
      }
    })(),

    // Update timestamp
    updatedAt: new Date(),
  };

  // Log the updated fields for debugging
  console.log(
    "Market Intelligence Data:",
    updatedCaseStudy.marketIntelligenceData ? "Present" : "Not present"
  );
  console.log(
    "Market Metrics Data:",
    updatedCaseStudy.marketMetricsData ? "Present" : "Not present"
  );
  console.log("Market Size:", updatedCaseStudy.marketSize);
  console.log("Market CAGR:", updatedCaseStudy.marketCAGR);
  console.log("Market ROI:", updatedCaseStudy.marketROI);
  console.log("====================================\n");

  return updatedCaseStudy;
  } catch (error) {
    console.error('Error updating case study with MIUAgent data:', error);
    return caseStudy; // Return the original case study if there's an error
  }
}

// Send a single case study to MIUAgent API
export async function sendCaseStudyToMIUAgent(
  caseStudy: any,
  options: {
    timeout?: number;
    verboseLogging?: boolean;
  } = {}
): Promise<MIUAgentResponse> {
  const {
    timeout = 60000, // 60 seconds default timeout
    verboseLogging = true
  } = options;

  try {
    if (verboseLogging) {
      console.log(
        `\n[MIUAgent] Processing case study ID: ${caseStudy.id}, Title: ${caseStudy.useCaseTitle}`
      );
    }

    // Convert the case study to the required format
    const useCase = convertCaseStudyToUseCase(caseStudy);

    // Convert the case study to JSON string format as required
    const useCaseString = JSON.stringify(useCase);

    // Create the request body with the required format
    const requestBody: MIUAgentRequest = {
      id: `req-${caseStudy.id}-${Date.now()}`,
      data: useCaseString,
    };

    if (verboseLogging) {
      // Log the stringified data for verification
      console.log("\n==== STRINGIFIED USE CASE DATA ====");
      console.log("JSON String Length:", useCaseString.length);
      console.log("First 100 chars:", useCaseString.substring(0, 100) + "...");
      console.log("================================\n");

      // Log the request data
      console.log("\n==== SENDING TO MIUAGENT API ====");
      console.log("Request ID:", requestBody.id);
      console.log("API URL:", `${MIUAGENT_API_CONFIG.baseUrl}${API_ENDPOINTS.triggerInsights}`);
      console.log("================================\n");

      // Log API request details
      console.log("\n==== SENDING API REQUEST ====");
      console.log("API URL:", `${MIUAGENT_API_CONFIG.baseUrl}${API_ENDPOINTS.triggerInsights}`);
      console.log("API Key configured:", !!MIUAGENT_API_CONFIG.apiKey);
      console.log("API Key length:", MIUAGENT_API_CONFIG.apiKey.length);
      console.log("Request body size:", JSON.stringify(requestBody).length, "bytes");
      console.log("============================\n");
    }

    // Prepare the final request body
    const finalRequestBody = JSON.stringify(requestBody);

    if (verboseLogging) {
      // Log the final request body for verification
      console.log("Final request body (first 100 chars):", finalRequestBody.substring(0, 100) + "...");
    }

    // Create a promise that will reject after the timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timed out after ${timeout}ms`));
      }, timeout);
    });

    // Create the actual API call promise
    const fetchPromise = async () => {
      const response = await fetch(
        `${MIUAGENT_API_CONFIG.baseUrl}${API_ENDPOINTS.triggerInsights}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${MIUAGENT_API_CONFIG.apiKey}`,
            "Content-Type": "application/json",
          },
          body: finalRequestBody,
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`MIUAgent API error: ${response.status} ${errorText}`);
        throw new Error(`MIUAgent API error: ${response.status} ${errorText}`);
      }

      if (verboseLogging) {
        console.log("API Response Status:", response.status);
      }

      return await response.json();
    };

    // Race the API call against the timeout
    const responseData: MIUAgentResponse = await Promise.race([fetchPromise(), timeoutPromise]);

    if (verboseLogging) {
      // Log the response data
      console.log("\n==== RECEIVED FROM MIUAGENT API ====");
      console.log("Response Success:", responseData.success);
      console.log("Response Request ID:", responseData.request_id);
      console.log("Has Data:", !!responseData.data);
    }

    if (responseData.data) {
      if (verboseLogging) {
        console.log("Data Type:", typeof responseData.data);
      }

      // If the data is a string, try to parse it as JSON
      if (typeof responseData.data === 'string') {
        try {
          const parsedData = JSON.parse(responseData.data);

          if (verboseLogging) {
            console.log("Parsed Data Keys:", Object.keys(parsedData));
          }

          // Replace the string data with the parsed object for easier processing
          responseData.data = parsedData;
        } catch (error) {
          console.error("Error parsing response data as JSON:", error);

          if (verboseLogging) {
            console.log("Raw data (first 100 chars):", responseData.data.substring(0, 100) + "...");
          }
        }
      } else if (typeof responseData.data === 'object' && verboseLogging) {
        console.log("Data Keys:", Object.keys(responseData.data));
      }
    }

    if (verboseLogging) {
      console.log("====================================\n");
    }

    return responseData;
  } catch (error) {
    console.error("Error sending case study to MIUAgent:", error);
    return {
      success: false,
      request_id: `req-${caseStudy.id}-${Date.now()}`,
      error: error instanceof Error ? error.message : "Unknown error",
      data: null
    };
  }
}

// Helper functions for batch processing
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Retry function with exponential backoff
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 1000
): Promise<T> {
  let retries = 0;

  while (true) {
    try {
      return await fn();
    } catch (error) {
      retries++;
      if (retries > maxRetries) {
        throw error;
      }

      // Calculate exponential backoff delay
      const backoffDelay = initialDelay * Math.pow(2, retries - 1);
      // Add some jitter to prevent all retries happening at the same time
      const jitter = Math.random() * 200;
      const totalDelay = backoffDelay + jitter;

      console.log(`Retry ${retries}/${maxRetries} after ${Math.round(totalDelay)}ms`);
      await delay(totalDelay);
    }
  }
}

// Process a single case study with retry logic and timeout
async function processSingleCaseStudyWithRetry(
  caseStudy: any,
  index: number,
  total: number,
  options: {
    maxRetries?: number;
    initialRetryDelay?: number;
    requestTimeout?: number;
    verboseLogging?: boolean;
  } = {}
): Promise<{
  id: number;
  success: boolean;
  requestId?: string;
  error?: string;
  data?: any;
}> {
  const {
    maxRetries = 3,
    initialRetryDelay = 1000,
    requestTimeout = 60000, // 60 seconds default timeout
    verboseLogging = true,
  } = options;

  if (verboseLogging) {
    console.log(
      `\n[MIUAgent Batch] Processing case ${index + 1} of ${total}: ID ${caseStudy.id}`
    );
  }

  try {
    // Create the actual API call promise with retry logic
    const apiCallPromise = retryWithBackoff(
      async () => await sendCaseStudyToMIUAgent(caseStudy, {
        timeout: requestTimeout,
        verboseLogging: verboseLogging
      }),
      maxRetries,
      initialRetryDelay
    );

    // Execute the API call with retry logic (timeout is handled inside sendCaseStudyToMIUAgent)
    const response = await apiCallPromise;

    if (response.success && response.data) {
      // Update the case study with the MIUAgent data
      const updatedData = updateCaseStudyWithMIUAgentData(
        caseStudy,
        response.data
      );

      // Only log detailed changes if verbose logging is enabled
      if (verboseLogging) {
        console.log("\n==== CHANGES TO BE APPLIED TO DATABASE ====");
        const changes: Record<string, { from: any; to: any }> = {};
        for (const key in updatedData) {
          if (updatedData[key] !== caseStudy[key]) {
            changes[key] = {
              from: caseStudy[key],
              to: updatedData[key],
            };
          }
        }
        // Only log a summary of changes to save memory
        console.log(`Fields changed: ${Object.keys(changes).length}`);
        console.log(`Fields: ${Object.keys(changes).join(', ')}`);
        console.log("=========================================\n");
      }

      if (verboseLogging) {
        console.log(
          `[MIUAgent Batch] Successfully processed case study ID: ${caseStudy.id}`
        );
      }

      return {
        id: caseStudy.id,
        success: true,
        requestId: response.request_id,
        data: response.data, // Include the data in the result
      };
    } else {
      if (verboseLogging) {
        console.error(
          `[MIUAgent Batch] Failed to process case study ID: ${
            caseStudy.id
          }. Error: ${response.error || "Unknown error"}`
        );
      }

      return {
        id: caseStudy.id,
        success: false,
        requestId: response.request_id,
        error: response.error || "Unknown error",
      };
    }
  } catch (error) {
    if (verboseLogging) {
      console.error(
        `[MIUAgent Batch] Exception processing case study ID: ${caseStudy.id}. Error:`,
        error
      );
    }

    return {
      id: caseStudy.id,
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      data: null
    };
  }
}

// Process a chunk of case studies with controlled concurrency
async function processChunk(
  chunk: any[],
  startIndex: number,
  totalCases: number,
  concurrency: number = 3,
  options: {
    maxRetries?: number;
    initialRetryDelay?: number;
    requestTimeout?: number;
    verboseLogging?: boolean;
  } = {}
): Promise<Array<{
  id: number;
  success: boolean;
  requestId?: string;
  error?: string;
  data?: any;
}>> {
  console.log(`[MIUAgent Batch] Processing chunk of ${chunk.length} cases (${startIndex + 1}-${startIndex + chunk.length} of ${totalCases})`);

  // Process in batches with controlled concurrency
  const results: Array<{
    id: number;
    success: boolean;
    requestId?: string;
    error?: string;
    data?: any;
  }> = [];

  // Process in batches of 'concurrency' size
  for (let i = 0; i < chunk.length; i += concurrency) {
    const batch = chunk.slice(i, i + concurrency);
    const batchPromises = batch.map((caseStudy, batchIndex) =>
      processSingleCaseStudyWithRetry(
        caseStudy,
        startIndex + i + batchIndex,
        totalCases,
        options
      )
    );

    // Wait for all promises in this batch to resolve
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Add a small delay between batches to avoid overwhelming the API
    if (i + concurrency < chunk.length) {
      await delay(500);
    }
  }

  return results;
}

// Process multiple case studies in batch with chunking and concurrency
export async function processCaseStudiesBatch(
  caseStudies: any[],
  options: {
    chunkSize?: number;
    concurrency?: number;
    maxRetries?: number;
    initialRetryDelay?: number;
    requestTimeout?: number;
    verboseLogging?: boolean;
  } = {}
): Promise<{
  processed: number;
  successful: number;
  failed: number;
  results: Array<{
    id: number;
    success: boolean;
    requestId?: string;
    error?: string;
    data?: any;
  }>;
}> {
  const {
    chunkSize = 50,
    concurrency = 3,
    maxRetries = 3,
    initialRetryDelay = 1000,
    requestTimeout = 60000,
    verboseLogging = true,
  } = options;

  console.log(
    `\n[MIUAgent Batch] Starting batch processing of ${caseStudies.length} case studies`
  );
  console.log(`[MIUAgent Batch] Configuration: chunkSize=${chunkSize}, concurrency=${concurrency}, maxRetries=${maxRetries}`);

  const results: Array<{
    id: number;
    success: boolean;
    requestId?: string;
    error?: string;
    data?: any;
  }> = [];

  // Process in chunks to avoid memory issues with large batches
  for (let i = 0; i < caseStudies.length; i += chunkSize) {
    const chunk = caseStudies.slice(i, i + chunkSize);

    // Process this chunk with controlled concurrency
    const chunkResults = await processChunk(
      chunk,
      i,
      caseStudies.length,
      concurrency,
      {
        maxRetries,
        initialRetryDelay,
        requestTimeout,
        verboseLogging,
      }
    );

    results.push(...chunkResults);

    // Add a delay between chunks to allow system to recover
    if (i + chunkSize < caseStudies.length) {
      console.log(`[MIUAgent Batch] Completed chunk ${i / chunkSize + 1}. Waiting 2 seconds before next chunk...`);
      await delay(2000);
    }
  }

  // Count successful and failed results
  const successful = results.filter(r => r.success).length;
  const failed = results.length - successful;

  console.log(
    `\n[MIUAgent Batch] Batch processing completed. Processed: ${results.length}, Successful: ${successful}, Failed: ${failed}`
  );

  return {
    processed: results.length,
    successful,
    failed,
    results,
  };
}
