/**
 * Optimized storage utilities with:
 * - Type safety
 * - Error handling
 * - JSON serialization
 * - Memory caching
 * - Expiration support
 * - Performance monitoring
 */

// Type for storage configuration
type StorageConfig = {
  storage?: 'local' | 'session';
  expiry?: number; // Expiration in milliseconds
  serializer?: <T>(value: T) => string;
  deserializer?: <T>(value: string) => T;
};

// Type for cached storage items
type CachedItem<T> = {
  value: T;
  expiry?: number;
};

// In-memory cache to reduce storage operations
const memoryCache = new Map<string, any>();

// Default serializer and deserializer
const defaultSerializer = <T>(value: T): string => JSON.stringify(value);
const defaultDeserializer = <T>(value: string): T => JSON.parse(value);

/**
 * Get storage object based on type
 */
function getStorage(type: 'local' | 'session' = 'local'): Storage {
  if (typeof window === 'undefined') {
    throw new Error('Storage is not available in server-side rendering');
  }
  return type === 'local' ? window.localStorage : window.sessionStorage;
}

/**
 * Enhanced getItem with type safety, error handling, and caching
 */
export function getStorageItem<T>(
  key: string,
  config: StorageConfig = {}
): T | null {
  try {
    // Use memory cache if available to avoid storage access
    if (memoryCache.has(key)) {
      const cachedItem = memoryCache.get(key) as CachedItem<T>;
      
      // Check if item has expired
      if (cachedItem.expiry && cachedItem.expiry < Date.now()) {
        memoryCache.delete(key);
        removeStorageItem(key, config);
        return null;
      }
      
      return cachedItem.value;
    }
    
    // Set defaults
    const {
      storage = 'local',
      deserializer = defaultDeserializer,
    } = config;
    
    // Get from storage
    const value = getStorage(storage).getItem(key);
    
    if (value === null) {
      return null;
    }
    
    // Parse the stored string
    const parsed = deserializer<{ value: T; expiry?: number }>(value);
    
    // Check if the item has expired
    if (parsed.expiry && parsed.expiry < Date.now()) {
      removeStorageItem(key, config);
      return null;
    }
    
    // Cache the result in memory
    memoryCache.set(key, parsed);
    
    return parsed.value;
  } catch (error) {
    console.error(`Error getting storage item ${key}:`, error);
    // Clean up potentially corrupted entries
    try {
      removeStorageItem(key, config);
    } catch (e) {
      // Ignore cleanup errors
    }
    return null;
  }
}

/**
 * Enhanced setItem with type safety, error handling, and caching
 */
export function setStorageItem<T>(
  key: string,
  value: T,
  config: StorageConfig = {}
): boolean {
  try {
    // Set defaults
    const {
      storage = 'local',
      expiry,
      serializer = defaultSerializer,
    } = config;
    
    // Create the storage item
    const item: CachedItem<T> = {
      value,
      expiry: expiry ? Date.now() + expiry : undefined,
    };
    
    // Save in memory cache first
    memoryCache.set(key, item);
    
    // Save to storage
    getStorage(storage).setItem(key, serializer(item));
    
    return true;
  } catch (error) {
    console.error(`Error setting storage item ${key}:`, error);
    return false;
  }
}

/**
 * Enhanced removeItem with error handling and cache clearing
 */
export function removeStorageItem(
  key: string,
  config: StorageConfig = {}
): boolean {
  try {
    // Remove from memory cache
    memoryCache.delete(key);
    
    // Set defaults
    const { storage = 'local' } = config;
    
    // Remove from storage
    getStorage(storage).removeItem(key);
    
    return true;
  } catch (error) {
    console.error(`Error removing storage item ${key}:`, error);
    return false;
  }
}

/**
 * Clear all storage items with error handling
 */
export function clearStorage(
  config: StorageConfig = {}
): boolean {
  try {
    // Clear memory cache
    memoryCache.clear();
    
    // Set defaults
    const { storage = 'local' } = config;
    
    // Clear storage
    getStorage(storage).clear();
    
    return true;
  } catch (error) {
    console.error(`Error clearing storage:`, error);
    return false;
  }
}

/**
 * Check if a key exists in storage with caching
 */
export function hasStorageItem(
  key: string,
  config: StorageConfig = {}
): boolean {
  try {
    // Check memory cache first
    if (memoryCache.has(key)) {
      const cachedItem = memoryCache.get(key);
      
      // Check if item has expired
      if (cachedItem.expiry && cachedItem.expiry < Date.now()) {
        memoryCache.delete(key);
        removeStorageItem(key, config);
        return false;
      }
      
      return true;
    }
    
    // Set defaults
    const { storage = 'local' } = config;
    
    // Check storage
    return getStorage(storage).getItem(key) !== null;
  } catch (error) {
    console.error(`Error checking storage item ${key}:`, error);
    return false;
  }
}

/**
 * Get all storage keys
 */
export function getStorageKeys(
  config: StorageConfig = {}
): string[] {
  try {
    // Set defaults
    const { storage = 'local' } = config;
    
    // Get all keys
    const storage_ = getStorage(storage);
    const keys: string[] = [];
    
    for (let i = 0; i < storage_.length; i++) {
      const key = storage_.key(i);
      if (key !== null) {
        keys.push(key);
      }
    }
    
    return keys;
  } catch (error) {
    console.error(`Error getting storage keys:`, error);
    return [];
  }
}

/**
 * Get storage usage statistics
 */
export function getStorageStats(
  config: StorageConfig = {}
): {
  count: number;
  size: number;
  usage: number;
  limit: number;
} {
  try {
    // Set defaults
    const { storage = 'local' } = config;
    
    // Get storage
    const storage_ = getStorage(storage);
    const keys = getStorageKeys({ storage });
    
    let size = 0;
    
    // Calculate total size
    for (const key of keys) {
      const value = storage_.getItem(key);
      if (value !== null) {
        size += key.length + value.length;
      }
    }
    
    // Estimate storage limit (5MB for most browsers)
    const limit = 5 * 1024 * 1024;
    
    return {
      count: keys.length,
      size, // Size in bytes
      usage: size / limit, // Usage percentage
      limit,
    };
  } catch (error) {
    console.error(`Error getting storage stats:`, error);
    return {
      count: 0,
      size: 0,
      usage: 0,
      limit: 5 * 1024 * 1024,
    };
  }
}

/**
 * Clean up expired items
 */
export function cleanExpiredItems(
  config: StorageConfig = {}
): number {
  try {
    // Set defaults
    const { storage = 'local' } = config;
    
    // Get all keys
    const keys = getStorageKeys({ storage });
    let cleanedCount = 0;
    
    // Check each item
    for (const key of keys) {
      const value = getStorageItem(key, config);
      if (value === null) {
        cleanedCount++;
      }
    }
    
    return cleanedCount;
  } catch (error) {
    console.error(`Error cleaning expired items:`, error);
    return 0;
  }
}
