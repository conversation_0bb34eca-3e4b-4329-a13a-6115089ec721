import { sql } from 'drizzle-orm';
import dotenv from 'dotenv';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';

// Load environment variables from .env file
dotenv.config();

async function createPlansSchema() {
  console.log('Creating plans schema...');

  // Check if POSTGRES_URL is set
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not set');
  }

  console.log('Connecting to database...');

  // Create a new connection to the database
  const migrationClient = postgres(process.env.POSTGRES_URL, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Create plans table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS plans (
        id SERIAL PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        price_monthly INTEGER,
        price_yearly INTEGER,
        stripe_price_id_monthly VARCHAR(100),
        stripe_price_id_yearly VARCHAR(100),
        stripe_product_id VARCHAR(100),
        features JSONB NOT NULL,
        max_users INTEGER NOT NULL DEFAULT 1,
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_public BOOLEAN NOT NULL DEFAULT true,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `);
    console.log('Plans table created successfully');

    // Create user_subscriptions table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS user_subscriptions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        team_id INTEGER NOT NULL REFERENCES teams(id),
        plan_id INTEGER NOT NULL REFERENCES plans(id),
        stripe_subscription_id VARCHAR(100),
        stripe_customer_id VARCHAR(100),
        status VARCHAR(50) NOT NULL,
        current_period_start TIMESTAMP NOT NULL,
        current_period_end TIMESTAMP NOT NULL,
        cancel_at_period_end BOOLEAN NOT NULL DEFAULT false,
        canceled_at TIMESTAMP,
        trial_start TIMESTAMP,
        trial_end TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `);
    console.log('User subscriptions table created successfully');

    // Create subscription_items table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS subscription_items (
        id SERIAL PRIMARY KEY,
        subscription_id INTEGER NOT NULL REFERENCES user_subscriptions(id),
        stripe_item_id VARCHAR(100) NOT NULL,
        stripe_price_id VARCHAR(100) NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 1,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `);
    console.log('Subscription items table created successfully');

    // Create subscription_history table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS subscription_history (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        team_id INTEGER NOT NULL REFERENCES teams(id),
        plan_id INTEGER NOT NULL REFERENCES plans(id),
        stripe_subscription_id VARCHAR(100),
        action VARCHAR(50) NOT NULL,
        previous_status VARCHAR(50),
        new_status VARCHAR(50),
        previous_plan_id INTEGER REFERENCES plans(id),
        metadata JSONB,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `);
    console.log('Subscription history table created successfully');

    // First, clear any existing plans to avoid duplicates
    await db.execute(sql`DELETE FROM plans WHERE name IN ('starter', 'pro', 'enterprise')`);
    console.log('Cleared existing plans');

    // Insert updated plans with correct pricing and features
    await db.execute(sql`
      INSERT INTO plans (
        name,
        display_name,
        description,
        price_monthly,
        price_yearly,
        stripe_price_id_monthly,
        stripe_price_id_yearly,
        stripe_product_id,
        features,
        max_users,
        is_active,
        is_public
      ) VALUES
      (
        'starter',
        'Starter',
        'Perfect for individuals getting started with AI - 14 day free trial',
        1200,
        NULL,
        'price_1RKdR9IiRJwYTHShansVCelM',
        NULL,
        'prod_SF7gtzlTQKtCGy',
        '{"trial": {"enabled": true, "days": 14, "description": "14-day free trial"}, "caseStudies": {"limit": 10, "description": "Access to 10 curated AI use cases"}, "users": {"limit": 1, "description": "1 registered user"}, "features": [
          {"name": "14-day free trial", "included": true},
          {"name": "Access to 10 curated AI use cases", "included": true},
          {"name": "Preview of Market Intelligence Unit (MIU)", "included": true},
          {"name": "1 registered user", "included": true},
          {"name": "Unlimited use case access", "included": false},
          {"name": "Full MIU access", "included": false},
          {"name": "Roadmap tools & prioritization framework", "included": false},
          {"name": "Team collaboration features", "included": false},
          {"name": "Dedicated account support", "included": false}
        ]}',
        1,
        true,
        true
      ),
      (
        'pro',
        'Pro',
        'For teams ready to scale with AI - unlimited access',
        2900,
        29000,
        'price_1RKdRAIiRJwYTHShF0hsJ0Yi',
        'price_pro_yearly',
        'prod_SF7gkIcBmxEIXk',
        '{"trial": {"enabled": false, "days": 0, "description": "No trial period"}, "caseStudies": {"limit": -1, "description": "Unlimited case study access"}, "users": {"limit": 3, "description": "Up to 3 users"}, "features": [
          {"name": "Unlimited case study access", "included": true},
          {"name": "Full MIU access", "included": true},
          {"name": "Roadmap tools & prioritization framework", "included": true},
          {"name": "Team collaboration features", "included": true},
          {"name": "Dedicated account support", "included": true},
          {"name": "Up to 3 users", "included": true},
          {"name": "Priority email support", "included": true}
        ]}',
        3,
        true,
        true
      ),
      (
        'enterprise',
        'Enterprise',
        'For large organizations - contact us for pricing',
        NULL,
        NULL,
        NULL,
        NULL,
        'prod_enterprise',
        '{"trial": {"enabled": false, "days": 0, "description": "Custom trial available"}, "caseStudies": {"limit": -1, "description": "Unlimited case study access"}, "users": {"limit": 7, "description": "Up to 7 CXO-level users"}, "features": [
          {"name": "Everything in Pro", "included": true},
          {"name": "Up to 7 CXO-level users", "included": true},
          {"name": "Custom integrations", "included": true},
          {"name": "Dedicated account manager", "included": true},
          {"name": "SLA guarantees", "included": true},
          {"name": "Custom branding", "included": true},
          {"name": "SSO integration", "included": true}
        ]}',
        7,
        true,
        true
      )
    `);
    console.log('Default plans inserted successfully');

    console.log('Plans schema created successfully');
  } catch (error) {
    console.error('Error creating plans schema:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    console.log('Closing database connection...');
    await migrationClient.end();
  }
}

// Run the migration
createPlansSchema().catch(console.error);
