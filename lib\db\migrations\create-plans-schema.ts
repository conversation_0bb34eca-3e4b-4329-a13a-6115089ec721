import { sql } from 'drizzle-orm';
import dotenv from 'dotenv';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';

// Load environment variables from .env file
dotenv.config();

async function createPlansSchema() {
  console.log('Creating plans schema...');

  // Check if POSTGRES_URL is set
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not set');
  }

  console.log('Connecting to database...');

  // Create a new connection to the database
  const migrationClient = postgres(process.env.POSTGRES_URL, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Create plans table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS plans (
        id SERIAL PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        price_monthly INTEGER NOT NULL,
        price_yearly INTEGER,
        stripe_price_id_monthly VARCHAR(100) NOT NULL,
        stripe_price_id_yearly VARCHAR(100),
        stripe_product_id VARCHAR(100) NOT NULL,
        features JSONB NOT NULL,
        max_users INTEGER NOT NULL DEFAULT 1,
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_public BOOLEAN NOT NULL DEFAULT true,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `);
    console.log('Plans table created successfully');

    // Create user_subscriptions table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS user_subscriptions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        team_id INTEGER NOT NULL REFERENCES teams(id),
        plan_id INTEGER NOT NULL REFERENCES plans(id),
        stripe_subscription_id VARCHAR(100),
        stripe_customer_id VARCHAR(100),
        status VARCHAR(50) NOT NULL,
        current_period_start TIMESTAMP NOT NULL,
        current_period_end TIMESTAMP NOT NULL,
        cancel_at_period_end BOOLEAN NOT NULL DEFAULT false,
        canceled_at TIMESTAMP,
        trial_start TIMESTAMP,
        trial_end TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `);
    console.log('User subscriptions table created successfully');

    // Create subscription_items table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS subscription_items (
        id SERIAL PRIMARY KEY,
        subscription_id INTEGER NOT NULL REFERENCES user_subscriptions(id),
        stripe_item_id VARCHAR(100) NOT NULL,
        stripe_price_id VARCHAR(100) NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 1,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `);
    console.log('Subscription items table created successfully');

    // Create subscription_history table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS subscription_history (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        team_id INTEGER NOT NULL REFERENCES teams(id),
        plan_id INTEGER NOT NULL REFERENCES plans(id),
        stripe_subscription_id VARCHAR(100),
        action VARCHAR(50) NOT NULL,
        previous_status VARCHAR(50),
        new_status VARCHAR(50),
        previous_plan_id INTEGER REFERENCES plans(id),
        metadata JSONB,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `);
    console.log('Subscription history table created successfully');

    // Insert default plans
    await db.execute(sql`
      INSERT INTO plans (
        name,
        display_name,
        description,
        price_monthly,
        stripe_price_id_monthly,
        stripe_product_id,
        features,
        max_users,
        is_active,
        is_public
      ) VALUES
      (
        'starter',
        'Starter Plan',
        'Ideal for early explorers and small teams',
        1200,
        'price_1RKdR9IiRJwYTHShansVCelM',
        'prod_SF7gtzlTQKtCGy',
        '{"features": [
          {"name": "Access to 10 curated AI use cases", "included": true},
          {"name": "Preview of Market Intelligence Unit (MIU)", "included": true},
          {"name": "1 registered user", "included": true},
          {"name": "Unlimited use case access", "included": false},
          {"name": "Full MIU access", "included": false},
          {"name": "Roadmap tools & prioritization framework", "included": false},
          {"name": "Team collaboration features", "included": false},
          {"name": "Dedicated account support", "included": false}
        ]}',
        1,
        true,
        true
      ),
      (
        'pro',
        'Pro Plan',
        'Ideal for teams ready to build strategic AI capabilities',
        6000,
        'price_1RKdRAIiRJwYTHShF0hsJ0Yi',
        'prod_SF7gkIcBmxEIXk',
        '{"features": [
          {"name": "Unlimited access to full AI use case library", "included": true},
          {"name": "Full Market Intelligence Unit (MIU) access", "included": true},
          {"name": "Roadmap tools & prioritization framework", "included": true},
          {"name": "Up to 3 users", "included": true},
          {"name": "Team collaboration features", "included": true},
          {"name": "Dedicated account support", "included": false},
          {"name": "Quarterly AI trend reports", "included": false}
        ]}',
        3,
        true,
        true
      ),
      (
        'enterprise',
        'Enterprise Plan',
        'Ideal for CXOs driving large-scale AI transformation',
        0,
        '',
        '',
        '{"features": [
          {"name": "Everything in Pro", "included": true},
          {"name": "Up to 7 CXO-level users", "included": true},
          {"name": "Dedicated account support", "included": true},
          {"name": "Quarterly AI trend reports & adoption benchmarks", "included": true},
          {"name": "Custom onboarding and strategy sessions", "included": true},
          {"name": "Early access to new features", "included": true}
        ]}',
        7,
        true,
        true
      )
    `);
    console.log('Default plans inserted successfully');

    console.log('Plans schema created successfully');
  } catch (error) {
    console.error('Error creating plans schema:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    console.log('Closing database connection...');
    await migrationClient.end();
  }
}

// Run the migration
createPlansSchema().catch(console.error);
