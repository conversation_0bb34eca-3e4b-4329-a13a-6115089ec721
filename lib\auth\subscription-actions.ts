'use server';

import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';
import { getUserSubscription } from '@/lib/services/subscription-service';
import { PlanFeatures, PLAN_CONFIG, getPlanFeatures, isActiveSubscription, isWithinPlanLimit } from '@/lib/config/plans';

// Use centralized functions from plans config

/**
 * Server action to check if user has access to a specific feature
 */
export async function hasFeatureAccess(feature: keyof PlanFeatures): Promise<boolean> {
  try {
    const user = await getUser();
    if (!user) return false;

    // Owners have access to all features
    if (user.role === 'owner') return true;

    const team = await getTeamForUser(user.id);
    if (!team) return false;

    // Check subscription status
    if (!isActiveSubscription(team.subscriptionStatus)) {
      return false;
    }

    const planFeatures = getPlanFeatures(team.planName);
    return planFeatures[feature];
  } catch (error) {
    console.error('Feature access check error:', error);
    return false;
  }
}

/**
 * Server action to get current plan limits
 */
export async function getCurrentPlanLimits(): Promise<PlanFeatures | null> {
  try {
    const user = await getUser();
    if (!user) return null;

    // Owners get enterprise features
    if (user.role === 'owner') return PLAN_CONFIG.enterprise;

    const team = await getTeamForUser(user.id);
    if (!team) return null;

    return getPlanFeatures(team.planName);
  } catch (error) {
    console.error('Get plan limits error:', error);
    return null;
  }
}

/**
 * Server action to check if user can perform an action based on limits
 */
export async function checkPlanLimit(
  limitType: 'maxUsers' | 'maxCaseStudies' | 'maxStorage',
  currentCount: number
): Promise<{ allowed: boolean; limit: number; message?: string }> {
  try {
    const user = await getUser();
    if (!user) {
      return { allowed: false, limit: 0, message: 'No active subscription' };
    }

    // Owners have unlimited access
    if (user.role === 'owner') {
      return { allowed: true, limit: -1 };
    }

    const team = await getTeamForUser(user.id);
    if (!team) {
      return { allowed: false, limit: 0, message: 'No team found' };
    }

    // Use centralized plan limit checking
    return isWithinPlanLimit(team.planName, limitType, currentCount);
  } catch (error) {
    console.error('Plan limit check error:', error);
    return { allowed: false, limit: 0, message: 'Error checking plan limits' };
  }
}

/**
 * Server action to get current subscription status
 */
export async function getSubscriptionStatus(): Promise<{
  hasActiveSubscription: boolean;
  status: string | null;
  planName: string | null;
  planFeatures: PlanFeatures;
  isOwner: boolean;
}> {
  try {
    const user = await getUser();
    if (!user) {
      return {
        hasActiveSubscription: false,
        status: null,
        planName: null,
        planFeatures: PLAN_CONFIG.starter,
        isOwner: false,
      };
    }

    // Owners have access to everything
    if (user.role === 'owner') {
      return {
        hasActiveSubscription: true,
        status: 'active',
        planName: 'enterprise',
        planFeatures: PLAN_CONFIG.enterprise,
        isOwner: true,
      };
    }

    const team = await getTeamForUser(user.id);
    if (!team) {
      return {
        hasActiveSubscription: false,
        status: null,
        planName: null,
        planFeatures: PLAN_CONFIG.starter,
        isOwner: false,
      };
    }

    const hasActiveSubscription = isActiveSubscription(team.subscriptionStatus);
    const planFeatures = getPlanFeatures(team.planName);

    return {
      hasActiveSubscription,
      status: team.subscriptionStatus,
      planName: team.planName,
      planFeatures,
      isOwner: false,
    };
  } catch (error) {
    console.error('Get subscription status error:', error);
    return {
      hasActiveSubscription: false,
      status: null,
      planName: null,
      planFeatures: PLAN_CONFIG.starter,
      isOwner: false,
    };
  }
}
