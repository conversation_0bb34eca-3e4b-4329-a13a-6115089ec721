'use client';

import { ReactNode, useState, useEffect } from 'react';
import { useStore } from 'react-redux';

interface SafeReduxWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * A wrapper component that ensures Redux is properly initialized
 * before rendering children that use Redux hooks
 */
export default function SafeReduxWrapper({ 
  children, 
  fallback = <div>Loading...</div> 
}: SafeReduxWrapperProps) {
  const store = useStore();
  const [isStoreReady, setIsStoreReady] = useState(false);

  useEffect(() => {
    // Check if the store is properly initialized
    if (store) {
      setIsStoreReady(true);
    }
  }, [store]);

  if (!isStoreReady) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
