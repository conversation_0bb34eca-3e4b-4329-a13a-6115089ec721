import { NextResponse } from 'next/server';
import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'daf8hefrg',
  api_key: process.env.CLOUDINARY_API_KEY || '614559198916115',
  api_secret: process.env.CLOUDINARY_API_SECRET || 'OV5jQ9RurrjEubiLdOTpUB0VMFo',
});

export async function POST(req: Request) {
  try {
    // Get the parameters from the request
    const { folder = 'case-studies', public_id, resource_type = 'image' } = await req.json();

    // Generate a timestamp and signature for Cloudinary
    const timestamp = Math.floor(Date.now() / 1000);

    // Create the string to sign
    const paramsToSign = {
      timestamp,
      folder,
      ...(public_id && { public_id }),
    };

    // Convert params to string
    const paramsStr = Object.entries(paramsToSign)
      .map(([key, value]) => `${key}=${value}`)
      .sort()
      .join('&');

    // Generate signature
    const signature = cloudinary.utils.api_sign_request(
      paramsToSign,
      cloudinary.config().api_secret || ''
    );

    console.log('\n========== CLOUDINARY SIGNATURE ==========');
    console.log('Params to sign:', paramsStr);
    console.log('Generated signature:', signature);
    console.log('========== CLOUDINARY SIGNATURE END ==========\n');

    // Return the signature and other parameters
    return NextResponse.json({
      signature,
      timestamp,
      cloudName: cloudinary.config().cloud_name,
      apiKey: cloudinary.config().api_key,
      folder,
      ...(public_id && { public_id }),
    });
  } catch (error) {
    console.error('Error generating Cloudinary signature:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}
