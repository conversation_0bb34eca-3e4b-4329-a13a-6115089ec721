import { sql } from 'drizzle-orm';
import { pgTable, serial, varchar, timestamp, boolean, integer } from 'drizzle-orm/pg-core';

export async function up(db: any) {
  // Add isVerified column to users table
  await db.execute(sql`
    ALTER TABLE users 
    ADD COLUMN IF NOT EXISTS is_verified BOOLEAN NOT NULL DEFAULT FALSE;
  `);

  // Create otp_verifications table
  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS otp_verifications (
      id SERIAL PRIMARY KEY,
      email VARCHAR(255) NOT NULL,
      otp VARCHAR(6) NOT NULL,
      purpose VARCHAR(20) NOT NULL,
      expires_at TIMESTAMP NOT NULL,
      verified BOOLEAN NOT NULL DEFAULT FALSE,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
      attempts INTEGER NOT NULL DEFAULT 0
    );
  `);
}

export async function down(db: any) {
  // Drop otp_verifications table
  await db.execute(sql`DROP TABLE IF EXISTS otp_verifications;`);
  
  // Remove isVerified column from users table
  await db.execute(sql`
    ALTER TABLE users 
    DROP COLUMN IF EXISTS is_verified;
  `);
}
