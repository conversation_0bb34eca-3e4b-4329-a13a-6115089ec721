require('dotenv').config();
const Stripe = require('stripe');

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function createStripeProducts() {
  console.log('Creating Stripe products and prices with USD currency...');

  try {
    // Create Starter product
    const starterProduct = await stripe.products.create({
      name: 'Starter',
      description: 'Ideal for early explorers and small teams',
    });
    console.log('Created Starter product:', starterProduct.id);

    // Create Starter price with USD currency
    const starterPrice = await stripe.prices.create({
      product: starterProduct.id,
      unit_amount: 1200, // $12 in cents
      currency: 'usd', // Explicitly set USD currency
      recurring: {
        interval: 'month',
        trial_period_days: 14,
      },
    });
    console.log('Created Starter price:', starterPrice.id);

    // Create Pro product
    const proProduct = await stripe.products.create({
      name: 'Pro',
      description: 'Ideal for teams ready to build strategic AI capabilities',
    });
    console.log('Created Pro product:', proProduct.id);

    // Create Pro price with USD currency
    const proPrice = await stripe.prices.create({
      product: proProduct.id,
      unit_amount: 6000, // $60 in cents
      currency: 'usd', // Explicitly set USD currency
      recurring: {
        interval: 'month',
        trial_period_days: 14,
      },
    });
    console.log('Created Pro price:', proPrice.id);

    // Create Enterprise product
    const enterpriseProduct = await stripe.products.create({
      name: 'Enterprise',
      description: 'For large organizations with custom needs',
    });
    console.log('Created Enterprise product:', enterpriseProduct.id);

    // Create Enterprise price with USD currency
    const enterprisePrice = await stripe.prices.create({
      product: enterpriseProduct.id,
      unit_amount: 99900, // $999 in cents
      currency: 'usd', // Explicitly set USD currency
      recurring: {
        interval: 'month',
        trial_period_days: 14,
      },
    });
    console.log('Created Enterprise price:', enterprisePrice.id);

    console.log('\nSummary of created products and prices:');
    console.log('Starter Plan:');
    console.log('  Product ID:', starterProduct.id);
    console.log('  Price ID:', starterPrice.id);
    console.log('Pro Plan:');
    console.log('  Product ID:', proProduct.id);
    console.log('  Price ID:', proPrice.id);
    console.log('Enterprise Plan:');
    console.log('  Product ID:', enterpriseProduct.id);
    console.log('  Price ID:', enterprisePrice.id);
    
    console.log('\nPlease update your pricing page and API routes with these new price IDs.');
  } catch (error) {
    console.error('Error creating Stripe products and prices:', error);
  }
}

createStripeProducts();
