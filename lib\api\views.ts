export const updateCaseStudyViewsOnClick = async (caseStudyId: number) => {
  try {
    const response = await fetch('/api/case-studies/views', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ caseStudyId }),
    });
    if (!response.ok) {
      throw new Error('Failed to update views');
    }
    const data = await response.json();
    console.log('🚨 🟠 Views updated successfully:', data);
  } catch (error) {
    console.log('🚨 🟠 Error updating views:', error);
  }
};
