'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { CloudinaryImage } from '@/components/ui/cloudinary-image';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  ArrowRightIcon,
  MagnifyingGlassIcon,
  BarChartIcon,
  BookmarkIcon,
} from '@radix-ui/react-icons';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { CaseStudy } from '@/lib/db/schema';

interface CaseStudyCardProps {
  caseStudy: CaseStudy & { isBookmarked?: boolean };
}

export function CaseStudyCard({ caseStudy }: CaseStudyCardProps) {
  const [isBookmarked, setIsBookmarked] = useState(
    caseStudy.isBookmarked || false
  );
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const toggleBookmark = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent the link from navigating
    e.stopPropagation(); // Prevent event bubbling

    setIsLoading(true);
    try {
      const action = isBookmarked ? 'DELETE' : 'POST';
      const response = await fetch('/api/bookmarks', {
        method: action,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ caseStudyId: caseStudy.id }),
      });

      if (response.ok) {
        setIsBookmarked(!isBookmarked);
        toast({
          title: isBookmarked ? 'Bookmark removed' : 'Bookmark added',
          description: isBookmarked
            ? 'Case study removed from bookmarks'
            : 'Case study added to bookmarks',
        });
        router.refresh();
      } else if (response.status === 401) {
        console.error('User not authenticated');
        toast({
          title: 'Authentication Required',
          description: 'Please sign in to bookmark case studies',
          variant: 'destructive',
        });
        // Optionally redirect to login
        // router.push('/sign-in');
      } else {
        console.error('Failed to toggle bookmark', await response.text());
        toast({
          title: 'Error',
          description: 'Failed to update bookmark',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while updating bookmark',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Link
      href={`/dashboard/case-studies/${caseStudy.id}`}
      className='block transition-transform hover:scale-[1.02]'
    >
      <Card className='h-full overflow-hidden border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all'>
        <div className='relative h-48 w-full overflow-hidden bg-gray-100'>
          {caseStudy.featureImageUrl ||
          caseStudy.previewImageUrl ||
          (caseStudy.icons &&
            caseStudy.icons.find((icon) => icon.iconType === 'icon')
              ?.iconUrl) ? (
            <CloudinaryImage
              src={
                caseStudy.featureImageUrl ||
                caseStudy.previewImageUrl ||
                (caseStudy.icons &&
                  caseStudy.icons.find((icon) => icon.iconType === 'icon')
                    ?.iconUrl)
              }
              alt={caseStudy.useCaseTitle || 'Case study'}
              width={800}
              height={400}
              className='object-cover w-full h-full'
            />
          ) : (
            <div className='flex h-full w-full items-center justify-center bg-gray-200'>
              <span className='text-gray-500'>No image</span>
            </div>
          )}

          {/* Market Intelligence Badge */}
          {caseStudy.marketIntelligenceData && (
            <div className='absolute top-2 right-2'>
              <Badge className='bg-blue-600 text-white flex items-center gap-1'>
                <MagnifyingGlassIcon className='h-3 w-3' />
                Market Intelligence
              </Badge>
            </div>
          )}

          {/* Bookmark Button */}
          <div className='absolute top-2 left-2'>
            <Button
              onClick={toggleBookmark}
              disabled={isLoading}
              variant={isBookmarked ? 'default' : 'outline'}
              size='sm'
              className={`flex items-center gap-1 relative ${
                isBookmarked ? 'bg-green-600 text-white' : 'bg-white'
              }`}
            >
              {isLoading ? (
                <>
                  <span className='opacity-0'>
                    <BookmarkIcon className='h-4 w-4 mr-1' />
                    {isBookmarked ? 'Saved' : 'Save'}
                  </span>
                  <span className='absolute inset-0 flex items-center justify-center'>
                    <Loader2 className='h-3 w-3 animate-spin' />
                  </span>
                </>
              ) : (
                <>
                  <BookmarkIcon className='h-4 w-4 mr-1' />
                  {isBookmarked ? 'Saved' : 'Save'}
                </>
              )}
            </Button>
          </div>
        </div>

        <CardContent className='p-5'>
          <div className='mb-2 flex flex-wrap gap-2'>
            {caseStudy.industry && (
              <Badge variant='outline' className='bg-gray-100'>
                {caseStudy.industry}
              </Badge>
            )}
            {caseStudy.role && (
              <Badge variant='outline' className='bg-gray-100'>
                {caseStudy.role}
              </Badge>
            )}

            {/* Market Metrics Badge */}
            {caseStudy.marketMetricsData && (
              <Badge
                variant='outline'
                className='bg-green-50 text-green-700 border-green-200 flex items-center gap-1'
              >
                <BarChartIcon className='h-3 w-3' />
                Metrics
              </Badge>
            )}
          </div>

          <h3 className='text-xl font-semibold mb-2 line-clamp-2'>
            {caseStudy.useCaseTitle}
          </h3>

          <p className='text-gray-600 mb-4 line-clamp-3'>
            {caseStudy.introductionText || 'No description available.'}
          </p>

          <div className='flex justify-end'>
            <Button
              variant='ghost'
              className='text-blue-600 hover:text-blue-800 p-0 flex items-center gap-1'
            >
              View Case Study
              <ArrowRightIcon className='h-4 w-4' />
            </Button>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
