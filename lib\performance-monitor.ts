/**
 * Performance monitoring utility
 * Tracks web vitals and custom performance metrics
 */

type PerformanceMetric = {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
};

type MetricName = 
  | 'FCP' // First Contentful Paint
  | 'LCP' // Largest Contentful Paint
  | 'FID' // First Input Delay
  | 'CLS' // Cumulative Layout Shift
  | 'TTFB' // Time to First Byte
  | 'TTI' // Time to Interactive
  | string; // Custom metrics

// Store collected metrics
const metrics: Record<string, PerformanceMetric> = {};

// Thresholds for rating metrics (in milliseconds, except for CLS which is unitless)
const thresholds: Record<string, [number, number]> = {
  FCP: [1800, 3000],  // Good: < 1.8s, Poor: > 3s
  LCP: [2500, 4000],  // Good: < 2.5s, Poor: > 4s
  FID: [100, 300],    // Good: < 100ms, Poor: > 300ms
  CLS: [0.1, 0.25],   // Good: < 0.1, Poor: > 0.25
  TTFB: [800, 1800],  // Good: < 800ms, Poor: > 1.8s
  TTI: [3800, 7300],  // Good: < 3.8s, Poor: > 7.3s
};

/**
 * Get performance rating based on metric value and thresholds
 */
function getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const [good, poor] = thresholds[name] || [1000, 3000]; // Default thresholds
  
  if (value <= good) return 'good';
  if (value >= poor) return 'poor';
  return 'needs-improvement';
}

/**
 * Record a performance metric
 */
export function recordMetric(name: MetricName, value: number): void {
  metrics[name] = {
    name,
    value,
    rating: getRating(name, value)
  };
  
  // Log poor metrics to console in development
  if (process.env.NODE_ENV === 'development' && getRating(name, value) === 'poor') {
    console.warn(`Poor performance detected: ${name} = ${value}`);
  }
  
  // If we're in a browser environment and the site has a performance observer endpoint
  if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_PERFORMANCE_ENDPOINT) {
    try {
      // Queue metric to be sent in batch on next idle period
      queueMetricForBatch(name, value);
    } catch (error) {
      console.error('Failed to queue metric', error);
    }
  }
}

// Queue for batching metrics before sending
const metricQueue: Array<{name: string; value: number; timestamp: number}> = [];
let sendTimeout: NodeJS.Timeout | null = null;

/**
 * Queue a metric to be sent in batch
 */
function queueMetricForBatch(name: string, value: number): void {
  metricQueue.push({
    name,
    value,
    timestamp: Date.now()
  });
  
  if (sendTimeout === null) {
    // Send batch after a short delay or when browser is idle
    if (typeof window.requestIdleCallback === 'function') {
      window.requestIdleCallback(() => sendMetricBatch());
    } else {
      sendTimeout = setTimeout(sendMetricBatch, 3000);
    }
  }
}

/**
 * Send queued metrics in batch
 */
async function sendMetricBatch(): void {
  if (metricQueue.length === 0) return;
  
  const batch = [...metricQueue];
  metricQueue.length = 0; // Clear queue
  sendTimeout = null;
  
  try {
    await fetch(process.env.NEXT_PUBLIC_PERFORMANCE_ENDPOINT!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        metrics: batch,
        url: window.location.pathname,
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      }),
      // Use keepalive to ensure the request completes even if page is unloading
      keepalive: true
    });
  } catch (error) {
    console.error('Failed to send metrics batch', error);
    // On failure, add metrics back to the queue for retry
    metricQueue.push(...batch);
  }
}

/**
 * Start monitoring web vitals metrics
 * Call this function early in your application lifecycle
 */
export function startPerformanceMonitoring(): void {
  if (typeof window === 'undefined') return;
  
  // Check browser support for PerformanceObserver API
  if (!('PerformanceObserver' in window)) return;
  
  try {
    // Track First Contentful Paint
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          recordMetric('FCP', entry.startTime);
        }
      }
    }).observe({ type: 'paint', buffered: true });
    
    // Track Largest Contentful Paint
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      recordMetric('LCP', lastEntry.startTime);
    }).observe({ type: 'largest-contentful-paint', buffered: true });
    
    // Track First Input Delay
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        recordMetric('FID', entry.processingStart - entry.startTime);
      }
    }).observe({ type: 'first-input', buffered: true });
    
    // Track Cumulative Layout Shift
    let cumulativeLayoutShift = 0;
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (!entry.hadRecentInput) {
          cumulativeLayoutShift += entry.value;
          recordMetric('CLS', cumulativeLayoutShift);
        }
      }
    }).observe({ type: 'layout-shift', buffered: true });
    
    // Track navigation timing metrics
    new PerformanceObserver(() => {
      const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navEntry) {
        // Time to First Byte
        recordMetric('TTFB', navEntry.responseStart);
        
        // Time to DOM Interactive
        if (navEntry.domInteractive) {
          recordMetric('TTI', navEntry.domInteractive);
        }
      }
    }).observe({ type: 'navigation', buffered: true });
    
  } catch (error) {
    console.error('Failed to initialize performance monitoring', error);
  }
}

/**
 * Get all collected metrics
 */
export function getAllMetrics(): Record<string, PerformanceMetric> {
  return { ...metrics };
}

/**
 * Custom performance timing for measuring specific operations
 */
export function measurePerformance<T>(
  operationName: string,
  fn: () => T
): T {
  const start = performance.now();
  const result = fn();
  const duration = performance.now() - start;
  
  console.log(`Operation "${operationName}" took ${duration.toFixed(2)}ms`);
  
  // For async operations, return the result as-is
  return result;
}

/**
 * Async version of measurePerformance
 */
export async function measurePerformanceAsync<T>(
  operationName: string,
  fn: () => Promise<T>
): Promise<T> {
  const start = performance.now();
  const result = await fn();
  const duration = performance.now() - start;
  
  console.log(`Async operation "${operationName}" took ${duration.toFixed(2)}ms`);
  
  return result;
}
