import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { createCheckoutSession, updateSubscription } from '@/lib/payments/stripe';
import { getTeamForUser } from '@/lib/db/queries';
import { getStripePrices } from '@/lib/payments/stripe';
import { z } from 'zod';
import { getPlanByName } from '@/lib/services/subscription-service';

// Schema for checkout request
const checkoutSchema = z.object({
  planName: z.string().min(1),
  priceId: z.string().optional(), // Add priceId as an optional parameter
  isUpgrade: z.boolean().optional(), // Flag to indicate if this is an upgrade
  isDowngrade: z.boolean().optional(), // Flag to indicate if this is a downgrade
});

export async function POST(request: NextRequest) {
  try {
    const user = await getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an owner - owners don't need to purchase plans
    if (user.role === 'owner') {
      return NextResponse.json({ error: 'Owners cannot purchase plans' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = checkoutSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const { planName, priceId: requestPriceId, isUpgrade, isDowngrade } = validationResult.data;

    console.log('Checkout API called with:', {
      planName,
      priceId: requestPriceId,
      isUpgrade,
      isDowngrade
    });

    // For enterprise plan, redirect to contact page
    if (planName.toLowerCase() === 'enterprise') {
      return NextResponse.json({
        url: `${process.env.NEXT_PUBLIC_APP_URL}/contact?subject=Enterprise%20Plan%20Inquiry`
      });
    }

    // Get the team for the user
    const team = await getTeamForUser(user.id);

    if (!team) {
      return NextResponse.json({ error: 'No team found for user' }, { status: 404 });
    }

    // Get all available Stripe prices
    const prices = await getStripePrices();

    // Find the price ID for the requested plan
    let price;
    let dbPlan;

    // First, try to get the plan from the database
    try {
      dbPlan = await getPlanByName(planName.toLowerCase());
      console.log('Found plan in database:', dbPlan);
    } catch (error) {
      console.error('Error getting plan from database:', error);
    }

    // If a specific priceId was provided, use it
    if (requestPriceId) {
      console.log(`Using provided price ID: ${requestPriceId}`);
      price = prices.find(p => p.id === requestPriceId);
    }
    // If we found the plan in the database, use its price ID
    else if (dbPlan) {
      console.log(`Using price ID from database: ${dbPlan.stripePriceIdMonthly}`);
      price = prices.find(p => p.id === dbPlan.stripePriceIdMonthly);
    }
    // Otherwise, fall back to the hardcoded price IDs
    else {
      console.log(`Looking up price ID for plan: ${planName}`);
      price = prices.find(p => {
        if (planName.toLowerCase() === 'starter') {
          return p.id === 'price_1RKd0KIiRJwYTHShIBRY6cd6'; // Starter plan price ID
        } else if (planName.toLowerCase() === 'pro') {
          return p.id === 'price_1RKd0LIiRJwYTHShusv7wdRZ'; // Pro plan price ID
        } else {
          return p.productId.toLowerCase().includes(planName.toLowerCase());
        }
      });
    }

    if (!price) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 });
    }

    try {
      console.log('Processing plan change request:', {
        planName,
        priceId: price.id,
        teamId: team.id,
        hasCustomerId: !!team.stripeCustomerId,
        hasSubscriptionId: !!team.stripeSubscriptionId,
        currentPlan: team.planName,
        subscriptionStatus: team.subscriptionStatus
      });

      // Check if user is trying to switch to the same plan they already have
      if (team.planName && team.planName.toLowerCase() === planName.toLowerCase() &&
          (team.subscriptionStatus === 'active' || team.subscriptionStatus === 'trialing')) {
        console.log('User already subscribed to this plan');
        return NextResponse.json({
          error: 'Already subscribed',
          details: `You are already subscribed to the ${planName} plan.`
        }, { status: 400 });
      }

      // Handle the subscription update or creation
      const url = await updateSubscription({
        team,
        priceId: price.id,
        isUpgrade: isUpgrade === true,
        isDowngrade: isDowngrade === true
      });

      console.log('Subscription update/creation successful, redirecting to:', url);
      return NextResponse.json({ url });
    } catch (error) {
      console.error('Error processing plan change:', error);
      return NextResponse.json({
        error: 'Failed to process plan change',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Checkout error:', error);
    return NextResponse.json({ error: 'Failed to create checkout session' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const user = await getUser();

    if (!user) {
      return NextResponse.redirect(new URL('/sign-up', request.url));
    }

    // Check if user is an owner - owners don't need to purchase plans
    if (user.role === 'owner') {
      return NextResponse.redirect(new URL('/dashboard/owner', request.url));
    }

    // Get the plan from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const planName = searchParams.get('plan');

    if (!planName) {
      return NextResponse.json({ error: 'Plan name is required' }, { status: 400 });
    }

    // Get the team for the user
    const team = await getTeamForUser(user.id);

    if (!team) {
      return NextResponse.json({ error: 'No team found for user' }, { status: 404 });
    }

    // Get all available Stripe prices
    const prices = await getStripePrices();

    // Find the price ID for the requested plan
    let price;
    let dbPlan;

    // First, try to get the plan from the database
    try {
      dbPlan = await getPlanByName(planName.toLowerCase());
      console.log('Found plan in database:', dbPlan);
    } catch (error) {
      console.error('Error getting plan from database:', error);
    }

    // If we found the plan in the database, use its price ID
    if (dbPlan) {
      console.log(`Using price ID from database: ${dbPlan.stripePriceIdMonthly}`);
      price = prices.find(p => p.id === dbPlan.stripePriceIdMonthly);
    }
    // Otherwise, fall back to the hardcoded price IDs
    else {
      console.log(`Looking up price ID for plan: ${planName}`);
      price = prices.find(p => {
        if (planName.toLowerCase() === 'starter') {
          return p.id === 'price_1RKd0KIiRJwYTHShIBRY6cd6'; // Starter plan price ID
        } else if (planName.toLowerCase() === 'pro') {
          return p.id === 'price_1RKd0LIiRJwYTHShusv7wdRZ'; // Pro plan price ID
        } else {
          return p.productId.toLowerCase().includes(planName.toLowerCase());
        }
      });
    }

    if (!price) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 });
    }

    try {
      console.log('Processing plan change request (GET):', {
        planName,
        priceId: price.id,
        teamId: team.id,
        hasCustomerId: !!team.stripeCustomerId,
        hasSubscriptionId: !!team.stripeSubscriptionId,
        currentPlan: team.planName,
        subscriptionStatus: team.subscriptionStatus
      });

      // Check if user is trying to switch to the same plan they already have
      if (team.planName && team.planName.toLowerCase() === planName.toLowerCase() &&
          (team.subscriptionStatus === 'active' || team.subscriptionStatus === 'trialing')) {
        console.log('User already subscribed to this plan');
        return NextResponse.redirect(new URL('/dashboard/billing?error=already-subscribed', request.url));
      }

      // Handle the subscription update or creation
      // Determine if this is an upgrade or downgrade
      const isUpgrade = team.planName?.toLowerCase() === 'starter' && planName.toLowerCase() === 'pro';
      const isDowngrade = team.planName?.toLowerCase() === 'pro' && planName.toLowerCase() === 'starter';

      console.log('Plan change type:', { isUpgrade, isDowngrade });

      const url = await updateSubscription({
        team,
        priceId: price.id,
        isUpgrade,
        isDowngrade
      });

      console.log('Subscription update/creation successful, redirecting to:', url);
      return NextResponse.redirect(new URL(url));
    } catch (error) {
      console.error('Error processing plan change:', error);
      return NextResponse.redirect(new URL(`/dashboard/billing?error=subscription-failed&message=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`, request.url));
    }

  } catch (error) {
    console.error('Checkout error:', error);
    return NextResponse.json({ error: 'Failed to create checkout session' }, { status: 500 });
  }
}
