import { NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { getActivityLogsByUserId } from '@/lib/db/queries';

export async function GET() {
  try {
    const user = await getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const logs = await getActivityLogsByUserId(user.id);
    return NextResponse.json(logs);
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
