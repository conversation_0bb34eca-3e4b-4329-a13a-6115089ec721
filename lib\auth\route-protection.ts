'use server';

import { redirect } from 'next/navigation';
import { getUser } from '@/lib/db/server-queries';

// Define route patterns for different roles
const OWNER_ROUTE_PATTERNS = [
  '/dashboard/owner',
  '/dashboard/add-case',
  '/dashboard/import-cases',
  '/dashboard/categories',
  '/dashboard/team',
  '/dashboard/case-studies',
  // '/dashboard/reports',
  '/dashboard/search',
  '/dashboard/coupon-codes',
  '/dashboard/users',
];

const MEMBER_ROUTE_PATTERNS = [
  '/dashboard/member',
  '/dashboard/trends',
  '/dashboard/reports',
  '/dashboard/user',
  '/dashboard/user/case-studies',
  '/dashboard/user/search',
];

const TEAM_MEMBER_ROUTE_PATTERNS = [
  '/dashboard/add-case',
  '/dashboard/import-cases',
  '/dashboard/categories',
  '/dashboard/team',
  '/dashboard/case-studies',
  // '/dashboard/reports',
  '/dashboard/search',
  '/dashboard/coupon-codes',
  '/dashboard/users',
  ...MEMBER_ROUTE_PATTERNS,
];

const TEAM_VIEWER_ROUTE_PATTERNS = [
  '/dashboard/categories',
  '/dashboard/team',
  '/dashboard/case-studies',
  // '/dashboard/reports',
  '/dashboard/search',
  '/dashboard/users',
  ...MEMBER_ROUTE_PATTERNS,
];

// Define shared routes accessible to both roles
const SHARED_ROUTE_PATTERNS = [
  '/dashboard/settings',
  '/dashboard/help',
  '/dashboard/case-studies',
];

/**
 * Checks if a given path matches any of the patterns
 */
function matchesPattern(path: string, patterns: string[]): boolean {
  return patterns.some((pattern) => {
    // Exact match
    if (pattern === path) return true;
    // Path starts with pattern
    if (path.startsWith(`${pattern}/`)) return true;

    // Handle case study detail pages specifically
    if (
      pattern === '/dashboard/case-studies' &&
      path.match(/^\/dashboard\/case-studies\/\d+$/)
    ) {
      return true;
    }

    return false;
  });
}

/**
 * Middleware to protect owner routes from non-owner users
 */
export async function protectOwnerRoute() {
  const user = await getUser();

  if (!user) {
    console.log('protectOwnerRoute - No user found, redirecting to sign-in');
    redirect('/sign-in');
  }

  // Check if the user is an owner
  if (
    user.role !== 'owner' &&
    user.role !== 'teamMember' &&
    user.role !== 'viewer'
  ) {
    console.log(
      `Access denied: Non-owner (role: ${user.role}) attempting to access owner route`
    );
    redirect('/dashboard/member');
  }

  console.log('protectOwnerRoute - Access granted for owner');
}

/**s
 * Middleware to protect member routes from non-member users
 */
export async function protectMemberRoute() {
  const user = await getUser();
  // console.log('protectMemberRoute - User:', JSON.stringify(user, null, 2));

  if (!user) {
    console.log('protectMemberRoute - No user found, redirecting to sign-in');
    redirect('/sign-in');
  }

  // Check if the user is a member
  if (user.role !== 'member') {
    console.log(
      `Access denied: Non-member (role: ${user.role}) attempting to access member route`
    );
    redirect('/dashboard/owner');
  }

  console.log('protectMemberRoute - Access granted for member');
}

/**
 * Validates the current route based on the user's role
 * @param currentPath The current path being accessed
 */
export async function validateRouteAccess(currentPath?: string) {
  const user = await getUser();

  if (!user) {
    redirect('/sign-in');
  }

  // If no path is provided, just check authentication
  if (!currentPath) {
    return;
  }

  const isOwnerRoute = matchesPattern(currentPath, OWNER_ROUTE_PATTERNS);
  const isMemberRoute = matchesPattern(currentPath, MEMBER_ROUTE_PATTERNS);
  const isSharedRoute = matchesPattern(currentPath, SHARED_ROUTE_PATTERNS);
  const isTeamMemberRoute = matchesPattern(
    currentPath,
    TEAM_MEMBER_ROUTE_PATTERNS
  );
  const isTeamViewerRoute = matchesPattern(
    currentPath,
    TEAM_VIEWER_ROUTE_PATTERNS
  );

  // If it's a shared route, allow access to both roles
  if (isSharedRoute) {
    console.log(
      `Shared route access granted: ${user.role} accessing shared route: ${currentPath}`
    );
    return; // Allow access
  }

  if (
    isTeamMemberRoute &&
    user.role === 'owner' &&
    user.teamRole === 'teamMember'
  ) {
    console.log(
      `Team member route access granted: ${user.teamRole} accessing team member route: ${currentPath}`
    );
    return; // Allow access for team members
  }

  if (
    isTeamViewerRoute &&
    user.role === 'member' &&
    user.teamRole === 'viewer'
  ) {
    console.log(
      `Team viewer route access granted: ${user.role} accessing team viewer route: ${currentPath}`
    );
    return; // Allow access for team viewers
  }

  // If it's an owner route but user is not an owner
  if (
    isOwnerRoute &&
    (user.role !== 'owner' ||
      user.role !== 'teamMember' ||
      user.role !== 'viewer')
  ) {
    console.log(
      `Accessing denied: ${user.role} attempting to access owner route: ${currentPath}`
    );
    redirect('/dashboard/member');
  }

  // If it's a member route but user is not a member
  if (isMemberRoute && user.role !== 'member') {
    console.log(
      `Accessing denied: ${user.role} attempting to access member route: ${currentPath}`
    );
    redirect('/dashboard/owner');
  }
}
