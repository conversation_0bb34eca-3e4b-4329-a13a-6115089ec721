'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { SafeImage } from '@/components/SafeImage';

interface CaseStudyIcon {
  id: number;
  caseStudyId: number;
  iconType: string;
  iconUrl: string;
  order: number;
}

interface CaseStudy {
  id: number;
  useCaseTitle: string;
  industry?: string | null;
  role?: string | null;
  headerImage?: string | null;
  featureImageUrl?: string | null;
  previewImageUrl?: string | null;
  introductionText?: string | null;
  icons?: CaseStudyIcon[];
}

interface SimilarCaseStudiesClientProps {
  similarCaseStudies: CaseStudy[];
}

export function SimilarCaseStudiesClient({ similarCaseStudies }: SimilarCaseStudiesClientProps) {
  const router = useRouter();

  // If no similar case studies found, return null
  if (!similarCaseStudies || similarCaseStudies.length === 0) {
    return null;
  }

  // Get the featured case study (first one)
  const featuredCaseStudy = similarCaseStudies[0];
  // Get the remaining case studies
  const otherCaseStudies = similarCaseStudies.slice(1);

  return (
    <div className="border border-gray-100 dark:border-gray-900 rounded-lg p-6 bg-white dark:bg-black">
      <div className="mb-4">
        <h2 className="text-2xl font-bold text-black dark:text-white">Case Studies</h2>
        <p className="text-sm text-black dark:text-white">Real-World AI Success Stories & Lessons Learned</p>
      </div>

      {/* Featured case study - full width */}
      {featuredCaseStudy && (
        <div className="mb-6 bg-white dark:bg-black rounded-lg overflow-hidden border border-gray-100 dark:border-gray-900">
          <div className="flex flex-col md:flex-row">
            <div className="relative w-full md:w-2/5 h-64 md:h-auto">
              <SafeImage
                src={featuredCaseStudy.featureImageUrl || featuredCaseStudy.previewImageUrl || (featuredCaseStudy.icons && featuredCaseStudy.icons.find(icon => icon.iconType === 'icon')?.iconUrl) || featuredCaseStudy.headerImage || '/placeholder-image.jpg'}
                alt={featuredCaseStudy.useCaseTitle}
                fallbackSrc="/placeholder-image.jpg"
                fill
                className="object-cover"
              />
              <div className="absolute top-4 left-4 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-white text-xs px-2 py-1 rounded">
                FEATURED CASE STUDY
              </div>
            </div>
            <div className="p-6 md:w-3/5">
              <h3 className="text-xl font-bold mb-2 text-black dark:text-white">{featuredCaseStudy.useCaseTitle}</h3>
              <p className="text-black dark:text-white text-sm mb-4">
                {featuredCaseStudy.introductionText || 'No description available.'}
              </p>
              <div className="mb-4">
                <h4 className="font-semibold mb-2 text-black dark:text-white">Key Findings:</h4>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 text-blue-500 dark:text-blue-400 rounded-full w-2 h-2 flex-shrink-0"></div>
                    <span className="text-sm text-black dark:text-white">AI agents range from reactive (instant decision-making) to proactive (strategic planning)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 text-blue-500 dark:text-blue-400 rounded-full w-2 h-2 flex-shrink-0"></div>
                    <span className="text-sm text-black dark:text-white">Widespread adoption expected within 5 years across industries</span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 text-blue-500 dark:text-blue-400 rounded-full w-2 h-2 flex-shrink-0"></div>
                    <span className="text-sm text-black dark:text-white">Continuous learning capabilities allow for expertise development</span>
                  </li>
                </ul>
              </div>
              <Button
                className="bg-blue-500 hover:bg-blue-600 text-white"
                onClick={() => router.push(`/dashboard/case-studies/${featuredCaseStudy.id}`)}
              >
                Read Full Case Study
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Three smaller case studies in a row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {otherCaseStudies.map((cs) => (
          <div
            key={cs.id}
            className="bg-white dark:bg-black rounded-lg overflow-hidden border border-gray-100 dark:border-gray-900 h-full cursor-pointer"
            onClick={() => router.push(`/dashboard/case-studies/${cs.id}`)}
          >
            <div className="relative w-full h-40">
              <SafeImage
                src={cs.featureImageUrl || cs.previewImageUrl || (cs.icons && cs.icons.find(icon => icon.iconType === 'icon')?.iconUrl) || cs.headerImage || '/placeholder-image.jpg'}
                alt={cs.useCaseTitle}
                fallbackSrc="/placeholder-image.jpg"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-4">
              <div className="mb-1">
                <span className="text-xs text-amber-600 dark:text-amber-400 font-medium uppercase">
                  {cs.industry || 'Leadership'}
                </span>
              </div>
              <h3 className="text-sm font-semibold mb-1 line-clamp-2 text-black dark:text-white">{cs.useCaseTitle}</h3>
              <p className="text-xs text-black dark:text-white line-clamp-3">
                {cs.introductionText || 'No description available.'}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
