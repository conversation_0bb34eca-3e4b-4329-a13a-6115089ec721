'use client';

import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

// Type definitions for virtualized list
interface VirtualListProps<T> {
  // Array of items to render
  items: T[];
  // Function to render each item
  renderItem: (item: T, index: number, isVisible: boolean) => React.ReactNode;
  // Height of each item (can be fixed or a function)
  itemHeight: number | ((item: T, index: number) => number);
  // Height of the container
  height: number;
  // Width of the container
  width?: number | string;
  // Class name for the container
  className?: string;
  // Buffer of items to render outside of view (in pixels)
  overscan?: number;
  // Whether to use window as the scrolling container
  useWindowScroll?: boolean;
  // Callback for visible items change
  onVisibleItemsChange?: (startIndex: number, endIndex: number) => void;
  // ID for the container
  id?: string;
  // Whether to use ResizeObserver to handle container resizing
  observeResize?: boolean;
}

// Use either useEffect or useLayoutEffect based on environment
const useIsomorphicLayoutEffect =
  typeof window !== 'undefined' ? useLayoutEffect : useEffect;

/**
 * VirtualList component for efficiently rendering large lists
 * Only renders items that are visible in the viewport with a buffer
 * 
 * @param props Component props
 * @returns React component
 */
export function VirtualList<T>({
  items,
  renderItem,
  itemHeight,
  height,
  width = '100%',
  className,
  overscan = 300,
  useWindowScroll = false,
  onVisibleItemsChange,
  id,
  observeResize = true,
}: VirtualListProps<T>) {
  // Reference to the scrollable container
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Store pre-calculated item heights for variable height mode
  const itemHeightsRef = useRef<number[]>([]);
  
  // State for scroll position and container dimensions
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(height);
  
  // Determine if we're using fixed or variable height
  const isFixedHeight = typeof itemHeight === 'number';
  
  // Calculate item heights if using variable height
  useIsomorphicLayoutEffect(() => {
    if (!isFixedHeight) {
      // Calculate heights for all items
      itemHeightsRef.current = items.map((item, index) =>
        (itemHeight as (item: T, index: number) => number)(item, index)
      );
    }
  }, [items, itemHeight, isFixedHeight]);
  
  // Calculate total height of all items
  const getTotalHeight = useCallback(() => {
    if (isFixedHeight) {
      return items.length * (itemHeight as number);
    } else {
      return itemHeightsRef.current.reduce((sum, height) => sum + height, 0);
    }
  }, [items.length, itemHeight, isFixedHeight]);
  
  // Observe container resizing if enabled
  useEffect(() => {
    if (!observeResize || !containerRef.current) return;
    
    // Create ResizeObserver to update container dimensions
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        if (entry.target === containerRef.current) {
          setContainerHeight(entry.contentRect.height);
        }
      }
    });
    
    // Start observing
    resizeObserver.observe(containerRef.current);
    
    // Clean up
    return () => {
      resizeObserver.disconnect();
    };
  }, [observeResize]);
  
  // Handle scroll events
  const handleScroll = useCallback((event: Event) => {
    if (useWindowScroll) {
      setScrollTop(window.scrollY);
    } else {
      const target = event.target as HTMLDivElement;
      setScrollTop(target.scrollTop);
    }
  }, [useWindowScroll]);
  
  // Add scroll event listener
  useEffect(() => {
    const scrollContainer = useWindowScroll ? window : containerRef.current;
    if (!scrollContainer) return;
    
    scrollContainer.addEventListener('scroll', handleScroll, {
      passive: true,
    });
    
    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll, useWindowScroll]);
  
  // Calculate range of visible items
  const getVisibleRange = useCallback(() => {
    if (items.length === 0) return { startIndex: 0, endIndex: 0 };
    
    // Starting position
    const scrollPos = scrollTop;
    
    if (isFixedHeight) {
      // Fixed height calculation is simpler
      const fixedItemHeight = itemHeight as number;
      const startIndex = Math.max(
        0,
        Math.floor((scrollPos - overscan) / fixedItemHeight)
      );
      const endIndex = Math.min(
        items.length - 1,
        Math.ceil((scrollPos + containerHeight + overscan) / fixedItemHeight)
      );
      
      return { startIndex, endIndex };
    } else {
      // Variable height calculation requires iterating
      const itemHeights = itemHeightsRef.current;
      let accumulatedHeight = 0;
      let startIndex = 0;
      let endIndex = 0;
      
      // Find start index
      for (let i = 0; i < items.length; i++) {
        const height = itemHeights[i];
        if (accumulatedHeight + height >= scrollPos - overscan) {
          startIndex = i;
          break;
        }
        accumulatedHeight += height;
      }
      
      // Find end index
      for (let i = startIndex; i < items.length; i++) {
        if (accumulatedHeight >= scrollPos + containerHeight + overscan) {
          endIndex = i;
          break;
        }
        accumulatedHeight += itemHeights[i];
        endIndex = i;
      }
      
      return { startIndex, endIndex };
    }
  }, [
    items.length,
    scrollTop,
    isFixedHeight,
    itemHeight,
    containerHeight,
    overscan,
  ]);
  
  // Calculate item positions
  const getItemPosition = useCallback(
    (index: number) => {
      if (isFixedHeight) {
        return (itemHeight as number) * index;
      } else {
        const itemHeights = itemHeightsRef.current;
        let position = 0;
        for (let i = 0; i < index; i++) {
          position += itemHeights[i];
        }
        return position;
      }
    },
    [isFixedHeight, itemHeight]
  );
  
  // Get current visible range
  const { startIndex, endIndex } = getVisibleRange();
  
  // Notify parent component about visible range change
  useEffect(() => {
    if (onVisibleItemsChange) {
      onVisibleItemsChange(startIndex, endIndex);
    }
  }, [startIndex, endIndex, onVisibleItemsChange]);
  
  // Create rendered items
  const renderedItems = [];
  for (let i = startIndex; i <= endIndex; i++) {
    if (i >= items.length) break;
    
    const item = items[i];
    const position = getItemPosition(i);
    const itemHeightValue = isFixedHeight
      ? (itemHeight as number)
      : itemHeightsRef.current[i];
    
    renderedItems.push(
      <div
        key={i}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: itemHeightValue,
          transform: `translateY(${position}px)`,
        }}
        data-index={i}
      >
        {renderItem(item, i, true)}
      </div>
    );
  }
  
  // Get total list height
  const totalHeight = getTotalHeight();
  
  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-auto', className)}
      style={{
        height: useWindowScroll ? '100%' : height,
        width,
      }}
      id={id}
    >
      <div
        style={{
          height: totalHeight,
          position: 'relative',
        }}
      >
        {renderedItems}
      </div>
    </div>
  );
}

/**
 * Hook to get the visible items in a VirtualList
 * 
 * @param containerId ID of the VirtualList container
 * @param itemCount Total number of items
 * @returns Object with visible item indices
 */
export function useVisibleItems(
  containerId: string,
  itemCount: number
) {
  const [visibleItems, setVisibleItems] = useState({
    startIndex: 0,
    endIndex: Math.min(20, itemCount - 1),
  });
  
  // Update visible items
  const handleVisibleItemsChange = useCallback(
    (startIndex: number, endIndex: number) => {
      setVisibleItems({ startIndex, endIndex });
    },
    []
  );
  
  return {
    visibleItems,
    handleVisibleItemsChange,
  };
}

export default VirtualList;
