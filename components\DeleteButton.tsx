'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { TrashIcon } from '@radix-ui/react-icons';
import { useRouter } from 'next/navigation';
import { toast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface DeleteButtonProps {
  caseStudyId: number;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function DeleteButton({
  caseStudyId,
  variant = "outline",
  size = "sm",
  className = "text-red-500"
}: DeleteButtonProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const router = useRouter();

  const handleDelete = async () => {
    try {
      setIsDeleting(true);

      // Call the API to delete the case study
      const response = await fetch(`/api/case-studies/${caseStudyId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || 'Failed to delete case study');
      }

      // Show success message
      toast({
        title: 'Success',
        description: 'Case study deleted successfully',
      });

      // Refresh the page or redirect based on current location
      if (window.location.pathname.includes(`/case-studies/${caseStudyId}`)) {
        // If we're on the detail page of the deleted case study, redirect to list
        router.push('/dashboard/case-studies');
      } else {
        // Otherwise, refresh the current page to update the list
        router.refresh();
      }
    } catch (error) {
      console.error('Error deleting case study:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete case study',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setShowConfirmDialog(false);
    }
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => setShowConfirmDialog(true)}
        disabled={isDeleting}
      >
        <TrashIcon className="mr-2 h-4 w-4" />
        {size === "icon" ? null : (isDeleting ? 'Deleting...' : 'Delete')}
      </Button>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the case study
              and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
