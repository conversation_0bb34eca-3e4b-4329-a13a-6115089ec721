import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { z } from 'zod';

// Schema for checking user
const checkUserSchema = z.object({
  email: z.string().email(),
});

// POST - Check if user exists
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validationResult = checkUserSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({ 
        exists: false,
        message: 'Invalid request data',
        errors: validationResult.error.errors
      }, { status: 400 });
    }
    
    const { email } = validationResult.data;
    
    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);
    
    return NextResponse.json({ 
      exists: existingUser.length > 0,
    });
    
  } catch (error) {
    console.error('Error checking user:', error);
    return NextResponse.json({ 
      exists: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
