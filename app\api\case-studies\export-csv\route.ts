import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { stringify } from 'csv-stringify/sync';
import { isNull, and } from 'drizzle-orm';

export async function GET(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get all case studies
    const results = await db.query.caseStudies.findMany({
      where: and(
        // isNull(caseStudies.deletedAt)
      ),
    });

    // Transform data for CSV export
    const csvData = results.map(caseStudy => ({
      use_case_title: caseStudy.useCaseTitle,
      industry: caseStudy.industry || '',
      role: caseStudy.role || '',
      vector: caseStudy.vector || '',
      potentially_impacted_kpis: caseStudy.potentiallyImpactedKpis || '',
      potentially_impacted_kpis_1: caseStudy.potentiallyImpactedKpis ?
        caseStudy.potentiallyImpactedKpis.split(',')[0]?.trim() || '' : '',
      potentially_impacted_kpis_2: caseStudy.potentiallyImpactedKpis ?
        caseStudy.potentiallyImpactedKpis.split(',')[1]?.trim() || '' : '',
      potentially_impacted_kpis_3: caseStudy.potentiallyImpactedKpis ?
        caseStudy.potentiallyImpactedKpis.split(',')[2]?.trim() || '' : '',
      introduction_title: caseStudy.introductionTitle || '',
      introduction_text: caseStudy.introductionText || '',
      transition_to_challange: caseStudy.transitionToChallange || '',
      challange_1: caseStudy.challange1 || '',
      challange_2: caseStudy.challange2 || '',
      challange_3: caseStudy.challange3 || '',
      transition_to_questions: caseStudy.transitionToQuestions || '',
      question_1: caseStudy.question1 || '',
      question_2: caseStudy.question2 || '',
      question_3: caseStudy.question3 || '',
      process_title: caseStudy.processTitle || '',
      process_step_1_title: caseStudy.processStep1Title || '',
      process_step_1_description: caseStudy.processStep1Description || '',
      process_step_2_title: caseStudy.processStep2Title || '',
      process_step_2_description: caseStudy.processStep2Description || '',
      process_step_3_title: caseStudy.processStep3Title || '',
      process_step_3_description: caseStudy.processStep3Description || '',
      solution_title: caseStudy.solutionTitle || '',
      solution_1_title: caseStudy.solution1Title || '',
      solution_1_description: caseStudy.solution1Description || '',
      solution_2_title: caseStudy.solution2Title || '',
      solution_2_description: caseStudy.solution2Description || '',
      solution_3_title: caseStudy.solution3Title || '',
      solution_3_description: caseStudy.solution3Description || '',
      solution_4_title: '',
      solution_4_description: '',
      solution_5_title: '',
      solution_5_description: '',
      impact_title: caseStudy.impactTitle || '',
      potential_impact_1_qualitative: caseStudy.potentialImpact1 || '',
      potential_impact_1_quantitative: caseStudy.impactValue1 || '',
      potential_impact_1_icon_image_url: '',
      potential_impact_2_qualitative: caseStudy.potentialImpact2 || '',
      potential_impact_2_quantitative: caseStudy.impactValue2 || '',
      potential_impact_2_icon_image_url: '',
      potential_impact_3_qualitative: caseStudy.potentialImpact3 || '',
      potential_impact_3_quantitative: caseStudy.impactValue3 || '',
      potential_impact_3_icon_image_url: '',
      potential_impact_4_qualitative: caseStudy.potentialImpact4 || '',
      potential_impact_4_quantitative: caseStudy.impactValue4 || '',
      potential_impact_4_icon_image_url: '',
      conclusion_title: caseStudy.conclusionTitle || '',
      conclusion_text: caseStudy.conclusionText || '',
      conclusion_result_title: caseStudy.conclusionResultTitle || '',
      conclusion_result_dec: caseStudy.conclusionResult || '',
      header_image_url: caseStudy.featureImageUrl || '',
      icon_image_url: '',
    }));

    // Generate CSV
    const csv = stringify(csvData, {
      header: true,
    });

    // Set headers for file download
    const headers = new Headers();
    headers.append('Content-Type', 'text/csv');
    headers.append('Content-Disposition', 'attachment; filename="case-studies.csv"');

    return new NextResponse(csv, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('Error exporting case studies:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}
