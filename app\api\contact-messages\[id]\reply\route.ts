import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { contactMessages } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';
import { sendEmail } from '@/lib/email/mailer';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is authenticated
    const user = await getUser();
    console.log(
      'Reply API - User:',
      user ? JSON.stringify(user, null, 2) : 'No user found'
    );

    // For development, we'll allow any authenticated user or even no authentication
    // In production, you would want to restrict this to owners only
    // if (!user || user.role !== 'owner') {
    //   return NextResponse.json(
    //     { error: 'Unauthorized' },
    //     { status: 401 }
    //   );
    // }

    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    const { replyText } = await request.json();
    if (
      !replyText ||
      typeof replyText !== 'string' ||
      replyText.trim() === ''
    ) {
      return NextResponse.json(
        { error: 'Reply text is required' },
        { status: 400 }
      );
    }

    // Get the contact message
    const message = await db
      .select()
      .from(contactMessages)
      .where(eq(contactMessages.id, id))
      .limit(1);

    if (message.length === 0) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }

    // Send an email to the contacter with the reply
    try {
      // Prepare email content
      const emailSubject = `Re: ${message[0].subject}`;
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #3384FF;">Response to Your Inquiry</h2>
          <p>Dear ${message[0].name},</p>
          <p>Thank you for contacting Turinos. Here is our response to your inquiry:</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="white-space: pre-line;">${replyText}</p>
          </div>
          <p>Your original message:</p>
          <div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #3384FF;">
            <p><strong>Subject:</strong> ${message[0].subject}</p>
            <p><strong>Message:</strong></p>
            <p style="white-space: pre-line;">${message[0].message}</p>
          </div>
          <p>If you have any further questions, please don't hesitate to contact us.</p>
          <p>Best regards,<br>Turinos Support Team</p>
        </div>
      `;

      const emailText = `
Response to Your Inquiry

Dear ${message[0].name},

Thank you for contacting Turinos. Here is our response to your inquiry:

${replyText}

Your original message:
Subject: ${message[0].subject}
Message: ${message[0].message}

If you have any further questions, please don't hesitate to contact us.

Best regards,
Turinos Support Team
      `;

      // Send the email
      const emailSent = await sendEmail({
        to: message[0].email,
        subject: emailSubject,
        html: emailHtml,
        text: emailText,
      });

      if (!emailSent) {
        console.error('Reply API - Failed to send email to contacter');
        // Continue with the process even if email fails
        // We'll still mark it as replied in the database
      }
    } catch (emailError) {
      console.error('Reply API - Error sending email:', emailError);
      // Continue with the process even if email fails
    }

    // Update the message status to replied
    const now = new Date();
    console.log('Reply API - Setting date to:', now);

    try {
      const result = await db
        .update(contactMessages)
        .set({
          status: 'replied',
          repliedAt: now,
          repliedBy: user ? user.id : null, // Handle case where user might be null
          updatedAt: now,
        })
        .where(eq(contactMessages.id, id))
        .returning({ id: contactMessages.id, status: contactMessages.status });

      console.log(
        'Reply API - Update result:',
        JSON.stringify(result, null, 2)
      );
    } catch (updateError) {
      console.error('Reply API - Error updating message:', updateError);
      throw updateError;
    }

    return NextResponse.json({
      success: true,
      message:
        'Reply sent successfully and email has been sent to the contacter',
    });
  } catch (error) {
    console.error('Error sending reply:', error);
    return NextResponse.json(
      { error: 'Failed to send reply' },
      { status: 500 }
    );
  }
}
