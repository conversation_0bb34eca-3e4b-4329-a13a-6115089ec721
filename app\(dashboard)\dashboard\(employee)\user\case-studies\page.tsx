'use client';

import { useState } from 'react';
import { CaseStudyResponse } from '@/hooks/useCaseStudies';

// Extended interface to match the actual data structure
interface ExtendedCaseStudyResponse extends CaseStudyResponse {
  icons?: Array<{
    iconType: string;
    iconUrl: string;
  }>;
  shortDescription?: string;
  userId?: string;
  user?: {
    name?: string;
  };
}
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useRouter } from 'next/navigation';
import { EyeOpenIcon } from '@radix-ui/react-icons';
import { SearchBarWrapper } from '@/components/SearchBarWrapper';
import {
  useGetCaseStudiesQuery,
} from '@/lib/redux/api/caseStudiesApi';
import SafeReduxWrapper from '@/components/SafeReduxWrapper';
import { Loading } from '@/components/ui/loading';
import { useSubscription } from '@/lib/hooks/use-subscription';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon, ArrowUpIcon } from 'lucide-react';
import Link from 'next/link';

export default function MemberCaseStudiesPage() {
  return (
    <SafeReduxWrapper fallback={<Loading text="Loading case studies..." size="lg" />}>
      <MemberCaseStudiesContent />
    </SafeReduxWrapper>
  );
}

function MemberCaseStudiesContent() {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const { data, isLoading, error } = useGetCaseStudiesQuery({ page, pageSize: 9 });
  const { planName, planFeatures } = useSubscription();

  // Handle API errors that indicate subscription issues
  if (error && 'status' in error) {
    if (error.status === 402) {
      // Payment required - redirect to pricing
      router.push('/pricing');
      return <Loading text="Redirecting to pricing..." size="lg" />;
    }
  }

  if (isLoading) return <Loading text="Loading case studies..." size="lg" />;
  if (error) return <div className="p-6 text-red-500">Error loading case studies</div>;

  // Check if there's a limit message from the API
  const hasLimitReached = data?.limitReached;
  const limitMessage = data?.message;
  const caseStudyLimit = planFeatures?.maxCaseStudies || 10;
  const isStarterPlan = planName === 'starter';

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Case Studies</h1>

        <div className="flex-1 mx-4 max-w-md">
          <SearchBarWrapper userRole="member" />
        </div>
      </div>

      {/* Case Study Limit Information */}
      {isStarterPlan && (
        <Alert className="mb-6 border-blue-200 bg-blue-50">
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span>
                <strong>Starter Plan:</strong> You have access to {caseStudyLimit} curated case studies.
                {data?.caseStudies && (
                  <span className="ml-2">
                    Showing {data.caseStudies.length} of {data.pagination?.total || caseStudyLimit} available.
                  </span>
                )}
              </span>
              <Button asChild size="sm" className="ml-4">
                <Link href="/pricing">
                  <ArrowUpIcon className="h-4 w-4 mr-2" />
                  Upgrade for Unlimited
                </Link>
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Limit Reached Message */}
      {hasLimitReached && limitMessage && (
        <Alert className="mb-6 border-orange-200 bg-orange-50">
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span>{limitMessage}</span>
              <Button asChild size="sm" variant="outline" className="ml-4">
                <Link href="/pricing">View Plans</Link>
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data?.caseStudies.map((caseStudy) => (
          <Card key={caseStudy.id} className="overflow-hidden">
            <CardHeader className="p-0">
              <div className="relative h-40 w-full">
                {/* First try to find a thumbnail in the icons array */}
                {(caseStudy as ExtendedCaseStudyResponse).icons?.find(icon => icon.iconType === 'icon')?.iconUrl ? (
                  <img
                    src={(caseStudy as ExtendedCaseStudyResponse).icons!.find(icon => icon.iconType === 'icon')?.iconUrl}
                    alt={caseStudy.useCaseTitle}
                    className="w-full h-full object-cover"
                  />
                ) : caseStudy.headerImage ? (
                  <img
                    src={caseStudy.headerImage}
                    alt={caseStudy.useCaseTitle}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-muted flex items-center justify-center">
                    <span className="text-muted-foreground">No image</span>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="p-4">
              <CardTitle className="text-lg mb-2 line-clamp-2">
                {caseStudy.useCaseTitle}
              </CardTitle>
              <div className="flex gap-2 mb-3">
                {caseStudy.industry && (
                  <Badge variant="outline">{caseStudy.industry}</Badge>
                )}
                {caseStudy.role && (
                  <Badge variant="outline">{caseStudy.role}</Badge>
                )}
              </div>
              <div className="text-sm text-muted-foreground mb-2 line-clamp-3">
                {(caseStudy as ExtendedCaseStudyResponse).shortDescription || 'No description available'}
              </div>
              <div className="flex justify-end mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/dashboard/case-studies/${caseStudy.id}`)}
                >
                  <EyeOpenIcon className="h-4 w-4 mr-1" />
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {data?.pagination && data.pagination.totalPages > 1 && (
        <div className="flex justify-center mt-8 gap-2">
          <Button
            variant="outline"
            onClick={() => setPage((p) => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="py-2 px-4">
            Page {page} of {data.pagination.totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setPage((p) => Math.min(data.pagination.totalPages, p + 1))}
            disabled={page === data.pagination.totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
