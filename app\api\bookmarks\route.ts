import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { bookmarks } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { and, eq, sql } from 'drizzle-orm';

export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      console.log('Unauthorized: No user ID in session');
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Check if the user is a member (not an owner)
    if (session.user.role !== 'member') {
      console.log(
        `Access denied: Non-member (role: ${session.user.role}) attempting to bookmark case study`
      );
      return new NextResponse(
        'Forbidden: Only members can bookmark case studies',
        { status: 403 }
      );
    }

    let caseStudyId;
    try {
      const body = await req.json();
      caseStudyId = body.caseStudyId;
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return new NextResponse('Invalid request body', { status: 400 });
    }

    if (!caseStudyId) {
      return new NextResponse('Case study ID is required', { status: 400 });
    }

    try {
      // Check if bookmark already exists
      const existingBookmark = await db.query.bookmarks.findFirst({
        where: and(
          eq(bookmarks.userId, session.user.id),
          eq(bookmarks.caseStudyId, caseStudyId)
        ),
      });

      if (existingBookmark) {
        return new NextResponse('Bookmark already exists', { status: 409 });
      }

      // Create new bookmark
      const result = await db
        .insert(bookmarks)
        .values({
          userId: session.user.id,
          caseStudyId: caseStudyId,
          createdAt: new Date(),
        })
        .returning();

      return NextResponse.json(result[0]);
    } catch (dbError) {
      console.error('Database error:', dbError);

      // If the bookmarks table doesn't exist yet, create it
      try {
        await db.execute(sql`
          CREATE TABLE IF NOT EXISTS bookmarks (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id),
            case_study_id INTEGER NOT NULL REFERENCES case_studies(id),
            created_at TIMESTAMP NOT NULL DEFAULT NOW()
          );
        `);

        // Try again after creating the table
        const result = await db
          .insert(bookmarks)
          .values({
            userId: session.user.id,
            caseStudyId: caseStudyId,
            createdAt: new Date(),
          })
          .returning();

        return NextResponse.json(result[0]);
      } catch (createTableError) {
        console.error('Error creating bookmarks table:', createTableError);
        return new NextResponse('Failed to create bookmarks table', {
          status: 500,
        });
      }
    }
  } catch (error) {
    console.error('Error creating bookmark:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}

export async function DELETE(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      console.log('Unauthorized: No user ID in session');
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Check if the user is a member (not an owner)
    if (session.user.role !== 'member') {
      console.log(
        `Access denied: Non-member (role: ${session.user.role}) attempting to remove bookmark`
      );
      return new NextResponse('Forbidden: Only members can manage bookmarks', {
        status: 403,
      });
    }

    let caseStudyId;
    try {
      const body = await req.json();
      caseStudyId = body.caseStudyId;
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return new NextResponse('Invalid request body', { status: 400 });
    }

    if (!caseStudyId) {
      return new NextResponse('Case study ID is required', { status: 400 });
    }

    try {
      // Delete the bookmark
      const result = await db
        .delete(bookmarks)
        .where(
          and(
            eq(bookmarks.userId, session.user.id),
            eq(bookmarks.caseStudyId, caseStudyId)
          )
        )
        .returning();

      if (result.length === 0) {
        return new NextResponse('Bookmark not found', { status: 404 });
      }

      return NextResponse.json(result[0]);
    } catch (dbError) {
      console.error('Database error:', dbError);
      // If the bookmarks table doesn't exist yet, return a 404
      return new NextResponse('Bookmark not found', { status: 404 });
    }
  } catch (error) {
    console.error('Error deleting bookmark:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}

export async function GET(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      // Return empty results for unauthenticated users instead of error
      console.log('Unauthenticated user trying to access bookmarks');
      const url = new URL(req.url);
      const caseStudyId = url.searchParams.get('caseStudyId');

      if (caseStudyId) {
        return NextResponse.json({ isBookmarked: false });
      } else {
        return NextResponse.json([]);
      }
    }

    const url = new URL(req.url);
    const caseStudyId = url.searchParams.get('caseStudyId');

    try {
      if (caseStudyId) {
        // Check if a specific case study is bookmarked
        const bookmark = await db.query.bookmarks.findFirst({
          where: and(
            eq(bookmarks.userId, session.user.id),
            eq(bookmarks.caseStudyId, parseInt(caseStudyId))
          ),
        });

        return NextResponse.json({ isBookmarked: !!bookmark });
      } else {
        // Get all bookmarks for the user
        const userBookmarks = await db.query.bookmarks.findMany({
          where: eq(bookmarks.userId, session.user.id),
          with: {
            caseStudy: true,
          },
        });

        return NextResponse.json(userBookmarks);
      }
    } catch (dbError) {
      console.error('Database error:', dbError);
      // If the bookmarks table doesn't exist yet, return empty results
      if (caseStudyId) {
        return NextResponse.json({ isBookmarked: false });
      } else {
        return NextResponse.json([]);
      }
    }
  } catch (error) {
    console.error('Error fetching bookmarks:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}
