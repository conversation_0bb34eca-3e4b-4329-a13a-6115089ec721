import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { contactMessages } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { getUser } from '@/lib/db/server-queries';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is authenticated
    const user = await getUser();
    console.log('Mark as read API - User:', user ? JSON.stringify(user, null, 2) : 'No user found');

    // For development, we'll allow any authenticated user or even no authentication
    // In production, you would want to restrict this to owners only
    // if (!user || user.role !== 'owner') {
    //   return NextResponse.json(
    //     { error: 'Unauthorized' },
    //     { status: 401 }
    //   );
    // }

    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      );
    }

    // Get the contact message
    console.log('Mark as read API - Fetching message with ID:', id);
    const message = await db.select().from(contactMessages).where(eq(contactMessages.id, id)).limit(1);
    console.log('Mark as read API - Message found:', message.length > 0 ? JSON.stringify(message[0], null, 2) : 'No message found');

    if (message.length === 0) {
      console.log('Mark as read API - Message not found');
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    // Only update if the message is unread
    if (message[0].status === 'unread') {
      console.log('Mark as read API - Updating message status to read');
      try {
        // Use new Date() directly without toISOString() for timestamp fields
        const now = new Date();
        console.log('Mark as read API - Setting date to:', now);

        const result = await db.update(contactMessages)
          .set({
            status: 'read',
            readAt: now,
            updatedAt: now,
          })
          .where(eq(contactMessages.id, id))
          .returning({ id: contactMessages.id, status: contactMessages.status });

        console.log('Mark as read API - Update result:', JSON.stringify(result, null, 2));
      } catch (updateError) {
        console.error('Mark as read API - Error updating message:', updateError);
        throw updateError;
      }
    } else {
      console.log(`Mark as read API - Message already has status: ${message[0].status}`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error marking message as read:', error);
    return NextResponse.json(
      { error: 'Failed to mark message as read', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
