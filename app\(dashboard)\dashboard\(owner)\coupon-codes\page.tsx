'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from '@/components/ui/use-toast';
import { PlusIcon, Pencil1Icon, TrashIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';

type CouponCode = {
  id: number;
  code: string;
  discountType: 'percentage' | 'fixed';
  discountAmount: number;
  description: string | null;
  maxUses: number | null;
  currentUses: number;
  validFrom: string;
  validUntil: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

type CouponFormData = {
  code: string;
  discountType: 'percentage' | 'fixed';
  discountAmount: number;
  description: string;
  maxUses: number | null;
  validFrom: string;
  validUntil: string | null;
  isActive: boolean;
};

const initialFormData: CouponFormData = {
  code: '',
  discountType: 'percentage',
  discountAmount: 10,
  description: '',
  maxUses: null,
  validFrom: new Date().toISOString().split('T')[0],
  validUntil: null,
  isActive: true,
};

export default function CouponCodesPage() {
  const [coupons, setCoupons] = useState<CouponCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState<CouponFormData>(initialFormData);
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch coupon codes
  const fetchCoupons = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/coupon-codes');

      if (!response.ok) {
        let errorMessage = 'Failed to fetch coupon codes';
        try {
          const errorData = await response.json();
          if (errorData && errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      setCoupons(data);
    } catch (error) {
      console.error('Error fetching coupon codes:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to load coupon codes',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCoupons();
  }, []);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleNumberChange = (name: string, value: string) => {
    const numberValue = value === '' ? null : Number(value);
    setFormData(prev => ({ ...prev, [name]: numberValue }));
  };

  // Reset form
  const resetForm = () => {
    setFormData(initialFormData);
    setIsEditing(false);
    setEditingId(null);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const endpoint = isEditing
      ? `/api/coupon-codes/${editingId}`
      : '/api/coupon-codes';

    const method = isEditing ? 'PATCH' : 'POST';

    // Format dates properly for API
    const formattedData = {
      ...formData,
      // Convert date strings to ISO format
      validFrom: formData.validFrom ? new Date(formData.validFrom).toISOString() : new Date().toISOString(),
      validUntil: formData.validUntil ? new Date(formData.validUntil).toISOString() : null,
    };

    console.log('Submitting data:', formattedData);

    try {
      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formattedData),
      });

      // Get response data regardless of status
      let responseData;
      try {
        responseData = await response.json();
      } catch (e) {
        console.error('Error parsing response:', e);
        responseData = {};
      }

      // Handle non-successful response
      if (!response.ok) {
        console.error('API error response:', responseData);

        // Handle validation errors specifically
        if (responseData.error === 'Validation error' && responseData.details) {
          const validationErrors = responseData.details.map((err: any) => {
            const field = err.path[0];
            return `${field}: ${err.message}`;
          }).join(', ');

          toast({
            title: 'Validation Error',
            description: validationErrors,
            variant: 'destructive',
          });
        } else {
          // Handle other errors
          const errorMessage = responseData.error || 'Failed to save coupon code';
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'destructive',
          });
        }

        return; // Exit early without closing dialog
      }

      // Success case
      toast({
        title: 'Success',
        description: isEditing
          ? 'Coupon code updated successfully'
          : 'Coupon code created successfully',
      });

      resetForm();
      setIsDialogOpen(false);
      fetchCoupons();
    } catch (error) {
      // This catches network errors or other exceptions
      console.error('Error saving coupon code:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save coupon code',
        variant: 'destructive',
      });
    }
  };

  // Edit coupon
  const handleEdit = (coupon: CouponCode) => {
    setFormData({
      code: coupon.code,
      discountType: coupon.discountType,
      discountAmount: coupon.discountAmount,
      description: coupon.description || '',
      maxUses: coupon.maxUses,
      validFrom: coupon.validFrom.split('T')[0],
      validUntil: coupon.validUntil ? coupon.validUntil.split('T')[0] : null,
      isActive: coupon.isActive,
    });
    setIsEditing(true);
    setEditingId(coupon.id);
    setIsDialogOpen(true);
  };

  // Delete coupon
  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this coupon code?')) {
      return;
    }

    try {
      const response = await fetch(`/api/coupon-codes/${id}`, {
        method: 'DELETE',
      });

      // Get response data regardless of status
      let responseData;
      try {
        responseData = await response.json();
      } catch (e) {
        console.error('Error parsing response:', e);
        responseData = {};
      }

      // Handle non-successful response
      if (!response.ok) {
        const errorMessage = responseData.error || 'Failed to delete coupon code';
        console.error('API error:', errorMessage);

        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
        return;
      }

      // Success case
      toast({
        title: 'Success',
        description: 'Coupon code deleted successfully',
      });

      fetchCoupons();
    } catch (error) {
      // This catches network errors or other exceptions
      console.error('Error deleting coupon code:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete coupon code',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Coupon Codes</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              resetForm();
              setIsDialogOpen(true);
            }}>
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Coupon Code
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{isEditing ? 'Edit Coupon Code' : 'Add Coupon Code'}</DialogTitle>
              <DialogDescription>
                {isEditing
                  ? 'Update the coupon code details below.'
                  : 'Create a new coupon code for your customers.'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="code" className="text-right">
                    Code
                  </Label>
                  <Input
                    id="code"
                    name="code"
                    value={formData.code}
                    onChange={handleInputChange}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="discountType" className="text-right">
                    Discount Type
                  </Label>
                  <Select
                    value={formData.discountType}
                    onValueChange={(value) => handleSelectChange('discountType', value)}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select discount type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="percentage">Percentage</SelectItem>
                      <SelectItem value="fixed">Fixed Amount</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="discountAmount" className="text-right">
                    Discount Amount
                  </Label>
                  <Input
                    id="discountAmount"
                    name="discountAmount"
                    type="number"
                    value={formData.discountAmount}
                    onChange={handleInputChange}
                    className="col-span-3"
                    required
                    min="0"
                    step={formData.discountType === 'percentage' ? '1' : '0.01'}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="col-span-3"
                    rows={2}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="maxUses" className="text-right">
                    Max Uses
                  </Label>
                  <Input
                    id="maxUses"
                    name="maxUses"
                    type="number"
                    value={formData.maxUses === null ? '' : formData.maxUses}
                    onChange={(e) => handleNumberChange('maxUses', e.target.value)}
                    className="col-span-3"
                    placeholder="Leave empty for unlimited"
                    min="1"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="validFrom" className="text-right">
                    Valid From
                  </Label>
                  <Input
                    id="validFrom"
                    name="validFrom"
                    type="date"
                    value={formData.validFrom}
                    onChange={handleInputChange}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="validUntil" className="text-right">
                    Valid Until
                  </Label>
                  <Input
                    id="validUntil"
                    name="validUntil"
                    type="date"
                    value={formData.validUntil || ''}
                    onChange={handleInputChange}
                    className="col-span-3"
                    placeholder="Leave empty for no expiration"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="isActive" className="text-right">
                    Active
                  </Label>
                  <div className="flex items-center space-x-2 col-span-3">
                    <Switch
                      id="isActive"
                      checked={formData.isActive}
                      onCheckedChange={(checked) => handleSwitchChange('isActive', checked)}
                    />
                    <Label htmlFor="isActive">
                      {formData.isActive ? 'Active' : 'Inactive'}
                    </Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  {isEditing ? 'Update' : 'Create'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Coupon Codes</CardTitle>
          <CardDescription>
            Manage discount codes for your customers
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading coupon codes...</p>
            </div>
          ) : coupons.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No coupon codes found</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setIsDialogOpen(true)}
              >
                Create your first coupon code
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Code</TableHead>
                  <TableHead>Discount</TableHead>
                  <TableHead>Uses</TableHead>
                  <TableHead>Valid Period</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {coupons.map((coupon) => (
                  <TableRow key={coupon.id}>
                    <TableCell className="font-medium">{coupon.code}</TableCell>
                    <TableCell>
                      {coupon.discountType === 'percentage'
                        ? `${coupon.discountAmount}%`
                        : `$${coupon.discountAmount.toFixed(2)}`}
                    </TableCell>
                    <TableCell>
                      {coupon.currentUses} / {coupon.maxUses === null ? '∞' : coupon.maxUses}
                    </TableCell>
                    <TableCell>
                      {format(new Date(coupon.validFrom), 'MMM d, yyyy')}
                      {coupon.validUntil
                        ? ` - ${format(new Date(coupon.validUntil), 'MMM d, yyyy')}`
                        : ' - No expiration'}
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        coupon.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {coupon.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(coupon)}
                        >
                          <Pencil1Icon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500"
                          onClick={() => handleDelete(coupon.id)}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
