'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSubscription } from '@/lib/hooks/use-subscription';
import { PlanFeatures } from '@/lib/auth/subscription-middleware';
import { Loader2 } from 'lucide-react';

interface WithSubscriptionOptions {
  requiredFeature?: keyof PlanFeatures;
  redirectTo?: string;
  fallback?: React.ComponentType;
  requireActiveSubscription?: boolean;
}

/**
 * Higher-order component that protects pages based on subscription status
 */
export function withSubscription<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithSubscriptionOptions = {}
) {
  const {
    requiredFeature,
    redirectTo = '/dashboard/billing',
    fallback: FallbackComponent,
    requireActiveSubscription = true,
  } = options;

  return function ProtectedComponent(props: P) {
    const router = useRouter();
    const {
      hasActiveSubscription,
      planFeatures,
      isLoading,
      error
    } = useSubscription();

    useEffect(() => {
      if (isLoading) return;

      // Check if subscription is required and user doesn't have one
      if (requireActiveSubscription && !hasActiveSubscription) {
        const currentPath = window.location.pathname;
        const redirectUrl = `${redirectTo}?error=subscription-required&redirect=${encodeURIComponent(currentPath)}`;
        router.push(redirectUrl);
        return;
      }

      // Check if specific feature is required and user doesn't have access
      if (requiredFeature && hasActiveSubscription) {
        const hasFeature = planFeatures[requiredFeature];
        if (!hasFeature) {
          const currentPath = window.location.pathname;
          const redirectUrl = `${redirectTo}?error=feature-restricted&feature=${requiredFeature}&redirect=${encodeURIComponent(currentPath)}`;
          router.push(redirectUrl);
          return;
        }
      }
    }, [
      isLoading,
      hasActiveSubscription,
      planFeatures,
      requiredFeature,
      router
    ]);

    // Don't render anything while redirecting
    if (!isLoading && requireActiveSubscription && !hasActiveSubscription) {
      return null;
    }

    if (!isLoading && requiredFeature && hasActiveSubscription) {
      const hasFeature = planFeatures[requiredFeature];
      if (!hasFeature) {
        return null;
      }
    }

    // Show loading state
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Checking subscription...</p>
          </div>
        </div>
      );
    }

    // Show error state
    if (error) {
      if (FallbackComponent) {
        return <FallbackComponent />;
      }

      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <p className="text-red-600 mb-4">Error loading subscription data</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    // Check subscription requirements
    if (requireActiveSubscription && !hasActiveSubscription) {
      // This should be handled by the useEffect redirect, but as a fallback
      if (FallbackComponent) {
        return <FallbackComponent />;
      }
      return null;
    }

    // Check feature requirements
    if (requiredFeature && hasActiveSubscription) {
      const hasFeature = planFeatures[requiredFeature];
      if (!hasFeature) {
        // This should be handled by the useEffect redirect, but as a fallback
        if (FallbackComponent) {
          return <FallbackComponent />;
        }
        return null;
      }
    }

    // All checks passed, render the component
    return <WrappedComponent {...props} />;
  };
}

/**
 * Hook for protecting specific actions based on subscription
 */
export function useSubscriptionAction() {
  const { hasActiveSubscription, planFeatures } = useSubscription();
  const router = useRouter();

  const checkAndExecute = React.useCallback(
    (
      action: () => void | Promise<void>,
      options: {
        requiredFeature?: keyof PlanFeatures;
        requireActiveSubscription?: boolean;
        onRestricted?: () => void;
      } = {}
    ) => {
      const {
        requiredFeature,
        requireActiveSubscription = true,
        onRestricted,
      } = options;

      // Check subscription requirement
      if (requireActiveSubscription && !hasActiveSubscription) {
        if (onRestricted) {
          onRestricted();
        } else {
          const currentPath = window.location.pathname;
          router.push(`/dashboard/billing?error=subscription-required&redirect=${encodeURIComponent(currentPath)}`);
        }
        return false;
      }

      // Check feature requirement
      if (requiredFeature && hasActiveSubscription) {
        const hasFeature = planFeatures[requiredFeature];
        if (!hasFeature) {
          if (onRestricted) {
            onRestricted();
          } else {
            const currentPath = window.location.pathname;
            router.push(`/dashboard/billing?error=feature-restricted&feature=${requiredFeature}&redirect=${encodeURIComponent(currentPath)}`);
          }
          return false;
        }
      }

      // Execute the action
      try {
        const result = action();
        if (result instanceof Promise) {
          result.catch(console.error);
        }
        return true;
      } catch (error) {
        console.error('Error executing protected action:', error);
        return false;
      }
    },
    [hasActiveSubscription, planFeatures, router]
  );

  return { checkAndExecute };
}

/**
 * Component for conditionally rendering content based on subscription
 */
interface SubscriptionConditionalProps {
  children: React.ReactNode;
  requiredFeature?: keyof PlanFeatures;
  requireActiveSubscription?: boolean;
  fallback?: React.ReactNode;
  inverse?: boolean; // Show content when condition is NOT met
}

export function SubscriptionConditional({
  children,
  requiredFeature,
  requireActiveSubscription = true,
  fallback = null,
  inverse = false,
}: SubscriptionConditionalProps) {
  const { hasActiveSubscription, planFeatures, isLoading } = useSubscription();

  if (isLoading) {
    return null; // Don't show anything while loading
  }

  let shouldShow = true;

  // Check subscription requirement
  if (requireActiveSubscription && !hasActiveSubscription) {
    shouldShow = false;
  }

  // Check feature requirement
  if (requiredFeature && hasActiveSubscription) {
    const hasFeature = planFeatures[requiredFeature];
    if (!hasFeature) {
      shouldShow = false;
    }
  }

  // Apply inverse logic if specified
  if (inverse) {
    shouldShow = !shouldShow;
  }

  return shouldShow ? <>{children}</> : <>{fallback}</>;
}
