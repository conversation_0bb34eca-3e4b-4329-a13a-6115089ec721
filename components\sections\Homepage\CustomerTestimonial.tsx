import { TestimonialCard } from '@/components/magicui/testimonial-card';
import { AnimatedGridPattern } from '@/components/magicui/animated-grid-pattern';
import { Badge } from '@/components/ui/badge';
import { testimonials } from '@/lib/constants';
import { Marquee } from '@/components/magicui/marquee';

export default function CustomerTestimonial() {
  // Split testimonials into two arrays - first row for standard cards, second row for featured

  const firstRowTestimonials = testimonials.slice(0, 5);
  const secondRowTestimonials = testimonials.slice(5, 10);
  return (
    <section className='relative pb-10 md:py-32 w-full overflow-hidden'>
      {/* Animated Grid Background */}
      <div className='absolute inset-0'>
        <AnimatedGridPattern
          width={60}
          height={60}
          className='text-gray-200/80 md:hidden'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={10}
          x={0}
          y={0}
        />
        <AnimatedGridPattern
          width={150}
          height={150}
          className='text-gray-200/80 hidden md:block'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={20}
          x={0}
          y={0}
        />
      </div>

      {/* Radial Gradients */}
      <div className='absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0)_0%,rgba(255,255,255,0.2)_25%,rgba(255,255,255,0.9)_50%,rgba(255,255,255,1)_75%)] pointer-events-none'></div>

      <div className='relative z-10 container mx-auto px-4 md:px-6 lg:px-8'>
        {/* Header */}
        <div className='text-center space-y-6 mb-16'>
          <Badge
            variant='secondary'
            className='px-3 md:px-4 py-1.5 text-sm bg-white/95 backdrop-blur-sm border-none shadow-md text-gray-600'
          >
            Trusted by Leaders
          </Badge>
          <h2 className='text-3xl md:text-5xl font-bold'>
            What <span className='text-blue-500'>customer</span> say about us
          </h2>
          <p className='text-base md:text-lg text-gray-600 max-w-2xl mx-auto'>
            A platform with 400+ AI use cases to help business discover, adapt,
            and implement intelligent solutions.
          </p>
        </div>

        {/* Testimonials Marquees */}
        <div className='space-y-4 sm:space-y-6 md:space-y-8'>
          {/* First Marquee */}
          <div className='relative'>
            {/* Left fade */}
            <div className='absolute left-0 top-0 bottom-0 w-8 xs:w-12 sm:w-16 md:w-20 lg:w-40 bg-gradient-to-r from-white to-transparent z-20'></div>
            {/* Right fade */}
            <div className='absolute right-0 top-0 bottom-0 w-8 xs:w-12 sm:w-16 md:w-20 lg:w-40 bg-gradient-to-l from-white to-transparent z-20'></div>

            <Marquee className='pt-4' pauseOnHover={true} speed='fast'>
              {firstRowTestimonials.map((testimonial, index) => (
                <TestimonialCard
                  key={index}
                  {...testimonial}
                  className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border border-[#EAEFF4]'
                />
              ))}
            </Marquee>
          </div>

          {/* Second Marquee */}
          <div className='relative'>
            {/* Left fade */}
            <div className='absolute left-0 top-0 bottom-0 w-8 xs:w-12 sm:w-16 md:w-20 lg:w-40 bg-gradient-to-r from-white to-transparent z-20'></div>
            {/* Right fade */}
            <div className='absolute right-0 top-0 bottom-0 w-8 xs:w-12 sm:w-16 md:w-20 lg:w-40 bg-gradient-to-l from-white to-transparent z-20'></div>

            <Marquee
              className='py-4'
              pauseOnHover={true}
              reverse={true}
              speed='normal'
            >
              {secondRowTestimonials.map((testimonial, index) => (
                <TestimonialCard
                  key={index}
                  {...testimonial}
                  variant='featured'
                  className='bg-gradient-to-b from-[#FFFFFF] to-[#F3F3F3] border border-[#EAEFF4]'
                />
              ))}
            </Marquee>
          </div>
        </div>
      </div>
    </section>
  );
}
