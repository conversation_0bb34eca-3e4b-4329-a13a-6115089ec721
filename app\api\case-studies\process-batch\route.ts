import { NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { db } from '@/lib/db/drizzle';
import { caseStudies, activityLogs, ActivityType } from '@/lib/db/schema';
import { and, isNull, eq, or, isNotNull, lt } from 'drizzle-orm';
import { processCaseStudiesBatch, updateCaseStudyWithMIUAgentData } from '@/lib/api/miuagent';

// Configuration for batch processing
const BATCH_CONFIG = {
  // Number of cases to fetch from the database in a single query
  FETCH_SIZE: 100,

  // Number of cases to process in a single chunk
  CHUNK_SIZE: 50,

  // Number of cases to process concurrently
  CONCURRENCY: 3,

  // Maximum number of retries for failed requests
  MAX_RETRIES: 3,

  // Initial delay before retrying (ms)
  INITIAL_RETRY_DELAY: 1000,

  // Request timeout (ms) - 2 minutes
  REQUEST_TIMEOUT: 120000,

  // Whether to enable verbose logging
  VERBOSE_LOGGING: false
};

// Maximum time for the entire batch processing (10 minutes)
const OVERALL_TIMEOUT = 10 * 60 * 1000;

// Process all cases regardless of when they were last processed
export async function POST(req: Request) {
  // Create a controller to abort the request if it takes too long
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
  }, OVERALL_TIMEOUT);

  try {
    // Check for API key authentication
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const apiKey = authHeader.split(' ')[1];
    // if (apiKey !== process.env.BATCH_PROCESSING_API_KEY) {
    //   return new NextResponse('Invalid API key', { status: 401 });
    // }

    // Get all cases for processing without checking when they were last processed
    const casesToProcess = await db.query.caseStudies.findMany({
      where: and(
        // isNull(caseStudies.deletedAt)
      ),
      limit: BATCH_CONFIG.FETCH_SIZE,
    });

    if (casesToProcess.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No cases to process',
        processed: 0,
      });
    }

    console.log(`Found ${casesToProcess.length} cases to process`);

    // Process the cases with optimized configuration
    const result = await processCaseStudiesBatch(casesToProcess, {
      chunkSize: BATCH_CONFIG.CHUNK_SIZE,
      concurrency: BATCH_CONFIG.CONCURRENCY,
      maxRetries: BATCH_CONFIG.MAX_RETRIES,
      initialRetryDelay: BATCH_CONFIG.INITIAL_RETRY_DELAY,
      requestTimeout: BATCH_CONFIG.REQUEST_TIMEOUT,
      verboseLogging: BATCH_CONFIG.VERBOSE_LOGGING
    });

    // Update the database with the results
    console.log('\n==== PROCESSING RESULTS ====');
    console.log('Total results:', result.results.length);
    console.log('Successful:', result.successful);
    console.log('Failed:', result.failed);
    console.log('==========================\n');

    for (const caseResult of result.results) {
      const caseStudy = casesToProcess.find(c => c.id === caseResult.id);
      if (!caseStudy) {
        console.log(`Case study with ID ${caseResult.id} not found in the batch`);
        continue;
      }

      if (caseResult.success) {
        console.log('\n==== CASE RESULT DATA ====');
        console.log('Case Study ID:', caseStudy.id);
        console.log('Success:', caseResult.success);
        console.log('Request ID:', caseResult.requestId);
        console.log('Has data property:', 'data' in caseResult);
        console.log('Data is null or undefined:', caseResult.data == null);
        if (caseResult.data) {
          console.log('Data type:', typeof caseResult.data);
          console.log('Data keys:', Object.keys(caseResult.data));
        }
        console.log('==========================\n');

        // Get the updated case study data with AI-processed information
        const updatedCaseData = caseResult.data ?
          updateCaseStudyWithMIUAgentData(caseStudy, caseResult.data) :
          {};

        console.log('\n==== UPDATING CASE STUDY WITH AI DATA ====');
        console.log('Case Study ID:', caseStudy.id);
        console.log('Has AI data:', !!caseResult.data);
        console.log('Updated data keys:', Object.keys(updatedCaseData));
        console.log('Market Intelligence Data present:', !!updatedCaseData.marketIntelligenceData);
        console.log('Market Metrics Data present:', !!updatedCaseData.marketMetricsData);

        // Prepare the update data
        const updateData = {
          // Only include these fields if they exist in updatedCaseData
          ...(updatedCaseData.marketIntelligenceData && { marketIntelligenceData: updatedCaseData.marketIntelligenceData }),
          ...(updatedCaseData.marketMetricsData && { marketMetricsData: updatedCaseData.marketMetricsData }),
          ...(updatedCaseData.marketSize && { marketSize: updatedCaseData.marketSize }),
          ...(updatedCaseData.marketCAGR && { marketCAGR: updatedCaseData.marketCAGR }),
          ...(updatedCaseData.marketROI && { marketROI: updatedCaseData.marketROI }),

          // Include any other fields from the response that should be updated
          ...(updatedCaseData.industry && { industry: updatedCaseData.industry }),
          ...(updatedCaseData.role && { role: updatedCaseData.role }),
          ...(updatedCaseData.vector && { vector: updatedCaseData.vector }),

          // Include introduction fields if they exist
          ...(updatedCaseData.introductionTitle && { introductionTitle: updatedCaseData.introductionTitle }),
          ...(updatedCaseData.introductionText && { introductionText: updatedCaseData.introductionText }),

          // Include challenge fields if they exist
          ...(updatedCaseData.challange1 && { challange1: updatedCaseData.challange1 }),
          ...(updatedCaseData.challange2 && { challange2: updatedCaseData.challange2 }),
          ...(updatedCaseData.challange3 && { challange3: updatedCaseData.challange3 }),

          // Always include these fields
          aiProcessed: new Date(),
          aiProcessingStatus: 'success',
          aiProcessingError: null,
          aiRequestId: caseResult.requestId || null,
          updatedAt: new Date(),
        };

        console.log('\n==== DATABASE UPDATE DATA ====');
        console.log('Update data keys:', Object.keys(updateData));
        console.log('Market Intelligence Data included:', !!updateData.marketIntelligenceData);
        console.log('Market Metrics Data included:', !!updateData.marketMetricsData);

        // Log additional fields for better debugging
        console.log('Industry updated:', updateData.industry || 'No');
        console.log('Role updated:', updateData.role || 'No');
        console.log('Vector updated:', updateData.vector || 'No');
        console.log('Introduction Title updated:', updateData.introductionTitle || 'No');
        console.log('Introduction Text updated:', updateData.introductionText || 'No');
        console.log('Challenge 1 updated:', updateData.challange1 || 'No');
        console.log('Challenge 2 updated:', updateData.challange2 || 'No');
        console.log('Challenge 3 updated:', updateData.challange3 || 'No');
        console.log('============================\n');

        // Update the case study with the AI-processed data
        try {
          // Ensure market size is an integer if present
          if (updateData.marketSize !== undefined && updateData.marketSize !== null) {
            // Convert to integer if it's not already
            if (typeof updateData.marketSize === 'number') {
              updateData.marketSize = Math.floor(updateData.marketSize);
            } else if (typeof updateData.marketSize === 'string') {
              updateData.marketSize = Math.floor(parseFloat(updateData.marketSize));
            }

            // Log the converted value
            console.log('Converted market size to integer:', updateData.marketSize);
          }

          await db
            .update(caseStudies)
            .set(updateData)
            .where(eq(caseStudies.id, caseStudy.id));

          console.log('Case study updated successfully');
        } catch (error) {
          console.error('Error updating case study in database:', error);

          // Try again with a simplified update that excludes problematic fields
          try {
            console.log('Attempting simplified update without market metrics...');

            // Create a simplified update without the problematic fields
            const safeUpdateData = {
              aiProcessed: new Date(),
              aiProcessingStatus: 'success',
              aiProcessingError: null,
              aiRequestId: caseResult.requestId || null,
              updatedAt: new Date(),
            };

            await db
              .update(caseStudies)
              .set(safeUpdateData)
              .where(eq(caseStudies.id, caseStudy.id));

            console.log('Simplified case study update successful');
          } catch (fallbackError) {
            console.error('Even simplified update failed:', fallbackError);
          }
        }

        console.log('====================================\n');

        // Log the activity
        await db.insert(activityLogs).values({
          teamId: 1, // Default team ID, adjust as needed
          userId: null, // System action
          action: ActivityType.AI_PROCESS_CASE,
          ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
        });
      } else {
        // Update the case study with the error
        await db
          .update(caseStudies)
          .set({
            aiProcessingStatus: 'error',
            aiProcessingError: caseResult.error || 'Unknown error',
            updatedAt: new Date(),
          })
          .where(eq(caseStudies.id, caseStudy.id));
      }
    }

    // Clear the timeout since we're done
    clearTimeout(timeoutId);

    return NextResponse.json({
      success: true,
      message: `Processed ${result.processed} cases`,
      processed: result.processed,
      successful: result.successful,
      failed: result.failed,
    });
  } catch (error) {
    // Clear the timeout in case of error
    clearTimeout(timeoutId);

    // Check if this was an abort error
    if (error instanceof DOMException && error.name === 'AbortError') {
      console.error('Batch processing aborted due to timeout');
      return new NextResponse(
        JSON.stringify({
          success: false,
          error: 'Timeout Error',
          message: `Batch processing exceeded the maximum allowed time of ${OVERALL_TIMEOUT / 60000} minutes`,
        }),
        { status: 408, headers: { 'Content-Type': 'application/json' } }
      );
    }

    console.error('Error processing batch:', error);
    return new NextResponse(
      JSON.stringify({
        success: false,
        error: 'Internal Error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
