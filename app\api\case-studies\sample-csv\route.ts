import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

export async function GET() {
  try {
    // Read the sample CSV file from the public directory
    const filePath = path.join(process.cwd(), 'public', 'case-study-template.csv');
    const fileContent = await fs.readFile(filePath, 'utf-8');

    // Return the file with appropriate headers
    return new NextResponse(fileContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="case-studies-template.csv"',
      },
    });
  } catch (error) {
    console.error('Error serving sample CSV:', error);
    return new NextResponse('Error serving sample CSV', { status: 500 });
  }
}