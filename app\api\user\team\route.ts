import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { getTeamForUser } from '@/lib/db/queries';

export async function GET(request: NextRequest) {
  try {
    const user = await getUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Check if user has a team association
    // We'll verify actual team membership in the getTeamForUser function
    // This allows any user with a team ID to access their team data
    if (!user.teamId) {
      return NextResponse.json(
        { error: 'No team association found' },
        { status: 403 }
      );
    }

    const team = await getTeam<PERSON>orUser(user.id);

    if (!team) {
      return NextResponse.json({ error: 'No team found' }, { status: 404 });
    }

    // Return only the necessary data for subscription management
    return NextResponse.json({
      id: team.id,
      name: team.name,
      planName: team.planName,
      subscriptionStatus: team.subscriptionStatus,
      stripeCustomerId: team.stripeCustomerId,
      stripeSubscriptionId: team.stripeSubscriptionId,
    });
  } catch (error) {
    console.error('Error fetching team data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team data' },
      { status: 500 }
    );
  }
}
