/**
 * Memoization utility for optimizing expensive calculations
 * and function calls with caching based on input parameters.
 */

interface MemoizeOptions {
  // Maximum cache size to prevent memory leaks
  maxSize?: number;
  // Time-to-live in milliseconds (0 means no expiry)
  ttl?: number;
  // Cache key generator function
  keyGenerator?: (...args: any[]) => string;
}

// Default simple cache key generator
const defaultKeyGenerator = (...args: any[]): string => {
  return JSON.stringify(args);
};

/**
 * Memoize a function to cache its results based on input parameters
 * 
 * @param fn Function to memoize
 * @param options Memoization options
 * @returns Memoized function
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  options: MemoizeOptions = {}
): T {
  const {
    maxSize = 100,
    ttl = 0,
    keyGenerator = defaultKeyGenerator,
  } = options;

  // Cache to store function results
  const cache = new Map<string, { value: any; expiry?: number }>();
  
  // Return memoized function
  const memoized = ((...args: Parameters<T>): ReturnType<T> => {
    // Generate cache key
    const key = keyGenerator(...args);
    
    // Current time for TTL checking
    const now = Date.now();
    
    // Check if result is cached and not expired
    if (cache.has(key)) {
      const cached = cache.get(key)!;
      
      // Check if cached value has expired
      if (!cached.expiry || cached.expiry > now) {
        return cached.value;
      }
      
      // Remove expired entry
      cache.delete(key);
    }
    
    // Calculate new value
    const result = fn(...args);
    
    // Handle promise results
    if (result instanceof Promise) {
      return result.then(value => {
        // Cache the resolved value
        cache.set(key, {
          value,
          expiry: ttl ? now + ttl : undefined,
        });
        
        // Enforce max size
        if (cache.size > maxSize) {
          const firstKey = cache.keys().next().value;
          cache.delete(firstKey);
        }
        
        return value;
      }) as ReturnType<T>;
    }
    
    // Cache the result
    cache.set(key, {
      value: result,
      expiry: ttl ? now + ttl : undefined,
    });
    
    // Enforce max size
    if (cache.size > maxSize) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    return result;
  }) as T;
  
  // Add cache control functions
  Object.defineProperties(memoized, {
    clearCache: {
      value: () => cache.clear(),
    },
    getCacheSize: {
      value: () => cache.size,
    },
    getCacheStats: {
      value: () => ({
        size: cache.size,
        maxSize,
        hitRate: '--', // Would require tracking in production
      }),
    },
  });
  
  return memoized;
}

/**
 * Memoize a function that returns a promise
 * Specialized version of memoize for async functions
 * 
 * @param fn Async function to memoize
 * @param options Memoization options
 * @returns Memoized async function
 */
export function memoizeAsync<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: MemoizeOptions = {}
): T {
  return memoize(fn, options);
}

/**
 * Create a memoized selector function for Redux-like state
 * Excellent for optimizing expensive derived data calculations
 * 
 * @param selectors Input selector functions
 * @param resultFn Function to combine selector results
 * @param options Memoization options
 * @returns Memoized selector function
 */
export function createSelector<R>(
  selectors: ((...args: any[]) => any)[],
  resultFn: (...selectorResults: any[]) => R,
  options: MemoizeOptions = {}
): (...args: any[]) => R {
  const memoizedResultFn = memoize(resultFn, options);
  
  return (...args: any[]): R => {
    const selectorResults = selectors.map(selector => selector(...args));
    return memoizedResultFn(...selectorResults);
  };
}

export default memoize;
