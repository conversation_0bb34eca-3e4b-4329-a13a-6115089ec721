import { db } from '@/lib/db/drizzle';
import {
  plans,
  userSubscriptions,
  subscriptionHistory,
  teams,
  teamMembers
} from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { stripe } from '@/lib/payments/stripe';
import { getTeamForUser } from '@/lib/db/queries';

/**
 * Get a plan by name
 */
export async function getPlanByName(planName: string) {
  return db.query.plans.findFirst({
    where: eq(plans.name, planName.toLowerCase()),
  });
}

/**
 * Get a plan by ID
 */
export async function getPlanById(planId: number) {
  return db.query.plans.findFirst({
    where: eq(plans.id, planId),
  });
}

/**
 * Get all active plans
 */
export async function getActivePlans(includeNonPublic = false) {
  let query = db.select().from(plans);

  // Apply filters
  if (includeNonPublic) {
    return query.where(eq(plans.isActive, true));
  } else {
    return query.where(and(
      eq(plans.isActive, true),
      eq(plans.isPublic, true)
    ));
  }
}

/**
 * Get a user's current subscription
 */
export async function getUserSubscription(userId: number) {
  const team = await getTeamForUser(userId);
  if (!team) return null;

  return db.query.userSubscriptions.findFirst({
    where: eq(userSubscriptions.teamId, team.id),
    with: {
      plan: true,
    },
    orderBy: (userSubscriptions, { desc }) => [desc(userSubscriptions.createdAt)],
  });
}

/**
 * Create a new subscription record
 */
export async function createSubscription({
  userId,
  teamId,
  planId,
  stripeSubscriptionId,
  stripeCustomerId,
  status,
  currentPeriodStart,
  currentPeriodEnd,
  trialStart,
  trialEnd,
}: {
  userId: number;
  teamId: number;
  planId: number;
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
  status: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  trialStart?: Date;
  trialEnd?: Date;
}) {
  // Create the subscription
  const [subscription] = await db.insert(userSubscriptions).values({
    userId,
    teamId,
    planId,
    stripeSubscriptionId,
    stripeCustomerId,
    status,
    currentPeriodStart,
    currentPeriodEnd,
    trialStart,
    trialEnd,
  }).returning();

  // Record the subscription history
  await db.insert(subscriptionHistory).values({
    userId,
    teamId,
    planId,
    stripeSubscriptionId,
    action: 'created',
    newStatus: status,
  });

  // Update the team record with the subscription info
  await db.update(teams)
    .set({
      stripeCustomerId,
      stripeSubscriptionId,
      planName: (await getPlanById(planId))?.name || null,
      subscriptionStatus: status,
      updatedAt: new Date(),
    })
    .where(eq(teams.id, teamId));

  return subscription;
}

/**
 * Update a subscription record
 */
export async function updateSubscription(
  subscriptionId: number,
  data: {
    planId?: number;
    status?: string;
    currentPeriodStart?: Date;
    currentPeriodEnd?: Date;
    cancelAtPeriodEnd?: boolean;
    canceledAt?: Date | null;
    trialStart?: Date | null;
    trialEnd?: Date | null;
  }
) {
  // Get the current subscription
  const currentSubscription = await db.query.userSubscriptions.findFirst({
    where: eq(userSubscriptions.id, subscriptionId),
  });

  if (!currentSubscription) {
    throw new Error('Subscription not found');
  }

  // Update the subscription
  const [updatedSubscription] = await db.update(userSubscriptions)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(eq(userSubscriptions.id, subscriptionId))
    .returning();

  // Record the subscription history
  await db.insert(subscriptionHistory).values({
    userId: currentSubscription.userId,
    teamId: currentSubscription.teamId,
    planId: data.planId || currentSubscription.planId,
    stripeSubscriptionId: currentSubscription.stripeSubscriptionId || undefined,
    action: 'updated',
    previousStatus: currentSubscription.status,
    newStatus: data.status || currentSubscription.status,
    previousPlanId: data.planId ? currentSubscription.planId : undefined,
  });

  // If the plan or status changed, update the team record
  if (data.planId || data.status) {
    let planName = null;
    if (data.planId) {
      const plan = await getPlanById(data.planId);
      planName = plan?.name || null;
    }

    await db.update(teams)
      .set({
        planName: planName || undefined,
        subscriptionStatus: data.status || undefined,
        updatedAt: new Date(),
      })
      .where(eq(teams.id, currentSubscription.teamId));
  }

  return updatedSubscription;
}

/**
 * Cancel a subscription
 */
export async function cancelSubscription(subscriptionId: number, cancelImmediately = false) {
  // Get the current subscription
  const currentSubscription = await db.query.userSubscriptions.findFirst({
    where: eq(userSubscriptions.id, subscriptionId),
  });

  if (!currentSubscription) {
    throw new Error('Subscription not found');
  }

  // If there's a Stripe subscription, cancel it there first
  if (currentSubscription.stripeSubscriptionId) {
    try {
      await stripe.subscriptions.update(currentSubscription.stripeSubscriptionId, {
        cancel_at_period_end: !cancelImmediately,
      });

      if (cancelImmediately) {
        await stripe.subscriptions.cancel(currentSubscription.stripeSubscriptionId);
      }
    } catch (error) {
      console.error('Error canceling Stripe subscription:', error);
      // Continue with local cancellation even if Stripe fails
    }
  }

  // Update the subscription
  const [updatedSubscription] = await db.update(userSubscriptions)
    .set({
      status: cancelImmediately ? 'canceled' : 'active',
      cancelAtPeriodEnd: !cancelImmediately,
      canceledAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(userSubscriptions.id, subscriptionId))
    .returning();

  // Record the subscription history
  await db.insert(subscriptionHistory).values({
    userId: currentSubscription.userId,
    teamId: currentSubscription.teamId,
    planId: currentSubscription.planId,
    stripeSubscriptionId: currentSubscription.stripeSubscriptionId || undefined,
    action: cancelImmediately ? 'canceled' : 'scheduled_cancellation',
    previousStatus: currentSubscription.status,
    newStatus: cancelImmediately ? 'canceled' : 'active',
  });

  // If canceling immediately, update the team record
  if (cancelImmediately) {
    await db.update(teams)
      .set({
        planName: null,
        subscriptionStatus: 'canceled',
        updatedAt: new Date(),
      })
      .where(eq(teams.id, currentSubscription.teamId));
  }

  return updatedSubscription;
}

/**
 * Handle a subscription update from Stripe
 */
export async function handleStripeSubscriptionUpdate(
  stripeSubscriptionId: string,
  status: string,
  customerId: string,
  priceId: string,
  productId: string,
  currentPeriodStart: number,
  currentPeriodEnd: number,
  cancelAtPeriodEnd: boolean,
  canceledAt: number | null,
  trialStart: number | null,
  trialEnd: number | null
) {
  // Find the team with this Stripe customer ID
  const team = await db.query.teams.findFirst({
    where: eq(teams.stripeCustomerId, customerId),
  });

  if (!team) {
    throw new Error('Team not found for Stripe customer ID: ' + customerId);
  }

  // Get the plan type from the product metadata
  let planName = 'starter'; // Default to starter if no metadata

  try {
    // Fetch the product from Stripe to get its metadata
    const product = await stripe.products.retrieve(productId);

    // Get the plan type from metadata
    if (product.metadata && product.metadata.plan_type) {
      planName = product.metadata.plan_type.toLowerCase();
    }

    console.log(`Retrieved plan type from Stripe product metadata: ${planName}`);
  } catch (error) {
    console.error('Error fetching product from Stripe:', error);
  }

  // Find the plan with this name
  const plan = await db.query.plans.findFirst({
    where: eq(plans.name, planName),
  });

  // If plan doesn't exist in the database, create it
  let planId: number;

  if (!plan) {
    console.log(`Plan not found for name: ${planName}, creating it...`);

    // Create a new plan in the database
    const [newPlan] = await db.insert(plans).values({
      name: planName,
      displayName: planName.charAt(0).toUpperCase() + planName.slice(1),
      description: `${planName.charAt(0).toUpperCase() + planName.slice(1)} Plan`,
      priceMonthly: 0, // We'll get this from Stripe
      stripePriceIdMonthly: priceId,
      stripeProductId: productId,
      features: { features: [] },
      maxUsers: planName === 'pro' ? 3 : 1,
      isActive: true,
      isPublic: true,
    }).returning();

    planId = newPlan.id;
  } else {
    planId = plan.id;

    // Update the plan with the latest Stripe information
    await db.update(plans)
      .set({
        stripePriceIdMonthly: priceId,
        stripeProductId: productId,
        updatedAt: new Date(),
      })
      .where(eq(plans.id, planId));
  }

  // Find the user who owns the team
  const teamOwner = await db.query.teamMembers.findFirst({
    where: and(
      eq(teamMembers.teamId, team.id),
      eq(teamMembers.role, 'owner')
    ),
  });

  if (!teamOwner) {
    throw new Error('Team owner not found for team ID: ' + team.id);
  }

  // Find the existing subscription
  const existingSubscription = await db.query.userSubscriptions.findFirst({
    where: eq(userSubscriptions.stripeSubscriptionId, stripeSubscriptionId),
  });

  // Always update the team record with the latest subscription information
  await db.update(teams)
    .set({
      stripeSubscriptionId,
      stripeProductId: productId,
      planName: planName,
      subscriptionStatus: status,
      updatedAt: new Date(),
    })
    .where(eq(teams.id, team.id));

  console.log(`Updated team ${team.id} with subscription status: ${status}, plan: ${planName}`);

  if (existingSubscription) {
    // Update the existing subscription
    return updateSubscription(existingSubscription.id, {
      planId: planId,
      status,
      currentPeriodStart: new Date(currentPeriodStart * 1000),
      currentPeriodEnd: new Date(currentPeriodEnd * 1000),
      cancelAtPeriodEnd,
      canceledAt: canceledAt ? new Date(canceledAt * 1000) : null,
      trialStart: trialStart ? new Date(trialStart * 1000) : null,
      trialEnd: trialEnd ? new Date(trialEnd * 1000) : null,
    });
  } else {
    // Create a new subscription
    return createSubscription({
      userId: teamOwner.userId,
      teamId: team.id,
      planId: planId,
      stripeSubscriptionId,
      stripeCustomerId: customerId,
      status,
      currentPeriodStart: new Date(currentPeriodStart * 1000),
      currentPeriodEnd: new Date(currentPeriodEnd * 1000),
      trialStart: trialStart ? new Date(trialStart * 1000) : undefined,
      trialEnd: trialEnd ? new Date(trialEnd * 1000) : undefined,
    });
  }
}
