import { NextResponse } from 'next/server';

export async function GET(req: Request) {
  return NextResponse.json({
    success: true,
    message: 'Test endpoint working',
    env: {
      cronSecretExists: !!process.env.CRON_SECRET,
      batchProcessingApiKeyExists: !!process.env.BATCH_PROCESSING_API_KEY,
      miuagentApiKeyExists: !!process.env.MIUAGENT_API_KEY,
      baseUrl: process.env.BASE_URL || 'http://localhost:3000',
    }
  });
}
