-- Add missing fields from CSV to case_studies table
ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "impact_metric_1" text;
ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "impact_value_1" text;
ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "impact_metric_2" text;
ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "impact_value_2" text;
ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "impact_metric_3" text;
ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "impact_value_3" text;
ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "impact_metric_4" text;
ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "impact_value_4" text;
ALTER TABLE "case_studies" ADD COLUMN IF NOT EXISTS "conclusion_result_title" text;
