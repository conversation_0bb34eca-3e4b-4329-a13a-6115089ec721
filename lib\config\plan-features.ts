// Shared plan features configuration
// This file ensures consistency between pricing page and billing page

export interface PlanFeature {
  name: string;
  included: boolean;
}

export interface PlanConfig {
  features: PlanFeature[];
  description: string;
  maxUsers: number;
  isPopular: boolean;
  cta: string;
  color: string;
  note: string;
}

export const planFeaturesConfig: Record<string, PlanConfig> = {
  starter: {
    features: [
      { name: '14 Day Free Trial', included: true },
      { name: 'Access to 10 curated AI use cases', included: true },
      { name: 'Preview of Market Intelligence Unit (MIU)', included: true },
      { name: '1 registered user', included: true },
      { name: 'Unlimited use case access', included: false },
      { name: 'Full MIU access', included: false },
      { name: 'Roadmap tools & prioritization framework', included: false },
      { name: 'Team collaboration features', included: false },
      { name: 'Dedicated account support', included: false },
      { name: 'Quarterly AI trend reports', included: false },
    ],
    description: 'Perfect for individuals getting started - 14 day free trial',
    maxUsers: 1,
    isPopular: false,
    cta: 'Get Started Now',
    color: 'white',
    note: 'Perfect for individuals testing the waters',
  },
  pro: {
    features: [
      { name: 'Unlimited access to full AI use case library', included: true },
      { name: 'Full Market Intelligence Unit (MIU) access', included: true },
      { name: 'Roadmap tools & prioritization framework', included: true },
      { name: 'Up to 3 users', included: true },
      { name: 'Team collaboration features', included: true },
      { name: 'Priority support', included: false },
      { name: 'Quarterly AI trend reports', included: false },
      { name: 'Dedicated account support', included: false },
    ],
    description: 'For teams and businesses ready to scale with AI',
    maxUsers: 3, // Up to 3 users
    isPopular: true,
    cta: 'Get Started Now',
    color: 'bg-blue-500 bg-[#2563eb]',
    note: 'For teams evaluating, prioritizing, and implementing AI initiatives',
  },
  enterprise: {
    features: [
      { name: 'Everything in Pro', included: true },
      { name: 'Up to 7 CXO-level users', included: true },
      { name: 'Priority support', included: true },
      { name: 'Quarterly AI trend reports & adoption benchmarks', included: true },
      { name: 'Dedicated account support', included: true },
      { name: 'Custom branding', included: true },
      { name: 'SSO integration', included: true },
      { name: 'Custom integrations', included: true },
      { name: 'Custom onboarding and strategy sessions', included: true },
      { name: 'Early access to new features', included: true },
    ],
    description: 'For large organizations with advanced needs',
    maxUsers: 7, // Up to 7 CXO-level users
    isPopular: false,
    cta: 'Contact Us',
    color: 'white',
    note: 'For enterprises shaping AI strategy at scale',
  },
};

// Helper function to get plan configuration
export function getPlanConfig(planType: string): PlanConfig | null {
  return planFeaturesConfig[planType] || null;
}

// Helper function to get all plan types
export function getAllPlanTypes(): string[] {
  return Object.keys(planFeaturesConfig);
}
