import { NextResponse } from 'next/server';
import { getUser } from '@/lib/db/server-queries';
import { getMembersWithActivePlans } from '@/lib/db/member-queries';

export async function GET(
  request: Request,
  { params }: { params: { teamId: string } }
) {
  try {
    const user = await getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const teamId = parseInt(params.teamId);
    if (isNaN(teamId)) {
      return NextResponse.json({ error: 'Invalid team ID' }, { status: 400 });
    }
    
    const members = await getMembersWithActivePlans(teamId);
    return NextResponse.json(members);
  } catch (error) {
    console.error('Error fetching members with active plans:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
