import { NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';

/**
 * This endpoint is deprecated.
 * Cloudinary uploads should be handled directly from the client.
 */
export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    return new NextResponse(
      JSON.stringify({
        error: 'Deprecated API',
        message: 'This API endpoint is deprecated. Please use Cloudinary direct uploads instead.',
      }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error:', error);
    return new NextResponse(
      JSON.stringify({
        error: 'Internal Error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}