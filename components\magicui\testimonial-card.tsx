import { cn } from "@/lib/utils";

interface TestimonialCardProps {
  name: string;
  role: string;
  testimonial: string;
  avatar: string;
  isEdgeCard?: boolean;
  position?: "left" | "right";
  variant?: "default" | "featured";
  className?: string;
}

export function TestimonialCard({
  name,
  role,
  testimonial,
  avatar,
  isEdgeCard,
  position,
  variant = "default",
  className,
}: TestimonialCardProps) {
  return (
    <div
      className={cn(
        "rounded-2xl p-6 mx-3",
        variant === "default" ? "w-[280px]" : "w-[320px]",
        className
      )}
    >
      <div className="space-y-4">
        {/* Quote */}
        <p className="text-black text-sm leading-relaxed">{testimonial}</p>
        
        {/* Separator Line */}
        <div className="h-px bg-[#EAEFF4] w-full"></div>

        <div className="flex justify-between items-center space-y-3">
        
          {/* Profile */}
          <div className="flex items-center gap-3 ">
            {/* <img
              src={avatar}
              alt={name}
              className="w-10 h-10 rounded-full object-cover"
            /> */}
           <div className="flex flex-col gap-1">
           <div>
              <p className="text-sm text-gray-500">{name}</p>
              <h4 className="font-semibold text-gray-900">{role}</h4>
            </div>

              {/* Stars */}
              <div className="flex gap-0.5">
            {[1, 2, 3, 4, 5].map((star) => (
              <svg
                key={star}
                className="w-4 h-4"
                viewBox="0 0 20 20"
                fill="yellow"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
           </div>
            
          </div>

          
        </div>
      </div>
    </div>
  );
} 