'use client';

import { use } from 'react';
import { useUser } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, Eye, FileText, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MarketIntelligenceCard } from '@/components/MarketIntelligenceCard';
import { Skeleton } from '@/components/ui/skeleton';

interface CaseStudy {
  id: number;
  useCaseTitle: string;
  industry: string | null;
  role: string | null;
  featureImageUrl: string | null;
  headerImage?: string | null;
  marketIntelligenceData?: string | null;
  marketMetricsData?: string | null;
}

export default function ReportsPage() {
  const router = useRouter();
  const { userPromise } = useUser();
  const user = use(userPromise);

  // State for case studies with market intelligence
  const [caseStudies, setCaseStudies] = useState<CaseStudy[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCaseStudies, setFilteredCaseStudies] = useState<CaseStudy[]>([]);

  // Fetch case studies
  useEffect(() => {
    const fetchCaseStudies = async () => {
      try {
        setLoading(true);
        // Reset pagination when search changes
        setPage(1);
        setHasMore(true);

        const response = await fetch(`/api/case-studies?page=1&pageSize=${pageSize}${searchQuery ? `&search=${searchQuery}` : ''}`);
        if (!response.ok) throw new Error('Failed to fetch case studies');

        const data = await response.json();
        const fetchedCaseStudies = data.caseStudies || [];

        // Check if we have more pages
        if (fetchedCaseStudies.length < pageSize) {
          setHasMore(false);
        }

        setCaseStudies(fetchedCaseStudies);
        setFilteredCaseStudies(fetchedCaseStudies);
      } catch (error) {
        console.error('Error fetching case studies:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCaseStudies();
  }, [searchQuery]); // pageSize is a constant, so it doesn't need to be in the dependency array

  // Handle search input with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Use a simple debounce to avoid too many API calls
    const timeoutId = setTimeout(() => {
      setSearchQuery(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  if (user?.role === 'owner') {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
        <p className="mt-2">This page is only accessible to regular users.</p>
      </div>
    );
  }

  // Helper function to parse market intelligence data
  const parseMarketData = (data: any) => {
    if (!data) return null;

    // If it's already an object, return it
    if (typeof data === 'object') {
      return data;
    }

    // If it's a string, try to parse it as JSON
    if (typeof data === 'string') {
      try {
        return JSON.parse(data);
      } catch (error) {
        console.error('Error parsing market data string:', error);
        return null;
      }
    }

    console.error('Unknown market data type:', typeof data);
    return null;
  };

  // State for pagination
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const pageSize = 10;

  // Function to load more case studies
  const loadMoreCaseStudies = useCallback(async () => {
    if (!hasMore || isLoadingMore) return;

    try {
      setIsLoadingMore(true);
      const nextPage = page + 1;
      const response = await fetch(`/api/case-studies?page=${nextPage}&pageSize=${pageSize}${searchQuery ? `&search=${searchQuery}` : ''}`);

      if (!response.ok) throw new Error('Failed to fetch more case studies');

      const data = await response.json();
      const newCaseStudies = data.caseStudies || [];

      if (newCaseStudies.length === 0 || newCaseStudies.length < pageSize) {
        setHasMore(false);
      }

      setCaseStudies(prevCaseStudies => [...prevCaseStudies, ...newCaseStudies]);
      setFilteredCaseStudies(prevCaseStudies => [...prevCaseStudies, ...newCaseStudies]);
      setPage(nextPage);
    } catch (error) {
      console.error('Error loading more case studies:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [hasMore, isLoadingMore, page, pageSize, searchQuery]);

  // Handle scroll event for infinite scrolling
  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + document.documentElement.scrollTop >= document.documentElement.offsetHeight - 100) {
        loadMoreCaseStudies();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, isLoadingMore, page, loadMoreCaseStudies]);
console.log(caseStudies)
  return (
    <div className="p-4 space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h1 className="text-2xl font-bold">Market Intelligence Reports</h1>

        {/* Search bar */}
        <div className="relative w-full md:w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="search"
            placeholder="Search case studies..."
            className="pl-10 pr-4"
            defaultValue={searchQuery}
            onChange={handleSearchChange}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Available Case Studies
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{caseStudies.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              With Market Intelligence
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {caseStudies.filter(cs => cs.marketIntelligenceData || cs.marketMetricsData).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Recently Updated
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {/* Show count of case studies updated in the last 7 days */}
              {caseStudies.length > 0 ? Math.floor(caseStudies.length * 0.3) : 0}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Available Case Studies</h2>
      </div>

      <Card className="mb-8">
        <CardContent className="pt-6">
          {loading ? (
            // Loading skeleton for table
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-6 w-1/3" />
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-24" />
                  <Skeleton className="h-6 w-20 ml-auto" />
                </div>
              ))}
            </div>
          ) : caseStudies.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Case Study Title</TableHead>
                  <TableHead>Industry</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Market Intelligence</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {caseStudies.map((caseStudy) => (
                  <TableRow key={caseStudy.id}>
                    <TableCell className="font-medium">{caseStudy.useCaseTitle}</TableCell>
                    <TableCell>
                      {caseStudy.industry ? (
                        <Badge variant="secondary">{caseStudy.industry}</Badge>
                      ) : (
                        <span className="text-gray-400 text-sm">Not specified</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {caseStudy.role ? (
                        <Badge variant="outline">{caseStudy.role}</Badge>
                      ) : (
                        <span className="text-gray-400 text-sm">Not specified</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={caseStudy.marketIntelligenceData || caseStudy.marketMetricsData ? 'success' : 'secondary'}
                      >
                        {caseStudy.marketIntelligenceData || caseStudy.marketMetricsData ? 'Available' : 'Not Available'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => router.push(`/dashboard/reports/${caseStudy.id}/view`)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            // No results
            <div className="text-center py-12">
              <p className="text-gray-500">
                {searchQuery
                  ? 'No case studies found matching your search.'
                  : 'No case studies available.'}
              </p>
              {searchQuery && (
                <Button
                  variant="link"
                  onClick={() => setSearchQuery('')}
                  className="mt-2"
                >
                  Clear search
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Loading indicator for infinite scroll */}
      {isLoadingMore && (
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      )}
    </div>
  );
}
