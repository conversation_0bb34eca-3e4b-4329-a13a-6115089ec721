'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { z } from 'zod';
import { Button } from './button';
import { cn } from '@/lib/utils';

interface OptimizedFormProps<T extends z.ZodType<any, any>> {
  // Schema for form validation
  schema: T;
  // Initial form values
  defaultValues?: z.infer<T>;
  // Function to call on form submission
  onSubmit: (values: z.infer<T>) => void | Promise<void>;
  // Function to render form fields
  children: (props: OptimizedFormRenderProps<T>) => React.ReactNode;
  // CSS class name for the form
  className?: string;
  // Whether to validate on blur
  validateOnBlur?: boolean;
  // Whether to validate on change
  validateOnChange?: boolean;
  // Debounce time for validation in milliseconds
  validationDebounce?: number;
}

interface OptimizedFormRenderProps<T extends z.ZodType<any, any>> {
  // Current form values
  values: z.infer<T>;
  // Form errors
  errors: Record<string, string>;
  // Whether the form is submitting
  isSubmitting: boolean;
  // Whether the form is valid
  isValid: boolean;
  // Whether a field has been touched
  touched: Record<string, boolean>;
  // Function to handle field change
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => void;
  // Function to set a specific field value
  setFieldValue: (field: string, value: any) => void;
  // Function to handle field blur
  handleBlur: (
    e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => void;
  // Function to validate the form
  validateForm: () => boolean;
  // Function to reset the form
  resetForm: () => void;
}

/**
 * OptimizedForm component with:
 * - Zod schema validation
 * - Performance optimization with debouncing
 * - Field-level validation
 * - Proper error handling
 * - Type safety
 */
export function OptimizedForm<T extends z.ZodType<any, any>>({
  schema,
  defaultValues,
  onSubmit,
  children,
  className,
  validateOnBlur = true,
  validateOnChange = true,
  validationDebounce = 300,
}: OptimizedFormProps<T>) {
  // Form state
  const [values, setValues] = useState<z.infer<T>>(defaultValues || ({} as z.infer<T>));
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Validation timer for debouncing
  const validationTimerRef = React.useRef<NodeJS.Timeout | null>(null);
  
  // Handle form submission
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      
      // Validate form
      try {
        const validatedData = schema.parse(values);
        setErrors({});
        setIsSubmitting(true);
        
        try {
          await onSubmit(validatedData);
        } catch (error) {
          console.error('Form submission error:', error);
        } finally {
          setIsSubmitting(false);
        }
      } catch (error) {
        if (error instanceof z.ZodError) {
          // Format Zod errors for display
          const fieldErrors: Record<string, string> = {};
          error.errors.forEach((err) => {
            const path = err.path.join('.');
            fieldErrors[path] = err.message;
          });
          setErrors(fieldErrors);
        } else {
          console.error('Form validation error:', error);
        }
      }
    },
    [schema, values, onSubmit]
  );
  
  // Handle field changes
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
      const { name, value, type } = e.target;
      
      // Handle different input types
      let fieldValue = value;
      if (type === 'number') {
        fieldValue = value === '' ? '' : Number(value);
      } else if (type === 'checkbox') {
        fieldValue = (e.target as HTMLInputElement).checked;
      }
      
      // Update values
      setValues((prevValues) => ({
        ...prevValues,
        [name]: fieldValue,
      }));
      
      // Mark field as touched
      setTouched((prevTouched) => ({
        ...prevTouched,
        [name]: true,
      }));
      
      // Validate field if needed
      if (validateOnChange) {
        if (validationTimerRef.current) {
          clearTimeout(validationTimerRef.current);
        }
        
        validationTimerRef.current = setTimeout(() => {
          validateField(name, fieldValue);
        }, validationDebounce);
      }
    },
    [validateOnChange, validationDebounce]
  );
  
  // Set specific field value
  const setFieldValue = useCallback(
    (field: string, value: any) => {
      setValues((prevValues) => ({
        ...prevValues,
        [field]: value,
      }));
      
      // Mark field as touched
      setTouched((prevTouched) => ({
        ...prevTouched,
        [field]: true,
      }));
      
      // Validate field if needed
      if (validateOnChange) {
        if (validationTimerRef.current) {
          clearTimeout(validationTimerRef.current);
        }
        
        validationTimerRef.current = setTimeout(() => {
          validateField(field, value);
        }, validationDebounce);
      }
    },
    [validateOnChange, validationDebounce]
  );
  
  // Handle field blur
  const handleBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
      const { name, value, type } = e.target;
      
      // Mark field as touched
      setTouched((prevTouched) => ({
        ...prevTouched,
        [name]: true,
      }));
      
      // Validate field if needed
      if (validateOnBlur) {
        // Handle different input types
        let fieldValue = value;
        if (type === 'number') {
          fieldValue = value === '' ? '' : Number(value);
        } else if (type === 'checkbox') {
          fieldValue = (e.target as HTMLInputElement).checked;
        }
        
        validateField(name, fieldValue);
      }
    },
    [validateOnBlur]
  );
  
  // Validate specific field
  const validateField = useCallback(
    (field: string, value: any) => {
      try {
        // Create a partial schema with just this field
        const fieldSchema = z.object({
          [field]: schema.shape[field],
        });
        
        // Validate just this field
        fieldSchema.parse({ [field]: value });
        
        // Clear error if validation passes
        setErrors((prevErrors) => {
          const newErrors = { ...prevErrors };
          delete newErrors[field];
          return newErrors;
        });
        
        return true;
      } catch (error) {
        if (error instanceof z.ZodError) {
          // Format Zod errors for display
          error.errors.forEach((err) => {
            setErrors((prevErrors) => ({
              ...prevErrors,
              [field]: err.message,
            }));
          });
        }
        
        return false;
      }
    },
    [schema]
  );
  
  // Validate entire form
  const validateForm = useCallback(() => {
    try {
      schema.parse(values);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Format Zod errors for display
        const fieldErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          fieldErrors[path] = err.message;
        });
        setErrors(fieldErrors);
      }
      return false;
    }
  }, [schema, values]);
  
  // Reset form
  const resetForm = useCallback(() => {
    setValues(defaultValues || ({} as z.infer<T>));
    setErrors({});
    setTouched({});
  }, [defaultValues]);
  
  // Calculate form validity
  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0;
  }, [errors]);
  
  // Render props for child components
  const renderProps: OptimizedFormRenderProps<T> = {
    values,
    errors,
    isSubmitting,
    isValid,
    touched,
    handleChange,
    setFieldValue,
    handleBlur,
    validateForm,
    resetForm,
  };
  
  return (
    <form
      onSubmit={handleSubmit}
      className={cn('space-y-4', className)}
      noValidate
    >
      {children(renderProps)}
      
      {/* Reset client-side validation timer on unmount */}
      {React.useEffect(() => {
        return () => {
          if (validationTimerRef.current) {
            clearTimeout(validationTimerRef.current);
          }
        };
      }, [])}
    </form>
  );
}

/**
 * Form submit button with loading state handling
 */
export function FormSubmitButton({
  children,
  isSubmitting,
  disabled = false,
  className,
  ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement> & {
  isSubmitting: boolean;
}) {
  return (
    <Button
      type="submit"
      disabled={isSubmitting || disabled}
      className={cn(
        'relative',
        isSubmitting && 'text-transparent',
        className
      )}
      {...props}
    >
      {children}
      
      {isSubmitting && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
        </div>
      )}
    </Button>
  );
}

export default OptimizedForm;
