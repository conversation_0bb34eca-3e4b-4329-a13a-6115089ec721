'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useSubscription } from '@/lib/hooks/use-subscription';
import { SubscriptionGuard, SubscriptionStatus } from '@/components/subscription/subscription-guard';
import { SubscriptionConditional, withSubscription } from '@/lib/auth/with-subscription';
import { CheckCircle, XCircle, AlertTriangle, Crown, Zap, X } from 'lucide-react';

/**
 * Test page for subscription middleware functionality
 * This page is protected and requires an active subscription
 */
function TestSubscriptionPage() {
  const {
    hasActiveSubscription,
    planFeatures,
    planName,
    status,
    isLoading,
    error
  } = useSubscription();

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading Subscription</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Subscription Test Page</h1>
        <p className="text-muted-foreground">
          This page tests the subscription middleware functionality. If you can see this content,
          your subscription validation is working correctly.
        </p>
      </div>

      {/* Subscription Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Subscription Status
          </CardTitle>
          <CardDescription>
            Current subscription information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <span className="font-medium">Status:</span>
              <SubscriptionStatus />
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium">Plan:</span>
                <p className="capitalize">{planName || 'None'}</p>
              </div>
              <div>
                <span className="font-medium">Active:</span>
                <p className={hasActiveSubscription ? 'text-green-600' : 'text-red-600'}>
                  {hasActiveSubscription ? 'Yes' : 'No'}
                </p>
              </div>
              <div>
                <span className="font-medium">Max Users:</span>
                <p>{planFeatures.maxUsers === -1 ? 'Unlimited' : planFeatures.maxUsers}</p>
              </div>
              <div>
                <span className="font-medium">Analytics:</span>
                <p className={planFeatures.advancedAnalytics ? 'text-green-600' : 'text-gray-500'}>
                  {planFeatures.advancedAnalytics ? 'Available' : 'Not Available'}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feature Tests */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Advanced Analytics Test - Feature Removed */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5" />
              Advanced Analytics
            </CardTitle>
            <CardDescription>
              Advanced analytics feature has been removed from all plans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-500">
                <X className="h-4 w-4" />
                <span>Advanced analytics is no longer available</span>
              </div>
              <p className="text-sm text-muted-foreground">
                This feature has been removed from all subscription plans.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* API Access Test - Feature Removed */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              API Access
            </CardTitle>
            <CardDescription>
              API access feature has been removed from all plans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-500">
                <X className="h-4 w-4" />
                <span>API access is no longer available</span>
              </div>
              <p className="text-sm text-muted-foreground">
                This feature has been removed from all subscription plans.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Case Study Limits Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5" />
              Case Study Access
            </CardTitle>
            <CardDescription>
              Tests case study access limits based on subscription plan
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Plan: {planName || 'Unknown'}</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-600" />
                <span>
                  Case Study Limit: {planFeatures?.maxCaseStudies === -1 ? 'Unlimited' : planFeatures?.maxCaseStudies || 'Unknown'}
                </span>
              </div>
              {planName === 'starter' && (
                <div className="mt-2 p-2 bg-blue-50 rounded border border-blue-200">
                  <p className="text-sm text-blue-800">
                    <strong>Starter Plan:</strong> Access to 10 curated case studies only.
                  </p>
                </div>
              )}
              {planName === 'pro' && (
                <div className="mt-2 p-2 bg-green-50 rounded border border-green-200">
                  <p className="text-sm text-green-800">
                    <strong>Pro Plan:</strong> Unlimited access to all case studies.
                  </p>
                </div>
              )}
              <Button size="sm" variant="outline" asChild>
                <a href="/dashboard/user/case-studies">Test Case Study Access</a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Conditional Content Tests */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Conditional Content Tests</CardTitle>
          <CardDescription>
            Tests conditional rendering based on subscription features
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <SubscriptionConditional requireActiveSubscription>
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <span>This content is only visible to active subscribers</span>
              </div>
            </div>
          </SubscriptionConditional>

          <SubscriptionConditional requiredFeature="customBranding">
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 text-blue-800">
                <Crown className="h-4 w-4" />
                <span>Custom branding features are available</span>
              </div>
            </div>
          </SubscriptionConditional>

          <SubscriptionConditional
            requiredFeature="customBranding"
            inverse
            fallback={null}
          >
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertTriangle className="h-4 w-4" />
                <span>Upgrade to access custom branding features</span>
              </div>
            </div>
          </SubscriptionConditional>
        </CardContent>
      </Card>

      {/* Feature Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Feature Matrix</CardTitle>
          <CardDescription>
            Complete overview of your plan features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(planFeatures).map(([feature, available]) => {
              if (typeof available === 'boolean') {
                return (
                  <div key={feature} className="flex items-center justify-between p-3 border rounded-lg">
                    <span className="font-medium capitalize">
                      {feature.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                    {available ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                );
              } else {
                return (
                  <div key={feature} className="flex items-center justify-between p-3 border rounded-lg">
                    <span className="font-medium capitalize">
                      {feature.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                    <Badge variant="outline">
                      {available === -1 ? 'Unlimited' : available}
                    </Badge>
                  </div>
                );
              }
            })}
          </div>
        </CardContent>
      </Card>

      {/* Test Actions */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Test Actions</CardTitle>
          <CardDescription>
            Actions to test subscription functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" asChild>
              <a href="/dashboard/billing">Manage Subscription</a>
            </Button>
            <Button variant="outline" asChild>
              <a href="/pricing">View Plans</a>
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Export the component wrapped with subscription protection
export default withSubscription(TestSubscriptionPage, {
  requireActiveSubscription: true,
  redirectTo: '/dashboard/billing'
});
