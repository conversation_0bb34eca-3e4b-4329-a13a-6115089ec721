import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies, caseStudyIcons } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { eq } from 'drizzle-orm';
import { redirect } from 'next/navigation';

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return redirect('/sign-in');
    }

    // Check if the user is an owner
    if (session.user.role !== 'owner') {
      console.log(`Access denied: Non-owner (role: ${session.user.role}) attempting to delete case study`);
      return redirect('/dashboard/case-studies');
    }

    const id = params.id;
    if (!id || isNaN(parseInt(id))) {
      return redirect('/dashboard/case-studies');
    }

    console.log(`Deleting case study with ID: ${id}`);

    try {
      // Start a transaction to ensure all related data is deleted
      await db.transaction(async (tx) => {
        // Delete case study icons first
        await tx.delete(caseStudyIcons).where(eq(caseStudyIcons.caseStudyId, parseInt(id)));

        // Delete bookmarks related to this case study (if any)
        try {
          const { bookmarks } = await import('@/lib/db/schema');
          await tx.delete(bookmarks).where(eq(bookmarks.caseStudyId, parseInt(id)));
        } catch (err) {
          console.log('No bookmarks table or no bookmarks to delete');
        }

        // Delete the case study itself
        const result = await tx.delete(caseStudies).where(eq(caseStudies.id, parseInt(id))).returning();

        if (result.length === 0) {
          throw new Error('Case study not found');
        }
      });

      // If we get here, the deletion was successful
      console.log(`Successfully deleted case study with ID: ${id}`);
    } catch (txError) {
      console.error('Transaction error:', txError);
      return redirect('/dashboard/case-studies');
    }

    // Redirect back to case studies list
    return redirect('/dashboard/case-studies');
  } catch (error) {
    console.error('Error deleting case study:', error);
    return redirect('/dashboard/case-studies');
  }
}
