import { eq } from 'drizzle-orm';
import { db } from '@/lib/db/drizzle';
import { users, teams, teamMembers } from '@/lib/db/schema';
import { setSession, getSession } from '@/lib/auth/session';
import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/payments/stripe';
import <PERSON><PERSON> from 'stripe';
import { getUser } from '@/lib/db/server-queries';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const sessionId = searchParams.get('session_id');
  const planName = searchParams.get('plan');
  const priceId = searchParams.get('price_id');
  const couponCode = searchParams.get('coupon');

  console.log('Checkout API called with params:', { sessionId, planName, priceId, couponCode });

  // If we have a plan parameter, we need to create a checkout session
  if (planName) {
    try {
      // Get the user from the session
      const user = await getUser();
      console.log('Checkout - User:', user);

      if (!user) {
        return NextResponse.redirect(new URL('/sign-up', request.url));
      }

      // Check if user is an owner - owners don't need to purchase plans
      if (user.role === 'owner') {
        return NextResponse.redirect(new URL('/dashboard/owner', request.url));
      }

      // Get the team for the user
      const userTeam = await db
        .select({
          teamId: teamMembers.teamId,
        })
        .from(teamMembers)
        .where(eq(teamMembers.userId, user.id))
        .limit(1);

      if (userTeam.length === 0) {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }

      // Get the team details
      const teamResult = await db
        .select()
        .from(teams)
        .where(eq(teams.id, userTeam[0].teamId))
        .limit(1);

      if (teamResult.length === 0) {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }

      const team = teamResult[0];

      // Use the price ID from the URL parameter if available
      let stripePrice: string | null = null;

      // If price_id is provided directly, use it
      if (priceId) {
        stripePrice = priceId;
        console.log(`Using provided price ID: ${stripePrice}`);
      } else {
        // Map of plan names to their Stripe price IDs (with USD currency)
        const planPriceIds: Record<string, string> = {
          'starter': 'price_1RKd0KIiRJwYTHShIBRY6cd6', // Starter plan ($12/month)
          'pro': 'price_1RKd0LIiRJwYTHShusv7wdRZ', // Pro plan ($60/month)
          // Enterprise plan is handled via contact form, not direct checkout
        };

        // Get the price ID for the requested plan
        if (planName && planName.toLowerCase() in planPriceIds) {
          stripePrice = planPriceIds[planName.toLowerCase()];
          console.log(`Using mapped price ID ${stripePrice} for plan ${planName}`);
        } else {
          // Fallback to hardcoded IDs if plan name is not recognized
          if (planName === 'starter') {
            stripePrice = 'price_1RKd0KIiRJwYTHShIBRY6cd6'; // Starter plan ($12/month)
          } else if (planName === 'pro') {
            stripePrice = 'price_1RKd0LIiRJwYTHShusv7wdRZ'; // Pro plan ($60/month)
          } else if (planName === 'enterprise') {
            // Enterprise plan should redirect to contact page, not checkout
            return NextResponse.redirect(new URL('/contact?subject=Enterprise%20Plan%20Inquiry', request.url));
          }
          console.log('Using fallback price ID:', stripePrice);
        }
      }

      if (!stripePrice) {
        console.log('No matching price found for plan:', planName);
        return NextResponse.json({ error: 'Plan not found. Please create products in your Stripe dashboard first.' }, { status: 404 });
      }

      // Create a checkout session
      console.log('Creating checkout session for team:', {
        teamId: team.id,
        stripeCustomerId: team.stripeCustomerId
      });

      // Check if a coupon code was provided
      let discountOptions = {};

      if (couponCode) {
        try {
          // Validate the coupon code in our database first
          const response = await fetch(`${request.nextUrl.origin}/api/coupon-codes/validate`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ code: couponCode }),
          });

          const data = await response.json();

          if (data.valid) {
            console.log('Valid coupon code applied:', data.coupon);

            // For our own database coupon codes, we need to create a Stripe coupon or promotion code
            // For simplicity, we'll use Stripe's built-in promotion codes feature
            discountOptions = {
              discounts: [{
                coupon: couponCode,
              }]
            };

            // Increment the usage count for this coupon
            await db.execute(
              `UPDATE coupon_codes
               SET current_uses = current_uses + 1
               WHERE code = $1`,
              [couponCode]
            );
          } else {
            console.log('Invalid coupon code:', couponCode);
          }
        } catch (error) {
          console.error('Error validating coupon code:', error);
        }
      }

      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price: stripePrice,
            quantity: 1
          }
        ],
        mode: 'subscription',
        success_url: `${request.nextUrl.origin}/api/stripe/checkout?session_id={CHECKOUT_SESSION_ID}&redirect_to=member`,
        cancel_url: `${request.nextUrl.origin}/pricing`,
        customer: team.stripeCustomerId || undefined,
        client_reference_id: user.id.toString(),
        allow_promotion_codes: true,
        currency: 'usd', // Explicitly set currency to USD
        subscription_data: {
          trial_period_days: 14 // 14-day free trial as mentioned in the FAQ
        },
        ...discountOptions
      });

      console.log('Checkout session created, redirecting to:', session.url);
      return NextResponse.redirect(session.url!);
    } catch (error) {
      console.error('Error creating checkout session:', error);
      return NextResponse.redirect(new URL('/error', request.url));
    }
  }

  // If we have a session_id parameter, we need to handle a successful checkout
  if (!sessionId) {
    return NextResponse.redirect(new URL('/pricing', request.url));
  }

  try {
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['customer', 'subscription'],
    });

    if (!session.customer || typeof session.customer === 'string') {
      throw new Error('Invalid customer data from Stripe.');
    }

    const customerId = session.customer.id;
    const subscriptionId =
      typeof session.subscription === 'string'
        ? session.subscription
        : session.subscription?.id;

    if (!subscriptionId) {
      throw new Error('No subscription found for this session.');
    }

    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['items.data.price.product'],
    });

    const plan = subscription.items.data[0]?.price;

    if (!plan) {
      throw new Error('No plan found for this subscription.');
    }

    const productId = (plan.product as Stripe.Product).id;

    if (!productId) {
      throw new Error('No product ID found for this subscription.');
    }

    const userId = session.client_reference_id;
    if (!userId) {
      throw new Error("No user ID found in session's client_reference_id.");
    }

    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, Number(userId)))
      .limit(1);

    if (user.length === 0) {
      throw new Error('User not found in database.');
    }

    const userTeam = await db
      .select({
        teamId: teamMembers.teamId,
      })
      .from(teamMembers)
      .where(eq(teamMembers.userId, user[0].id))
      .limit(1);

    if (userTeam.length === 0) {
      throw new Error('User is not associated with any team.');
    }

    // Use centralized plan name normalization
    const { normalizePlanName } = await import('@/lib/config/plans');
    let planName = normalizePlanName((plan.product as Stripe.Product).name);

    console.log('Updating team subscription after successful checkout:', {
      customerId,
      subscriptionId,
      productId,
      planName,
      status: subscription.status
    });

    await db
      .update(teams)
      .set({
        stripeCustomerId: customerId,
        stripeSubscriptionId: subscriptionId,
        stripeProductId: productId,
        planName: planName,
        subscriptionStatus: subscription.status,
        updatedAt: new Date(),
      })
      .where(eq(teams.id, userTeam[0].teamId));

    await setSession(user[0]);

    // Add a small delay to ensure database is updated before redirect
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if we should redirect to the member dashboard
    const redirectTo = searchParams.get('redirect_to');
    if (redirectTo === 'member') {
      return NextResponse.redirect(new URL('/dashboard/member?payment_success=true', request.url));
    }

    return NextResponse.redirect(new URL('/dashboard?payment_success=true', request.url));
  } catch (error) {
    console.error('Error handling successful checkout:', error);
    return NextResponse.redirect(new URL('/error', request.url));
  }
}
