import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { contactMessages, users } from '../schema';
import { eq } from 'drizzle-orm';
import 'dotenv/config';

// This migration doesn't actually change the database schema
// It's just to refresh the relation definitions in the Drizzle ORM

async function fixContactMessagesRelation() {
  console.log('Starting relation fix migration...');
  
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not set');
  }

  const connectionString = process.env.POSTGRES_URL;
  const client = postgres(connectionString);
  const db = drizzle(client);

  try {
    // Just a simple query to verify connection
    const result = await db.select().from(contactMessages).limit(1);
    console.log(`Connection successful. Found ${result.length} contact messages.`);
    
    console.log('Relation fix migration completed successfully.');
  } catch (error) {
    console.error('Error during relation fix migration:', error);
    throw error;
  } finally {
    await client.end();
  }
}

fixContactMessagesRelation()
  .then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
