import { AnimatedGridPattern } from '@/components/magicui/animated-grid-pattern';
import { Badge } from '@/components/ui/badge';

export default function BusinessPotential() {
  return (
    <section className='relative pb-16 md:py-20 w-full overflow-hidden'>
      {/* Animated Grid Background */}
      <div className='absolute inset-0'>
        <AnimatedGridPattern
          width={70}
          height={70}
          className='text-gray-200/80 md:hidden'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={8}
          x={0}
          y={0}
        />
        <AnimatedGridPattern
          width={150}
          height={150}
          className='text-gray-200/80 hidden md:block'
          strokeWidth={1}
          maxOpacity={0.6}
          duration={5}
          numSquares={20}
          x={0}
          y={0}
        />
      </div>
      {/* Updated Radial Gradient */}
      <div className='absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0)_0%,rgba(255,255,255,0.2)_25%,rgba(255,255,255,0.9)_50%,rgba(255,255,255,1)_75%)] pointer-events-none'></div>

      <div className='relative z-10 container mx-auto px-4 md:px-6 lg:px-8'>
        {/* Header */}
        <div className='text-center mb-10 md:mb-16'>
          <div className='inline-block mb-3'>
            {/* <Badge
              variant="secondary"
              className="px-3 md:px-4 py-1.5 text-sm bg-white/95 backdrop-blur-sm border-none shadow-md text-gray-600"
            >
              Get Started in Minutes
            </Badge> */}
          </div>
          <h2 className='text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-6'>
            Unlock Your{' '}
            <span className='text-blue-500'>Business Potential</span> Today!
          </h2>
          <p className='text-base md:text-lg text-gray-600'>
            Find your next big opportunity with our curated use cases.
          </p>
        </div>

        {/* Features Grid */}
        <div className='grid grid-cols-1 gap-y-12 md:gap-y-16'>
          {/* First Row - Market Trends */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 items-center'>
            {/* Left - Image */}
            <div className='bg-gray-50 rounded-2xl md:rounded-3xl p-6 md:p-8 aspect-[4/3] flex items-center justify-center'>
              <img
                src='/images/market-trend.png'
                alt='Market Trends'
                className='w-full h-full object-contain'
              />
            </div>
            {/* Right - Content */}
            <div className='space-y-3 md:space-y-4 px-4 md:px-0'>
              {/* <Badge
                variant="secondary"
                className="inline-flex px-3 md:px-4 py-1.5 text-sm bg-white shadow-sm text-black"
              >
                Trending Now
              </Badge> */}
              <h3 className='text-3xl font-bold'>Trending Now</h3>
              <div className='space-y-2'>
                <div className='text-4xl md:text-5xl font-bold text-black'></div>
                <p className='text-base md:text-lg text-gray-600'>
                  High-demand use cases gaining traction across our ecosystem
                </p>
              </div>
            </div>
          </div>

          {/* Second Row - Sentiment Analysis */}

          <div className='flex flex-col-reverse sm:grid sm:grid-start  sm:grid-cols-2 gap-6 md:gap-8 items-center'>
            {/* Left - Content */}
            <div className='space-y-3 md:space-y-4 px-4 md:px-0'>
              {/* <Badge
                variant="secondary"
                className="inline-flex px-3 md:px-4 py-1.5 text-sm bg-white shadow-sm text-black"
              >
                Market Signals
              </Badge> */}
              <h3 className='text-3xl font-bold'>Market Signals</h3>
              <p className='text-base md:text-lg text-gray-600'>
                Use cases backed by market intelligence, adoption data &
                external trends
              </p>
            </div>
            {/* Right - Image */}
            <div className='bg-gray-50 rounded-2xl md:rounded-3xl p-6 md:p-8 aspect-[4/3] flex items-center justify-center'>
              <div className='relative w-full h-full'>
                <div className='absolute inset-0 bg-blue-50/50 rounded-full'></div>
                <img
                  src='/images/sentiment-analysis.png'
                  alt='Sentiment Analysis'
                  className='w-full h-full object-contain relative z-10'
                />
              </div>
            </div>
          </div>

          {/* Third Row - Route Optimization */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 items-center'>
            {/* Left - Image */}
            <div className='bg-gray-50 rounded-2xl md:rounded-3xl p-6 md:p-8 aspect-[4/3] flex items-center justify-center'>
              <img
                src='/images/route-optimization.png'
                alt='Route Optimization'
                className='w-full h-full object-contain'
              />
            </div>
            {/* Right - Content */}
            <div className='space-y-3 md:space-y-4 px-4 md:px-0'>
              {/* <Badge
                variant="secondary"
                className="inline-flex px-3 md:px-4 py-1.5 text-sm bg-white shadow-sm text-black"
              >
                CXO Priorities
              </Badge> */}
              <h3 className='text-3xl font-bold'>CXO Priorities</h3>
              <p className='text-base md:text-lg text-gray-600'>
                Use cases mapped to strategic business vectors like revenue,
                growth, data, and efficiency
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
