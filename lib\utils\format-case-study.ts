import { CaseStudyData } from '@/components/CaseStudyCard/types';
import { ensureCaseStudyData } from './case-study-fallbacks';


export function formatCaseStudyForDisplay(caseStudy: any): CaseStudyData {
  // Log the raw case study data to see what we're working with
  console.log('Raw case study data:', {
    caseStudy
  });
  // Parse potentially JSON fields or comma-separated string
  let potentiallyImpactedKpis = {};
  if (caseStudy.potentiallyImpactedKpis) {
    // Check if it's already an object
    if (typeof caseStudy.potentiallyImpactedKpis === 'object') {
      potentiallyImpactedKpis = caseStudy.potentiallyImpactedKpis;
    } else if (typeof caseStudy.potentiallyImpactedKpis === 'string') {
      // First try to parse as JSON if it looks like JSON
      const trimmed = caseStudy.potentiallyImpactedKpis.trim();
      if ((trimmed.startsWith('{') && trimmed.endsWith('}')) ||
        (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
        try {
          potentiallyImpactedKpis = JSON.parse(caseStudy.potentiallyImpactedKpis);
        } catch (e) {
          console.error('Error parsing potentiallyImpactedKpis as JSON:', e);
        }
      } else {
        // Handle as comma-separated string
        console.log('Handling potentiallyImpactedKpis as comma-separated string:', caseStudy.potentiallyImpactedKpis);
        const kpiArray = caseStudy.potentiallyImpactedKpis.split(',').map((kpi: string) => kpi.trim());

        // Create an object with the KPIs using the actual KPI names from the CSV
        // Instead of using fixed keys, use the KPI names directly
        for (let i = 0; i < kpiArray.length; i++) {
          if (kpiArray[i]) {
            // Use the KPI name as the key and value
            potentiallyImpactedKpis = {
              ...potentiallyImpactedKpis,
              [`kpi${i + 1}`]: kpiArray[i]
            };
          }
        }

        console.log('Parsed KPIs:', potentiallyImpactedKpis);
      }
    }
  }

  // Helper function to clean text by removing special characters and fixing encoding issues
  const cleanText = (text: string | null | undefined): string => {
    if (!text) return '';

    // First, replace the replacement character � with empty string
    let cleaned = text.replace(/�/g, '');

    // Replace common encoding issues from CSV imports
    cleaned = cleaned.replace(/ï¿½/g, ''); // Common CSV encoding issue
    cleaned = cleaned.replace(/Â/g, ''); // Non-breaking space encoding issue
    cleaned = cleaned.replace(/â€™/g, "'"); // Smart single quote
    cleaned = cleaned.replace(/â€œ/g, '"'); // Smart opening double quote
    cleaned = cleaned.replace(/â€/g, '"'); // Smart closing double quote
    cleaned = cleaned.replace(/â€"/g, '—'); // Em dash
    cleaned = cleaned.replace(/â€"/g, '–'); // En dash

    // Trim whitespace
    return cleaned.trim();
  };

  // Helper function to validate URLs
  const isValidUrl = (url: string | null | undefined): boolean => {
    if (!url) return false;
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  // Helper function to get a valid icon URL or fallback
  const getValidIconUrl = (icon: any, iconType: string = '', index: number = 0): string => {
    console.log('Processing icon:', icon, 'Type:', iconType, 'Index:', index);

    // If no icon is provided, return a type-specific fallback
    if (!icon) {
      console.log(`Icon is null or undefined for ${iconType || 'unknown'} at index ${index}`);
      return getFallbackIconUrl(iconType || (icon?.iconType || 'icon'), index);
    }

    if (!icon.iconUrl) {
      console.log('Icon URL is missing');
      return getFallbackIconUrl(iconType || (icon?.iconType || 'icon'), index);
    }

    // Make sure Cloudinary URLs use https
    let iconUrl = icon.iconUrl;
    if (iconUrl.startsWith('http://')) {
      iconUrl = iconUrl.replace('http://', 'https://');
      console.log('Converted to HTTPS:', iconUrl);
    }

    // Ensure the URL is valid
    try {
      new URL(iconUrl);
      console.log('Using valid icon URL:', iconUrl);
      return iconUrl;
    } catch (e) {
      console.log('Invalid URL:', iconUrl);

      // Check if it might be a relative URL
      if (iconUrl.startsWith('/')) {
        console.log('Treating as relative URL:', iconUrl);
        return iconUrl;
      }

      console.log('Using placeholder for invalid URL');
      return getFallbackIconUrl(iconType || (icon?.iconType || 'icon'), index);
    }
  };

  // Helper function to get fallback icon URLs based on type and index
  const getFallbackIconUrl = (iconType: string, index: number): string => {
    if (iconType === 'impact') {
      // Return specific fallback icons for impact metrics
      const impactIcons = [
        '/icons/impact-1.svg',
        '/icons/impact-2.svg',
        '/icons/impact-3.svg',
        '/icons/impact-4.svg'
      ];
      return impactIcons[index] || `/icons/impact-${index + 1}.svg`;
    } else if (iconType === 'process') {
      return `/icons/process-${index + 1}.svg`;
    } else if (iconType === 'solution') {
      return `/icons/solution-${index + 1}.svg`;
    } else if (iconType === 'challenge') {
      // Return specific fallback icons for challenges
      const challengeIcons = [
        '/icons/challenge-1.svg',
        '/icons/challenge-2.svg',
        '/icons/challenge-3.svg'
      ];
      return challengeIcons[index] || `/icons/challenge-${index + 1}.svg`;
    } else if (iconType === 'question') {
      // Return specific fallback icons for questions
      const questionIcons = [
        '/icons/question-1.svg',
        '/icons/question-2.svg',
        '/icons/question-3.svg'
      ];
      return questionIcons[index] || `/icons/question-${index + 1}.svg`;
    }

    // Default fallback
    return `/placeholder-${iconType || 'icon'}.png`;
  };
  console.log('Case Study Data:', JSON.stringify({
    id: caseStudy.id,
    title: caseStudy.useCaseTitle || caseStudy.title,
    hasIcons: !!caseStudy.icons,
    iconsCount: caseStudy.icons?.length || 0,
    icons: caseStudy.icons?.map((icon: any) => ({
      id: icon.id,
      type: icon.iconType,
      url: icon.iconUrl,
      order: icon.order
    }))
  }, null, 2))
  // Find header image from icons
  const headerImageIcon = caseStudy.icons?.find((icon: any) => icon.iconType === 'header');
  const headerImage = headerImageIcon ? getValidIconUrl(headerImageIcon) :
    (isValidUrl(caseStudy.featureImageUrl) ? caseStudy.featureImageUrl : '');

  // Find thumbnail image from icons
  const thumbnailImageIcon = caseStudy.icons?.find((icon: any) => icon.iconType === 'icon');
  const thumbnailImage = thumbnailImageIcon ? getValidIconUrl(thumbnailImageIcon) :
    (isValidUrl(caseStudy.previewImageUrl) ? caseStudy.previewImageUrl : '');

  const formattedData: Partial<CaseStudyData> = {
    // Basic information
    id: caseStudy.id?.toString(),
    title: cleanText(caseStudy.useCaseTitle || caseStudy.title),
    headerImage: headerImage,
    thumbnailImage: thumbnailImage,
    industry: cleanText(caseStudy.industry || ''),
    role: cleanText(caseStudy.role || ''),
    vector: cleanText(caseStudy.vector || ''),

    // Include the raw potentiallyImpactedKpis string for direct access
    potentiallyImpactedKpis: typeof caseStudy.potentiallyImpactedKpis === 'string' ?
      caseStudy.potentiallyImpactedKpis : null,

    // Map KPIs if available
    kpis: Object.keys(potentiallyImpactedKpis).length > 0 ? potentiallyImpactedKpis : {},

    // Map introduction fields
    introduction: {
      title: cleanText(caseStudy.introductionTitle || 'Introduction'),
      text: cleanText(caseStudy.introductionText || ''),
      transitionToChallange: cleanText(caseStudy.transitionToChallange|| 'Key Challenges:'), // Keep original field name
      problems: [
        cleanText(caseStudy.challange1 || ''),
        cleanText(caseStudy.challange2 || ''),
        cleanText(caseStudy.challange3 || '')
      ].filter(Boolean),
      problemIcons: [
        getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'challenge' && icon.order === 0), 'challenge', 0),
        getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'challenge' && icon.order === 1), 'challenge', 1),
        getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'challenge' && icon.order === 2), 'challenge', 2)
      ],

      // Debug challenge icons
      ...(console.log('Challenge icons:', {
        icon0: caseStudy.icons?.find((icon: any) => icon.iconType === 'challenge' && icon.order === 0),
        icon1: caseStudy.icons?.find((icon: any) => icon.iconType === 'challenge' && icon.order === 1),
        icon2: caseStudy.icons?.find((icon: any) => icon.iconType === 'challenge' && icon.order === 2),
        allIcons: caseStudy.icons?.filter((icon: any) => icon.iconType === 'challenge')
      }), {}),
      transitionToQuestions: cleanText(caseStudy.transitionToQuestions || 'Key Questions:'),
      questions: [
        cleanText(caseStudy.question1 || ''),
        cleanText(caseStudy.question2 || ''),
        cleanText(caseStudy.question3 || '')
      ].filter(Boolean),
      questionIcons: [
        getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'question' && icon.order === 0), 'question', 0),
        getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'question' && icon.order === 1), 'question', 1),
        getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'question' && icon.order === 2), 'question', 2)
      ],

      // Debug question icons
      ...(console.log('Question icons:', {
        icon0: caseStudy.icons?.find((icon: any) => icon.iconType === 'question' && icon.order === 0),
        icon1: caseStudy.icons?.find((icon: any) => icon.iconType === 'question' && icon.order === 1),
        icon2: caseStudy.icons?.find((icon: any) => icon.iconType === 'question' && icon.order === 2),
        allIcons: caseStudy.icons?.filter((icon: any) => icon.iconType === 'question')
      }), {})
    },

    // Map process fields
    process: {
      title: cleanText(caseStudy.processTitle || 'Process'),
      steps: [
        caseStudy.processStep1Title ? {
          title: cleanText(caseStudy.processStep1Title),
          description: cleanText(caseStudy.processStep1Description || ''),
          icon: getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'process' && icon.order === 0))
        } : null,
        caseStudy.processStep2Title ? {
          title: cleanText(caseStudy.processStep2Title),
          description: cleanText(caseStudy.processStep2Description || ''),
          icon: getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'process' && icon.order === 1))
        } : null,
        caseStudy.processStep3Title ? {
          title: cleanText(caseStudy.processStep3Title),
          description: cleanText(caseStudy.processStep3Description || ''),
          icon: getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'process' && icon.order === 2))
        } : null
      ].filter(Boolean) as any
    },

    // Map solution fields
    solution: {
      title: cleanText(caseStudy.solutionTitle || 'Solution'),
      description: cleanText(caseStudy.solutionDescription || ''),
      items: [
        caseStudy.solution1Title ? {
          title: cleanText(caseStudy.solution1Title),
          description: cleanText(caseStudy.solution1Description || ''),
          icon: getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'solution' && icon.order === 0))
        } : null,
        caseStudy.solution2Title ? {
          title: cleanText(caseStudy.solution2Title),
          description: cleanText(caseStudy.solution2Description || ''),
          icon: getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'solution' && icon.order === 1))
        } : null,
        caseStudy.solution3Title ? {
          title: cleanText(caseStudy.solution3Title),
          description: cleanText(caseStudy.solution3Description || ''),
          icon: getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'solution' && icon.order === 2))
        } : null,
        caseStudy.solution4Title ? {
          title: cleanText(caseStudy.solution4Title),
          description: cleanText(caseStudy.solution4Description || ''),
          icon: getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'solution' && icon.order === 3))
        } : null,
        caseStudy.solution5Title ? {
          title: cleanText(caseStudy.solution5Title),
          description: cleanText(caseStudy.solution5Description || ''),
          icon: getValidIconUrl(caseStudy.icons?.find((icon: any) => icon.iconType === 'solution' && icon.order === 4))
        } : null
      ].filter(Boolean) as any
    },

    // Map impact fields
    impact: {
      title: cleanText(caseStudy.impactTitle || 'Impact'),
      metrics: [
        // Impact metric 1
        {
          metric: cleanText(caseStudy.impactMetric1 || 'Impact 1'),
          value: cleanText(caseStudy.potentialImpact1 || ''),
          description: cleanText(caseStudy.impactValue1 || ''),
          icon: getValidIconUrl(
            caseStudy.icons?.find((icon: any) => icon.iconType === 'impact' && icon.order === 0),
            'impact',
            0
          )
        },

        // Impact metric 2
        {
          metric: cleanText(caseStudy.impactMetric2 || 'Impact 2'),
          value: cleanText(caseStudy.potentialImpact2 || ''),
          description: cleanText(caseStudy.impactValue2 || ''),
          icon: getValidIconUrl(
            caseStudy.icons?.find((icon: any) => icon.iconType === 'impact' && icon.order === 1),
            'impact',
            1
          )
        },

        // Impact metric 3
        {
          metric: cleanText(caseStudy.impactMetric3 || 'Impact 3'),
          value: cleanText(caseStudy.potentialImpact3 || ''),
          description: cleanText(caseStudy.impactValue3 || ''),
          icon: getValidIconUrl(
            caseStudy.icons?.find((icon: any) => icon.iconType === 'impact' && icon.order === 2),
            'impact',
            2
          )
        },

        // Impact metric 4 - Include even if potentialImpact4 is empty
        {
          metric: cleanText(caseStudy.impactMetric4 || 'Impact 4'),
          value: cleanText(caseStudy.potentialImpact4 || ''),
          description: cleanText(caseStudy.impactValue4 || ''),
          icon: getValidIconUrl(
            caseStudy.icons?.find((icon: any) => icon.iconType === 'impact' && icon.order === 3),
            'impact',
            3
          )
        }
      ]
    },

    // Map conclusion fields
    conclusion: {
      title: cleanText(caseStudy.conclusionTitle || 'Conclusion'),
      text: cleanText(caseStudy.conclusionText || ''),
      resultTitle: cleanText(caseStudy.conclusionResultTitle || 'The result?'),
      result: cleanText(caseStudy.conclusionResult || '')
    },

    // Handle market intelligence data
    marketIntelligence: caseStudy.marketIntelligence ||
      (typeof caseStudy.marketIntelligenceData === 'string' && caseStudy.marketIntelligenceData ?
        safeJsonParse(caseStudy.marketIntelligenceData, []) :
        caseStudy.marketIntelligenceData) || [],

    // Handle market metrics data
    marketMetrics: {
      marketSize: caseStudy.marketSize || null,
      marketCAGR: caseStudy.marketCAGR || null,
      marketROI: caseStudy.marketROI || null,
      ...(caseStudy.marketMetrics ||
        (typeof caseStudy.marketMetricsData === 'string' && caseStudy.marketMetricsData ?
          safeJsonParse(caseStudy.marketMetricsData, {}) :
          caseStudy.marketMetricsData) || {})
    },

    // Store the raw market data for debugging
    marketIntelligenceData: caseStudy.marketIntelligenceData,
    marketMetricsData: caseStudy.marketMetricsData,

    // AI processing status
    aiProcessed: caseStudy.aiProcessed || false,
    aiProcessingStatus: caseStudy.aiProcessingStatus || null,
    aiProcessingError: caseStudy.aiProcessingError || null
  };

  // Log the formatted data before applying fallbacks
  console.log('Formatted data before fallbacks:', {
    id: formattedData.id,
    title: formattedData.title,
    introduction: {
      title: formattedData.introduction?.title,
      text: formattedData.introduction?.text,
      transitionToChallange: formattedData.introduction?.transitionToChallange,
      transitionToQuestions: formattedData.introduction?.transitionToQuestions,
      problems: formattedData.introduction?.problems,
      questions: formattedData.introduction?.questions
    }
  });

  // Apply fallbacks to ensure all required fields have values
  const finalData = ensureCaseStudyData(formattedData);

  // Log the final data after applying fallbacks
  console.log('Final data after fallbacks:', {
    id: finalData.id,
    title: finalData.title,
    introduction: {
      title: finalData.introduction?.title,
      text: finalData.introduction?.text,
      transitionToChallange: finalData.introduction?.transitionToChallange,
      transitionToQuestions: finalData.introduction?.transitionToQuestions,
      problems: finalData.introduction?.problems?.length,
      questions: finalData.introduction?.questions?.length
    }
  });

  return finalData;
}

/**
 * Safely parses JSON with a fallback value if parsing fails
 */
function safeJsonParse(jsonString: string, fallback: any) {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Error parsing JSON:', error);
    return fallback;
  }
}
