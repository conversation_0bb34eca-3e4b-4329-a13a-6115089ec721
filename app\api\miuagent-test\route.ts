import { NextResponse } from 'next/server';

export async function GET(req: Request) {
  try {
    console.log('Testing MIUAgent API connection...');

    // Prepare a simple test request
    const testRequest = {
      id: `test-req-${Date.now()}`,
      data: JSON.stringify({
        use_case_id: 'test-case',
        title: 'Test Case',
        short_description: 'This is a test case to verify API connectivity',
        problem_statement: 'Testing API connectivity',
        industry: 'Technology',
        role: 'Developer',
      }),
    };

    console.log('Request body:', JSON.stringify(testRequest, null, 2));

    // Try different API endpoints to see which one works
    const endpoints = [
      'https://miuagent-api.leafcraftstudios.com/v1/insights/trigger',
      'https://miuagent-api.leafcraftstudios.com/insights/trigger',
      'https://miuagent-api.leafcraftstudios.com/v1',
      'https://miuagent-api.leafcraftstudios.com',
      'https://miuagent-api.leafcraftstudios.com/api/insights/trigger',
      'https://miuagent-api.leafcraftstudios.com/api/v1/insights/trigger',
    ];

    const results = [];

    for (const endpoint of endpoints) {
      try {
        console.log(`Testing endpoint: ${endpoint}`);

        const response = await fetch(endpoint, {
          method: endpoint.includes('metrics') ? 'GET' : 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.MIUAGENT_API_KEY || 'test-key'}`,
          },
          ...(endpoint.includes('metrics') ? {} : { body: JSON.stringify(testRequest) }),
        });

        console.log(`Response status for ${endpoint}: ${response.status}`);

        const responseText = await response.text();
        let responseData;

        try {
          responseData = JSON.parse(responseText);
        } catch (e) {
          responseData = { text: responseText };
        }

        results.push({
          endpoint,
          status: response.status,
          response: responseData,
        });
      } catch (error) {
        console.error(`Error testing ${endpoint}:`, error);
        results.push({
          endpoint,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'MIUAgent API test completed',
      results,
    });
  } catch (error) {
    console.error('Error testing MIUAgent API:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
