/**
 * Simple structured logger that enhances console.log with additional context
 * This can be replaced with a more robust solution like <PERSON> or <PERSON><PERSON> in the future
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogOptions {
  module?: string;
  context?: Record<string, any>;
}

class Logger {
  private static instance: Logger;
  private isDevelopment: boolean;

  private constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private formatMessage(level: LogLevel, message: string, options?: LogOptions): string {
    const timestamp = new Date().toISOString();
    const module = options?.module ? `[${options.module}]` : '';
    return `${timestamp} ${level.toUpperCase()} ${module} ${message}`;
  }

  private logToConsole(level: LogLevel, message: string, options?: LogOptions, ...args: any[]) {
    const formattedMessage = this.formatMessage(level, message, options);
    
    // Add context as structured data
    const logData = {
      timestamp: new Date().toISOString(),
      level,
      message,
      module: options?.module,
      ...options?.context
    };

    switch (level) {
      case 'debug':
        if (this.isDevelopment) {
          console.debug(formattedMessage, ...args, logData);
        }
        break;
      case 'info':
        console.info(formattedMessage, ...args, logData);
        break;
      case 'warn':
        console.warn(formattedMessage, ...args, logData);
        break;
      case 'error':
        console.error(formattedMessage, ...args, logData);
        break;
    }
  }

  public debug(message: string, options?: LogOptions, ...args: any[]) {
    this.logToConsole('debug', message, options, ...args);
  }

  public info(message: string, options?: LogOptions, ...args: any[]) {
    this.logToConsole('info', message, options, ...args);
  }

  public warn(message: string, options?: LogOptions, ...args: any[]) {
    this.logToConsole('warn', message, options, ...args);
  }

  public error(message: string, options?: LogOptions, ...args: any[]) {
    this.logToConsole('error', message, options, ...args);
  }
}

// Export a singleton instance
export const logger = Logger.getInstance();
