import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { teamMembers, invitations } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { eq, and } from 'drizzle-orm';

// POST /api/team/invitations/cancel - Cancel a pending invitation
export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a team owner
    const userTeamMember = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, session.user.id),
        eq(teamMembers.role, 'owner')
      ),
      with: {
        team: true,
      },
    });

    if (!userTeamMember?.team) {
      return NextResponse.json({ error: 'Team not found or you are not an owner' }, { status: 403 });
    }

    const body = await req.json();
    const { invitationId } = body;

    if (!invitationId) {
      return NextResponse.json({ error: 'Invitation ID is required' }, { status: 400 });
    }

    // Check if the invitation exists and belongs to the user's team
    const invitationToCancel = await db.query.invitations.findFirst({
      where: and(
        eq(invitations.id, invitationId),
        eq(invitations.teamId, userTeamMember.team.id),
        eq(invitations.status, 'pending')
      ),
    });

    if (!invitationToCancel) {
      return NextResponse.json({ error: 'Invitation not found or already processed' }, { status: 404 });
    }

    // Mark the invitation as canceled instead of deleting it
    await db
      .update(invitations)
      .set({ status: 'canceled' })
      .where(eq(invitations.id, invitationId));

    return NextResponse.json({ success: 'Invitation canceled successfully' });
  } catch (error) {
    console.error('Error canceling invitation:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
