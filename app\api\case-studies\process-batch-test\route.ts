import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies, activityLogs, ActivityType } from '@/lib/db/schema';
import { and, isNull, eq, or, isNotNull, lt } from 'drizzle-orm';
import { processCaseStudiesBatch, sendCaseStudyToMIUAgent, updateCaseStudyWithMIUAgentData } from '@/lib/api/miuagent';

// Test endpoint for batch processing that doesn't require authentication
export async function POST(req: Request) {
  try {
    console.log('Starting test batch processing...');

    // Get cases that need processing
    const casesToProcess = await db.query.caseStudies.findMany({
      where: and(
        // isNull(caseStudies.deletedAt)
      ),
      limit: 2, // Just process 2 cases for testing
    });

    console.log(`Found ${casesToProcess.length} cases for testing`);

    if (casesToProcess.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No cases to process',
        processed: 0,
      });
    }

    // Process the cases one by one for better debugging
    const results = [];
    let successful = 0;
    let failed = 0;

    for (const caseStudy of casesToProcess) {
      try {
        console.log(`Processing case study ID: ${caseStudy.id}, Title: ${caseStudy.useCaseTitle}`);

        // Prepare the data to send to MIUAgent API
        const useCaseData = {
          use_case_id: `case-${caseStudy.id}`,
          title: caseStudy.useCaseTitle,
          short_description: caseStudy.introductionText || '',
          problem_statement: [
            caseStudy.challange1,
            caseStudy.challange2,
            caseStudy.challange3,
          ].filter(Boolean).join('\n\n'),
          industry: caseStudy.industry || '',
          role: caseStudy.role || '',
        };

        // Log what we're sending to the API
        console.log('\n==== SENDING TO MIUAGENT API ====');
        console.log(JSON.stringify(useCaseData, null, 2));
        console.log('================================\n');

        // Make the actual API call to MIUAgent API
        console.log('\n==== CALLING MIUAGENT API ====');

        // Prepare the request body according to the MIUAgent API documentation
        const requestBody = {
          id: `req-${caseStudy.id}-${Date.now()}`,
          data: JSON.stringify(useCaseData)
        };

        // Make the API call to MIUAgent
        console.log('API URL:', 'https://miuagent-api.leafcraftstudio.com/v1/insights/trigger');
        // console.log('API Key exists:', !!process.env.MIUAGENT_API_KEY);
        console.log('Request body:', JSON.stringify(requestBody));

        // Parse the response
        let apiResponse;

        try {
          // Try to call the actual API
          const response = await fetch('https://miuagent-api.leafcraftstudios.com/trigger', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              // 'Authorization': `Bearer ${process.env.MIUAGENT_API_KEY || 'test-key'}`
            },
            body: JSON.stringify(requestBody)
          });

          console.log('Response status:', response);

          if (response.ok) {
            apiResponse = await response.json();
            console.log('\n==== RECEIVED FROM MIUAGENT API ====');
            console.log(JSON.stringify(apiResponse, null, 2));
            console.log('====================================\n');
          } else {
            // If API call fails, use mock data for testing
            console.log('API call failed, using mock data for testing');
            apiResponse = {
              success: true,
              request_id: `req-${caseStudy.id}-${Date.now()}`,
              data: {
                use_case_id: `case-${caseStudy.id}`,
                fetched_at: new Date().toISOString(),
                MarketIntelligenceSection: [
                  {
                    title: "NS Insights Hub",
                    description: "Expert analysis and research findings",
                    insights: [
                      {
                        type: "trend",
                        summary: "[Appian](https://appian.com) predicts a 40% growth in the adoption of process intelligence technologies by 2027, with a 35% improvement in claims processing efficiency for insurance companies.",
                        source_url: "https://appian.com/blog/acp/insurance/process-intelligence-insurance"
                      },
                      {
                        type: "trend",
                        summary: "[Skan.ai](https://skan.ai) forecasts a 30% increase in claims automation by 2026, resulting in cost reductions of over 20% and a significant enhancement in customer satisfaction for insurers.",
                        source_url: "https://www.skan.ai/blogs/data-driven-insurance-top-process-intelligence-use-cases"
                      }
                    ]
                  },
                  {
                    title: "Business Impact",
                    description: "How AI affects business operations",
                    insights: [
                      {
                        type: "Case Study",
                        summary: "[Tractable](https://tractable.ai/) achieved a 90% reduction in claims processing time by implementing AI-driven automation for damage assessments, resulting in $1 billion savings annually.",
                        source_url: "https://research.aimultiple.com/insurance-claims-ai/"
                      }
                    ]
                  }
                ],
                MetricsDashboardSection: {
                  "Market Size ($ Value)": 198130000000,
                  "CAGR": 13.3,
                  "ROI Range": 40
                }
              }
            };
          }
        } catch (fetchError) {
          // If fetch fails, use mock data for testing
          console.error('Fetch error details:', fetchError);
          console.log('Fetch failed, using mock data for testing');
          apiResponse = {
            success: true,
            request_id: `req-${caseStudy.id}-${Date.now()}`,
            data: {
              use_case_id: `case-${caseStudy.id}`,
              fetched_at: new Date().toISOString(),
              MarketIntelligenceSection: [
                {
                  title: "NS Insights Hub",
                  description: "Expert analysis and research findings",
                  insights: [
                    {
                      type: "trend",
                      summary: "[Appian](https://appian.com) predicts a 40% growth in the adoption of process intelligence technologies by 2027, with a 35% improvement in claims processing efficiency for insurance companies.",
                      source_url: "https://appian.com/blog/acp/insurance/process-intelligence-insurance"
                    },
                    {
                      type: "trend",
                      summary: "[Skan.ai](https://skan.ai) forecasts a 30% increase in claims automation by 2026, resulting in cost reductions of over 20% and a significant enhancement in customer satisfaction for insurers.",
                      source_url: "https://www.skan.ai/blogs/data-driven-insurance-top-process-intelligence-use-cases"
                    }
                  ]
                },
                {
                  title: "Business Impact",
                  description: "How AI affects business operations",
                  insights: [
                    {
                      type: "Case Study",
                      summary: "[Tractable](https://tractable.ai/) achieved a 90% reduction in claims processing time by implementing AI-driven automation for damage assessments, resulting in $1 billion savings annually.",
                      source_url: "https://research.aimultiple.com/insurance-claims-ai/"
                    }
                  ]
                }
              ],
              MetricsDashboardSection: {
                "Market Size ($ Value)": 198130000000,
                "CAGR": 13.3,
                "ROI Range": 40
              }
            }
          };
        }

        // Update the case study with the API response data
        const updatedCaseStudy = updateCaseStudyWithMIUAgentData(caseStudy, apiResponse.data);

        // Log the market intelligence data that will be stored
        console.log('\n==== MARKET INTELLIGENCE DATA TO BE STORED ====');
        console.log('Market Intelligence Data:', apiResponse.data.MarketIntelligenceSection ? 'Present' : 'Not present');
        console.log('Market Metrics Data:', apiResponse.data.MetricsDashboardSection ? 'Present' : 'Not present');
        console.log('====================================\n');

        // Save the updated case study to the database
        await db
          .update(caseStudies)
          .set({
            ...updatedCaseStudy,
            // Store the market intelligence data
            marketIntelligenceData: apiResponse.data.MarketIntelligenceSection,
            marketMetricsData: apiResponse.data.MetricsDashboardSection,
            marketSize: apiResponse.data.MetricsDashboardSection?.['Market Size ($ Value)'] || null,
            marketCAGR: apiResponse.data.MetricsDashboardSection?.['CAGR'] || null,
            marketROI: apiResponse.data.MetricsDashboardSection?.['ROI Range'] || null,
            // Update AI processing fields
            aiProcessed: new Date(),
            aiProcessingStatus: 'success',
            aiProcessingError: null,
            aiRequestId: apiResponse.request_id,
            updatedAt: new Date(),
          })
          .where(eq(caseStudies.id, caseStudy.id));

        successful++;
        results.push({
          id: caseStudy.id,
          title: caseStudy.useCaseTitle,
          success: true,
          requestId: apiResponse.request_id,
        });

        console.log(`Successfully processed case study ID: ${caseStudy.id}`);
      } catch (error) {
        failed++;
        results.push({
          id: caseStudy.id,
          title: caseStudy.useCaseTitle,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });

        console.error(`Failed to process case study ID: ${caseStudy.id}. Error:`, error);

        // Update the case study with the error
        await db
          .update(caseStudies)
          .set({
            aiProcessingStatus: 'error',
            aiProcessingError: error instanceof Error ? error.message : 'Unknown error',
            updatedAt: new Date(),
          })
          .where(eq(caseStudies.id, caseStudy.id));
      }
    }

    // Get the updated case studies to verify changes
    const updatedCases = await db.query.caseStudies.findMany({
      where: and(
        // isNull(caseStudies.deletedAt),
        or(
          ...casesToProcess.map(c => eq(caseStudies.id, c.id))
        )
      ),
    });

    // Format the updated cases for the response
    const updatedCaseInfo = updatedCases.map(c => ({
      id: c.id,
      title: c.useCaseTitle,
      industry: c.industry,
      role: c.role,
      aiProcessed: c.aiProcessed,
      aiProcessingStatus: c.aiProcessingStatus,
      aiRequestId: c.aiRequestId,
      introductionTitle: c.introductionTitle,
      // Market intelligence data
      hasMarketIntelligence: !!c.marketIntelligenceData,
      hasMarketMetrics: !!c.marketMetricsData,
      marketSize: c.marketSize,
      marketCAGR: c.marketCAGR,
      marketROI: c.marketROI,
    }));

    return NextResponse.json({
      success: true,
      message: `Processed ${casesToProcess.length} cases`,
      processed: casesToProcess.length,
      successful,
      failed,
      results,
      updatedCases: updatedCaseInfo,
    });
  } catch (error) {
    console.error('Error in test batch processing:', error);
    return new NextResponse(
      JSON.stringify({
        success: false,
        error: 'Internal Error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
