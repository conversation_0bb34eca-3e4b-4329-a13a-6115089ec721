/**
 * Debug script to verify the complete purchase flow
 */

async function debugPurchaseFlow() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🔍 Debugging Complete Purchase Flow...\n');

  try {
    // Test 1: Check current user state
    console.log('1️⃣ Checking current user state...');
    
    const teamResponse = await fetch(`${baseUrl}/api/user/team`);
    if (teamResponse.status === 401) {
      console.log('❌ Not authenticated - please sign in first');
      return;
    }
    
    if (teamResponse.ok) {
      const teamData = await teamResponse.json();
      console.log('✅ Current team state:');
      console.log('   Team ID:', teamData.id);
      console.log('   Plan Name:', teamData.planName || 'NULL');
      console.log('   Subscription Status:', teamData.subscriptionStatus || 'NULL');
      console.log('   Stripe Customer ID:', teamData.stripeCustomerId || 'NULL');
      console.log('   Stripe Subscription ID:', teamData.stripeSubscriptionId || 'NULL');
    }

    // Test 2: Check subscription API
    console.log('\n2️⃣ Testing subscription API...');
    const subResponse = await fetch(`${baseUrl}/api/user/subscription`);
    
    if (subResponse.ok) {
      const subData = await subResponse.json();
      console.log('✅ Subscription API response:');
      console.log('   Has Subscription:', subData.hasSubscription);
      
      if (subData.hasSubscription) {
        console.log('   Plan Name:', subData.subscription?.planName);
        console.log('   Status:', subData.subscription?.status);
        console.log('   Is Active:', subData.subscription?.isActive);
        console.log('   Data Inconsistency:', subData.subscription?.dataInconsistency);
      } else {
        console.log('   Redirect To:', subData.redirectTo);
        console.log('   Message:', subData.message);
      }
    }

    // Test 3: Check available plans
    console.log('\n3️⃣ Checking available plans...');
    const plansResponse = await fetch(`${baseUrl}/api/plans`);
    
    if (plansResponse.ok) {
      const plansData = await plansResponse.json();
      console.log('✅ Available plans:');
      plansData.forEach(plan => {
        console.log(`   - ${plan.name}: $${plan.monthlyPrice}/month (${plan.stripePriceId})`);
      });
    }

    // Test 4: Test sync functionality
    console.log('\n4️⃣ Testing subscription sync...');
    const syncResponse = await fetch(`${baseUrl}/api/subscriptions/sync`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (syncResponse.ok) {
      const syncData = await syncResponse.json();
      console.log('✅ Sync response:');
      console.log('   Success:', syncData.success);
      console.log('   Message:', syncData.message);
    }

    console.log('\n📋 Purchase Flow Analysis:');
    console.log('✅ Fixed: Billing page now redirects to pricing when no plan');
    console.log('✅ Enhanced: Better error handling in checkout success handler');
    console.log('✅ Added: Comprehensive logging for database updates');
    
    console.log('\n🔧 Next Steps for Testing:');
    console.log('1. Visit /pricing and purchase a plan');
    console.log('2. Check server logs for database update messages');
    console.log('3. After purchase, visit /dashboard/billing to verify plan shows');
    console.log('4. If plan doesn\'t show, click "Sync Subscription" button');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugPurchaseFlow();
