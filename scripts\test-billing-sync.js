/**
 * Test script to check billing page subscription sync
 */

async function testBillingSync() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Testing Billing Page Subscription Sync...\n');

  try {
    // Test 1: Check user team endpoint
    console.log('1️⃣ Testing GET /api/user/team...');
    const teamResponse = await fetch(`${baseUrl}/api/user/team`);
    
    if (teamResponse.status === 401) {
      console.log('❌ Not authenticated - please sign in first');
      return;
    }
    
    if (teamResponse.ok) {
      const teamData = await teamResponse.json();
      console.log('✅ Team data retrieved:');
      console.log('   Team ID:', teamData.id);
      console.log('   Plan Name:', teamData.planName || 'NULL');
      console.log('   Subscription Status:', teamData.subscriptionStatus || 'NULL');
      console.log('   Stripe Customer ID:', teamData.stripeCustomerId || 'NULL');
      console.log('   Stripe Subscription ID:', teamData.stripeSubscriptionId || 'NULL');
      
      if (!teamData.planName && teamData.stripeSubscriptionId) {
        console.log('⚠️  Issue detected: Has Stripe subscription but no plan name');
        console.log('   This indicates a sync issue between Stripe and database');
      }
    } else {
      console.log('❌ Failed to get team data:', teamResponse.status);
    }

    // Test 2: Check subscription sync endpoint
    console.log('\n2️⃣ Testing POST /api/subscriptions/sync...');
    const syncResponse = await fetch(`${baseUrl}/api/subscriptions/sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    if (syncResponse.ok) {
      const syncData = await syncResponse.json();
      console.log('✅ Sync response:');
      console.log('   Success:', syncData.success);
      console.log('   Message:', syncData.message);
      
      if (syncData.validation) {
        console.log('   Validation Valid:', syncData.validation.isValid);
        if (!syncData.validation.isValid) {
          console.log('   Validation Issues:', syncData.validation.issues);
        }
      }
    } else {
      console.log('❌ Sync failed:', syncResponse.status);
    }

    // Test 3: Check user subscription endpoint
    console.log('\n3️⃣ Testing GET /api/user/subscription...');
    const subResponse = await fetch(`${baseUrl}/api/user/subscription`);
    
    if (subResponse.ok) {
      const subData = await subResponse.json();
      console.log('✅ Subscription data:');
      console.log('   Has Subscription:', subData.hasSubscription);
      
      if (subData.hasSubscription && subData.subscription) {
        console.log('   Plan Name:', subData.subscription.planName);
        console.log('   Plan Display Name:', subData.subscription.planDisplayName);
        console.log('   Status:', subData.subscription.status);
        console.log('   Is Active:', subData.subscription.isActive);
        console.log('   Is Trialing:', subData.subscription.isTrialing);
      }
      
      if (subData.redirectTo) {
        console.log('   Redirect To:', subData.redirectTo);
      }
    } else {
      console.log('❌ Failed to get subscription data:', subResponse.status);
    }

    console.log('\n🎉 Billing sync tests completed!');
    console.log('\n📝 Recommendations:');
    console.log('1. If you see "Has Stripe subscription but no plan name", run the sync endpoint');
    console.log('2. Check the billing page and click "Sync Subscription" if plan is not showing');
    console.log('3. Verify that Stripe webhooks are properly configured');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testBillingSync();
