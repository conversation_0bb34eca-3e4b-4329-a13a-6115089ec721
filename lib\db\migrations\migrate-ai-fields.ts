import { db } from '../drizzle';
import { sql } from 'drizzle-orm';

async function migrateAiFields() {
  console.log('Running migration to add AI processing fields...');

  try {
    // Add AI processing fields to case_studies table
    await db.execute(sql`
      ALTER TABLE "case_studies" 
      ADD COLUMN IF NOT EXISTS "ai_processed" timestamp,
      ADD COLUMN IF NOT EXISTS "ai_processing_status" varchar(50),
      ADD COLUMN IF NOT EXISTS "ai_processing_error" text,
      ADD COLUMN IF NOT EXISTS "ai_request_id" varchar(100)
    `);

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateAiFields().catch(console.error);
