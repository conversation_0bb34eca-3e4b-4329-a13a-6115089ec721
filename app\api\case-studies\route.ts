import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { caseStudies, caseStudyIcons } from '@/lib/db/schema';
import { getSession } from '@/lib/auth/session';
import { z } from 'zod';
import { getTeamForUser } from '@/lib/db/queries';
import { getPlanLimit } from '@/lib/config/plans';
import { eq, sql, and, isNull } from 'drizzle-orm';

// Type for case study with icons and user
type CaseStudyWithIcons = typeof caseStudies.$inferSelect & {
  icons: (typeof caseStudyIcons.$inferSelect)[];
  user?: {
    id: number;
    name: string | null;
    email: string;
  };
};

// Format URL for images
function formatUrl(url: string | null): string | null {
  if (!url) return null;

  // If URL is already a full URL, return it
  if (url.startsWith('http')) return url;

  // Otherwise, it's a relative path, prepend the base URL
  return `${process.env.NEXT_PUBLIC_BASE_URL || ''}${url}`;
}

// Validation schema for case study
const caseStudySchema = z.object({
  useCaseTitle: z.string().min(1),
  industry: z.string().optional(),
  role: z.string().optional(),
  vector: z.string().optional(),
  headerImage: z.string().optional(),
  thumbnailImage: z.string().optional(),
  previewImageUrl: z.string().optional(),
  kpis: z.object({
    claimCycleTime: z.string(),
    straightThroughRate: z.string(),
    customerComplaintVolume: z.string()
  }),
  introduction: z.object({
    title: z.string(),
    text: z.string(),
    problems: z.array(z.string()),
    questions: z.array(z.string())
  }),
  process: z.object({
    title: z.string(),
    steps: z.array(z.object({
      title: z.string(),
      description: z.string(),
      icon: z.string().optional()
    }))
  }),
  solution: z.object({
    title: z.string(),
    description: z.string(),
    items: z.array(z.object({
      title: z.string(),
      description: z.string(),
      icon: z.string().optional()
    }))
  }),
  impact: z.object({
    title: z.string(),
    metrics: z.array(z.object({
      metric: z.string(),
      value: z.string(),
      description: z.string(),
      icon: z.string().optional()
    }))
  }),
  conclusion: z.object({
    title: z.string(),
    text: z.string()
  })
});

export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const body = await req.json();
    console.log('Received case study data:', body);

    // Check if the data is in flat format (from AddCaseForm) or nested format (from validation schema)
    // If useCaseTitle exists but kpis is not an object, it's likely from the flat form
    const isFlat = body.useCaseTitle && (typeof body.kpis !== 'object' || !body.kpis);

    let dbData;
    if (isFlat) {
      // Handle flat structure from AddCaseForm
      const {
        headerImage,
        ...flatData
      } = body;

      dbData = {
        useCaseTitle: flatData.useCaseTitle,
        industry: flatData.industry || null,
        role: flatData.role || null,
        potentiallyImpactedKpis: flatData.potentiallyImpactedKpis || null,
        introductionTitle: flatData.introductionTitle || null,
        introductionText: flatData.introductionText || null,
        transitionToChallange: flatData.transitionToChallange || null,
        challange1: flatData.challange1 || null,
        challange2: flatData.challange2 || null,
        challange3: flatData.challange3 || null,
        transitionToQuestions: flatData.transitionToQuestions || null,
        question1: flatData.question1 || null,
        question2: flatData.question2 || null,
        question3: flatData.question3 || null,
        processTitle: flatData.processTitle || null,
        processStep1Title: flatData.processStep1Title || null,
        processStep1Description: flatData.processStep1Description || null,
        processStep2Title: flatData.processStep2Title || null,
        processStep2Description: flatData.processStep2Description || null,
        processStep3Title: flatData.processStep3Title || null,
        processStep3Description: flatData.processStep3Description || null,
        solutionTitle: flatData.solutionTitle || null,
        solutionDescription: flatData.solutionDescription || null,
        solution1Title: flatData.solution1Title || null,
        solution1Description: flatData.solution1Description || null,
        solution2Title: flatData.solution2Title || null,
        solution2Description: flatData.solution2Description || null,
        solution3Title: flatData.solution3Title || null,
        solution3Description: flatData.solution3Description || null,
        impactTitle: flatData.impactTitle || null,
        potentialImpact1: flatData.potentialImpact1 || null,
        potentialImpact2: flatData.potentialImpact2 || null,
        potentialImpact3: flatData.potentialImpact3 || null,
        potentialImpact4: flatData.potentialImpact4 || null,
        conclusionTitle: flatData.conclusionTitle || null,
        conclusionText: flatData.conclusionText || null
      };
    } else {
      // Handle nested structure (from validation schema)
      try {
        const validatedData = caseStudySchema.parse(body);
        const {
          headerImage,
          thumbnailImage,
          ...nestedData
        } = validatedData;

        dbData = {
          useCaseTitle: nestedData.useCaseTitle,
          industry: nestedData.industry || null,
          role: nestedData.role || null,
          potentiallyImpactedKpis: nestedData.kpis ? JSON.stringify(nestedData.kpis) : null,
          introductionTitle: nestedData.introduction?.title || null,
          introductionText: nestedData.introduction?.text || null,
          challange1: nestedData.introduction?.problems?.[0] || null,
          challange2: nestedData.introduction?.problems?.[1] || null,
          challange3: nestedData.introduction?.problems?.[2] || null,
          question1: nestedData.introduction?.questions?.[0] || null,
          question2: nestedData.introduction?.questions?.[1] || null,
          question3: nestedData.introduction?.questions?.[2] || null,
          processTitle: nestedData.process?.title || null,
          // Map process steps
          processStep1Title: nestedData.process?.steps?.[0]?.title || null,
          processStep1Description: nestedData.process?.steps?.[0]?.description || null,
          processStep2Title: nestedData.process?.steps?.[1]?.title || null,
          processStep2Description: nestedData.process?.steps?.[1]?.description || null,
          processStep3Title: nestedData.process?.steps?.[2]?.title || null,
          processStep3Description: nestedData.process?.steps?.[2]?.description || null,
          // Map solution
          solutionTitle: nestedData.solution?.title || null,
          solutionDescription: nestedData.solution?.description || null,
          solution1Title: nestedData.solution?.items?.[0]?.title || null,
          solution1Description: nestedData.solution?.items?.[0]?.description || null,
          solution2Title: nestedData.solution?.items?.[1]?.title || null,
          solution2Description: nestedData.solution?.items?.[1]?.description || null,
          solution3Title: nestedData.solution?.items?.[2]?.title || null,
          solution3Description: nestedData.solution?.items?.[2]?.description || null,
          solution1: nestedData.solution?.items?.[0]?.description || null,
          solution2: nestedData.solution?.items?.[1]?.description || null,
          solution3: nestedData.solution?.items?.[2]?.description || null,
          solution4: nestedData.solution?.items?.[3]?.description || null,
          solution5: nestedData.solution?.items?.[4]?.description || null,
          // Map impact
          impactTitle: nestedData.impact?.title || null,
          potentialImpact1: nestedData.impact?.metrics?.[0]?.description || null,
          potentialImpact2: nestedData.impact?.metrics?.[1]?.description || null,
          potentialImpact3: nestedData.impact?.metrics?.[2]?.description || null,
          potentialImpact4: nestedData.impact?.metrics?.[3]?.description || null,
          // Map conclusion
          conclusionTitle: nestedData.conclusion?.title || null,
          conclusionText: nestedData.conclusion?.text || null
        };
      } catch (error) {
        console.error('Validation error:', error);
        return new NextResponse('Invalid case study data', { status: 400 });
      }
    }

    // Insert case study with the user ID from the session
    const result = await db.insert(caseStudies).values({
      ...dbData,
      userId: session.user.id,
      // deletedAt: null // Explicitly set to null to prevent accidental deletion
    }).returning();
    const caseStudy = result[0];

    // Extract headerImage from original body
    const headerImage = body.headerImage;

    // Insert images if provided
    const imageInserts = [];

    if (headerImage) {
      imageInserts.push({
        caseStudyId: caseStudy.id,
        iconType: 'header',
        iconUrl: headerImage,
        order: 0,
      });
    }

    // Add other image types if they exist in the original request
    if (body.thumbnailImage) {
      imageInserts.push({
        caseStudyId: caseStudy.id,
        iconType: 'icon',
        iconUrl: body.thumbnailImage,
        order: 0,
      });
    }

    // Process step icons
    if (body.process?.steps) {
      body.process.steps.forEach((step: any, index: number) => {
        if (step.icon) {
          imageInserts.push({
            caseStudyId: caseStudy.id,
            iconType: 'process',
            iconUrl: step.icon,
            order: index,
          });
        }
      });
    }

    // Solution icons
    if (body.solution?.items) {
      body.solution.items.forEach((item: any, index: number) => {
        if (item.icon) {
          imageInserts.push({
            caseStudyId: caseStudy.id,
            iconType: 'solution',
            iconUrl: item.icon,
            order: index,
          });
        }
      });
    }

    // Impact icons
    if (body.impact?.metrics) {
      body.impact.metrics.forEach((metric: any, index: number) => {
        if (metric.icon) {
          imageInserts.push({
            caseStudyId: caseStudy.id,
            iconType: 'impact',
            iconUrl: metric.icon,
            order: index,
          });
        }
      });
    }

    // Note: We've already handled all image types above

    // Insert all images
    if (imageInserts.length > 0) {
      await db.insert(caseStudyIcons).values(imageInserts);
    }

    return NextResponse.json(caseStudy);
  } catch (error) {
    console.error('Error creating case study:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}

export async function GET(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const url = new URL(req.url);
    const { searchParams } = url;

    // Check case study access limits for non-owners
    if (session.user.role !== 'owner') {
      const team = await getTeamForUser(session.user.id);
      const planName = team?.planName || 'starter';
      const caseStudyLimit = getPlanLimit(planName, 'maxCaseStudies');

      // If not unlimited (-1), apply the limit
      if (caseStudyLimit !== -1) {
        const requestedPageSize = parseInt(searchParams.get('pageSize') || '10');
        const requestedPage = parseInt(searchParams.get('page') || '1');
        const maxAllowedResults = Math.min(caseStudyLimit, requestedPageSize);

        // For Starter plan (10 case studies), only allow access to first 10 case studies
        if (planName === 'starter') {
          // Override pagination to ensure we never exceed the limit
          searchParams.set('pageSize', Math.min(10, requestedPageSize).toString());
          if (requestedPage > 1) {
            // Calculate if this page would exceed the limit
            const startIndex = (requestedPage - 1) * requestedPageSize;
            if (startIndex >= caseStudyLimit) {
              return NextResponse.json({
                caseStudies: [],
                pagination: {
                  total: caseStudyLimit,
                  page: requestedPage,
                  pageSize: requestedPageSize,
                  totalPages: Math.ceil(caseStudyLimit / requestedPageSize),
                },
                limitReached: true,
                message: `Your ${planName} plan allows access to only ${caseStudyLimit} curated case studies. Upgrade to Pro for unlimited access.`
              });
            }
          }
        }
      }
    }

    // Get ID from URL path or query parameter
    let id: string | null = null;

    // Check if the URL contains an ID in the path (e.g., /api/case-studies/123)
    const pathParts = url.pathname.split('/');
    const pathId = pathParts[pathParts.length - 1];
    if (pathId && !isNaN(parseInt(pathId))) {
      id = pathId;
    } else {
      // Fall back to query parameter (e.g., /api/case-studies?id=123)
      id = searchParams.get('id');
    }

    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const withMarketData = searchParams.get('withMarketData') === 'true';
    const searchQuery = searchParams.get('search') || '';

    if (id) {
      // Get single case study with its icons - explicitly filter out soft-deleted records
      const caseStudy = await db.query.caseStudies.findFirst({
        where: and(
          eq(caseStudies.id, parseInt(id)),
          // isNull(caseStudies.deletedAt) // Ensure we only get non-deleted records
        ),
        with: {
          icons: {
            orderBy: (icons, { asc }) => [asc(icons.order)]
          },
          user: {
            columns: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }) as CaseStudyWithIcons | null;

      if (!caseStudy) {
        return new NextResponse('Case study not found', { status: 404 });
      }

      // Format all URLs
      const formattedCaseStudy = {
        ...caseStudy,
        featureImageUrl: formatUrl(caseStudy.featureImageUrl as string | null),
        previewImageUrl: formatUrl(caseStudy.previewImageUrl as string | null),
        icons: caseStudy.icons.map(icon => ({
          ...icon,
          iconUrl: formatUrl(icon.iconUrl as string | null)
        }))
      };

      return NextResponse.json(formattedCaseStudy);
    }

    // Build the query based on filters
    let query = db.select().from(caseStudies);

    // Add search filter if provided
    if (searchQuery) {
      const searchCondition = sql`
        ${caseStudies.useCaseTitle} ILIKE ${`%${searchQuery}%`} OR
        ${caseStudies.industry} ILIKE ${`%${searchQuery}%`} OR
        ${caseStudies.role} ILIKE ${`%${searchQuery}%`}
      `;
      query = db.select().from(caseStudies).where(searchCondition) as any;
    }

    // Add market data filter if requested
    if (withMarketData) {
      const marketCondition = sql`
        ${caseStudies.marketIntelligenceData} IS NOT NULL OR
        ${caseStudies.marketMetricsData} IS NOT NULL
      `;
      query = db.select().from(caseStudies).where(marketCondition) as any;
    }

    // Get total count for pagination
    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(query.as('filtered_case_studies'))
      .then((result) => result[0].count);

    // Get paginated case studies with their icons and user information
    console.log('Fetching case studies with user information');
    const allCaseStudies = await db.query.caseStudies.findMany({
      with: {
        icons: {
          orderBy: (icons, { asc }) => [asc(icons.order)]
        },
        user: {
          columns: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      limit: pageSize,
      offset: (page - 1) * pageSize,
      orderBy: (caseStudies, { desc }) => [desc(caseStudies.createdAt)],
      where: (caseStudies, { and, or, sql }) => {
        const conditions = [];

        // Add search filter if provided
        if (searchQuery) {
          conditions.push(
            or(
              sql`LOWER(${caseStudies.useCaseTitle}) LIKE LOWER(${'%' + searchQuery + '%'})`,
              sql`LOWER(${caseStudies.industry}) LIKE LOWER(${'%' + searchQuery + '%'})`,
              sql`LOWER(${caseStudies.role}) LIKE LOWER(${'%' + searchQuery + '%'})`
            ) as any
          );
        }

        // Add market data filter if requested
        if (withMarketData) {
          conditions.push(
            or(
              sql`${caseStudies.marketIntelligenceData} IS NOT NULL`,
              sql`${caseStudies.marketMetricsData} IS NOT NULL`
            ) as any
          );
        }

        return and(...conditions);
      }
    }) as CaseStudyWithIcons[];

    // Format URLs for all case studies
    console.log('Case studies before formatting:', allCaseStudies.map(cs => ({
      id: cs.id,
      title: cs.useCaseTitle,
      userId: cs.userId,
      user: cs.user ? { id: cs.user.id, name: cs.user.name } : null
    })));

    // Log any case studies that have a userId but no user object
    const missingUserInfo = allCaseStudies.filter(cs => cs.userId && !cs.user);
    if (missingUserInfo.length > 0) {
      console.log('WARNING: Found case studies with userId but no user object:', missingUserInfo.map(cs => ({
        id: cs.id,
        title: cs.useCaseTitle,
        userId: cs.userId
      })));
    }

    const formattedCaseStudies = allCaseStudies.map(caseStudy => {
      const formatted = {
        ...caseStudy,
        featureImageUrl: formatUrl(caseStudy.featureImageUrl as string | null),
        previewImageUrl: formatUrl(caseStudy.previewImageUrl as string | null),
        icons: caseStudy.icons.map(icon => ({
          ...icon,
          iconUrl: formatUrl(icon.iconUrl as string | null)
        }))
      };
      console.log(`Formatted case study ${caseStudy.id} - userId: ${caseStudy.userId}, user:`, caseStudy.user);
      return formatted;
    });

    // Apply case study limits for non-owners
    let finalCaseStudies = formattedCaseStudies;
    let finalTotal = totalCount;
    let limitMessage = undefined;

    if (session.user.role !== 'owner') {
      const team = await getTeamForUser(session.user.id);
      const planName = team?.planName || 'starter';
      const caseStudyLimit = getPlanLimit(planName, 'maxCaseStudies');

      if (caseStudyLimit !== -1) {
        // Limit the results to the plan's case study limit
        finalCaseStudies = formattedCaseStudies.slice(0, Math.min(caseStudyLimit, formattedCaseStudies.length));
        finalTotal = Math.min(totalCount, caseStudyLimit);

        if (totalCount > caseStudyLimit) {
          limitMessage = `Your ${planName} plan allows access to only ${caseStudyLimit} curated case studies. Upgrade to Pro for unlimited access.`;
        }
      }
    }

    return NextResponse.json({
      caseStudies: finalCaseStudies,
      pagination: {
        total: finalTotal,
        page,
        pageSize,
        totalPages: Math.ceil(finalTotal / pageSize),
      },
      ...(limitMessage && { limitReached: true, message: limitMessage }),
    });
  } catch (error) {
    console.error('Error fetching case studies:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}



export async function PUT(req: Request) {
  try {
    console.log('=== CASE STUDY UPDATE REQUEST STARTED ===');
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    console.log('User authenticated:', session.user.id);

    // Get ID from URL path or query parameter
    let id: string | null = null;
    const url = new URL(req.url);

    // Check if the URL contains an ID in the path (e.g., /api/case-studies/123)
    const pathParts = url.pathname.split('/');
    const pathId = pathParts[pathParts.length - 1];
    if (pathId && !isNaN(parseInt(pathId))) {
      id = pathId;
    } else {
      // Fall back to query parameter (e.g., /api/case-studies?id=123)
      id = url.searchParams.get('id');
    }

    if (!id) {
      return new NextResponse('ID is required', { status: 400 });
    }

    const body = await req.json();
    console.log('Received update data:', JSON.stringify({
      id: body.id,
      title: body.useCaseTitle,
      headerImage: body.headerImage,
      iconImage: body.iconImage,
      hasProcess: !!body.process,
      hasSolution: !!body.solution,
      hasImpact: !!body.impact
    }, null, 2));

    // Check if the data is in flat format (from AddCaseForm) or nested format (from validation schema)
    const isFlat = typeof body.kpis !== 'object' && body.introductionTitle !== undefined;

    // Extract image URLs from the body for later use with icons
    // These need to be declared outside the conditional blocks to be accessible later
    let headerImage = body.headerImage;
    let iconImage = body.iconImage;

    console.log('Initial image values:', { headerImage, iconImage });

    let dbData;
    if (isFlat) {
      // Handle flat structure from AddCaseForm
      // Destructure without headerImage to avoid conflicts
      const {
        headerImage: _headerImage, // Already extracted above
        iconImage: _iconImage,     // Already extracted above
        ...flatData
      } = body;

      dbData = {
        useCaseTitle: flatData.useCaseTitle,
        industry: flatData.industry || null,
        role: flatData.role || null,
        potentiallyImpactedKpis: flatData.potentiallyImpactedKpis || null,
        introductionTitle: flatData.introductionTitle || null,
        introductionText: flatData.introductionText || null,
        transitionToChallange: flatData.transitionToChallange || null,
        challange1: flatData.challange1 || null,
        challange2: flatData.challange2 || null,
        challange3: flatData.challange3 || null,
        transitionToQuestions: flatData.transitionToQuestions || null,
        question1: flatData.question1 || null,
        question2: flatData.question2 || null,
        question3: flatData.question3 || null,
        processTitle: flatData.processTitle || null,
        processStep1Title: flatData.processStep1Title || null,
        processStep1Description: flatData.processStep1Description || null,
        processStep2Title: flatData.processStep2Title || null,
        processStep2Description: flatData.processStep2Description || null,
        processStep3Title: flatData.processStep3Title || null,
        processStep3Description: flatData.processStep3Description || null,
        solutionTitle: flatData.solutionTitle || null,
        solutionDescription: flatData.solutionDescription || null,
        solution1Title: flatData.solution1Title || null,
        solution1Description: flatData.solution1Description || null,
        solution2Title: flatData.solution2Title || null,
        solution2Description: flatData.solution2Description || null,
        solution3Title: flatData.solution3Title || null,
        solution3Description: flatData.solution3Description || null,
        impactTitle: flatData.impactTitle || null,
        potentialImpact1: flatData.potentialImpact1 || null,
        potentialImpact2: flatData.potentialImpact2 || null,
        potentialImpact3: flatData.potentialImpact3 || null,
        potentialImpact4: flatData.potentialImpact4 || null,
        conclusionTitle: flatData.conclusionTitle || null,
        conclusionText: flatData.conclusionText || null,
        updatedAt: new Date()
      };
    } else {
      // Handle nested structure (from validation schema)
      try {
        const validatedData = caseStudySchema.parse(body);

        dbData = {
          useCaseTitle: validatedData.useCaseTitle,
          industry: validatedData.industry,
          role: validatedData.role,
          potentiallyImpactedKpis: JSON.stringify(validatedData.kpis),
          introductionTitle: validatedData.introduction.title,
          introductionText: validatedData.introduction.text,
          challange1: validatedData.introduction.problems[0],
          challange2: validatedData.introduction.problems[1],
          challange3: validatedData.introduction.problems[2],
          question1: validatedData.introduction.questions[0],
          question2: validatedData.introduction.questions[1],
          question3: validatedData.introduction.questions[2],
          processTitle: validatedData.process.title,
          // Map process steps
          processStep1Title: validatedData.process.steps[0]?.title || null,
          processStep1Description: validatedData.process.steps[0]?.description || null,
          processStep2Title: validatedData.process.steps[1]?.title || null,
          processStep2Description: validatedData.process.steps[1]?.description || null,
          processStep3Title: validatedData.process.steps[2]?.title || null,
          processStep3Description: validatedData.process.steps[2]?.description || null,
          // Map solution
          solutionTitle: validatedData.solution.title,
          solutionDescription: validatedData.solution.description,
          solution1Title: validatedData.solution.items[0]?.title || null,
          solution1Description: validatedData.solution.items[0]?.description || null,
          solution2Title: validatedData.solution.items[1]?.title || null,
          solution2Description: validatedData.solution.items[1]?.description || null,
          solution3Title: validatedData.solution.items[2]?.title || null,
          solution3Description: validatedData.solution.items[2]?.description || null,
          solution1: validatedData.solution.items[0]?.description || null,
          solution2: validatedData.solution.items[1]?.description || null,
          solution3: validatedData.solution.items[2]?.description || null,
          solution4: validatedData.solution.items[3]?.description || null,
          solution5: validatedData.solution.items[4]?.description || null,
          // Map impact
          impactTitle: validatedData.impact.title,
          potentialImpact1: validatedData.impact.metrics[0]?.description || null,
          potentialImpact2: validatedData.impact.metrics[1]?.description || null,
          potentialImpact3: validatedData.impact.metrics[2]?.description || null,
          potentialImpact4: validatedData.impact.metrics[3]?.description || null,
          conclusionTitle: validatedData.conclusion.title,
          conclusionText: validatedData.conclusion.text,
          updatedAt: new Date()
        };
      } catch (error) {
        console.error('Validation error:', error);
        return new NextResponse('Invalid case study data', { status: 400 });
      }
    }

    // Start a transaction to ensure both case study and icons are updated atomically
    await db.transaction(async (tx) => {
      // Get the existing case study to preserve the userId
      const existingCaseStudy = await tx.query.caseStudies.findFirst({
        where: eq(caseStudies.id, parseInt(id)),
        columns: {
          userId: true
        }
      });

      // Update the case study - explicitly set deletedAt to null to ensure it's never accidentally set
      const updated = await tx.update(caseStudies)
        .set({
          ...dbData,
          // Preserve the original userId if it exists
          userId: existingCaseStudy?.userId || null,
          // deletedAt: null // Explicitly set to null to prevent accidental deletion
        })
        .where(eq(caseStudies.id, parseInt(id)))
        .returning();

      if (updated.length === 0) {
        throw new Error('Case study not found');
      }

      // Process icons if they exist in the request
      // We already have headerImage and iconImage from earlier
      // Extract other icon types from the body
      console.log('Using headerImage for update:', headerImage);
      console.log('Using iconImage for update:', iconImage);
      const processStepIcons = body.process?.steps?.filter((step: any) => step?.icon) || [];
      const solutionIcons = body.solution?.items?.filter((item: any) => item?.icon) || [];
      const impactIcons = body.impact?.metrics?.filter((metric: any) => metric?.icon) || [];

      // Always update icons when performing an update
      console.log('Updating icons for case study:', id);
      console.log('Header image:', headerImage);
      console.log('Icon image:', iconImage);
      console.log('Process step icons:', processStepIcons.length);
      console.log('Solution icons:', solutionIcons.length);
      console.log('Impact icons:', impactIcons.length);

      // Delete existing icons first
      await tx.delete(caseStudyIcons).where(eq(caseStudyIcons.caseStudyId, parseInt(id)));

      // Prepare new icon inserts
      const iconInserts = [];

      if (headerImage) {
        console.log('Adding header image:', headerImage);
        iconInserts.push({
          caseStudyId: parseInt(id),
          iconType: 'header',
          iconUrl: headerImage,
          order: 0,
        });
      }

      if (iconImage) {
        console.log('Adding icon image:', iconImage);
        iconInserts.push({
          caseStudyId: parseInt(id),
          iconType: 'icon',
          iconUrl: iconImage,
          order: 0,
        });
      }

      // Process step icons
      if (processStepIcons?.length) {
        processStepIcons.forEach((step: any, index: number) => {
          console.log(`Adding process step ${index} icon:`, step.icon);
          iconInserts.push({
            caseStudyId: parseInt(id),
            iconType: 'process',
            iconUrl: step.icon,
            order: index,
          });
        });
      }

      // Solution icons
      if (solutionIcons?.length) {
        solutionIcons.forEach((item: any, index: number) => {
          console.log(`Adding solution ${index} icon:`, item.icon);
          iconInserts.push({
            caseStudyId: parseInt(id),
            iconType: 'solution',
            iconUrl: item.icon,
            order: index,
          });
        });
      }

      // Impact icons
      if (impactIcons?.length) {
        impactIcons.forEach((metric: any, index: number) => {
          console.log(`Adding impact ${index} icon:`, metric.icon);
          iconInserts.push({
            caseStudyId: parseInt(id),
            iconType: 'impact',
            iconUrl: metric.icon,
            order: index,
          });
        });
      }

      // Insert all new icons
      if (iconInserts.length > 0) {
        console.log(`Inserting ${iconInserts.length} icons for case study ${id}`);
        await tx.insert(caseStudyIcons).values(iconInserts);
      } else {
        console.log('No icons to insert for case study:', id);
      }
    });

    // Get the updated case study with icons using the query builder
    const updatedCaseStudy = await db.query.caseStudies.findFirst({
      where: eq(caseStudies.id, parseInt(id)),
      with: {
        icons: {
          orderBy: (icons, { asc }) => [asc(icons.order)]
        }
      }
    }) as CaseStudyWithIcons | null;

    if (!updatedCaseStudy) {
      console.error('Case study not found after update');
      return new NextResponse('Case study not found after update', { status: 404 });
    }

    // Format all URLs in the response
    const formattedCaseStudy = {
      ...updatedCaseStudy,
      featureImageUrl: formatUrl(updatedCaseStudy.featureImageUrl as string | null),
      previewImageUrl: formatUrl(updatedCaseStudy.previewImageUrl as string | null),
      icons: updatedCaseStudy.icons.map(icon => ({
        ...icon,
        iconUrl: formatUrl(icon.iconUrl as string | null)
      }))
    };

    console.log('Returning updated case study with icons:', {
      id: formattedCaseStudy.id,
      title: formattedCaseStudy.useCaseTitle,
      iconCount: formattedCaseStudy.icons.length,
      icons: formattedCaseStudy.icons.map(i => ({ type: i.iconType, url: i.iconUrl }))
    });

    return NextResponse.json(formattedCaseStudy);
  } catch (error) {
    console.error('Error updating case study:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}

export async function DELETE(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get ID from URL path or query parameter
    let id: string | null = null;
    const url = new URL(req.url);

    // Check if the URL contains an ID in the path (e.g., /api/case-studies/123)
    const pathParts = url.pathname.split('/');
    const pathId = pathParts[pathParts.length - 1];
    if (pathId && !isNaN(parseInt(pathId))) {
      id = pathId;
    } else {
      // Fall back to query parameter (e.g., /api/case-studies?id=123)
      id = url.searchParams.get('id');
    }

    if (!id) {
      return new NextResponse('ID is required', { status: 400 });
    }

    console.log(`Deleting case study with ID: ${id}`);

    // Delete case study icons first
    await db.delete(caseStudyIcons).where(eq(caseStudyIcons.caseStudyId, parseInt(id)));

    // Delete case study
    const result = await db
      .delete(caseStudies)
      .where(eq(caseStudies.id, parseInt(id)))
      .returning();

    if (result.length === 0) {
      return new NextResponse('Case study not found', { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting case study:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}