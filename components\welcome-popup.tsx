'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

export function WelcomePopup() {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Only run localStorage code on the client side after component is mounted
    if (typeof window !== 'undefined') {
      const hasSeenWelcome = localStorage.getItem('hasSeenWelcome');
      if (!hasSeenWelcome) {
        setIsOpen(true);
        localStorage.setItem('hasSeenWelcome', 'true');
      }
    }
  }, []);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[500px] p-0 gap-0">
        <div className="flex flex-col items-center p-6 text-center">
          {/* Welcome Image */}
          <div className="relative w-[300px] h-[300px] mb-6">
            <Image
              src="/welcome-illustration.svg"
              alt="Welcome"
              fill
              className="object-contain"
              priority
            />
          </div>

          {/* Welcome Text */}
          <DialogHeader className="space-y-4">
            <DialogTitle className="text-3xl font-semibold">
              Welcome to <span className="text-blue-500">Turinos</span> Dashboard!
            </DialogTitle>
            <p className="text-lg text-gray-600">
              Hello! Welcome to your dashboard. We&apos;ve designed this space to be simple and productive for you.
              Would you like a quick tour of the dashboard to get familiar with its features?
            </p>
          </DialogHeader>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 w-full mt-8">
            <Button
              variant="outline"
              className="flex-1 text-lg py-6"
              onClick={() => setIsOpen(false)}
            >
              No Thanks
            </Button>
            <Button
              className="flex-1 text-lg py-6 bg-blue-500 hover:bg-blue-600"
              onClick={() => {
                setIsOpen(false);
                // TODO: Start the tour functionality
              }}
            >
              Start My Journey
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
